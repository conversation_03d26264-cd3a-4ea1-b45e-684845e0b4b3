# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

This is an iOS project built with Xcode and Swift Package Manager. The main target is `ChatToDesign.app`.


### Opening the Project
```bash
# Open project in Xcode
open ChatToDesign.xcodeproj
```

## Project Architecture

### High-Level Structure
This is a SwiftUI-based iOS app with a modular, clean architecture organized into distinct layers:

- **Application Layer**: Use cases and business logic organized by feature modules
- **Domain Layer**: Entities, repositories, and core business models  
- **Infrastructure Layer**: External services, databases, networking, and third-party integrations
- **Presentation Layer**: SwiftUI views, view models, and UI components

### Key Architecture Patterns

#### Dependency Injection
The project uses a sophisticated modular dependency injection system with:
- `AppDependencyContainer`: Central dependency container 
- Feature-specific modules: `AuthModule`, `ChatModule`, `DesignModule`, `UserModule`, etc.
- Each module encapsulates its own dependencies and services

#### Repository Pattern
Data access is abstracted through repository interfaces:
- `ChatRepository`, `UserRepository`, `AssetRepository`, etc.
- Firebase implementations: `FirestoreChatRepository`, `FirestoreUserRepository`
- Mock implementations available for testing

#### Use Cases (Application Services)
Business logic is encapsulated in use cases:
- Authentication: `SignInUseCase`, `SignOutUseCase`
- Chat management: `CreateChatUseCase`, `GetMessagesUseCase`, `SendMessageUseCase`
- Asset management: `UploadImageWithAssetUseCase`, `DeleteAssetUseCase`

#### SWR Cache System
Advanced caching system implementing Stale-While-Revalidate pattern:
- `CacheManager`: Core caching functionality
- `QueryManager`: Query orchestration with SWR support
- `SWRHook`: Reactive data fetching with cache-first approach

### Feature Modules

#### Core Features
- **Authentication**: Firebase Auth integration with Google Sign-In
- **Chat**: Message handling with ExyteChat integration
- **Asset Management**: File upload, image processing, user asset management
- **Design Generation**: AI-powered design creation workflows
- **Video Effects**: Video processing and effects application
- **Subscription**: RevenueCat integration for premium features

#### Design System
Unified design system with:
- **Foundation**: Colors, Typography, Spacing, Animation systems
- **Tokens**: Semantic design tokens for consistency
- **Components**: `DSButton` and other reusable UI components
- **Themes**: Light/dark theme support

### Third-Party Integrations
- **Firebase**: Auth, Firestore, Storage, Analytics, Crashlytics
- **RevenueCat**: Subscription and paywall management
- **Sentry**: Error tracking and performance monitoring
- **ExyteChat**: Chat UI components
- **Kingfisher**: Image loading and caching
- **Lottie**: Animation support

### Key Files and Locations

#### Dependency Injection
- Main container: `ChatToDesign/Infrastructure/DI/AppDependencyContainer.swift`
- Module definitions: `ChatToDesign/Application/*/DI/*.swift`

#### Core Services
- User state: `ChatToDesign/Application/Services/UserStateManager.swift`
- Logging: `ChatToDesign/Infrastructure/Logger.swift`
- Cache system: `ChatToDesign/Infrastructure/Cache/`

#### UI Architecture
- Main app entry: `ChatToDesign/Application/ChatToDesignApp.swift`
- Root view: `ChatToDesign/Presentation/RootView.swift`
- Tab navigation: `ChatToDesign/Presentation/Main/MainTabView.swift`

#### Configuration
- App info: `ChatToDesign/Application/Info.plist`
- Firebase config: `ChatToDesign/Application/GoogleService-Info.plist`
- Entitlements: `ChatToDesign/ChatToDesign.entitlements`

## Development Guidelines

### Dependency Injection Usage
When creating new ViewModels, inject dependencies through the constructor:
```swift
final class MyViewModel: ObservableObject {
    private let myService: MyService
    
    init(myService: MyService = AppDependencyContainer.shared.myModule.myService) {
        self.myService = myService
    }
}
```

### Using the Cache System
For data fetching with caching, use the SWR pattern:
```swift
queryManager
    .query(
        key: "cache_key",
        maxAge: 300,      // 5 minutes
        staleTime: 3600,  // 1 hour  
        networkCall: { try await self.apiService.fetchData() }
    )
    .receive(on: DispatchQueue.main)
    .sink { result in /* handle result */ }
    .store(in: &cancellables)
```

### Error Handling
Use the centralized Logger for consistent error reporting:
```swift
Logger.error("Error message: \(error)")
Logger.info("Info message")
```


## Common Development Tasks

### Adding a New Feature Module
1. Create directory structure under `ChatToDesign/Application/NewFeature/`
2. Add DI module in `ChatToDesign/Application/NewFeature/DI/NewFeatureModule.swift`
3. Register module in `AppDependencyContainer`
4. Create use cases in `ChatToDesign/Application/NewFeature/UseCases/`
5. Add presentation layer in `ChatToDesign/Presentation/NewFeature/`

### Adding a New Repository
1. Define interface in `ChatToDesign/Domain/Interfaces/`
2. Add Firebase implementation in `ChatToDesign/Infrastructure/ThirdParty/Firebase/Database/`
3. Register in appropriate module's DI container
4. Create mock implementation for testing

### Working with the Design System
- Use existing design tokens from `ChatToDesign/DesignSystem/`
- Prefer `DSButton` over custom button implementations
- Follow the established color, typography, and spacing systems

The project is currently in active development with recent focus on design system unification and enhanced subscription management.