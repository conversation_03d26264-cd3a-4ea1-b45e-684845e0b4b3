---
description: 
globs: ChatToDesign/Presentation/**/*.swift
alwaysApply: false
---
---
type: agent_requested
description: Provides guidelines for generating modern, intuitive, and HIG-compliant SwiftUI UI/UX. Focuses on clarity, fluidity, content-first design, platform capabilities, and consistency for a great user experience.
---

# SwiftUI UI/UX Best Practices Rule

When generating SwiftUI code for UI elements or views, adhere to the following modern UI/UX principles in addition to the standard Apple Human Interface Guidelines (HIG):

## 1. Simplicity and Clarity (简洁与清晰)

*   **Prioritize Core Task:** Each view should have a clear primary purpose. Avoid cluttering screens with secondary actions or information.
*   **Minimalism:** Use only necessary UI elements. Prefer standard SwiftUI controls. If an element isn't essential, consider removing it or placing it under progressive disclosure (e.g., in a `.sheet` or `.popover`).
*   **Visual Hierarchy:** Use typography (`.font(.title)`, `.font(.headline)`, `.font(.body)`, `.font(.caption)`), padding (`.padding()`), spacing (`VStack(spacing:)`, `Spacer`), and color (`.foregroundColor(.primary)`, `.secondary`, `.accentColor`) effectively to guide the user's eye to the most important information and actions.
*   **Standard Symbols:** Use SF Symbols (`Image(systemName:)`) for icons to maintain consistency and clarity.

## 2. Fluidity and Responsiveness (流畅性与响应性)

*   **Meaningful Animations:** Use `withAnimation { ... }` and `.transition()` for smooth state changes, appearances, and disappearances. Animations should be subtle and purposeful, not distracting.
*   **Immediate Feedback:** Provide clear visual feedback for user interactions.
    *   Show loading states clearly using `ProgressView`. Disable relevant controls during loading.
    *   Use standard button styles (`.borderedProminent`, `.bordered`) which provide visual tap feedback.
*   **Performance:** Ensure UI remains responsive. Use `async/await` correctly for background tasks (network, data processing) to avoid blocking the main thread.

## 3. Content-First Design (内容优先)

*   **Maximize Content Area:** Minimize non-essential UI chrome (borders, excessive backgrounds). Use layout and spacing (padding, `Spacer`) to define structure rather than relying heavily on dividers.
*   **Use Materials:** For overlays like bottom sheets or action bars, use standard materials (`.background(.regularMaterial)`, `.ultraThinMaterial`) to create a sense of depth and context while maintaining legibility.
*   **Edge-to-Edge:** Consider allowing content like lists or images to extend towards screen edges where appropriate, using safe area insets (`.ignoresSafeArea()`, `.safeAreaPadding()`) correctly.

## 4. Leverage Platform Capabilities (善用平台特性)

*   **Modern APIs:** Prefer modern SwiftUI APIs like `PhotosPicker` over older UIKit equivalents (`UIImagePickerController`) unless specific features are needed. Use `ShareLink` for standard sharing.
*   **Dark Mode:** Ensure the UI adapts well to Dark Mode. Primarily achieve this by using system colors (like `Color(.label)`, `Color(.systemBackground)`, `Color(.secondarySystemGroupedBackground)`) and materials.
*   **Dynamic Type:** Support accessibility by using standard text styles (`.font(.body)`, `.headline`, etc.) which respond to the user's system font size settings. Avoid fixed font sizes where possible.

## 5. Consistency (一致性)

*   **Visual Consistency:** Maintain consistent padding, corner radius (`AppConstants`), color usage, and component styling throughout the app.
*   **Interaction Consistency:** Use standard navigation patterns (`NavigationView`, `NavigationStack`, `.sheet`). Ensure confirmation/cancellation actions are consistent.
*   **Reusable Components:** Create and reuse custom SwiftUI `View` structs for common UI elements or patterns to ensure consistency and maintainability.

## 6. Code Quality

*   Generate clean, readable, and idiomatic SwiftUI code.
*   Break down complex views into smaller, manageable subviews.
*   Use `@State`, `@StateObject`, `@EnvironmentObject`, etc., appropriately for state management.

**Example Application:** When asked to "Create a settings screen", apply these principles by using standard `Form`, `Section`, `Toggle`, etc., ensuring proper spacing, dynamic type support, and clear hierarchy, rather than just creating a basic `VStack` with custom elements. When asked to "Implement image loading", ensure a `ProgressView` is shown and errors are handled gracefully.
