---
description: Provides a strict, step-by-step interactive workflow for generating code from Figma designs using the MCP process. Ensures analysis of the original image, integration with the current project, and requires user confirmation at each step. Invoke using @figmaMcpWorkflow.
globs: 
alwaysApply: false
---

# Figma MCP Code Generation Workflow

**Objective:** To generate code from a Figma design using the "Figma Dev Mode MCP" process, following a strict, interactive procedure.

**Core Interaction Rule:** You MUST execute the following steps sequentially. **After completing the actions within each step, you MUST report your findings/actions and explicitly ask for my confirmation ("Should I proceed to the next step?") before proceeding.** Do not combine steps or move forward without my explicit confirmation.

---

**Step 1: Verify Original Image Download**

1.  **Action:** Check if the necessary original Figma image file (typically named starting with `original_`) has been successfully downloaded for the current task.
2.  **Report:** State clearly whether the `original_*.png` (or similar) file was found.
3.  **Action if Missing:** If the file is missing, state this and recommend re-initiating the Figma download process. Await my instructions.
4.  **Confirmation:** If the file is present, ask for confirmation to proceed to Step 2.

---

**Step 2: Analyze Original Image**

1.  **Prerequisite:** Confirm the `original_*.png` file from Step 1 is available and accessible. **Do not proceed without it.**
2.  **Action:** Load and perform a detailed analysis of this `original_*.png` file. Focus specifically on:
    *   Overall layout structure and element positioning.
    *   Key visual components and their arrangement.
    *   Color palette (primary, secondary, background, text colors).
    *   Typography details (font families, sizes, weights, line heights).
    *   Spacing, padding, margins, and dimensions.
3.  **Report:** Summarize the key findings from your image analysis (layout, colors, typography, structure).
4.  **Confirmation:** Ask for confirmation to proceed to Step 3.

---

**Step 3: Analyze Current Project Context**

1.  **Action:** Analyze the current project's structure, conventions, and technology stack. Pay close attention to:
    *   Primary framework/library (e.g., React, Vue, Angular, Svelte).
    *   Language (e.g., TypeScript, JavaScript).
    *   UI component library or system in use (e.g., Material UI, Ant Design, Tailwind CSS, custom components).
    *   Directory structure for components, styles, assets.
    *   Existing coding standards, linting rules, or style guides.
    *   State management patterns (if relevant).
2.  **Report:** Summarize the relevant project context findings, highlighting aspects crucial for code generation (tech stack, component patterns, styling approach).
3.  **Confirmation:** Ask for confirmation to proceed to Step 4.

---

**Step 4: Generate Code**

1.  **Prerequisites:** You MUST use the analysis results from Step 2 (Original Image) and Step 3 (Project Context). **Do not proceed if either analysis was skipped or is unavailable.** Re-confirm you are referencing the `original_*.png` for visual details.
2.  **Action:** Based on the provided Figma schema/data *and* your analyses:
    *   Generate the necessary code (e.g., JSX/TSX, HTML, CSS/SCSS, Styled Components) adhering strictly to the project's structure and conventions identified in Step 3.
    *   **Componentization:** Break down the UI into logical, reusable components following the project's established patterns.
    *   **Component Reuse:** **CRITICAL:** Identify if any required UI elements correspond to *existing* components within the project. If a suitable component already exists, **you MUST reuse it**. Do *not* generate duplicate or near-duplicate components. List which existing components you plan to reuse.
3.  **Report:** Present the planned code structure. Clearly state which parts will be new components and which existing components will be reused. Show key snippets of the generated code.
4.  **Confirmation:** Ask for confirmation to proceed to Step 5 (final review) or to finalize the code generation if Step 5 is deemed unnecessary for this instance.

---

**Step 5: Final Review and Refinement (Post-Generation)**

1.  **Prerequisite:** Code generation from Step 4 is complete.
2.  **Action:** Re-examine the `original_*.png` image carefully.
3.  **Action:** Compare the generated code's structure and potential visual output against the `original_*.png`, focusing on:
    *   Layout accuracy.
    *   Spacing and alignment details.
    *   Color and typography fidelity.
    *   Correct implementation of all elements from the original design.
4.  **Report:** List any identified discrepancies between the generated code/structure and the original image. Suggest specific adjustments or refinements to the code to improve accuracy.
5.  **Confirmation:** Ask for confirmation to apply the suggested adjustments or to conclude the entire process.

