---
description: 
globs: 
alwaysApply: true
---
```
// ChatToDesign Architecture Diagram

+---------------------------+
|      Presentation         |
|  (UI Layer / SwiftUI)     |
+------------+--------------+
             |
             v
+------------+--------------+
|        ViewModels         |
|  (MVVM / ObservableObject)|
+------------+--------------+
             |
             v
+---------------------------+
|    Application Services   |
| (Coordinate business      |
|  processes and functions) |
+------------+--------------+
             |
             v
+------------+--------------+
| Operations & Coordinators |
| (Specific operation       |
|  processes and coordinators)|
+------------+--------------+
             |
             v
+------------+--------------+
|        Use Cases          |
| (Specific business        |
|  scenario implementation) |
+------------+--------------+
             |
             v
+------------+--------------+
|      Domain Layer         |
|     Entities:             |
|      - Chat               |
|      - User               |
|      - Message            |
|                           |
|     Repository Interfaces:|
|      - ChatRepository     |
|      - UserRepository     |
|      - MessageRepository  |
+------------+--------------+
             |
             v
+---------------------------+
|    Infrastructure Layer   |
|                           |
|  Repository Implementations:|
|   - FirestoreChatRepo     |
|   - FirestoreUserRepo     |
|   - FirestoreMessageRepo  |
|                           |
|  Third-Party Services:    |
|   - Firebase Auth         |
|   - Firebase Storage      |
|   - Logging Service       |
|   - Sentry                |
+---------------------------+

## Updated Architecture Rules

### Dependency Relationships:
```
UI → ViewModel → Application Services → Operations/Coordinators → UseCases → Domain (Entities/Repositories) → Infrastructure
```

### Main Responsibilities of Each Layer:

#### 1. Presentation Layer
- SwiftUI view display
- User interaction handling
- ViewModel binding

#### 2. ViewModel Layer
- View state management
- UI logic processing
- Triggering Application Services

#### 3. Application Layer
- **Services**:
  - Chat session management (ChatSessionService)
  - User account management (UserAccountService)
  - Design generation service (DesignGenerationService)

- **Operations/Coordinators**:
  - Chat flow coordination (ChatFlowCoordinator)
  - Design export operation (DesignExportOperation)
  - Multimedia processing (MediaProcessingOperation)

- **Use Cases**:
  - Send message (SendMessageUseCase)
  - Generate design (GenerateDesignUseCase)
  - Share chat (ShareChatUseCase)
  - Create chat branch (ForkChatUseCase)

#### 4. Domain Layer
- **Entities**:
  - Chat
  - User
  - Message
  - Design

- **Repository Interfaces**:
  - ChatRepository
  - UserRepository
  - MessageRepository
  - DesignRepository

#### 5. Infrastructure Layer
- **Repository Implementations**:
  - FirestoreChatRepository
  - FirestoreUserRepository
  - FirestoreMessageRepository

- **Third-Party Services**:
  - Firebase (Auth, Firestore, Storage)
  - Logging service
  - Error tracking (Sentry)
  - AI model integration (Gemini API)

### Data Flow:

1. **Request Flow**:
   ```
   UI → ViewModel → Service → UseCase → Repository → Data Source
   ```

2. **Response Flow**:
   ```
   Data Source → Repository → UseCase → Service → ViewModel → UI
   ```

### Design Principles:

1. **Single Responsibility**:
   - Each class and module is responsible for only one function
   - Entities only define data structures and basic validation

2. **Dependency Inversion**:
   - High-level modules depend on low-level modules through interfaces
   - Use protocols to define repository and service interfaces

3. **SOLID Principles**:
   - Encapsulate points of change
   - Open for extension, closed for modification
   - Favor composition over inheritance

4. **Error Handling**:
   - Define clear error types
   - Handle errors at appropriate levels
   - Provide user-friendly error messages

5. **Testing Strategy**:
   - Domain entities: Unit tests
   - Repository interfaces: Mock tests
   - Use cases: Unit tests
   - Services: Integration tests
   - ViewModels: Unit tests
   - UI: Snapshot tests

```

Usage Guidelines:

1. When developing new features, think from top to bottom:
   - First define Service interfaces (what does the user need)
   - Then design Operations (how to combine implementations)
   - Finally implement specific UseCases (how to specifically do it)

2. Code Organization Principles:
   - Each layer only depends on the layer below it
   - Upper layers depend on lower layers through interfaces
   - Avoid cross-layer dependencies

3. Testing Strategy:
   - UI layer: UI tests
   - Service layer: Integration tests
   - Operations layer: Integration tests
   - UseCase layer: Unit tests
   - Domain layer: Unit tests
   - Infrastructure layer: Integration tests

4. Entity Design Specifications:
   - Entities should be defined in the Domain layer
   - Use value types (struct) to design entities
   - Provide factory methods to create instances
   - Implement necessary protocols (Codable, Identifiable, Equatable)
   - Avoid including business logic in entities

5. Inter-module Communication:
   - Use dependency injection to manage module dependencies
   - Use Coordinator to coordinate cross-module processes
   - Use Publisher/Subscriber pattern for asynchronous communication
   - Avoid direct dependencies between modules