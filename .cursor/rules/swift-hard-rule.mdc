---
description: 
globs: 
alwaysApply: true
---
1. When you encounter import errors, simply ignore them and continue processing. There is no need to address the errors caused by the import.
2. using Logger to print Log  forever, don't use print()
```
import Foundation

/// 全局日志访问器
enum Logger {
  /// 默认日志服务
  static var service: LoggerService = DefaultLoggerAdapter()
  static func debug(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    let context = LogContext(file: file, function: function, line: line)
    service.debug(message, context: context)
  }
  static func info(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    let context = LogContext(file: file, function: function, line: line)
    service.info(message, context: context)
  }
  static func warning(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    let context = LogContext(file: file, function: function, line: line)
    service.warning(message, context: context)
  }
  static func error(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    let context = LogContext(file: file, function: function, line: line)
    service.error(message, context: context)
  }
  static func log(
    _ message: String, level: LogLevel, file: String = #file, function: String = #function,
    line: Int = #line
  ) {
    let context = LogContext(file: file, function: function, line: line)
    service.log(message, level: level, context: context)
  }
}
```
4. add comment when it is necessary, because the code should be self-explained