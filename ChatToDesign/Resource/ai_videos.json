{"website": "Combined AI Video Effects Collection", "title": "AI Video Effects Collection (Vidu + Pollo AI)", "description": "Complete collection of AI video effects from multiple providers with direct video URLs, categorized by type", "total_videos": 212, "extracted_date": "2025-01-27", "providers": ["vidu", "pollo_ai"], "categories": {"Interaction": 25, "Entertainment": 37, "Appearance": 8, "Others": 91, "Hero/Villain": 11, "Horror/Fantasy": 2, "Emotions": 6, "Xmas": 3, "Transformation": 21, "Creative": 8, "All": 212}, "video_effects": [{"id": 1, "name": "AI Kissing Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-kissing.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-kissing.webp", "pageUrl": "https://pollo.ai/video-effects/ai-kissing", "isHot": true, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "pollo_ai"}, {"id": 2, "name": "Kiss Me AI", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm28onlup0003mupiao0piq40/video/1747203273492-ccc1efdf-0205-4d56-8bcc-225fc6f781bd.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/test/cm1hi72vw0000ojzlai3zu0am/poster/1745832284827-73ea65f7-13b5-42c0-a7ac-c8e8c33eb889.jpg", "pageUrl": "https://pollo.ai/video-effects/kiss-me-ai", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 3, "name": "AI Twerk Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-booty-shake-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-booty-shake-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-twerk-video-generator", "isHot": true, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 4, "name": "AI Hug", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-hug.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-hug.webp", "pageUrl": "https://pollo.ai/video-effects/ai-hug", "isHot": true, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "pollo_ai"}, {"id": 5, "name": "AI Muscle Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-muscle-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-muscle-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-muscle-generator", "isHot": true, "isNew": false, "category": "Appearance", "urlType": "template", "provider": "pollo_ai"}, {"id": 6, "name": "AI French Kissing", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-french-kissing.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-french-kissing.webp", "pageUrl": "https://pollo.ai/video-effects/ai-french-kiss-video-generator", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "pollo_ai"}, {"id": 7, "name": "AI Flying Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-flying-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-flying-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-flying-video-generator", "isHot": true, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 8, "name": "AI 360° Rotation Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-360-rotation-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-360-rotation-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-360-rotation-video-generator", "isHot": true, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 9, "name": "AI Jedi Video Effect", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/video/1747906256781-cd38a161-7349-4213-b226-19892343e904.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/poster/1747906256781-daf218b0-8cae-4b15-b5a6-acbec499aa9c.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-jedi-video-effect", "isHot": true, "isNew": false, "category": "Entertainment", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 10, "name": "AI Cloud Ride Video Generator", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/video/1748421574435-9ac5de7d-1e9b-4fbd-b3e0-873673f0824c.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/poster/1748421741685-9d0deba4-0043-4b65-9f38-1127e21385eb.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-cloud-ride-video-generator", "isHot": true, "isNew": false, "category": "Entertainment", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 11, "name": "AI Bikini", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-bikini.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-bikini.webp", "pageUrl": "https://pollo.ai/video-effects/ai-bikini", "isHot": true, "isNew": false, "category": "Appearance", "urlType": "template", "provider": "pollo_ai"}, {"id": 12, "name": "AI Jiggle Video Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-jiggle-video-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-jiggle-video-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-jiggle-video-effect", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 13, "name": "AI Mermaid Video Effect", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm2l89z02005cohh6cl26c8hn/video/1749202334406-97d16a34-bc1f-4750-9e70-91856d628008.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm2l89z02005cohh6cl26c8hn/image/1749202392273-f0d9920f-5e1b-46f2-8bcb-00656d44c930.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-mermaid-video-effect", "isHot": false, "isNew": true, "category": "Entertainment", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 14, "name": "AI Graduation Party", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm2l89z02005cohh6cl26c8hn/video/1749201277981-9135ebf2-9122-4da4-99b1-b3ef13c2b7b9.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm2l89z02005cohh6cl26c8hn/poster/1749201280805-03265abd-d00a-4c03-891d-32133374a159.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-graduation-party-video-effect", "isHot": false, "isNew": true, "category": "Interaction", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 15, "name": "AI Childhood Encounter Video Generator", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/video/1748420903163-fdb6b987-0103-4c26-8603-0008eab25710.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/poster/1748421533468-7fce259f-4265-494f-b5ef-bbcbcc1a01f8.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-childhood-encounter-video-generator", "isHot": false, "isNew": true, "category": "Interaction", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 16, "name": "AI Bridal Carry", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-bridal-carry.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-bridal-carry.webp", "pageUrl": "https://pollo.ai/video-effects/ai-bridal-carry", "isHot": true, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "pollo_ai"}, {"id": 17, "name": "<PERSON> <PERSON>", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-jesus-hug-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-jesus-hug-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-jesus-hug-video-generator", "isHot": true, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "pollo_ai"}, {"id": 18, "name": "AI Gender Swap Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-gender-swap-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-gender-swap-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-gender-swap-video-generator", "isHot": false, "isNew": false, "category": "Appearance", "urlType": "template", "provider": "pollo_ai"}, {"id": 19, "name": "AI Pet to Human Generator", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/video/1747988859876-c4acfeda-6763-443c-8a30-339183116972.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/poster/1747989162976-a4ca85aa-8cc1-45de-a9c7-8e3a2f601428.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-pet-to-human-generator", "isHot": false, "isNew": false, "category": "Others", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 20, "name": "AI Chibi Cartoon Video Effect", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/video/1747989197243-46dfde5d-86e2-4135-993e-aacd3391c029.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/poster/1747989331491-8ea050a2-f0dd-41d6-816e-90342ab3b067.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-chibi-cartoon-video-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 21, "name": "AI 180° Turn Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-180-turn-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-180-turn-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-180-turn-video-generator", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 22, "name": "AI Smooth Transition Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-smooth-transition-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-smooth-transition-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-smooth-transition-video-generator", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 23, "name": "AI Claw Machine Video Effect", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/video/1747377936876-26deae7b-99fb-4191-8e64-3d0866c04f59.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/poster/1747377993294-0c411d79-e4e6-43ec-9263-dcdffaa6fc42.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-claw-machine-video-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 24, "name": "AI Toy Figure Set Video Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-toy-figure-set-video-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-toy-figure-set-video-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-toy-figure-set-video-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 25, "name": "AI Paper Peel Off Effect", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/video/1747377758998-a8c4b7bc-d0f0-4cc5-8f7b-e61ed4506ff1.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm5gfnbsu0221mul5wwnnah40/poster/1747377843780-1ad7487e-33e8-4ebe-9b13-8ff13cd1ad3c.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-paper-peel-off-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 26, "name": "AI Studio Ghibli Effect", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm29s5pwx0003mu3ofqsjfrq8/video/1746688246607-e4919a2d-79cc-4eac-9061-e38e2f9bd881.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm29s5pwx0003mu3ofqsjfrq8/poster/1746688246607-708a5299-5887-43be-9b04-010ade821b6f.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-studio-ghibli-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 27, "name": "AI Suit Up Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-suit-up-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-suit-up-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-suit-up-video-generator", "isHot": false, "isNew": false, "category": "Appearance", "urlType": "template", "provider": "pollo_ai"}, {"id": 28, "name": "AI Minecraft Video Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-minecraft-video-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-minecraft-video-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-minecraft-video-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 29, "name": "AI 3D Polaroid Video Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-3d-polaroid-video-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-3d-polaroid-video-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-3d-polaroid-video-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 30, "name": "AI Toy Figure Transformation Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-toy-figure-transformation-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-toy-figure-transformation-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-toy-figure-transformation-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 31, "name": "AI Helicopter Pilot Video Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-helicopter-pilot-video-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-helicopter-pilot-video-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-helicopter-pilot-video-effect", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 32, "name": "AI Quad Anime Sticker Video Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-quad-anime-sticker-video-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-quad-anime-sticker-video-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-quad-anime-sticker-video-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 33, "name": "AI Plush Toy Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-plush-toy-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-plush-toy-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-plush-toy-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 34, "name": "AI Cigar Smoking Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-cigar-smoking-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-cigar-smoking-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-cigar-smoking-video-generator", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 35, "name": "AI Camera Orbit Video Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-camera-orbit-video-effect-general.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm29s5pwx0003mu3ofqsjfrq8/image/1748223890402-cde1eaca-637b-4a20-83b6-21dc025b3520.png", "pageUrl": "https://pollo.ai/video-effects/ai-camera-orbit-video-effect", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 36, "name": "AI Angel Wings Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-angel-wings-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-angel-wings-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-angel-wings-effect", "isHot": false, "isNew": false, "category": "Appearance", "urlType": "template", "provider": "pollo_ai"}, {"id": 37, "name": "AI Transformer Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-transformer-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-transformer-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-transformer-effect", "isHot": false, "isNew": false, "category": "<PERSON>/Villain", "urlType": "template", "provider": "pollo_ai"}, {"id": 38, "name": "AI Mecha Transformation", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-mecha-transformation.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-mecha-transformation.webp", "pageUrl": "https://pollo.ai/video-effects/ai-mecha-transformation", "isHot": false, "isNew": false, "category": "<PERSON>/Villain", "urlType": "template", "provider": "pollo_ai"}, {"id": 39, "name": "AI Oscar Award Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-oscar-award-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-oscar-award-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-oscar-award-video-generator", "isHot": false, "isNew": false, "category": "Horror/Fantasy", "urlType": "template", "provider": "pollo_ai"}, {"id": 40, "name": "AI Exotic Princess Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-exotic-princess-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-exotic-princess-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-exotic-princess-effect", "isHot": false, "isNew": false, "category": "Appearance", "urlType": "template", "provider": "pollo_ai"}, {"id": 41, "name": "AI <PERSON>o Bear Bloom Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-dooro-bear-bloom-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-dooro-bear-bloom-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-dooro-bear-bloom-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 42, "name": "AI Doll Transformation", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-doll-transformation.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-doll-transformation.webp", "pageUrl": "https://pollo.ai/video-effects/ai-doll-transformation", "isHot": false, "isNew": false, "category": "Horror/Fantasy", "urlType": "template", "provider": "pollo_ai"}, {"id": 43, "name": "AI Catwalk with Animal", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-catwalk-with-animal.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-catwalk-with-animal.webp", "pageUrl": "https://pollo.ai/video-effects/ai-catwalk-with-animal", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 44, "name": "AI Standee Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-standee-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-standee-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-standee-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 45, "name": "AI Slice Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-slice-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-slice-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-slice-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 46, "name": "AI Balloon Deflating Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-balloon-deflating-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-balloon-deflating-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-balloon-deflating-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 47, "name": "AI Squish Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-squish-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-squish-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-squish-effect", "isHot": true, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 48, "name": "AI Kung Fu Video Generator", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm28onlup0003mupiao0piq40/video/1742983405187-ac6bf455-5a68-4bf2-9fc9-793e05f3c7e5.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm28onlup0003mupiao0piq40/poster/1742983405187-b9cd46be-8b05-4dd9-8704-d7d1783271fe.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-kungfu-video-generator", "isHot": false, "isNew": false, "category": "Others", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 49, "name": "AI Catwalk Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-catwalk-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-catwalk-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-catwalk-generator", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 50, "name": "AI Fight Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-fight-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-fight-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-fight-generator", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 51, "name": "AI Bloom Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-bloom-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-bloom-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-bloom-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 52, "name": "AI Walking Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-walking.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-walking.webp", "pageUrl": "https://pollo.ai/video-effects/ai-walking", "isHot": true, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 53, "name": "AI Captain America Transformation", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm28onlup0003mupiao0piq40/video/1740972419062-069d0930-ed49-4da7-920b-0317cb7dbec1.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm28onlup0003mupiao0piq40/poster/1740972426934-57deba93-4e0d-4246-a425-f92fe0bc1550.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-captain-america-transformation", "isHot": false, "isNew": false, "category": "<PERSON>/Villain", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 54, "name": "AI Nap Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-instant-nap-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-instant-nap-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-nap-video-generator", "isHot": true, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 55, "name": "AI Red Hulk Transformation", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-red-hulk-transformation.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-red-hulk-transformation.webp", "pageUrl": "https://pollo.ai/video-effects/ai-red-hulk-transformation", "isHot": false, "isNew": false, "category": "<PERSON>/Villain", "urlType": "template", "provider": "pollo_ai"}, {"id": 56, "name": "AI Walk with Captain <PERSON>", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-walk-with-captain-am<PERSON><PERSON>.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-walk-with-captain-am<PERSON><PERSON>.webp", "pageUrl": "https://pollo.ai/video-effects/ai-walk-with-captain-am<PERSON>a", "isHot": false, "isNew": false, "category": "<PERSON>/Villain", "urlType": "template", "provider": "pollo_ai"}, {"id": 57, "name": "AI Descent with <PERSON> Hulk", "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm28onlup0003mupiao0piq40/video/1740972486121-058147da-084d-4149-844c-7344788692a6.mp4", "posterUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm28onlup0003mupiao0piq40/poster/1740972490280-c6ae758d-252e-4c7e-a2f7-5dd7a455131e.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-descent-with-red-hulk", "isHot": false, "isNew": false, "category": "<PERSON>/Villain", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 58, "name": "AI Old Photo Animation", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-old-photo-animation.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-old-photo-animation.webp", "pageUrl": "https://pollo.ai/video-effects/ai-old-photo-animation", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 59, "name": "AI Tiger Hug", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-tiger-hug-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-tiger-hug-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-tiger-hug-video-generator", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "pollo_ai"}, {"id": 60, "name": "AI Inflate Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-inflate-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-inflate-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-inflate-effect", "isHot": true, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 61, "name": "AI Handshake", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-handshake.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-handshake.webp", "pageUrl": "https://pollo.ai/video-effects/ai-handshake", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "pollo_ai"}, {"id": 62, "name": "AI Camera Movement Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-camera-movement-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-camera-movement-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-camera-movement-effect", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "pollo_ai"}, {"id": 63, "name": "AI Hand Heart", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-hand-heart.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-hand-heart.webp", "pageUrl": "https://pollo.ai/video-effects/ai-hand-heart", "isHot": false, "isNew": false, "category": "Emotions", "urlType": "template", "provider": "pollo_ai"}, {"id": 64, "name": "AI Smile Generator", "videoUrl": "https://videocdn.pollo.ai/styles/template2Video/video/AI Smile Generator.mp4", "posterUrl": "https://videocdn.pollo.ai/styles/template2Video/img/AI Smile Generator.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-smile-generator", "isHot": false, "isNew": false, "category": "Emotions", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 65, "name": "AI Explode Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-explode-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-explode-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-explode-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 66, "name": "AI Wedding Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-wedding-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-wedding-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-wedding-video-generator", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "pollo_ai"}, {"id": 67, "name": "AI Shocked Face Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-shocked-face-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-shocked-face-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-shocked-face-effect", "isHot": false, "isNew": false, "category": "Emotions", "urlType": "template", "provider": "pollo_ai"}, {"id": 68, "name": "AI Rose Giving", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-rose-giving.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-rose-giving.webp", "pageUrl": "https://pollo.ai/video-effects/ai-rose-giving", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "pollo_ai"}, {"id": 69, "name": "AI Curly Hair", "videoUrl": "https://videocdn.pollo.ai/styles/template2Video/video/AI Curly Hair.mp4", "posterUrl": "https://videocdn.pollo.ai/styles/template2Video/img/AI Curly Hair.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-curly-hair", "isHot": true, "isNew": false, "category": "Appearance", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 70, "name": "AI Laughing Face Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-laughing-face-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-laughing-face-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-laughing-face-effect", "isHot": false, "isNew": false, "category": "Emotions", "urlType": "template", "provider": "pollo_ai"}, {"id": 71, "name": "AI Hulk Transformation", "videoUrl": "https://videocdn.pollo.ai/styles/template2Video/video/AI Hulk Transformation.mp4", "posterUrl": "https://videocdn.pollo.ai/styles/template2Video/img/AI Hulk Transformation.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-hulk-transformation", "isHot": false, "isNew": false, "category": "<PERSON>/Villain", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 72, "name": "AI Melt Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-melt-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-melt-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-melt-effect", "isHot": true, "isNew": false, "category": "Others", "urlType": "template", "provider": "pollo_ai"}, {"id": 73, "name": "AI Venom Transformation", "videoUrl": "https://videocdn.pollo.ai/styles/template2Video/video/AI Venom Transformation.mp4", "posterUrl": "https://videocdn.pollo.ai/styles/template2Video/img/AI Venom Transformation.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-venom-transformation", "isHot": true, "isNew": false, "category": "<PERSON>/Villain", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 74, "name": "AI Proposal Video Generator", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-proposal-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-proposal-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/ai-proposal-video-generator", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "pollo_ai"}, {"id": 75, "name": "AI Batman Transformation", "videoUrl": "https://videocdn.pollo.ai/styles/template2Video/video/AI Batman Transformation.mp4", "posterUrl": "https://videocdn.pollo.ai/styles/template2Video/img/AI Batman Transformation.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-batman-transformation", "isHot": false, "isNew": false, "category": "<PERSON>/Villain", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 76, "name": "AI Hair Growth", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-hair-growth.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-hair-growth.webp", "pageUrl": "https://pollo.ai/video-effects/ai-hair-growth", "isHot": false, "isNew": false, "category": "Appearance", "urlType": "template", "provider": "pollo_ai"}, {"id": 77, "name": "AI Zombie Transformation", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-zombie-transformation.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-zombie-transformation.webp", "pageUrl": "https://pollo.ai/video-effects/ai-zombie-transformation", "isHot": false, "isNew": false, "category": "<PERSON>/Villain", "urlType": "template", "provider": "pollo_ai"}, {"id": 78, "name": "AI Gun Shooter Transformation", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-gun-shooter-transformation.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-gun-shooter-transformation.webp", "pageUrl": "https://pollo.ai/video-effects/ai-gun-shooter-transformation", "isHot": false, "isNew": false, "category": "<PERSON>/Villain", "urlType": "template", "provider": "pollo_ai"}, {"id": 79, "name": "AI Out of Frame Effect", "videoUrl": "https://videocdn.pollo.ai/styles/template2Video/video/AI%20Out%20of%20Frame%20Effect.mp4", "posterUrl": "https://videocdn.pollo.ai/styles/template2Video/img/AI%20Out%20of%20Frame%20Effect.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-out-of-frame-effect", "isHot": false, "isNew": false, "category": "Others", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 80, "name": "AI Santa Transformation", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-santa-transformation.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-santa-transformation.webp", "pageUrl": "https://pollo.ai/video-effects/ai-santa-transformation", "isHot": false, "isNew": false, "category": "Xmas", "urlType": "template", "provider": "pollo_ai"}, {"id": 81, "name": "AI Santa Giving Gifts", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/santa-gifting-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/santa-gifting-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/santa-gifting-video-generator", "isHot": false, "isNew": false, "category": "Xmas", "urlType": "template", "provider": "pollo_ai"}, {"id": 82, "name": "AI Christmas Toasting", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/christmas-toast-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/christmas-toast-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/christmas-toast-video-generator", "isHot": false, "isNew": false, "category": "Xmas", "urlType": "template", "provider": "pollo_ai"}, {"id": 83, "name": "AI Santa Hug", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/santa-hug-video-generator.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/santa-hug-video-generator.webp", "pageUrl": "https://pollo.ai/video-effects/santa-hug-video-generator", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "pollo_ai"}, {"id": 84, "name": "AI Breeze Blowing Effect", "videoUrl": "https://videocdn.pollo.ai/styles/template2Video/video/AI Breeze Blowing Effect.mp4", "posterUrl": "https://videocdn.pollo.ai/styles/template2Video/img/AI Breeze Blowing Effect.jpg", "pageUrl": "https://pollo.ai/video-effects/ai-breeze-blowing-effect", "isHot": false, "isNew": false, "category": "Emotions", "urlType": "user_generated", "provider": "pollo_ai"}, {"id": 85, "name": "AI Scared Face Effect", "videoUrl": "https://videocdn.pollo.ai/template_cover/video/ai-scared-face-effect.mp4", "posterUrl": "https://videocdn.pollo.ai/template_cover/static_webp/ai-scared-face-effect.webp", "pageUrl": "https://pollo.ai/video-effects/ai-scared-face-effect", "isHot": false, "isNew": false, "category": "Emotions", "urlType": "template", "provider": "pollo_ai"}, {"id": 86, "name": "变身美人鱼", "videoUrl": "https://image01.vidu.zone/vidu/media-asset/3-fdbb5ab1.mp4", "posterUrl": "https://image01.vidu.zone/vidu/media-asset/1-233089c3.webp", "isHot": false, "isNew": true, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "fishermen"}, "input_instruction": {"image_count": "仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu/media-asset/2-75289ebf.webp"], "person_count": "支持单人照片、双人合照、和宠物照片", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 'prompt' 参数", "effect_boundary": "当主体为人物时，可能会出现眉毛转变过程模糊，或人物转场效果不流畅的情况"}, "prompt": "在【场景/背景】中，画面中出现了【主体数量和种类描述】——【主体1描述】、【主体2描述】……他们最初的状态是为：正【动作状态，例如站立、坐着、仰望】，神情中透着【情绪关键词】。然而此刻，主体变成美人鱼的现象正在发生：每一个主体都发生的变化：场景切换至水下世界，主体已完全化身为人鱼形象，身穿鳞甲般的银色胸甲与鱼尾，肌肉线条清晰，目光坚定。四周是流动的水草与柔和光斑，海底植物如珊瑚、海藻在水流中起舞，缥缈唯美。说明：这里的主体可以是人物、动作、物品等.\\\nRequirements：\n1. 根据图片的具体内容替换上面的【】内容。最初的状态根据图片中的主体状态来描述。\n2. If the image is a close-up or medium shot, set Camera Movement to `Zoom out`.\n3. If there are two subjects or more subjects in the image, both should be performing `这里是具体的变化`.\n4. 详细描述每一个主体的变化过程，特别是动作的细节和变化的过程。(关于主体的区别，可以使用明显具有特征区分的方式。例如，左边的男士，右侧的女士/穿黑色卫衣的男士，穿白色裙子的小女孩等等)", "template": "fishermen"}, {"id": 87, "name": "生日派对", "videoUrl": "https://scene.vidu.zone/media-asset/071448-7F5lYULYr8gOqgms.mp4", "posterUrl": "https://scene.vidu.zone/media-asset/071448-X8NXqPlLyjO8Gq77.png", "isHot": false, "isNew": true, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "该视频输出比例随机", "credit_cost": "10", "scene": "happy_birthday"}, "input_instruction": {"image_count": "仅支持上传一张图片", "image_url": ["https://scene.vidu.zone/media-asset/071448-ixTIiw8afgO2y5iy.png"], "person_count": "支持单人照片、双人合照、多人合照、宠物照片或蛋仔角色", "attention": "当输入主体为单人且露出正面上半身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 'prompt' 参数", "effect_boundary": "有较低概率出现人物已松手但蛋糕仍悬浮于空中的情况"}, "prompt": "请勿包含原始图像中不存在的任何元素。\n图像中所有角色左右摇晃，充满活力，营造热闹的氛围。角色自然微笑。\n角色一只手托住蛋糕盘子，另外一只手臂左右挥动。\n相机视角：严格保持静止。\n元素限制：禁止添加原始图像中未出现的角色、面部细节、位置变动或装饰元素", "template": "happy_birthday"}, {"id": 88, "name": "日式漫画风-irasutoya", "videoUrl": "https://scene.vidu.zone/media-asset/071533-GQ6eKivFsOrxEX3G.mp4", "posterUrl": "https://scene.vidu.zone/media-asset/072151-RPn3L4YFBkStu2rG.png", "isHot": false, "isNew": true, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "irasutoya"}, "input_instruction": {"image_count": "仅支持上传一张图片", "image_url": ["https://scene.vidu.zone/media-asset/071533-xicfUD8oVomfzGQy.png"], "person_count": "支持单人照片、双人合照、多人合照、宠物照片或物体照片", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 'prompt' 参数", "effect_boundary": "在极少数情况下，可能出现转绘结果与原图不符，生成其他物体的情况"}, "prompt": "The subjects in the image are undergoing a style transformation into the Japanese Irasu<PERSON>ya illustration style. If there are multiple subjects in the image, all of them should go through this style transformation.", "template": "irasutoya"}, {"id": 89, "name": "美式漫画风", "videoUrl": "https://scene.vidu.zone/media-asset/071249-yUd4tDFq9K9HSAvn.mp4", "posterUrl": "https://scene.vidu.zone/media-asset/071249-jETsehE0ODjTWyui.png", "isHot": true, "isNew": true, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "american_comic"}, "input_instruction": {"image_count": "仅支持上传一张图片", "image_url": ["https://scene.vidu.zone/media-asset/071249-dUOR6r4ESoSnS95J.png"], "person_count": "支持单人照片、双人合照、多人合照、宠物照片或物体照片", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 'prompt' 参数", "effect_boundary": "当输入主体为多人时，可能会出现转绘后个别人物缺失的情况"}, "prompt": "In the [scene/background], the image features [number and type of subjects] — [description of subject 1], [description of subject 2] (if present). They are [action/state, such as standing, sitting, looking upward], with expressions that convey [emotion keyword]. At this moment, all the subjects in the image are undergoing a style transformation — shifting from their original style into the Rick and Morty-style of American animation, including changes to the surrounding environment.\\\nRequirements:\\Replace the placeholders above with the specific details based on the image content.", "template": "american_comic"}, {"id": 90, "name": "法式热吻", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/fashirewen_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/fashirewen_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "french_kiss"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/fashirewen_upload.png"], "person_count": "仅支持双人拼图/合照", "attention": "人物为正面上半身，且手中没道具，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当上传图片为全身图时，可能会出现没有亲上，或亲吻幅度较小、时长较短，没有热吻感觉的情况"}, "prompt": "The two figures in the painting move closer and then passionately kiss, alternating with deep and firm intensity.", "template": "french_kiss"}, {"id": 91, "name": "2.0 拥抱", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/hugging_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/hugging_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "hugging"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/hugging_upload.png"], "person_count": "仅支持双人拼图/合照，人宠拼图/合照", "attention": "人物露出超半身，且手中没道具，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当照片中两个人物大小不一致，会有概率出现模型未能识别，导致拥抱视频不符合物理规律的情况"}, "prompt": "视频内容\\\n画面中的两个主体转向彼此，并开始拥抱。\\\n# 要求\\\n将Motion Level设置为“Large”。", "template": "hugging"}, {"id": 92, "name": "随地大小睡", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0331/guonei/da<PERSON><PERSON><PERSON>_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0331/guonei/da<PERSON>osh<PERSON>_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "nap_me"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0331/guonei/da<PERSON>osh<PERSON>_upload.png"], "person_count": "仅支持男生或女生的单人照，和动物的单主体照", "attention": "当人物为半身照或者全身照时，动物为全身照时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当上传图片为纯色背景时，可能会出现人物竖直躺下，而不是侧身躺下的情况；在盖被子过程中，也有可能出现人物从身体两边抓被子的情况"}, "prompt": "Video content\\\n The camera slowly opens, and the character leisurely falls to the right, landing perfectly on a bed that appears out of nowhere. The character lies on the soft pillow, in a natural and relaxed posture, with a serene expression on his face. He gently pulls up a soft cotton blanket and lightly covers himself. The camera slowly zooms in, the character's breathing is steady and calm, his eyes gently closed, completely immersed in a sense of tranquility and relaxation, creating a warm and cozy sleeping atmosphere.", "template": "nap_me"}, {"id": 93, "name": "吉卜力风", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/jibuli_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/jibuli_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "<PERSON><PERSON><PERSON><PERSON>"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/jibuli_upload.png"], "person_count": "支持单人、单个物体、双人主体或纯风景照片", "attention": "整体效果表现良好，适用范围广", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "变身过程中可能会出现细节偏差，例如文字不准确、建筑细节缺失，或人物手部表现不佳等情况"}, "prompt": "If the subject is a person or an animal:\\\nSubject:'The subject waves their right hand toward the camera. The entire scene gradually and smoothly transforms into the style of a Studio Ghibli animation. Please ensure that the subject’s facial features and clothing details remain clearly visible, and that the transformation appears natural and fluid.'\\\nIf the subject is a landscape:\\\nSubject:'The entire scene smoothly transitions into the visual style of a Studio Ghibli animation' ", "template": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 94, "name": "变身肌肉男", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/bianshenjirounan_viseo.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/bianshenjirounan_cove.jpeg", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "muscling"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/0311/muscling_up.png"], "person_count": "支持写实风的单人照片", "attention": "当人物为半身，不露出手部，服装为非高领衣服时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当人物着装为高领衣服或者是多件衣服叠穿时会有可能出现脱衣不干净的情况；当人物露出手部时，有可能会出现脱衣运动模糊或尾帧手部崩坏的情况"}, "prompt": "Video content\\\n The video shows a man taking off his shirt, revealing his muscular chest.\\\n# Requirements\\\n1. If the garment is a shirt, he would first unbutton it before removing it, revealing his toned muscles underneath.\\\n2. Make sure that after the clothes are removed, they are thrown to one side of the frame.\\\n3. Motion Level：Large.\\\n4. The description of the 'Subject' should focus on the action of the person taking off the clothes, and then throwing the clothes aside while showing off their muscles.", "template": "muscling"}, {"id": 95, "name": "变身美队", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/bianshenmeidui_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/bianshenmeidui_cover.png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9、9:16", "credit_cost": "6", "scene": "captain_am<PERSON>a"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/bianshenmeidui_upload.png"], "person_count": "仅支持单人照片", "attention": "人物为半身照，或全身照的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现两个盾牌和翅膀融合或无人物变身为美队过程的情况"}, "prompt": "Video content\\\nThe figure in picture 1 flies in the sky, then slowly begins to transform his clothes to what he looks like in picture 2, spreads his wings, and then picks up a shield. \\\n# Requirements\\\n1.Accurately describe the clothing characteristics of the person in the first picture. \\\n2. Emphasize the changing process of clothing.\\ n3.No superhero descriptions, just a precise description of the costume. \\\n4.The facial features are consistent with Figure 1, and the final clothing features are consistent with Figure 2.", "template": "captain_am<PERSON>a"}, {"id": 96, "name": "变身浩克", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/bianshenhaoke_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/bianshenhaoke_cover.jpeg", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "hulk"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/bianshenhaoke_upload.png"], "person_count": "仅支持单人照片", "attention": "人物为半身照，或全身照的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现女生露出腹肌、人物消失崩坏、面部狰狞的情况"}, "prompt": "Video content\\\nThe character experiences a radical transformation where their body shatters into numerous fragments. From the dispersing pieces, a strong force emerges, remolding them into a much larger and more powerful form. The muscles enlarge and reshape, creating a huge, towering Red Giant. The remnants of the shredded body vanish into chaos, leaving only this powerful being with bulging muscles and a fierce look, wearing only tattered shorts. Its thunderous roar shakes the ground and debris swirls around, signifying the intense and violent appearance of this new, unstoppable form.", "template": "hulk"}, {"id": 97, "name": "美队同行", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/meiduitongxing_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/meiduitongxing_cover.jpeg", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9、9:16", "credit_cost": "6", "scene": "cap_walk"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/meiduitongxing_upload.jpeg"], "person_count": "仅支持单人照片", "attention": "人物为半身照，或全身照的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有一定概率出现生成的美队没有翅膀，或用户和美队两个人物重合的情况"}, "prompt": "Video content\\\nTwo characters holding round shields side by side walk towards the camera，One of the characters flaps his wings behind him.The background is a war-torn ruin.\\\n# Requirements\\\n1.Emphasize that everyone has a shield in their hands.\\\n2.Accurately describe the appearance of the person based on the picture.", "template": "cap_walk"}, {"id": 98, "name": "浩克俯冲", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/haokefuchong_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/haokefuchong_cover.png", "isHot": true, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9、9:16", "credit_cost": "6", "scene": "hulk_dive"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/haokefuchong_upload.jpeg"], "person_count": "仅支持单人照片", "attention": "人物为半身照，或全身照的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有一定概率出现浩克从地上跳起、人物在浩克下面或胳膊上的情况"}, "prompt": "Video content\\\nThe red Hulk flies through the sky, with the character from Image 1 positioned firmly on his back. <PERSON> then leaps to the ground. Then cracks appeared in the ground.The character from Image 1 remains steadfastly perched on <PERSON>'s shoulder, maintaining a secure grip around the red Hulk's neck.\\\n# Requirements\\\n1. Do not use uncertain words, such as 'seem', 'like', 'prepare', etc. n2. Emphasize that the character in picture 1 has been firmly seated on the Hulk's back.", "template": "hulk_dive"}, {"id": 99, "name": "异域公主", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/yiyugongzhu_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/yiyugongzhu_cover.png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9、9:16、1:1", "credit_cost": "6", "scene": "exotic_princess"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/yiyugongzhu_upload.png"], "person_count": "支持单人", "attention": "人物为女性五官照，无发型服饰时，效果最佳。另外，该玩法可通过配置API 请求体中的\"area\"参数，实现“随机生成”或“指定生成”美丽异国公主的效果，两种模式对应的\"area\"参数枚举值如下：随机生成：auto；指定生成：丹麦公主 denmark、英国公主 uk、非洲公主 africa、中国公主 china、墨西哥公主 mexico、瑞士公主 switzerland、俄罗斯公主 russia、意大利公主 ital、韩国公主 korea、泰国公主 thailand、印度公主 india、日本公主 japan", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现女生露出腹肌、人物消失崩坏、面部狰狞的情况"}, "prompt": "Video content\\\n Character [Detailed description of gender, facial features, and skin color in Figure 1] wearing [Description of hairstyle, headwear, clothing, and props in Figure 2], smiling in front of the camera, showing an elegant and noble state. ", "template": "exotic_princess"}, {"id": 100, "name": "与兽为伍", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/yushou<PERSON><PERSON>_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/yushouweiwu_cover.png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9、9:16、1:1", "credit_cost": "6", "scene": "beast_companion"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/yushouweiwu_upload.png"], "person_count": "仅支持单人照片", "attention": "人物为正面的半身或者全身时，效果最佳。另外，该玩法可通过配置API 请求体中的\"beast\"参数，实现“随机生成”或“指定生成”帅气野性伴侣的效果，两种模式对应的\"beast\"参数枚举值如下：随机生成：auto；指定生成：熊首男友 bear、虎首男友 tiger、鹿首男友 elk、蛇首男友 snake、狮首男友 lion、狼首男友 wolf", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现两个主体没有并排走或步频过快的情况"}, "prompt": "Video content\\\n The characters in Image 1 and Image 2 walk confidently side by side, slowly approaching the camera.\\\n# Requirements\\\n The setting is a modern fashion runway with a glossy white surface, surrounded by an audience seated in a dimly lit room. The atmosphere is sophisticated and focused, with the spotlight on the two characters as they make their way down the runway.\\\n# Requirements\\\n1.Please strictly follow my instructions; the image only supplements the character's appearance details.\\\n2.Camera Movement: Zoom out.\\\n3.<PERSON>phasize standing confidently，arrogant and look straight ahead.\\\n4.Do not include descriptions like holding hands.\\\n5.shot_size:Wide Shot.\\\n6.Emphasize that male roles are slightly higher than female roles.\\\n7.Emphasize starting with two characters side by side.\\\n8.Emphasize the character facing the camera.", "template": "beast_companion"}, {"id": 101, "name": "变身Q版玩偶", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/0317/Qwan<PERSON>_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/Qwanou_cover.png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "cartoon_doll"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/0317/Qwan<PERSON>_upload.png"], "person_count": "支持写实风的单人照片", "attention": "当人物露出为全身时（儿童更佳），效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动。", "effect_boundary": "可能会出现，变身后背景可能会变化、人物手持物品消失/崩坏；或没有完全变身Q版玩偶的情况"}, "prompt": "Video content\\\n The character in the picture jumped, turning into a smooth doll version of themselves.\\\n# Requirements\\\n1.Style:“3D Rendering, HD”.\\\n2.Ensure the character jumps in place.", "template": "cartoon_doll"}, {"id": 102, "name": "万物变身机器人", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/0331/guonei/ji<PERSON><PERSON>_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/0331/guonei/jiqiren_cover.png", "isHot": true, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "mecha_x"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/0331/guonei/jiqiren_upload.jpeg"], "person_count": "仅支持单个物体（汽车、电动车、建筑、家具）、单个动物和单个人物的照片", "attention": "当图片清晰且主体突出时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现变身后机器人色彩和输入图主体不一致的情况；也有可能出现人物或动物只有身体变为机甲，脸部没变的情况；或出现没有变身过程，机器人直接出现的情况"}, "prompt": "Video content\\\n The subject in the image gradually transforms into a futuristic robot. During this metamorphosis, the various parts of the original object are dynamically reassembled, disassembled, and recombined, ultimately forming an entirely new robotic figure that seamlessly blends mechanical details, illuminated elements, and fluid lines.", "template": "mecha_x"}, {"id": 103, "name": "吉卜力风", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/jibuli_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/jibuli_cover.png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "<PERSON><PERSON><PERSON><PERSON>"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/jibuli_upload.png"], "person_count": "支持单人、单个物体、双人主体或纯风景照片", "attention": "整体效果表现良好，适用范围广", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "变身过程中可能会出现细节偏差，例如文字不准确、建筑细节缺失，或人物手部表现不佳等情况"}, "prompt": "If the subject is a person or an animal:\\\nSubject:'The subject waves their right hand toward the camera. The entire scene gradually and smoothly transforms into the style of a Studio Ghibli animation. Please ensure that the subject’s facial features and clothing details remain clearly visible, and that the transformation appears natural and fluid.'\\\nIf the subject is a landscape:\\\nSubject:'The entire scene smoothly transitions into the visual style of a Studio Ghibli animation' ", "template": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 104, "name": "我的世界风", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/mc_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/mc_cover+(2).png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "minecraft"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/mc_shouzhen.png"], "person_count": "支持写实风人物和动物的单主体、双主体和多主体", "attention": "当照片中的人/动物为单主体，露出为全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现没有变身过程，或变身过程比较奇怪的情况"}, "prompt": "Video content\\\nthe person Part<PERSON> Dissolve to the second person.Blinding sparkles and radiant light rays swirl around his entire body, concealing the transformation process. As the light gradually fades, his final cubic and stylized form is revealed. The animation is smooth and seamless, with no visual jumps.\\\n Requirements:\\\n1.Do not include any description of 'Minecraft'.\\\n2.The background should only describe the final image's background.\\\n3.Design a reasonable motion transition based on the character's poses in the first and last frames.\\\n4.If there are multiple characters, all of them should undergo the same transformation effect", "template": "minecraft"}, {"id": 105, "name": "西装暴徒", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/zhifuyouhuo_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/xizhuangbaotu_cover.png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "style_me"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/zhifuyouhuo_upload.webp"], "person_count": "仅支持人物/动物的单主体", "attention": "当主体处于中景或远景时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "暂不支持多主体图像，容易导致主体识别失败；当输入图为近景大头照时，可能出现头部超出画面的情况；若人物穿着高领衣物，可能出现领口区域显示不完整的现象"}, "prompt": "Video content\\\nDepending on the main subject in the image, apply different transformations: \\\nIf the subject in the image is an animal, subject: The animal stands up and puts on a crisp suit, and then walks forward. \\\nIf the subject in the image is a female, subject: The character in the image gently touches the collar with both hands. Their upper body gradually transforms from the original outfit into a white long-sleeved shirt, with the collar open and the sleeves rolled up. The lower body smoothly transitions into a deep blue form-fitting skirt. They walk gracefully toward the camera. \\\nIf the subject in the image is a male, subject: The character puts on a crisp suit.\\\nRequirements:\\\n1. If the image is a close-up or medium shot, set Camera Movement to: 'Zoom out' \\\n2. Motion Level:Large", "template": "style_me"}, {"id": 106, "name": "星战光剑", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/saberwarrior_0428/saberwarrior_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/saberwarrior_0428/saberwarrior_cover.png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16  16:9  1:1", "credit_cost": "6", "scene": "saber_warrior"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/saberwarrior_0428/saberwarrior_upload.png"], "person_count": "仅适用于单人照片", "attention": "当画面主体为单人时，生成效果更佳；生成人物会随机穿着四套预设服装的任意一套", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能出现人物手中出现两把光剑，或光剑在人物周围漂浮不定的情况"}, "prompt": "In Figure 1, the character is wearing the traditional Jedi knight costume as seen in Figure 2. The character wields the lightsaber in an extremely obvious manner just like a Jedi knight and walks towards the camera from the center of the frame.\nRequirement:\n1.The character's face should be clearly visible.\n2.The character needs to have a very obvious sword-wielding action.\nBackground: Referring to Figure 3\nProhibition: The character is not allowed to wear any accessories, but the original hairstyle should be maintained. It is prohibited for the character to turn around. Characters are prohibited from speaking.\n'Shot Size':'Full Shot',\n'Composition':'Rule of Thirds Composition',\n'Camera Movement':'Zoom In',\nThe moving speed of the camera: Slow\nSpecial attention: If the generated content involves sensitive platform review, do not describe facial features that are blurred, obscured, coded, or distorted. If a clear face cannot be generated, please use simple titles instead (such as 'man,' 'woman,' 'girl,' 'boy,' etc.) without detailed physical descriptions or blurring ", "template": "saber_warrior"}, {"id": 107, "name": "宠物变真人", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/pet2human_0509/pet2human_video1.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/pet2human_0509/pet2human_cover.png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "仅支持输入9:16", "credit_cost": "10", "scene": "pet2human"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/pet2human_0509/pet2human_upload.png"], "person_count": "仅支持单个宠物", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能出现宠物消失过程不流畅，或尾帧展示不完全的情况"}, "prompt": "The subject in the picture went through a seamless transition", "template": "pet2human"}, {"id": 108, "name": "粘土风拍立得", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/clayshot_0509/clayshot_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/clayshot_0509/clayshot_cover.png", "isHot": true, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "仅支持输入9:16", "credit_cost": "10", "scene": "clayshot"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/clayshot_0509/clayshot_upload.png"], "person_count": "支持成人、儿童、动物、双人或三人家庭场景", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当主体为人物时，可能会出现眉毛转变过程模糊，或人物转场效果不流畅的情况"}, "prompt": "The subject in the picture went through a seamless transition", "template": "clayshot"}, {"id": 109, "name": "转圈变手办", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/toyme_0521/toyme_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/toyme_0521/toyme_cover.png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "toy_me"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/toyme_0521/toyme_upload.png"], "person_count": "支持单人照片、宠物照片、双人合照、人宠合照或多人合照", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现转身后未生成手办底座的情况；若原图人物姿势不完整或动作怪异，生成结果可能出现姿态变化；当输入为双人或多人时，若站位距离过远时，可能会出现未共用一个底座，甚至生成失败的情况；当场景内主体大于三个时，生成效果不佳"}, "prompt": "At the beginning of the video, the subject in the image stands naturally, facing the camera. Then, it slowly turns around. At the exact moment when its back is to the camera, the transformation begins — the surface becomes smooth and glossy, gradually taking on a plastic-like texture. By the end of the turn, the subject has fully transformed into a finely crafted figurine standing firmly on a smooth, circular base. Its pose and silhouette remain unchanged, with details such as surface textures, contours, and structural features clearly preserved, as if the figure has just been placed on display. The entire transformation is smooth and elegant, capturing the magical transition from a living or real-world entity to a collectible model.\n### Requirements:\n1. Determine the number of characters based on the input image — **all subject must undergo the transformation**.\n2. Strictly describe the subject's visual characteristics based on the image — do not add or assume features not shown.\n3. The final figurine(s) must be firmly and naturally placed on a round, smooth, circular base. The base should be clearly visible and consistent with collectible display models.", "template": "toy_me"}, {"id": 110, "name": "毕业盛典", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/graduation_0527/graduation_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/graduation_0527/graduation_cover.png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "仅支持输入2:3", "credit_cost": "10", "scene": "graduation"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Transformation/Transformation/graduation_0527/graduation_upload.png"], "person_count": "支持单人照片、双人合照或多人合照", "attention": "仅支持比例为 2:3 的输入图，生成的视频比例也为 2:3；当主体为单人或双人，且露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现出现首尾转场崩坏，或人物头部超出画面的情况；当输入图中人物发型或服饰性别特征不明显时，可能导致性别识别错误，换错礼服"}, "prompt": "The subject in the picture went through a seamless transition", "template": "graduation"}, {"id": 111, "name": "粘土风拍立得（多主体）", "videoUrl": "https://image01.vidu.zone/vidu/media-asset/2-4b1601a5.mp4", "posterUrl": "https://image01.vidu.zone/vidu/media-asset/7-3f614343.webp", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "该视频输出比例为2:3", "credit_cost": "10", "scene": "clayshot_duo"}, "input_instruction": {"image_count": "支持输入最多两张图片", "image_url": ["https://image01.vidu.zone/vidu/media-asset/3-06ca0a1e.webp", "https://image01.vidu.zone/vidu/media-asset/1-aee917df.webp"], "person_count": "支持单人照片、双人合照、和宠物照片", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 ‘prompt’ 参数", "effect_boundary": "当主体为人物时，可能会出现眉毛转变过程模糊，或人物转场效果不流畅的情况"}, "prompt": "The subject in the picture went through a seamless transition.", "template": "clayshot_duo"}, {"id": 112, "name": "变身美人鱼", "videoUrl": "https://image01.vidu.zone/vidu/media-asset/3-fdbb5ab1.mp4", "posterUrl": "https://image01.vidu.zone/vidu/media-asset/1-233089c3.webp", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "fishermen"}, "input_instruction": {"image_count": "仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu/media-asset/2-75289ebf.webp"], "person_count": "支持单人照片、双人合照、和宠物照片", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 'prompt' 参数", "effect_boundary": "当主体为人物时，可能会出现眉毛转变过程模糊，或人物转场效果不流畅的情况"}, "prompt": "在【场景/背景】中，画面中出现了【主体数量和种类描述】——【主体1描述】、【主体2描述】……他们最初的状态是为：正【动作状态，例如站立、坐着、仰望】，神情中透着【情绪关键词】。然而此刻，主体变成美人鱼的现象正在发生：每一个主体都发生的变化：场景切换至水下世界，主体已完全化身为人鱼形象，身穿鳞甲般的银色胸甲与鱼尾，肌肉线条清晰，目光坚定。四周是流动的水草与柔和光斑，海底植物如珊瑚、海藻在水流中起舞，缥缈唯美。说明：这里的主体可以是人物、动作、物品等.\\\nRequirements：\n1. 根据图片的具体内容替换上面的【】内容。最初的状态根据图片中的主体状态来描述。\n2. If the image is a close-up or medium shot, set Camera Movement to `Zoom out`.\n3. If there are two subjects or more subjects in the image, both should be performing `这里是具体的变化`.\n4. 详细描述每一个主体的变化过程，特别是动作的细节和变化的过程。(关于主体的区别，可以使用明显具有特征区分的方式。例如，左边的男士，右侧的女士/穿黑色卫衣的男士，穿白色裙子的小女孩等等)", "template": "fishermen"}, {"id": 113, "name": "日式漫画风-irasutoya", "videoUrl": "https://scene.vidu.zone/media-asset/071533-GQ6eKivFsOrxEX3G.mp4", "posterUrl": "https://scene.vidu.zone/media-asset/072151-RPn3L4YFBkStu2rG.png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "irasutoya"}, "input_instruction": {"image_count": "仅支持上传一张图片", "image_url": ["https://scene.vidu.zone/media-asset/071533-xicfUD8oVomfzGQy.png"], "person_count": "支持单人照片、双人合照、多人合照、宠物照片或物体照片", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 'prompt' 参数", "effect_boundary": "在极少数情况下，可能出现转绘结果与原图不符，生成其他物体的情况"}, "prompt": "The subjects in the image are undergoing a style transformation into the Japanese Irasu<PERSON>ya illustration style. If there are multiple subjects in the image, all of them should go through this style transformation.", "template": "irasutoya"}, {"id": 114, "name": "美式漫画风", "videoUrl": "https://scene.vidu.zone/media-asset/071249-yUd4tDFq9K9HSAvn.mp4", "posterUrl": "https://scene.vidu.zone/media-asset/071249-jETsehE0ODjTWyui.png", "isHot": false, "isNew": false, "category": "Transformation", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "american_comic"}, "input_instruction": {"image_count": "仅支持上传一张图片", "image_url": ["https://scene.vidu.zone/media-asset/071249-dUOR6r4ESoSnS95J.png"], "person_count": "支持单人照片、双人合照、多人合照、宠物照片或物体照片", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 'prompt' 参数", "effect_boundary": "当输入主体为多人时，可能会出现转绘后个别人物缺失的情况"}, "prompt": "In the [scene/background], the image features [number and type of subjects] — [description of subject 1], [description of subject 2] (if present). They are [action/state, such as standing, sitting, looking upward], with expressions that convey [emotion keyword]. At this moment, all the subjects in the image are undergoing a style transformation — shifting from their original style into the Rick and Morty-style of American animation, including changes to the surrounding environment.\\\nRequirements:\\Replace the placeholders above with the specific details based on the image content.", "template": "american_comic"}, {"id": 115, "name": "法式热吻", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/fashirewen_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/fashirewen_cover.png", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "french_kiss"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/fashirewen_upload.png"], "person_count": "仅支持双人拼图/合照", "attention": "人物为正面上半身，且手中没道具，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当上传图片为全身图时，可能会出现没有亲上，或亲吻幅度较小、时长较短，没有热吻感觉的情况"}, "prompt": "The two figures in the painting move closer and then passionately kiss, alternating with deep and firm intensity.", "template": "french_kiss"}, {"id": 116, "name": "2.0 拥抱", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/hugging_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/hugging_cover.png", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "hugging"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/hugging_upload.png"], "person_count": "仅支持双人拼图/合照，人宠拼图/合照", "attention": "人物露出超半身，且手中没道具，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当照片中两个人物大小不一致，会有概率出现模型未能识别，导致拥抱视频不符合物理规律的情况"}, "prompt": "视频内容\\\n画面中的两个主体转向彼此，并开始拥抱。\\\n# 要求\\\n将Motion Level设置为“Large”。", "template": "hugging"}, {"id": 117, "name": "2.0 亲吻", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/kissing_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/kissing_cover.png", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "kissing"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/kissing_upload.png"], "person_count": "仅支持双人拼图/合照，人宠拼图/合照", "attention": "人物露出超半身，且手中没道具，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有一定概率出现嘴巴亲吻鼻子，脸颊，下巴等情况"}, "prompt": "视频内容\\\n画面中的两个主体开始转向彼此，开始接吻，画面呈现充满爱意的氛围。\\\n# 要求\\\n1.亲吻时候，嘴唇的动作要彼此贴合。\\\n2.将Motion Level的值设置为“Large”.", "template": "kissing"}, {"id": 118, "name": "比心", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/bixin_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/bixin_cover.jpeg", "isHot": true, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "interaction"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/bixin_upload.jpeg"], "person_count": "仅支持双人拼图/合照", "attention": "人物近景上半身照、不露出手指，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "在人物手指露出时，有一定概率出现比心失败的情况"}, "prompt": "图片中的两个人[简要精准的外观描述]面对镜头，各自伸出一只手，合在一起在胸前比了一个爱心", "template": "interaction"}, {"id": 119, "name": "送玫瑰花", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/rose_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/rose_cover.jpeg", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "interaction"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/rose_upload.png"], "person_count": "仅支持双人拼图/合照", "attention": "人物近景上半身照、不露出手指，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "在人物手指露出时，有一定概率出现玫瑰花凭空出现的情况"}, "prompt": "视频内容\\\n镜头左边的人物[简要精准的外观描述、性别描述]拿起一只玫瑰花转身送给图片右边的人物[简要精准的外观描述]，右边的人物同样转身，面对面接受玫瑰花\\\n# 要求1.Shot Size：镜头缓慢自然的变为Medium Shot；2.Camera Movement:根据图片初始镜头确定镜头是Zoom Out 还是Zoom In才能变为Medium Shot，如果初始镜头为近景则需要Zoom Out，如果初始就是Medium Shot则为None", "template": "interaction"}, {"id": 120, "name": "拥抱 Pro", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/0331/guonei/yongbaopro_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/0331/guonei/yongbaopro_cover.png", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "hugging_pro"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/0331/guonei/yongbaopro_upload.png"], "person_count": "仅支持双人合照和双人拼图的照片", "attention": "当输入图清晰且人物为半身正面照时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现拥抱过程中手部轻微崩坏，或没有完全抱住的情况"}, "prompt": "Video content\\\n 画面中的主体开始转向彼此，并开始拥抱。\\\n# 要求\\\n将Motion Level设置为“Large”", "template": "hugging_pro"}, {"id": 121, "name": "亲吻 Pro", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/0331/guonei/qinwenpro_vodeo.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/0331/guonei/qinwenpro_cover.png", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "kissing_pro"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/0331/guonei/qinwenpro_upload.png"], "person_count": "仅支持双人合照和双人拼图的照片", "attention": "当输入图清晰且人物为半身正面照时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现亲吻过程中出现第三个人，或没有完全亲上的情况"}, "prompt": "Video content\\\n 画面中的两个主体开始转向彼此，开始接吻，画面呈现充满爱意的氛围。\\\nRequirements:\\\n1. 亲吻时候，嘴唇的动作要彼此贴合 2. 将Motion Level的值设置为“Large", "template": "kissing_pro"}, {"id": 122, "name": "雨中热吻", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/yuzhongrewen_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/0424/rain_kiss_cover.png", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16  16:9  1:1", "credit_cost": "6", "scene": "rain_kiss"}, "input_instruction": {"image_count": "必传，只支持输入一张照片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/yuzhongrewen_upload.png"], "person_count": "仅支持双人拼图/合照", "attention": "人物露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能出现视频中的人物与上传图片不一致，或亲吻时未能做到嘴对嘴贴合的情况"}, "prompt": "The picture depicts two people. The overall style is aesthetically pleasing and full of passion to highlight the fervor of the emotions. In the rain, the hair and clothes of the two people are completely soaked, and the water droplets on their clothes and hair are clearly visible. The two first look at each other affectionately, with love and longing filling their eyes, and then they kiss each other passionately.\\\nWhen kissing, the lips should be in sync with each other.\\\nThe movements are smooth and natural, without any stiff posture of pulling back and forth. The raindrops are normal and natural, and they don't splash around randomly. The camera captures their upper bodies and remains stationary.\\\nSoft rain fell, soaking their clothes until the fabric clung to their skin. Water droplets trickled down, glistening under the dim light.The fabric gradually turned heavy and sodden, clinging to their bodies as it absorbed the moisture. Dark patches spread across the material, marking where the water seeped in deepest.\\\nIt is prohibited to change the original appearance, clothing, and hairstyle of the characters.\\\n It is prohibited to have black liquid appear", "template": "rain_kiss"}, {"id": 123, "name": "一起走走", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/couplewalk_0516/couplewalk_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/couplewalk_0516/couplewalk_cover.png", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "couple_walk"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/couplewalk_0516/couplewalk_upload.png"], "person_count": "仅支持单人照片", "attention": "当画面主体露出膝盖以上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入图为近景半身照时，生成效果较差，人物整体美观度不佳；若为大头照，视频中可能出现跳帧现象；可能出现两人行走方向相反的情况；在少数情况下，可能无法生成另一位 CP；当图中包含 2 人以上时，可能无法生成情侣互动，仅表现为人物各自散步 "}, "prompt": "# Determine the video content based on the image\n- If the image contains only one person: The video begins with the character from the image appearing alone in the frame. After a moment, a character of the opposite sex naturally enters from off-screen. They hold hands and walk side by side with a relaxed pace, creating a warm and intimate atmosphere that conveys a sense of natural emotional connection.\n- If the image contains multiple people: The characters in the image hold hands and walk together naturally.\n\n# Requirements:\n- Accurately describe the characters’ appearance and gender as shown in the image. Avoid mentioning facial blurring.\n- If the new character is male, he should be described as tall, handsome, and confident.\n- If the new character is female, she should be described as beautiful, elegant, and stylish.\n- The hand-holding should appear spontaneous and mutual, without specifying who initiated it.\n- The entire scene should follow a clear narrative structure, suitable for video generation, with smooth visual rhythm and emotional continuity. ", "template": "couple_walk"}, {"id": 124, "name": "收花时刻", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/flowerreceive_0516/flowerreceive_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/flowerreceive_0516/flowerreceive_cover.png", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "flower_receive"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/flowerreceive_0516/flowerreceive_upload.png"], "person_count": "支持单人、双人以及多人照片", "attention": "当画面主体露出正面上半身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入为人物全身时，可能出现鲜花从天而降，而非由他人赠送的情况；当输入为人物背影时，人物转身时可能出现崩坏现象；有较低概率出现收花人出现在画面中的情况"}, "prompt": "A large bouquet of flowers enters the frame from off-screen and is handed to the character in the image. The character reaches out with both hands to receive the bouquet, eyes wide open in surprise, followed by a joyful smile.\n**# Requirements:**\n1. Only the hand of the person giving the flowers should appear in the frame.\n2. The bouquet should consist of roses in random colors.\n3. The bouquet must have delicate and refined packaging.\n4. If the image contains multiple characters, the flowers should be handed to one of them only.", "template": "flower_receive"}, {"id": 125, "name": "亲吻脸颊", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/cheekkiss_0530/cheekkiss_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/cheekkiss_0530/cheekkiss_cover.png", "isHot": false, "isNew": false, "category": "Interaction", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "cheek_kiss"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Interactive/Interactive/cheekkiss_0530/cheekkiss_upload.png"], "person_count": "支持双人合照或三人合照", "attention": "当主体露出正面上半身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "受人物身高差异或图像拍摄角度影响，亲吻位置可能出现偏移；此外，也有可能出现画面切换为其他景别，未呈现亲吻动作的情况"}, "prompt": "In [scene/background], [character 1], [character 2] (and [character 3], if present) are [standing/sitting] together, with a [warm/relaxed/shy] mood. \\\n- Two characters: Left kisses right on the cheek; right faces the camera, blushing with a shy smile. \\\n- Three characters: Left and right kiss the middle on both cheeks; middle faces forward, blushing, nervously smiling. \\\nRequirements:\\1.Replace all [ ] with actual details based on the image.\\\n2. Motion Level：Large", "template": "cheek_kiss"}, {"id": 126, "name": "万物生花", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/wanwushenghua_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/wanwushenghua_cover.jpeg", "isHot": false, "isNew": false, "category": "Creative", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "bloom_magic"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/wanwushenghua_upload.jpeg"], "person_count": "支持建筑、汽车、商品、动物等主体，基本无类型限制", "attention": "主体为结构和轮廓清晰的物品时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有可能出现生花生到一半画面突然叠化跳帧，不够流畅自然的情况"}, "prompt": "Many flowers elegantly and rapidly grow from various surfaces of the main subject in image 1, swaying in the wind, full of vitality.", "template": "bloom_magic"}, {"id": 127, "name": "万物生萌芽熊", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/0324/mengyaxiong_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/0324/mengyaxiong_cover.png", "isHot": true, "isNew": false, "category": "Creative", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16、16:9、1:1", "credit_cost": "6", "scene": "bloom_doorobear"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/0324/mengyaxiong_upload.png"], "person_count": "支持单一或多个商品图", "attention": "当人物为半身或全身、宠物为全身、物体为全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "会有可能出现萌芽熊最终没有趴在主体上方，或在生成过程中破碎的情况"}, "prompt": "Video content\\\n The person in picture one, looking directly at the camera without moving. Suddenly, a small sprout from picture three grows from the top of the person's head. Then, this sprout grows into the complete cartoon figure shown in the second image. The cartoon figure from the second image then lies on top of the person's head. The person or animal smiles happily. \\\n Requirements: \\\n1. Include the process of a small sprout growing from their head. \\\n2. The tiny cute bear character from Image 2 should fall onto their head as if descending from above. \\\n3. Ensure that the green succulent bear is tiny. \\\n4. If Figure 1 does not depict a person, update the subject field to reflect the actual subject. \\\n5. Motion Level: Small", "template": "bloom_doorobear"}, {"id": 128, "name": "丝滑转场", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/sihuazhaunchang_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/sihuazhaunchang_cover.png", "isHot": false, "isNew": false, "category": "Creative", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "smooth_shift"}, "input_instruction": {"image_count": "必传，仅支持上传两张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/sihuazhaunchang_upload1.png", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/sihuazhaunchang_upload2.png"], "person_count": "支持单人相同人物/单人不同人物", "attention": "当人物为单人时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "在生成过程中，可能会出现缺失中间过渡、首尾帧转变不自然、画面崩坏或生成内容异常诡异的情况。"}, "prompt": "Video content\\\n The person in the picture went through a seamless trantition", "template": "smooth_shift"}, {"id": 129, "name": "部落盒子", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/buluohezi_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/buluohezi_cover.png", "isHot": true, "isNew": false, "category": "Creative", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "要求输入图片为9:16", "credit_cost": "6", "scene": "box_me"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/buluohezi_upload.png"], "person_count": "支持写实风单人和动物", "attention": "照片中的人物或动物为全身或半身露出时，效果更好；此外，生成的手办盒子的风格随机，包括乐高、我的世界、芭比、毛绒玩具等多种样式", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入为双主体时，可能随机生成 1 个或 2 个盒子，结果不可控；存在未成功取走手办或误带走配件的概率；可能会出现双手同时取物的情况；要求输入图必须为9:16"}, "prompt": "Video content\\\n A large hand suddenly appears in the frame and swiftly picks up one of the dolls with ease, leaving behind the toy accessories on the right side.\\\nRequirement:\\\nMotion Level:Large", "template": "box_me"}, {"id": 130, "name": "漫画表情包", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/mangameme_0509/mangameme_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/mangameme_0509/mangameme_cover.png", "isHot": false, "isNew": false, "category": "Creative", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "仅支持输入9:16", "credit_cost": "10", "scene": "manga_meme"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/mangameme_0509/mangameme_upload.png"], "person_count": "支持单人、宠物、双人、人宠组合、三人家庭或多人场景", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有时会出现视频尾帧一侧曝光的情况，或视频前两秒运动较慢、无明显动作的情况"}, "prompt": "Identify the expressions of each subject in the image, and ensure that their movements match their initial expression settings.\nRequirement: \nFixed camera.\nMake the character's movements more expansive.\nProhibited:\nNo zooming in on any individual quadrant of the image", "template": "manga_meme"}, {"id": 131, "name": "四宫格表情包", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/quadmeme_0513/quadmeme_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/quadmeme_0513/quadmeme_cover.png", "isHot": false, "isNew": false, "category": "Creative", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "仅支持输入9:16", "credit_cost": "10", "scene": "quad_meme"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Creative/Creative/quadmeme_0513/quadmeme_upload.png"], "person_count": "支持单人照片、宠物照片、双人合照或人宠合照照", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现部分表情动画相似，缺乏明显差异；或视频前两秒运动较慢、无明显动作的情况"}, "prompt": "Identify and describe the number of characters, their actions, and expressions in each quadrant.\nThe four quadrants in the image are not related to each other and there is no interaction.\nBased on the number of characters in each quadrant and their expressions, design extremely exaggerated actions for each quadrant. Ensure that the actions in different quadrants have significant differences.\nRequirements:\n1.Keep the camera fixed.\n2.The actions of the subjects should be smooth and there should be no stillness.\n3.All the subjects in each quadrant sway extremely obviously and with a large amplitude from side to side.\n4.Maintain the original emotions of all the characters/animals in each quadrant and the emotional effects in the anime style.\nProhibitions:\nDo not zoom in on any single quadrant of the image.\nMotion Level:'Large' ", "template": "quad_meme"}, {"id": 132, "name": "像素风", "videoUrl": "https://image01.vidu.zone/vidu/media-asset/3-376efb09.mp4", "posterUrl": "https://image01.vidu.zone/vidu/media-asset/2-71f456bd.webp", "isHot": false, "isNew": false, "category": "Creative", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "该视频输出比例随机", "credit_cost": "10", "scene": "pixel_me"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu/media-asset/1-8890cfda.webp"], "person_count": "支持单人照片、双人合照或多人合照", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 'prompt' 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "生成过程中可能出现像素破损或画面短暂出现白雾效果的情况"}, "prompt": "Animate the pixel-style characters in the image in a natural and smooth manner, ensuring movements adhere to real-world motion logic to avoid stiffness or unnatural dynamics. While preserving the pixel art style, achieve lifelike dynamics through reasonable frame rates (e.g., 12-15 FPS) and coherent pose transitions. Maintain the melancholic atmosphere of the scene by reinforcing emotional tone through character postures (e.g., slightly hunched shoulders, slow movement) and environmental details (e.g., falling leaves, dim lighting).  \nCore Points:  \n- Movements are natural and coherent, following physical laws (e.g., gravity, inertia).  \n- Preserve the pixel art style while optimizing frame-by-frame fluidity.  \n- Sustain the melancholic atmosphere through postures and environmental details.", "template": "pixel_me"}, {"id": 133, "name": "生日派对", "videoUrl": "https://scene.vidu.zone/media-asset/071448-7F5lYULYr8gOqgms.mp4", "posterUrl": "https://scene.vidu.zone/media-asset/071448-X8NXqPlLyjO8Gq77.png", "isHot": false, "isNew": false, "category": "Creative", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "该视频输出比例随机", "credit_cost": "10", "scene": "happy_birthday"}, "input_instruction": {"image_count": "仅支持上传一张图片", "image_url": ["https://scene.vidu.zone/media-asset/071448-ixTIiw8afgO2y5iy.png"], "person_count": "支持单人照片、双人合照、多人合照、宠物照片或蛋仔角色", "attention": "当输入主体为单人且露出正面上半身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 'prompt' 参数", "effect_boundary": "有较低概率出现人物已松手但蛋糕仍悬浮于空中的情况"}, "prompt": "请勿包含原始图像中不存在的任何元素。\n图像中所有角色左右摇晃，充满活力，营造热闹的氛围。角色自然微笑。\n角色一只手托住蛋糕盘子，另外一只手臂左右挥动。\n相机视角：严格保持静止。\n元素限制：禁止添加原始图像中未出现的角色、面部细节、位置变动或装饰元素", "template": "happy_birthday"}, {"id": 134, "name": "流金岁月", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/liujinsuiyue_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/liujinsuiyue_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9、9:16、1:1", "credit_cost": "6", "scene": "golden_epoch"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/liujinsuiyue_upload.jpeg"], "person_count": "仅支持单人", "attention": "人物为五官照片，不带发型和服饰的时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有一定概率出现发型与旗袍不匹配、动作幅度过小、手部细节崩坏的情况"}, "prompt": "Video content\\\n The character displays a unique charm of elegance and intelligence, confidently looking at the camera, and giving a slight smile. Vintage film style, <PERSON> aesthetic style.\\\n# Requirements\\\n1.There is only one character. Accurately describe the character's appearance based on Figure 1, and accurately describe the character's clothing and background features based on Figure 2 \\\n2.Motion_level:Middle.", "template": "golden_epoch"}, {"id": 135, "name": "金像盛典", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/jinxiangshengdian_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/jinxiangshengdian_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9、9:16、1:1", "credit_cost": "6", "scene": "oscar_gala"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/jinxiangshengdian_upload.png"], "person_count": "仅支持单人", "attention": "人物为大头照，肩膀以上，不露出衣服的时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有可能出现人物前面没有生成立麦的情况"}, "prompt": "Video content\\\n The person in Figure One is dressed in the outfit from Figure Two, holding an Oscar statuette, standing at the center of the Oscar awards stage, smiling at the camera while delivering an acceptance speech.\\\n# Requirements\\\n1. A precise description of the person in Figure One, including their facial features, expression, hairstyle, and gender.\\\n2. Determine the type of attire based on the gender of the character in Image 1. If the character is female, she wears the gown from Image 2. If the character is male, he wears a stylish suit.\\\n3. Set Additional Information as 'None'.", "template": "oscar_gala"}, {"id": 136, "name": "时尚T台", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/shishangTtai_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/shishangTtai_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9、9:16、1:1", "credit_cost": "6", "scene": "fashion_stride"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/shishangTtai_upload.png"], "person_count": "仅支持单人和单宠物", "attention": "人物为半身或全身，宠物为全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有可能出现人物行进方向不笔直、人物转身或摆pose的情况"}, "prompt": "Video content\\\n The character in Image 1 walks confidently in the scene of Image 2, slowly approaching the camera, with slow motion capturing the character's confident expression.\\\n# Requirements\\\n1.Emphasize the slow-motion effect of ultra-high-speed photography.\\\n2.Please strictly follow my instructions; the image only supplements the character's appearance details.\\\n3.Motion_Level:small.\\\n4.Camera Movement: Zoom out.", "template": "fashion_stride"}, {"id": 137, "name": "星光红毯", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/xingguanghongtan_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/xingguanghongtan_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9、9:16、1:1", "credit_cost": "6", "scene": "star_carpet"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/xingguanghongtan_upload.png"], "person_count": "仅支持单人和单宠物", "attention": "人物为半身或全身，宠物为全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有可能出现人物在红毯上倒走或转身向后走的情况的情况"}, "prompt": "Video content\\\n The character in Image 1 walks confidently in the scene of Image 2, slowly approaching the camera, with slow motion capturing the character's confident expression.\\\n# Requirements\\\n1.Emphasize the slow-motion effect of ultra-high-speed photography.\\\n2.Please strictly follow my instructions; the image only supplements the character's appearance details.\\\n3.Motion_Level:small.\\\n4.Camera Movement: Zoom out.", "template": "star_carpet"}, {"id": 138, "name": "烈焰红毯", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/lieyanhongtan_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/lieyanhongtan_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9、9:16、1:1", "credit_cost": "6", "scene": "flame_carpet"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/lieyanhongtan_upload.png"], "person_count": "仅支持单人和单宠物", "attention": "人物为半身或全身，宠物为全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有可能出现人物步频过快、衣服燃烧的情况"}, "prompt": "Video content\\\n The character in Image 1 walks confidently in the scene of Image 2, slowly approaching the camera, with slow motion capturing the character's confident expression.\\\n# Requirements\\\n1.Emphasize the slow-motion effect of ultra-high-speed photography.\\\n2.Please strictly follow my instructions; the image only supplements the character's appearance details.\\\n3.Motion_Level:small.\\\n4.Camera Movement: Zoom out.", "template": "flame_carpet"}, {"id": 139, "name": "风雪红毯", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/fengxuehongtan_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/fengxuehongtan_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9、9:16、1:1", "credit_cost": "6", "scene": "frost_carpet"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/fengxuehongtan_upload.png"], "person_count": "仅支持单人和单宠物", "attention": "人物为半身或全身，宠物为全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有可能出现视频主体走出红毯或没有走上红毯的情况"}, "prompt": "Video content\\\n It is snowing heavily in the sky, and the character in image 1 confidently walks down the red carpet, slowly approaching the camera, with slow motion capturing the character's confident expression'Environment':'The setting is a stunning winter landscape, where heavy snowflakes cascade from the sky, blanketing the ground in a soft white layer. The red carpet stretches out on a smooth, icy surface, contrasting sharply with the surrounding snow, creating a striking visual that draws attention to the confident figure walking towards the camera.' \\\n# Requirements\\\n1.Emphasize the slow-motion effect of ultra-high-speed photography.\\\n2.Please strictly follow my instructions; the image only supplements the character's appearance details.\\\n3.Motion_Level:small.\\\n4.Camera Movement: Zoom out.", "template": "frost_carpet"}, {"id": 140, "name": "胶带写真", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/jiaodaixiezhen_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/jiadaixiezhen_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16  16:9  1:1", "credit_cost": "6", "scene": "tap_me"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Portrait/Portrait/jiaodaixiezhen_upload.png"], "person_count": "仅支持男生或女生的单人照片", "attention": "当照片中的人物为单主体时，效果更好；当视频输出比例为16:9时，效果相对较差", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能出现AI性别判断错误的情况；当人物动作幅度较大时，可能导致动作不自然或服装胶带崩坏"}, "prompt": "The person in Image 1 [brief description of facial features and gender], after putting on the clothes from Image 2 or Image 3, slightly adjusts the posture and makes confident eye contact with the camera, displaying a self-assured expression. This creates a vivid and engaging visual effect that captures the viewer’s attention.\n# requirement\n1. **Background**: The background must be consistent with Image 4.\n2. **Generate the final description based on the following rules**:\n(1) **Female**: If the person in Image 1 is female, the woman must wear a strapless mini dress made of yellow and black tape, cropped to reveal the abdomen, similar to the female shown in Image 3.\n(2) **Male**: If the person in Image 1 is male, the man should wear jeans as seen on the male in Image 2, with a bare upper body wrapped in tape, exposing the abdominal muscles, pectoral muscles, and biceps.\n(3) **Prohibitions**: \n- The subject must not turn around. \n- Fluttering tape must not appear in the image. \n- The subject must not be speaking. \n- Avoid any vague facial descriptions; use specific identifiers such as man/woman/boy/girl. \n- Do not use any pronouns; instead, describe the subject using concrete visual details.\n**Note**: The final gender and outfit of the subject must be strictly determined based on Image 1", "template": "tap_me"}, {"id": 141, "name": "樱花飘落", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/yinghuapiaoluo_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/yinghuapiaoluo_cover.jpeg", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "sakura_season"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/0311/sakura_season_up.png"], "person_count": "支持写实风的单/双人和二次元风的单人照片", "attention": "当背景在室外，人物露出超半身/全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动。", "effect_boundary": "当图片是胸部以上的近景半身照，可能会出现人物下半身埋在地面内的情况；或场景过于复杂时，可能不会出现樱花。"}, "prompt": "Video content\\\n The camera pulls back, and pink cherry blossom petals gently fall from the top of the frame under a cherry blossom tree. The subject faces the camera, tilting their head upward to gaze at the sky while the petals drift down from above, and the subject smiles happily.\\\n# Requirements\\\n1.Emphasize the subject's upward head movement. \\\n2.Camera movement: Zoom out. \\\n3.Shot size: Wide shot.\\\n4.Motion Level: Large.", "template": "sakura_season"}, {"id": 142, "name": "变身为圣诞老人", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/bianshengdanlaoren_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/bianshengdanlaoren_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "christmas"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/bianshengdanlaoren_upload.png"], "person_count": "支持单人、双人和多人", "attention": "人物为大半身照，或全身照的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有一定概率出现圣诞老人和原人物身高不一致的情况"}, "prompt": "视频内容\\\n画面中的人物从下前方轻轻举起鲜红的布，动作干净利落地将其从头覆盖到全身，然后随着人物将红布迅速向下扯下，人物变成了充满节日气息的圣诞老人。\\\n# 要求\\\n1.Style设定为:Realistic, HD\\\n2.根据用户上传图片确定人物数量，不要出现[一群人]、[们]等代词而是精准地指出人物数量\\\n3.如果用户上传图片有多个人物，则他们需要一起举起红布，所有人都变身为圣诞老人。不要出现图片中没有的人物", "template": "christmas"}, {"id": 143, "name": "圣诞老人来送礼", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/shengdansongli_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/shengdansongli_cover.png", "isHot": true, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "christmas"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/shengdansongli_upload.png"], "person_count": "支持单人、双人和多人", "attention": "人物为大半身照，或全身照的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有一定概率出现多个圣诞老人来送礼的情况"}, "prompt": "视频内容\\\n镜头中人物看向右边，然后一个圣诞老人满面笑容地从右边走入画面，手中捧着一个精美的礼物盒。他轻轻将礼物递给画面中的人物，动作自然且充满温暖。镜头聚焦在接到礼物的人物，人物神情中充满惊喜与感激。画面捕捉到人物的微表情和互动细节。\\\n# 要求\\\n1.Style设定为:Realistic, HD\\\n2.根据用户上传图片确定人物数量，不要出现[一群人]、[们]等代词而是精准地指出人物数量\\\n3.如果用户上传图片有多个人物，则他们需要一起举起红布，所有人都变身为圣诞老人。不要出现图片中没有的人物", "template": "christmas"}, {"id": 144, "name": "圣诞节举杯庆祝", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/jubeiqingzhu_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/jubeiqingzhu_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "christmas"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/jubeiqingzhu_upload.png"], "person_count": "支持单人、双人和多人", "attention": "人物为大半身照，或全身照的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有一定概率出现首帧镜头推移缓慢的情况"}, "prompt": "视频内容\\\n画面人物手里拿起香槟酒杯，庆祝圣诞快乐，随着镜头拉远，画面出现圣诞树等圣诞节日物品\\\n# 要求\\\n1.Take a step-by-step approach in your response\\\n2.以我的视频描述为第一要素，背景的描述统一、合理，不要描述两次.\\\n3.根据用户上传图片确定人物数量，不要出现[一群人]、[们]等代词而是精准地指出人物数量\\\n4.Motion Level 设定为：Middle\\\n5.如果用户上传图片有多个人物，每个人都需要拿起香槟酒杯。不要出现图片中没有的人物", "template": "christmas"}, {"id": 145, "name": "圣诞老人来拥抱", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/shengdanyongbao_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/shengdanyongbao_cover.png", "isHot": true, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "christmas"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/shengdanyongbao_upload.png"], "person_count": "支持单人、双人和多人", "attention": "人物为大半身照，或全身照的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有一定概率出现多个圣诞老人来拥抱的情况"}, "prompt": "视频内容\\\n镜头中人物看向画面外，接着一个圣诞老人满面笑容地从画面外走入画面，然后和人物拥抱，动作自然且充满温暖，镜头聚焦在一个温暖的拥抱，画面捕捉到人物的的微表情和互动细节\\\n# 要求\\\n1.根据人物的位置和状态合理的设计动作，而不是突兀的直接拥抱，要先描写人物转变到一个合适拥抱的姿势\\\n2.Take a step-by-step approach in your response\\\n3.根据用户上传图片确定人物数量，不要出现[一群人]、[们]等代词而是精准地指出人物数量\\\n4.如果图片中有多个人物，则需要一起和圣诞老人拥抱。不要出现图片中没有的人物\\\n5.强调只有1个圣诞老人", "template": "christmas"}, {"id": 146, "name": "2025新年烟花", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/2025xinnianyanhua_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/2025xinnianyanhua_cover.jpeg", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16", "credit_cost": "6", "scene": "lunar_newyear"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/2025xinnianyanhua_uplaod.jpeg"], "person_count": "支持单人、双人和多人", "attention": "人物为正面照，手中没其它道具的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有一定概率出现人物缺失、仙女棒颜色跳变的情况"}, "prompt": "视频内容\\\n视频展示人物手持仙女棒 面对镜头，人物身后绽放新年烟花 烟花上写2025 采用远景视角 动作自然且充满温暖，画面捕捉到人物的的微表情和互动细节 \\\n# 要求1.根据人物的位置和状态合理的设计动作.2.Take a step-by-step approach in your response.3.根据用户上传图片确定人物数量，不要出现[一群人]、[们]等代词而是精准地指出人物数量.4.如果图片中有多个人物，则需要一起手持仙女棒。不要出现图片中没有的人物.5.如果图片展示了背景图，则设定为背景并详细描述.6.保证人物始终在画面中心.", "template": "lunar_newyear"}, {"id": 147, "name": "双人举杯", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/shuangrenjubei_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/shuangrenjubei_cover.jpeg", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16", "credit_cost": "6", "scene": "lunar_newyear"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/shuangrenjubei_upload.png"], "person_count": "支持单人、双人", "attention": "人物为正面照，手中没其它道具的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有一定概率出现人物未拿起酒杯、干杯失败的情况"}, "prompt": "视频内容\\\n视频展示新年餐桌上人物拿着香槟高脚杯，干杯庆祝，场面十分红火热闹，动作自然且充满温暖，画面捕捉到人物的的微表情和互动细节.\\\n# 要求1.根据人物的位置和状态合理的设计动作.2.Take a step-by-step approach in your response.3.根据用户上传图片确定人物数量，不要出现[一群人]，不要出现图片中没有的人物.4.如果图片中有多个人物，则需要一起举杯庆祝。每个只拿一只杯子.5.如果图片展示了背景图，则设定为背景并详细描述.6.保证人物始终在画面中心 颜色温馨自然喜庆，不要黑白色.", "template": "lunar_newyear"}, {"id": 148, "name": "红包雨", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/hongbaoyu_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/hongbaoyu_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16", "credit_cost": "6", "scene": "lunar_newyear"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/festival/hongbaoyu_upload.webp"], "person_count": "支持单人、双人", "attention": "人物为正面照，手中没其它道具的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有一定概率出现人物缺失、没有开门动作的情况"}, "prompt": "视频内容\\\n视频展示人物推开屋门 门内在下金光闪闪的红包雨 洒在人物身上 人物很开心。人物动作自然且充满温暖，画面捕捉到人物的的微表情和互动细节 \\\n# 要求1.根据人物的位置和状态合理的设计动作.2.Take a step-by-step approach in your response.3.根据用户上传图片确定人物数量，不要出现[一群人]、[们]等代词而是精准地指出人物数量.4.不要出现图片中没有的人物.5.保证人物始终在画面中心.6.推开房门后才洒落红包雨，推开房门前不要蹦出东西.7.洒落的是红色的红包雨不是金币 .8.要有一把真实的门，要有打开房门的动作，红包从门外掉落.", "template": "lunar_newyear"}, {"id": 149, "name": "全家福比心", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/quanjiafubixin_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/quanjiafubixin_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "love_pose"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/quanjiafubixin_upload.png"], "person_count": "支持单人、双人和多人", "attention": "人物为正面照，手中没其它道具的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当合照主体人物手部被隐藏或者是复杂手势，可能会出现比心失败或者是多余肢体的情况"}, "prompt": "视频内容\\\n画中的角色面对镜头，抬起双手在胸前，做出比心的动作.\\\n# 要求-根据图片判断人物数量，如果有多个人，每个人物都要做出比心的动作。", "template": "love_pose"}, {"id": 150, "name": "和财神比心", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/caishenbixin_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/caishenbixin_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9", "credit_cost": "6", "scene": "wish_sender"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/festival/hecaishenbixin_upload.webp", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/caishenbenti.png", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/caishenbeijing.png"], "person_count": "支持单人、双人和多人", "attention": "人物为正面照，手中没其它道具的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当合照主体人物手部被隐藏或者是复杂手势，可能会出现比心失败或者是多余肢体的情况"}, "prompt": "视频内容\\\n 视频展示了红衣服财神角色站在人物角色旁边，他们一起面对镜头,每个人伸出双手各自在自己胸前比心，动作自然且充满温暖。\\\n# 要求 1. 如果图片中有多个人物，则每个人各自在自己胸前比心。不要出现图片中没有的人物。2. 强调只有1个财神。3.如果图片展示了背景图，则设定为背景并详细描述。4. 保证人物和财神始终在画面中心。", "template": "wish_sender"}, {"id": 151, "name": "和财神拥抱", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/caishenyongbao_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/caishenyongbao_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9", "credit_cost": "6", "scene": "wish_sender"}, "input_instruction": {"image_count": "必传，仅支持上传三张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/festival/hecaishenyongbao_upload.webp", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/caishenbenti.png", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/caishenbeijing.png"], "person_count": "仅支持单人照片，建议将财神图和背景图作为内置图", "attention": "人物为正面照，手中没其它道具的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "生成的视频可能会出现开头 1 秒静止的情况"}, "prompt": "视频内容\\\n 镜头中人物看向画面外，接着一个财神满面笑容地从画面外走入画面，然后和人物拥抱，动作自然且充满温暖，镜头聚焦在一个温暖的拥抱，画面捕捉到人物的的微表情和互动细节。\\\n# 要求 1.根据人物的位置和状态合理的设计动作，而不是突兀的直接拥抱，要先描写人物转变到一个合适拥抱的姿势。 2.Take a step-by-step approach in your response. 3.根据用户上传图片确定人物数量，不要出现[一群人]、[们]等代词而是精准地指出人物数量。4.如果图片中有多个人物，则需要一起和财神拥抱。不要出现图片中没有的人物。5.强调只有1个财神。6.如果图片展示了背景图，则设定为背景并详细描述。", "template": "wish_sender"}, {"id": 152, "name": "财神送元宝", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/caishenyuanbao_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/caishenyuanbao_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9", "credit_cost": "6", "scene": "wish_sender"}, "input_instruction": {"image_count": "必传，仅支持上传三张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/festival/caishensongyuanbao_upload.webp", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/caishenbenti.png", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/caishenbeijing.png"], "person_count": "仅支持单人照片，建议将财神图和背景图作为内置图", "attention": "人物为正面照，手中没其它道具的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现财神凭空出现的情况"}, "prompt": "视频内容\\\n 镜头中人物看向右边，然后一个财神满面笑容地从右边走入画面，手中捧着一个元宝。他轻轻将元宝递给画面中的人物，人物充满惊喜的表情双手接过元宝，动作自然且充满温暖。镜头聚焦在接到元宝的人物。画面捕捉到人物的微表情和互动细节。\\\n# 要求 1.Take a step-by-step approach in your response. 2.Motion Level: Middle. 3.首先描述照片中人物状态，然后描述财神走入画面。4.根据用户上传图片确定人物数量，不要出现[一群人]、[们]等代词而是精准地指出人物数量。5.如果用户上传图片有多个人物，则他们自然的聚在一起看着元宝，人物神情中都充满惊喜与感激。画面捕捉到每个人物的微表情和互动细节。不要出现图片中没有的人物。 6.强调只有1个元宝。7.如果图片展示了背景图，则设定为背景并详细描述。8.财神和人物始终在画面中心。", "template": "wish_sender"}, {"id": 153, "name": "财神发红包", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/caishenhongbao_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/caishenhongbao_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9", "credit_cost": "6", "scene": "wish_sender"}, "input_instruction": {"image_count": "必传，仅支持上传三张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/festival/caishenfahongbao_upload.webp", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/caishenbenti.png", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/caishenbeijing.png"], "person_count": "仅支持单人照片，建议将财神图和背景图作为内置图", "attention": "人物为正面照，手中没其它道具的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现财神发完红包后消失的情况"}, "prompt": "视频内容\\\n 视频展示一个财神角色与人物的互动。财神站在人物旁边，不断抛洒红包，红包如雨点般洒下。人物伸出双手接住2个沉甸甸的红包，脸上露出惊喜的笑容。红色喜庆背景，充满了中国春节元素，红包的细节。\\\n# 要求 1.根据图片精准描述人物外观。2.根据图片精准描述财神外观和红包外观。3.人物和财神始终保持在画面中心。4.如果图片展示了背景图，则设定为背景并详细描述。", "template": "wish_sender"}, {"id": 154, "name": "财神发金币", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/caishenjinbi_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/caishenjinbi_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "16:9", "credit_cost": "6", "scene": "wish_sender"}, "input_instruction": {"image_count": "必传，仅支持上传三张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/festival/caishenfajinbi_upload.webp", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/caishenbenti.png", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/caishenbeijing.png"], "person_count": "仅支持单人照片，建议将财神图和背景图作为内置图", "attention": "人物为正面照，手中没其它道具的时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现金币散落较慢或者财神消失的情况"}, "prompt": "视频内容\\\n 视频展示一个财神角色与人物的互动。财神在人物旁边站立着举起双手向上洒金币，然后金币如雨点般从空中散落下来，主体人物伸出双手开心的抓住2个金币拿在手里，脸上露出惊喜的笑容。红色喜庆背景，充满了中国春节元素，金币细节。\\\n# 要求 1.根据图片精准描述人物外观。2.根据图片精准描述财神外观和金币外观。3.如果图片展示了背景图，则设定为背景并详细描述。4.人物和财神时候总保持在画面中心。5.motion_level:small.", "template": "wish_sender"}, {"id": 155, "name": "古风换装", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/gufenghuanzhaung_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/gufenghuanzhuang_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16", "credit_cost": "6", "scene": "dynasty_dress"}, "input_instruction": {"image_count": "必传，仅支持上传一张或两张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/festival/gufenghuanzhaung_upload.webp"], "person_count": "仅支持一张人物单图，或一张人物单图和一张服装照片", "attention": "当人物为正面、露出脖子和头部的上半身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现人物的细节和肤色和输入图不完全一致的情况"}, "prompt": "视频内容\\\n一个角色穿着服装和鞋子面对镜头摆pose，眨眨眼睛，甜美微笑\\\n# 要求 - 对角色的面部细节详细描述，对服装配饰的各种细节要尽量精准详细的描述.- 如果图片展示了[服装、手提包、配饰、帽子、鞋子]，需要出现在对人物的服装描述中.- 如果图片展示了背景图，则设定为背景并详细描述。- 只有1个角色！不要出现任何复数代词.- Shot Size 为：Wide Shot- 角色不要有任何手部动作、腰部动作,强调自然的微笑.", "template": "dynasty_dress"}, {"id": 156, "name": "童年回忆", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/tongnianhuiyi_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/tongnianhuiyi_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16", "credit_cost": "6", "scene": "youth_rewind"}, "input_instruction": {"image_count": "", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/tongnianhuiyi_upload.png"], "person_count": "支持单人照片和宠物照片", "attention": "若输入为人物，当主体露出正面且手中无明显道具时，效果更好；若输入为宠物，当宠物面朝镜头，正面清晰可见时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入人物存在白发或胡须时，可能会出现面部特征（如白发、胡须）未随整体风格变化而保留原状的情况；当输入为宠物时，可能会出现无法实现拟人化站立并拿烟花或宠物被转绘为人形角色的情况"}, "prompt": "Video content\\ n The subject(s) is/are facing the camera, appearing as their 3-year-old self, wearing a red cotton-padded jacket, holding burning sparklers in both hands, and joyfully dancing in the snowy courtyard. The sparklers flicker brightly, casting a warm glow on their happy, smiling faces. The subject(s) performs lively, natural movements, such as waving the sparklers while skipping around, always remaining in the center of the frame. The scene is set in a snowy village courtyard during a festive winter evening, as shown in the provided background. Snow covers the ground and rooftops, while red lanterns hang overhead, glowing warmly in the golden sunlight of dusk. The wooden doors of nearby houses are adorned with festive decorations. The overall ambiance is filled with a nostalgic and joyful Chinese New Year atmosphere. The warm orange tones from the sunlight and the red lanterns harmonize with the vibrant movements of the subject(s), enhancing the festive mood. The steady camera ensures the subject(s) stays at the center of the frame throughout the joyous performance. \\ n #Requirements\\ 1. The subject(s) in the picture may be pets or one or more people. Generate according to the actual situation in the reference image. 2. The subject(s) should smile and immerse themselves in the festive atmosphere of Chinese New Year. 3. The subject(s) must always remain at the center of the frame, even while joyfully dancing. 4.The subject(s), including any pets, must wear red New Year cotton-padded jackets.", "template": "youth_rewind"}, {"id": 157, "name": "AI 情侣拥抱", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/coupleyongbao_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/coupleyongbao_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "couple_arrival"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/festival/AIqinglvyongbao_upload.webp"], "person_count": "仅支持单人照片", "attention": "两个人物为半身，正面时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动。比如更改 {user_input} 描述自定义 AI 伴侣形象", "effect_boundary": "可能会同时出现两个伴侣，或AI 伴侣没有正脸等形象残缺的情况"}, "prompt": "视频内容\\\n 主体站在画面中央，正面朝向镜头。主体的伴侣人从画面右侧以放松的步伐逐渐走入画面，接近站在中心的人，然后相互拥抱，动作充满温暖和爱意。拥抱完成后，两人转向镜头，正脸朝向镜头，展现出轻松与友好的氛围。主体的伴侣形象为：{user_input}\\\n# 要求 1.如果没有特别说明，针对主体伴侣，主体伴侣跟主体的种族、年龄、肤色应该是匹配的。主体伴侣的性别一般是跟主体是相反的。2.主体伴侣的数量跟主体的数量应该是一致的，如果图片中是一个主体，那么主体伴侣应该也是一个。3.拥抱完成后，主体伴侣跟主体一样正脸朝向镜头。", "template": "couple_arrival"}, {"id": 158, "name": "梦幻婚礼", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/menghuanhunli_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/menghuanhunli_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16", "credit_cost": "6", "scene": "dreamy_wedding"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/menghuanhunli_upload.png"], "person_count": "仅支持双人照片", "attention": "人物为正面、脖子以上大头照，头发无配饰时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当人物服装暴露过多时，可能会出现婚纱不生效的情况"}, "prompt": "视频内容\\\n 画面中的两个角色穿着婚纱和礼服，在草地上拍照。\\\n# 要求 1.根据图片严谨判断主体数量与性别，精准描述主体面部特点。", "template": "dreamy_wedding"}, {"id": 159, "name": "浪漫公主抱", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/lanmangongzhubao_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/lanmangongzhubao_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "romantic_lift"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/lanmangongzhubao_upload.png"], "person_count": "仅支持双人照片", "attention": "两个人物为全身，且能露出脚和大腿时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当图片中两个人物距离太近，可能会出现人物肢体错乱的情况"}, "prompt": "视频内容\\\n 画面中的有两个角色，其中一个角色使用公主抱[Princess carry]的动作抱起旁边的角色。\\\n# 要求 1. 根据图片精准描述角色外观，性别。2. 如果两个角色都是男生或者都是女生，则描述右边的角色抱起左边的角色。3.如果两个角色是一男一女，则描述男性角色抱起女性角色。", "template": "romantic_lift"}, {"id": 160, "name": "甜蜜求婚", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/tianmiqiuhun_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/tianmiqiuhun_upload.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "sweet_proposal"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/tianmiqiuhun_upload.png"], "person_count": "仅支持双人照片", "attention": "两个人物为全身，且能露出脚和大腿时候，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当图片中两个人物距离太近，可能会出现人物表现惊喜，但没人掏戒指下跪求婚的情况"}, "prompt": "视频内容\\\n 画面中的有两个角色，其中一个角色1[精准描述外观和性别]突然掏出红色的戒指盒，表情真挚的单膝下跪向旁边的角色2[精准描述外观和性别]求婚，角色2表现得很开心[描述惊喜的表情动作] 。\\\n# 要求 - 严格根据图片判断人物数量，性别。- 如果图片中有男性角色，则指定该男性角色为角色1。如果图片中只有女性角色，则随机指定一个女性为角色1。", "template": "sweet_proposal"}, {"id": 161, "name": "AI情侣送花", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/AIqinglvsonghua_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/AIqinglvsonghua_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "couple_arrival"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/AIqinglvsonghua_upload.png"], "person_count": "仅支持单人照片", "attention": "两个人物为半身，正面时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动。比如更改 {user_input} 描述自定义 AI 伴侣形象", "effect_boundary": "可能会同时出现两个伴侣，或AI 伴侣没有正脸等形象残缺的情况"}, "prompt": "视频内容\\\n 镜头中人物看向镜头，接着该人物的{伴侣}满面笑容地从画面外走入画面，递给了人物一束花，人物感到惊喜，动作自然且充满温暖，画面捕捉到人物的的微表情和互动细节。伴侣形象为：{user_input} \\\n# 要求1.根据人物的位置和状态合理的设计动作，而不是突兀的直接出现。2.如果镜头中的人物是女性，那么{伴侣}就是一个帅气的与之年龄相仿的男性；如果镜头中的人物是男性，那么{伴侣}就是一个美丽的与之年龄相仿的女性，如果用户输入了{伴侣}形象，则按照用户输入的为准。3.Take a step-by-step approach in your response. 4.最后画面中的人数为2人。", "template": "couple_arrival"}, {"id": 162, "name": "AI 情侣接吻", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/couplekiss_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/couplekiss.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "couple_arrival"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/festival/AIqinglvjiewen_upload.webp"], "person_count": "仅支持单人照片", "attention": "两个人物为半身，正面时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动。比如更改 {user_input} 描述自定义 AI 伴侣形象", "effect_boundary": "可能会同时出现两个伴侣，或AI 伴侣没有正脸等形象残缺的情况"}, "prompt": "视频内容\\\n 主体的视线、动作从原来的状态自然地转变为迎接主体的伴侣，主体的伴侣从画面左侧或者右侧以放松的步伐逐渐走入画面，接近站在中心的人。两人自然地接吻，动作充满温暖和爱意。接吻完成后，两人一同转向镜头，正脸朝向镜头，展现出轻松与友好的氛围。主体的伴侣形象为：{user_input}\\\n# 要求 1.如果没有特别说明，针对主体伴侣，主体伴侣与主体的种族、年龄、肤色应该相似。2.主体伴侣的性别通常与主体相反。主体伴侣的数量与主体数量一致，若图片中是一个主体，则主体伴侣应为一个。3.接吻完成后，主体伴侣与主体一样正脸朝向镜头。4.如果参考图片的镜头类型为近景，镜头在伴侣走近时可以稍微拉远，调整视角以容纳两人完整的画面。", "template": "couple_arrival"}, {"id": 163, "name": "AI 情侣挥手", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/coulpehuishou_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/0325_cn/0325_cn/couplehuishou_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "couple_arrival"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/festival/AIqinglvhuishou_upload.webp"], "person_count": "仅支持单人照片", "attention": "两个人物为半身，正面时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动。比如更改 {user_input} 描述自定义 AI 伴侣形象", "effect_boundary": "可能会同时出现两个伴侣，或AI 伴侣没有正脸等形象残缺的情况"}, "prompt": "视频内容\\\n 主体的视线和动作自然过渡，迎接主体的伴侣。主体的伴侣从画面一侧以放松的步伐逐渐进入画面，靠近主体。两人自然地对视，流露出亲密的情感，随后一同转向镜头，面带温暖的微笑。两人用靠近彼此的手同时向镜头挥手，动作协调流畅，传递出轻松友好的氛围。主体的伴侣形象为：{user_input}\\\n# 要求：1. 如果没有特别说明，主体伴侣应与主体在种族、年龄、肤色上匹配。2. 主体伴侣的性别通常与主体相反。3. 主体伴侣的数量应与主体一致，例如，若画面中有一位主体，则主体伴侣也应为一位。4. 主体伴侣与主体保持一致的动作和姿态，正面面向镜头并打招呼。", "template": "couple_arrival"}, {"id": 164, "name": "丘比特之箭", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/qiubite_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/qiubite_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "6", "scene": "cupid_arrow"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/qiubite_upload.png"], "person_count": "仅支持双人照片", "attention": "两个人物为漏出超过半身，手中无其它道具时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现被箭射只中后没有脸红心动、眼神互动的情况"}, "prompt": "视频内容\\\n 一支粉色丘比特之箭极速从镜头左边飞进来射入左边角色的胸口，角色微微惊讶，捂住胸口，然后心动的红晕浮现，氛围暧昧。\\\n# 要求 1.先描写人物被射中，然后描写转变惊讶心动。2.如果图片中有两个角色，被箭头射中的角色应该深情的看向对方。\\\n# 环境 -浪漫背景（如云朵、玫瑰花瓣、星光）。-空气中漂浮着粉色或金色的光点。-柔和的光晕环绕。-爱心符号或梦幻气泡渐渐浮现。", "template": "cupid_arrow"}, {"id": 165, "name": "童年合影", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/childmemory_0520/childmemory_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/childmemory_0520/childmemory_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "child_memory"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/childmemory_0520/childmemory_upload.webp"], "person_count": "仅支持写实风单人照片", "attention": "当画面主体为正面半身，且左右留有一定空白区域时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入为双人或多人图像时，生成的儿童角色可能在性别或数量上与预期不符，且失败率较高；当输入为全身照时，可能会出现无法生成儿童角色的问题；若图片左右留白不足，则可能导致生成失败"}, "prompt": "Please classify the images based on the number of people appearing in each, and generate a corresponding English description accordingly. There are three categories: \\\n1. **Single-person images**: \\\nUse the following fixed sentence: \\\n*“A child with facial features similar to the subject slowly walks in from one side of the frame and gently embraces the subject. The two share an emotional connection, and their movements are natural.”* \\\n2. **Two-person images**: \\\n* First, clearly describe and distinguish the two individuals based on their obvious features (such as gender, clothing, position, or posture). For example: “the man in a black hoodie on the left” and “the woman in white on the right.” \\\n* Then, describe how two children with similar facial features to each adult appear from the edges of the frame and approach them, each engaging in a one-to-one embrace. \\\n* The final frame contains four people. \\\n3. **Three-person images**: \\\n * Clearly identify and describe the three individuals based on distinct features (such as “the woman in a yellow jacket on the left,” “the man sitting in the center,” and “the girl standing on the right”). * Then, describe how three children, each with facial features resembling one of the adults, slowly walk in from the edges of the frame and embrace their respective counterparts. \\\n* The final frame contains six people, forming **three pairs of adult-child embraces**. \\\n**Notes**: \\\n* Each person must be individually and clearly described. Avoid vague references.", "template": "child_memory"}, {"id": 166, "name": "腾云驾雾", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/jumptocloud_0516/jumptocloud_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/jumptocloud_0516/jumptocloud_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "jump_to_cloud"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/jumptocloud_0516/jumptocloud_upload.png"], "person_count": "支持单人或单宠物", "attention": "当只有一个主体时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "小概率存在飞行距离较短的情况"}, "prompt": "The subject leaps gracefully into the air and lands lightly on a soft, white cloud. The cloud, as if powered by some mysterious force, gently lifts her(him) up and carries her(him) into the sky. As it glides through the air, (his)her figure gradually moves away from the camera.\\\nRequirements:\\\nMotion Level:Large", "template": "jump_to_cloud"}, {"id": 167, "name": "空降情侣", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/coupledrop_0516/coupledrop_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/coupledrop_0516/coupledrop_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "couple_drop"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/coupledrop_0516/coupledrop_upload.png"], "person_count": "支持单人或单宠物", "attention": "当画面主体露出上半身（至少至膝盖）或全身时，效果更好；其中全身照片效果最佳", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入为半身以上的近景图像时，生成的 CP 角色可能过小；当输入为儿童照片时，可能会生成同性 CP"}, "prompt": "At first, the characters in the image are naturally facing the camera. Then, a new character of the opposite gender gently and gracefully descends from the sky to the ground. The original character shows a surprised reaction—such as wide eyes or a change in expression. The two gradually approach and share a kiss, conveying a sincere and emotionally fluid interaction.\n### Requirements:\n1. Briefly describe all the characters in the image, including their appearance and gender. Then introduce a new character of the opposite gender who descends from the sky. This new character should look attractive (handsome or beautiful). If there are multiple characters in the image, randomly select one as the main character, and have a character of the opposite gender descend nearby to interact and kiss.\n2. If the main subject in the image is an animal, a second animal of the same species should appear, gently flying down or jumping down and landing nearby. These two animals should engage in intimate interactions through natural behaviors such as cuddling, gentle touching, or sniffing. Anthropomorphic behaviors (such as walking upright or wearing clothes) are strictly prohibited. All actions must conform to the natural behaviors of that species.\n3. The entire video should maintain visual stability and coherence, with biologically appropriate and realistic movements. Avoid any unnatural or abrupt actions.\n4. The newly landed character cannot be of the same gender as the character in the image.", "template": "couple_drop"}, {"id": 168, "name": "萌宠恋人", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/mengchonglianren_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/mengchonglianren_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16", "credit_cost": "6", "scene": "pet_lovers"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/mengchonglianren_upload.png"], "person_count": "仅支持双宠物照片", "attention": "两个宠物形象特征暴露较明显，无相互遮盖时，效果最佳", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现衣服穿不上的情况"}, "prompt": "视频内容\\\n 两只可爱的宠物以后肢站立，呈现类似人类腿部的形态，前肢则类似人类手臂的结构，身穿配套的情侣装。它们面对镜头，彼此互动紧密，例如前肢轻轻拥抱或靠在一起，整体呈现出拟人化的亲密氛围。", "template": "pet_lovers"}, {"id": 169, "name": "包你成粽", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/zongziwrap_0521/zongziwrap_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/zongziwrap_0521/zongziwrap_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "zongzi_wrap"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/zongziwrap_0521/zongziwrap_upload.png"], "person_count": "支持人物、动物、物体或建筑照片", "attention": "当画面主体为单人，且露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现粽子未按照原物体形状进行包裹或在包裹过程中，物体部分区域发生改变的情况"}, "prompt": "The subject in the image remains completely still. Along the edges of its silhouette, vibrant green textures begin to emerge, and several large zongzi leaves naturally grow and extend from key areas. These broad, resilient leaves unfold closely along the contours of the subject’s body. As each leaf completes its growth, it immediately curves inward, fitting precisely against the subject’s surface—gradually wrapping the limbs, torso, and head. When the final leaf seals the top, a complete zongzi structure is formed.\\\nRequirements:\\\n1. Emphasize a smooth and elegant transformation process.\\\n2. Fixed camera angle", "template": "zongzi_wrap"}, {"id": 170, "name": "爱从天降", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/lovedrop_0520/lovedrop_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/lovedrop_0520/lovedrop_cover.png", "isHot": true, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "love_drop"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/lovedrop_0520/lovedrop_upload.png"], "person_count": "支持单人照片，或单人与单宠物合照", "attention": "当画面主体露出正面上半身或全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入图为双人或双宠物时，无法实现由画面外其他角色主动亲喂其中一方，容易误生成互相亲吻的场景；在部分双人图中，可能会出现第三人从画面外走入，仅作为旁观者，并不会与主体角色产生亲密互动；有较低概率生成同性 CP 亲吻"}, "prompt": "Generate an appropriate video description based on the content of the image:\n- If the image shows a single person:\nCreate a romantic video scene in which an opposite-sex character descends from the sky and appears beside this person. The two gaze deeply into each other’s eyes, share a sweet smile, then embrace tightly and kiss passionately.\n- If the image shows multiple people and no pets:\nFirst, describe how many people are in the image and briefly summarize their general appearance (e.g., gender, age group, clothing, posture).\nThen, choose one person as the main character.\nCreate a romantic interaction where a new opposite-sex character enters the frame and shares an affectionate moment with the chosen individual.\nMake sure the new character is visually distinct from the original people to avoid blending or confusion.\n- If the image contains any pet(s):\nFirst, briefly describe the people (if any) and pets in the image, including general appearance, posture, and clothing.\nThen, create a warm and heartwarming scene in which a new person enters the frame and **gently kisses the pet**.\nIf there are people in the image, the new character’s gender must be **opposite to one of the existing people**, and the new character must **interact only with the pet**, without engaging with any person shown in the image.\nEnsure the new character is visually distinct and clearly separable from those in the original image.\nRequirements:\n1. Describe the entire video scene in fluent, cinematic English, including how characters appear, how they interact, and the emotional tone.\n2. The camera should remain fixed. Do not mention any camera cuts or facial blurring.\n3. Do not use any pronouns. Use only neutral terms such as 'boy,' 'girl,' 'man,' 'woman,' or 'pet.'\n4. The newly appearing character should be of a similar age and the same ethnicity as one of the original characters, but must be of the opposite gender. Same-gender interactions are strictly prohibited to maintain the intended dynamic.\n- Male characters should appear tall, fit, and handsome.\n- Female characters should appear beautiful, stylish, and have an attractive figure.\n5. The scene must include the entry of a new character into the frame and depict their interaction with the person or pet shown in the original image.\n6. The interaction between the new character and the person or pet must be described in **clear and progressive detail**, including how the new character moves, approaches, and initiates the affectionate action (such as a kiss).\n7. Keep the description of the original image brief and focused, to ensure the full interaction can be portrayed within a short video duration.\n8. The final description must have a clear and coherent visual narrative structure. It should specify the sequence of character appearances, progression of actions, and emotional transitions. The overall flow should align with the logic of cinematic shot development, ensuring it can be effectively understood and rendered by a video generation model ", "template": "love_drop"}, {"id": 171, "name": "龙舟拍立得", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/dragonboatshot_0527/dragonboatshot_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/dragonboatshot_0527/dragonboatshot_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "该视频输出比例随机", "credit_cost": "10", "scene": "dragonboat_shot"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/dragonboatshot_0527/dragonboatshot_upload.jpg"], "person_count": "支持单人照片、宠物照片、人宠合照、双人合照或三人合照", "attention": "当画面中只有一个主体且露出正面上半身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "龙舟可能会随机出现在拍立得相框的上方或下方；可能会出现拍立得相片被拿走，未能持续保留在画面中的情况；当输入图包含多个人物或宠物时，可能会出现异常的生成效果，如主体缺失、交互混乱等"}, "prompt": "The characters open their mouths and laugh happily, eyes blinking softly and naturally. \nThe hand holding the instant photo remains rigidly fixed in a stable position, ensuring the photo stays centered  within the frame at all times.\nThe unauthorized appearance of other characters is strictly prohibited.", "template": "dragonboat_shot"}, {"id": 172, "name": "天降巨粽", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/zongzidrop_0521/zongzidrop_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/zongzidrop_0521/zongzidrop_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "zongzi_drop"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Festival/Festival/zongzidrop_0521/zongzidrop_upload.png"], "person_count": "支持单人照片、宠物照片、双人合照或多人合照", "attention": "当画面主体露出全身时，效果更好；建议上传竖版照片，并确保画面上方留有足够空间，以便粽子从空中自然掉落", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现粽子未从空中掉落的情况；当输入为多主体时，可能出现部分人物未同时背起粽子，或粽子遮挡主体导致人物缺失的问题"}, "prompt": "A giant green Dragon Boat Festival zongzi falls from the sky onto the subject. The subject bends over as if to carry the giant zongzi on their back, with a relaxed and smiling expression.\\\n1. The subject can be a single person, multiple people, or a pet. If multiple subjects are involved, the output must specify the number of subjects.\\\n2. If there are multiple subjects, all of them should have relaxed and smiling ", "template": "zongzi_drop"}, {"id": 173, "name": "解压切切", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/ji<PERSON><PERSON>ie<PERSON>e_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/jieyaqie<PERSON>e_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "slice_therapy"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/jieyaqie<PERSON>e_upload.jpeg"], "person_count": "支持单主体的小物品", "attention": "主体背景干净，轮廓清晰圆润时，效果最佳", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现刀凭空出现、切的位置和划开位置不一致、没被切开的情况"}, "prompt": "Video content\\\n At the beginning of the video, one hand holds the subject in the image, while another hand reaches out from off-screen, using a tool to cut the surface of the subject. As the process continues, the interior or layers of the subject gradually become visible, showcasing rich colors, patterns, and textures. Throughout the process, the hand remains steady, carefully operating the tool, gradually revealing the complex design inside.", "template": "slice_therapy"}, {"id": 174, "name": "膨胀", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/pengzhang_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/pengzhang_cover.jpeg", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "morphlab"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/pengzhang_upload.png"], "person_count": "支持单人、双人和多人", "attention": "人物为正面、全身/上半身时，效果更好。", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动。", "effect_boundary": "可能会出现主体丢失、不完全膨胀的情况。"}, "prompt": "视频内容\\\n画面中主体开始膨胀变形，变得越来越大，越来越圆，就像一个气球，慢慢飘了起来\\\n# 要求\\\n1.根据用户上传图片确定主体数量,每个主体都要膨胀\\\n2.Motion Level 设定为:Middle\\\n3.以我的视频内容为第一要素，背景的描述统一、合理，不要描述两次.", "template": "morphlab"}, {"id": 175, "name": "捏捏", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/nienie_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/nienie_cover.jpeg", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "morphlab"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/nienie_upload.png"], "person_count": "支持单人、双人和多人", "attention": "人物为正面、全身/上半身时，效果更好。", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动。", "effect_boundary": "在手指捏之前，可能会出现主体微动的情况。"}, "prompt": "视频内容\\\n画面开始，主体静止不动。随后，一双大手出现，将主体像橡皮泥般抓住并挤压。\\\n随着主体逐渐缩小，被夹在手指间柔软变形\\\n# 要求\\\n1.根据用户上传图片确定主体数量,每个主体都要被捏扁\\\n2.Motion Level 设定为:Middle\\\n3.以我的视频内容为第一要素，背景的描述统一、合理，不要描述两次.", "template": "morphlab"}, {"id": 176, "name": "爆炸", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/baozha_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/baozha_cover.jpeg", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "morphlab"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/baozha_upload.png"], "person_count": "支持单人、双人和多人", "attention": "人物为正面、全身/上半身时，效果更好。", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动。", "effect_boundary": "可能会出现爆炸碎片的颜色和主体颜色不一致的情况。"}, "prompt": "视频内容\\\n画面开始主体突然爆炸，细碎的颗粒爆炸开来\\\n# 要求\\\n1.根据用户上传图片确定主体数量,每个主体都要爆炸\\\n2.Motion Level 设定为:Middle\\\n3.以我的视频内容为第一要素，背景的描述统一、合理，不要描述两次.", "template": "morphlab"}, {"id": 177, "name": "融化", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/ronghua_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/ronghua_cover.jpeg", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "morphlab"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/ronghua_upload.png"], "person_count": "支持单人、双人和多人", "attention": "人物为正面、全身/上半身时，效果更好。", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动。", "effect_boundary": "可能会出现融化的颜色和主体颜色不一致的情况。"}, "prompt": "视频内容\\\n画面中主体慢慢地开始融化，最终形成一个表面光滑的水坑\\\n# 要求\\\n1.根据用户上传图片确定主体数量,每个主体都要融化\\\n2.Motion Level 设定为:Middle\\\n3.以我的视频内容为第一要素，背景的描述统一、合理，不要描述两次.", "template": "morphlab"}, {"id": 178, "name": "AI换发", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/huanfa_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/huanfa_cover.jpeg", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "hair_swap"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/huanfa_upload.jpeg"], "person_count": "支持单人/动物、双人/动物", "attention": "人物为单人正面、上半身时，效果最佳。", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词的“{颜色英文名称}”和“{发型英文名称}”内容进行改动。支持的发型及对应触发提示词枚举如下：半扎公主发（Half-up Princess Hairstyle）、长发（长发）、卷发（Curly Hair）、空气感（Airy Style）、双马尾（Twin Tails）、顺直（Sleek Straight Hair）、微卷短发（Slightly Curled Short Hair）、羊毛卷（Curly Wool Hair）、慵懒长卷（Relaxed Long Curls）、鱼尾烫（Fishtail Perm）。支持的颜色及对应触发提示词枚举如下：原发色（Original Hair Color）、树莓紫（Raspberry Purple）、奶茶金（Milk Tea Gold）、脏橘色（Dirty Orange）、甜樱粉（Sweet Cherry Pink）、亚麻棕（Linen Brown）、初音色（Hatsune Color）。", "effect_boundary": "当原发型和变换后发型相差较大，可能会出现换发错误的情况。"}, "prompt": "视频内容\\\n角色正对镜头不动，眨眨眼睛，头发缓慢转变为“{Dirty Orange}”的“{Curly Wool Hair}”发型\\\n# 要求 角色静静地面对镜头，目光平和，眨眨眼睛，头发的变换成为画面的焦点，呈现出一场优雅的发型演化表演。", "template": "hair_swap"}, {"id": 179, "name": "变成气球飞走了", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/bianqiqiufeizou_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/bianqiqiufeizou_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "balloon_flyaway"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0317/bianqiqiufeizou_upload.png"], "person_count": "支持写实风人物和动物的单主体、双主体和多主体", "attention": "当照片中的人/动物为单主体，露出为全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当照片中的主体数量大于等于3，或者主体距离较近时，在变成气球飞走时可能会出现缺少主体的情况"}, "prompt": "Video content\\\n The subject in the image turns into a balloon and flies away while spinning.\\\n# Requirements\\\n1.Determine the style solely based on the image.\\\n2.Determine the number of subjects based on the image, and each subject should turn into a balloon and fly away.", "template": "balloon_flyaway"}, {"id": 180, "name": "飞行", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0317/flying_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/flying_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "flying"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0317/flying_upload.png"], "person_count": "支持写实风的单人照片", "attention": "当人物为单人全身照片，背景为风景图时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "有可能出现人物不向前飞、头部旋转180°时，主体崩坏的情况"}, "prompt": "Video content\\\n The character in the image floats and flies like a superhero.\\\n# Requirements\\\n1.Camera Movement：track-up shot.\\\n2.Motion Level：Large.", "template": "flying"}, {"id": 181, "name": "纸片人特效", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0317/zhipianren_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/zhipianren_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "paperman"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0317/zhipianren_upload.webp"], "person_count": "支持写实风的单人照片", "attention": "当人物露出主体超过半身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现手拿到半截停止了没有拿出画面外，或者手进入又离开但是没有带走任何东西的情况"}, "prompt": "Video content\\\n The character is actually a motionless paper puppet. A large hand appears from the left and removes the subject from the scene to the left, while the shot remains steady.\\\n# Requirements\\\n Motion Level: Large.", "template": "paperman"}, {"id": 182, "name": "2.0 捏捏", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0317/nienie_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/nienie_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "pinch"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0317/nienie_upload.jpeg"], "person_count": "支持人物的单/双/多主体，动物和物体的单主体", "attention": "当人物露出为半身/全身时，物体和动物为单主体时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当物体不是单件独立时，可能会存在只捏部分的情况；当输入图上下两部分清晰度不一致或者是前后存在错位感不可能会存在不能一起捏捏的情况；也有可能出现身体下半部分快速回缩的情况"}, "prompt": "Video content\\\n The scene begins, with the subject remaining still. Then, a pair of large hands appear, grabbing the subject and squeezing it flat like clay.As the subject gradually shrinks, it becomes soft and deformed, pinched between the fingers.\\\n# Requirements\\\n1.Determine the number of subjects based on the user's uploaded image, and each subject must be pinched flat.\\\n2. Motion Level set to: Middle\\\n3. Take my video content as the primary element, and ensure that the background description is consistent and reasonable, without repeating the description.", "template": "pinch"}, {"id": 183, "name": "性别转换", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0331/guonei/xingzhuan_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0331/guonei/xingzhuan_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "gender_swap"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0331/guonei/xingzhuan_upload.png"], "person_count": "仅支持男生或女生的单人照", "attention": "当人物为半身照或者全身照时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "对于主体是女生的输入图，可能会出现性别转换为男生后，裸露胸肌没穿上衣的情况"}, "prompt": "Video content\\\n The character raises both hands above their head, fingertips touching, and then undergoes a seamless transformation of gender. Their hairstyle and clothing change accordingly, while their facial features remain unchanged.\\\nRequirements:\\\n1. If the image is of a male, transform it into a typical female appearance. Conversely, do the same if it is a female.\\\n2. When describing the transformation, depict the change in gender, such as turning a male into a female, and vice versa.\\\n3.Motion Level:Large", "template": "gender_swap"}, {"id": 184, "name": "随地大小睡", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0331/guonei/da<PERSON><PERSON><PERSON>_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0331/guonei/da<PERSON>osh<PERSON>_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "nap_me"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0331/guonei/da<PERSON>osh<PERSON>_upload.png"], "person_count": "仅支持男生或女生的单人照，和动物的单主体照", "attention": "当人物为半身照或者全身照时，动物为全身照时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当上传图片为纯色背景时，可能会出现人物竖直躺下，而不是侧身躺下的情况；在盖被子过程中，也有可能出现人物从身体两边抓被子的情况"}, "prompt": "Video content\\\n The camera slowly opens, and the character leisurely falls to the right, landing perfectly on a bed that appears out of nowhere. The character lies on the soft pillow, in a natural and relaxed posture, with a serene expression on his face. He gently pulls up a soft cotton blanket and lightly covers himself. The camera slowly zooms in, the character's breathing is steady and calm, his eyes gently closed, completely immersed in a sense of tranquility and relaxation, creating a warm and cozy sleeping atmosphere.", "template": "nap_me"}, {"id": 185, "name": "变身比基尼/肌肉男", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0331/guonei/bijini_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0331/guonei/bijini_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "sexy_me"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/0331/guonei/bijini_upload.webp"], "person_count": "仅支持写实风格和二次元风格的女生单人照/男生单人照", "attention": "当人物为半身照或者全身照时，效果更好。当输入图主体是女生时，会生成变身比基尼穿着展示身材的视频，当输入图主体是男生时，会生成脱衣露出肌肉展示身材的视频", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当单主体性别特征不明显时，可能出现性别判断错误，变身比基尼和肌肉男混乱的情况；也有可能出现变身比基尼时，衣服没有完全脱掉的情况；当照片双人照时，可能会出现其中一个主体缺失的情况"}, "prompt": "Video content\\\n Subjects of different genders underwent different transformations.\\\nIf the image depicts a woman: 'The woman's outfit undergoes a seamless transformation, as her original clothing smoothly shifts into a sleek bikini. In the final moment, she confidently places her hands on her waist, exuding elegance and poise.'\\\nIf the image depicts a man: 'The man swiftly takes off his shirt, revealing his well-built muscles, which are the same color as his skin. Then, he steps forward.'\\\nRequirements:\\\n1. If the image is a close-up or medium shot, set Camera Movement to: 'Zoom out' \\\n2. Motion Level: Large", "template": "sexy_me"}, {"id": 186, "name": "瘫软在地", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/tanruanzaidi_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/tanruanzaidi_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "paper_fall"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/tanruanzaidi_upload.png"], "person_count": "支持写实风人物和单主体、双主体和多主体和动物", "attention": "照片中的人物或动物为全身或半身露出时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能出现纸片未完全舒展落地或最终直接消失，未能展现瘫倒在地状态的情况"}, "prompt": "Video content\\\n This video depicts the dynamic process of extracting the subject from a photo. The subject in the color photo is completely cut out and rolls off to the bottom of the frame, leaving behind a black-and-white layer that retains only the silhouette of the person", "template": "paper_fall"}, {"id": 187, "name": "抓娃娃", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/zhuawawa_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/zhauwawa-cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16", "credit_cost": "6", "scene": "claw_me"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/zhauwawa_upload.png"], "person_count": "支持写实风人物和动物的单主体、双主体和多主体", "attention": "当照片中人物露出全身或半身，动物露出全身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能出现钩子崩坏、抓取过程中画面崩坏、未成功抓起目标，或钩子动作不符合物理规律的情况"}, "prompt": "Video content\\\n The claw descends towards the doll, and the doll remains completely still throughout the process, undisturbed by anything. When the claw makes contact with the doll, it grabs it firmly. Subsequently, the claw lifts the doll entirely off the platform, suspending it in mid - air, and successfully capturing it. The doll doesn't move at all before the contact, and after being grabbed, it remains inside the display window of the claw machine.\\\nProhibited actions:\\\nThe doll is prohibited from moving towards the claw on its own.\\\nThe metal claw is prohibited from appearing outside the claw maachine\\\nThe doll is prohibited from appearing outside the claw machine", "template": "claw_me"}, {"id": 188, "name": "机长上线", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/pilot_0521/pilot_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/pilot_0521/pilot_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "pilot"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/pilot_0521/pilot_upload.png"], "person_count": "支持单人照片、宠物照片、双人合照或多人合照", "attention": "当画面主体露出腰部以上正面半身时，效果更好；若输入为双人或多人，建议并排站立，避免上下堆叠（使用全身照片时，生成效果较差）", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能出现主体部分位于飞机外部的情况；若输入为双人或多人，且人物上下堆叠排列，则可能识别异常，出现人物缺失或数量异常的情况"}, "prompt": "When the image features a pet (or pets):\\\nA helicopter suddenly appears from behind the pet. The pet is seated in the cockpit, acting as the pilot. As the rotor blades spin up with a roar, the helicopter gradually lifts off and flies steadily away from the camera, shrinking into the distance.\\\nWhen the image features a person (or multiple people):\\\nA helicopter suddenly appears from behind the person (or people). The person/people is/are seen seated in the cockpit, piloting the helicopter. As the rotor blades roar to life, the helicopter slowly ascends and flies away from the camera, gradually getting smaller in the distance.\\\nRequirements:\\\n1.Camera Movement: Zoom out.\\\n2.Motion Level:Large", "template": "pilot"}, {"id": 189, "name": "好梦相伴", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/sweetdreams_0516/sweetdreams_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/sweetdreams_0516/sweetdreams_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "sweet_dreams"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/sweetdreams_0516/seetdreams_upload.png"], "person_count": "仅支持双人合照", "attention": "当画面为双人合照且为近景大头照时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入为人物与宠物组合时，可能出现无被子、无枕头或被子覆盖不全的问题；当画面中人数不足或多于两人时，可能导致被子无法完整覆盖或主体缺失；可能出现入睡时人物半闭眼的情况"}, "prompt": "The camera slowly opens and the characters fall leisurely backwards, landing perfectly on a bed that seems to come out of nowhere. The characters lay on the same soft pillow in a naturally relaxed position with a calm expression on their faces. They gently pulled up a soft cotton blanket and gently covered them. The camera slowly pulls in, the characters' breathing is steady and calm, their eyes are gently closed, and they are completely immersed in a sense of tranquility and relaxation, creating a warm and comfortable sleeping atmosphere. \n# Requirements \n1. Accurately determine the number of people according to the image; If there are two people, they should pull up the same quilt, put on the same pillow, and sleep face to face, intimate and warm.", "template": "sweet_dreams"}, {"id": 190, "name": "灵魂出窍", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/souldepart_0530/souldepart_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/souldepart_0530/souldepart_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "soul_depart"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/souldepart_0530/souldepart_upload.png"], "person_count": "支持单人照片、双人合照、多人合照、宠物照片或物体照片", "attention": "当主体上方预留有足够空间时，生成效果更佳", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现灵魂漂浮时间较短，甚至未能脱离主体的情况"}, "prompt": "In the [scene/background], the image features [number and types of subjects] — [subject 1 description], [subject 2 description], etc. They are [action status, e.g., standing, sitting, looking up], and their expressions convey a sense of [emotion keyword].\\\nAt this very moment, a supernatural phenomenon is unfolding: from each subject’s body, a translucent soul form is rising, glowing with [light effect/color], mirroring the posture of the physical body but floating gently in the air. The soul figures appear semi-transparent like pure energy, with soft contours and edges shimmering with [glow, mist, stardust].\\\nRequirements:\\\n1.Replace each [ ] with information based on the specific content of the image.\\\n2.Motion Level:Large", "template": "soul_depart"}, {"id": 191, "name": "吃我一拳", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/punchhit_0530/punchhit_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/punchhit_0530/punchhit_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "punch_hit"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/punchhit_0530/punchhit_upload.png"], "person_count": "支持单人照片、双人合照、多人合照和宠物照片", "attention": "当主体为人物且露出正面上半身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入图为人物全身照时，可能出现拳头未击中面部，仅在人物面前挥过的情况；此外，低概率出现额外人物进入画面，或被击打后的倾倒方向不符合预期的情况"}, "prompt": "A clenched fist suddenly swings in from off-screen on the left and slams into [person/pet]’s face, distorting their features — lips pushed aside and skin visibly rippling from the impact.\\\nRequirements: \\\n1. Replace [person/pet] with the actual subject shown in the image. \\\n2. If there are two or more people or pets in the image, all of them must be struck by the fist, and the reaction of each one must be described individually.", "template": "punch_hit"}, {"id": 192, "name": "吃我一瓜", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/watermelonhit_0530/watermelonhit_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/watermelonhit_0530/watermelonhit_cover.png", "isHot": false, "isNew": false, "category": "Entertainment", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "watermelon_hit"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Funny/Funny/watermelonhit_0530/watermelonhit_upload.jpg"], "person_count": "支持单人照片、双人合照、多人合照和宠物照片", "attention": "当主体为人物且露出正面上半身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入图为人物全身照时，小概率会出现西瓜击中身体而非面部的情况；此外，也可能出现西瓜击中后未碎裂但果汁飞溅的情况"}, "prompt": "A whole watermelon hurtles in at high speed from the right side of the frame and slams into ${target}'s face, exploding on impact. Bright red flesh and juice spray in all directions as ${target}'s head jerks violently to the left, their expression frozen between shock and absurdity, splattered with pulverized fruit. The force of the impact visibly distorts their face — features compressed, lips twisted to one side, and skin visibly rippling from the blow. The collision is captured in slow motion, crimson juice arcing dynamically through the air.\\\nRequirements: \\\n1. Replace ${target} with the actual person or pet shown in the image. \\\n2. If there are two or three people or pets in the image, all of them must be struck by the watermelon and described as being affected, including how their faces are distorted by the impact — lips pushed aside and skin visibly rippling.", "template": "watermelon_hit"}, {"id": 193, "name": "甜美微笑", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/tianmeiweixiao_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/tianmeiweixiao_cover.jpeg", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "10", "scene": "live_photo"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/tianmeiweixiao_upload.png"], "person_count": "支持单人、双人和多人", "attention": "人物为正面、上半身、中性表情时，效果更好。", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动。", "effect_boundary": "可能会存在输出视频的色彩比输入图片微淡的情况。"}, "prompt": "主体面对镜头，露出了甜美动人的微笑", "template": "live_photo"}, {"id": 194, "name": "恐惧", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/kongju_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/kongju_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "emotionlab"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/dynamic/kongju_upload.webp"], "person_count": "支持单人、双人和多人", "attention": "人物为正面、上半身、中性表情时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当上传图片为全身照时，可能会出现跳变的情况"}, "prompt": "视频内容\\\n画面主体的表情开始慢慢变化，变得恐惧害怕，眼神里满是无助和惊慌，害怕的大声尖叫\\\n# 要求\\\n1.根据用户上传图片确定主体数量、人物性别、shot_size,每个主体都是同样的表情变化\\\n2.Motion Level 设定为:Middle\\\n3.以我的视频内容为第一要素，背景和图片保持一致，不要变化，不要描述两次\\\n4.镜头固定不要移动", "template": "emotionlab"}, {"id": 195, "name": "微笑", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/weixiao_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/weixiao_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "emotionlab"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/dynamic/weixiao_upload.webp"], "person_count": "支持单人、双人和多人", "attention": "人物为正面、上半身、中性表情时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当上传图片为全身照时，可能会出现跳变的情况"}, "prompt": "视频内容\\\n画面主体的表情开始慢慢变化,表情逐渐放松，嘴角轻轻上扬，眼神变得柔和，微笑逐渐展现出温和与宁静的氛围。\\\n# 要求\\\n1.根据用户上传图片确定主体数量、人物性别、shot_size,每个主体都是同样的表情变化\\\n2.Motion Level 设定为:Middle\\\n3.以我的视频内容为第一要素，背景的描述统一、合理，不要描述两次\\\n4.镜头固定不要移动\\\n5.根据人物初始状态选择比较微小的微笑，还是比较开朗的微笑.不要过于夸张的微笑", "template": "emotionlab"}, {"id": 196, "name": "狂笑", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/kuangxiao_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/kuangxiao_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "emotionlab"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/dynamic/kuangxaio_uplaod.webp"], "person_count": "支持单人、双人和多人", "attention": "人物为正面、上半身、中性表情时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当上传图片为全身照时，可能会出现跳变的情况"}, "prompt": "视频内容\\\n画面主体的表情开始慢慢变化,随着心情的放松，眉头慢慢舒展，嘴角开始上扬。然后他的笑容逐渐扩大，眼睛也变得更加明亮，仿佛看到了什么有趣的事物。最终，他大笑起来，嘴巴张得大大的，露出洁白的牙齿，动作夸张\\\n# 要求\\\n1.根据用户上传图片确定主体数量、人物性别、shot_size,每个主体都是同样的表情变化\\\n2.Motion Level 设定为:Middle\\\n3.以我的视频内容为第一要素，背景的描述统一、合理，不要描述两次\\\n4.镜头固定不要移动,人物表情变化要自然", "template": "emotionlab"}, {"id": 197, "name": "惊讶", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/jingya_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/jingya_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "360p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "emotionlab"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/dynamic/jingya_upload.webp"], "person_count": "支持单人、双人和多人", "attention": "人物为正面、上半身、中性表情时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当上传图片为全身照时，可能会出现跳变的情况"}, "prompt": "视频内容\\\n图中的人物逐渐露出特别惊讶的表情，眼睛睁大，嘴巴微微张开，透出难以置信的神情。\\\n# 要求\\\n1.根据用户上传图片确定主体数量、人物性别、shot_size,每个主体都是同样的表情变化\\\n2.Motion Level 设定为:Middle\\\n3.以我的视频内容为第一要素，背景的描述统一、合理，不要描述两次\\\n4.镜头固定不要移动,人物主要是表情变化，人物表情变化要自然", "template": "emotionlab"}, {"id": 198, "name": "风动", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/fengdong_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/fengdong_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "10", "scene": "live_photo"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/dynamic/fengdong_upload.webp"], "person_count": "支持单人、双人和多人", "attention": "人物为正面、上半身、长发时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会存在输出视频的色彩比输入图片微淡的情况"}, "prompt": "主体头发丝被风朝着某一个方向微微吹动", "template": "live_photo"}, {"id": 199, "name": "镜头动", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/jingtoudong_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/jingtoudong_cove.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "10", "scene": "live_photo"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/dynamic/jingtoudong_upload.webp"], "person_count": "支持单人、双人和多人", "attention": "人物为正面时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会存在输出视频的色彩比输入图片微淡的情况"}, "prompt": "镜头缓缓拉近,风轻轻吹着主体", "template": "live_photo"}, {"id": 200, "name": "走路", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/zoulu_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/zoulu_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "10", "scene": "live_photo"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Templat_Substitute_home/dynamic/zoulu_upload.webp"], "person_count": "支持单人、双人和多人", "attention": "人物为正面、露出腿部以上的半身时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当上传图片为不露腿的半身照时，可能会出现无法走路、面部不稳定的情况"}, "prompt": "视频内容\\\n画面主体，正对镜头走模特步\\\n# 要求\\\n1.根据img图片人物状态，设计合适的镜头运动，镜头应该慢慢转变为中景或者牛仔镜头【拉远或者推进】能够显露出人物的大半个身体\\\n2.根据img图片人物状态，设计合理的动作转变，最终人物应当面对镜头走模特步\\\n3.严格根据图片确定人物外观，不要出现图片没有的人物", "template": "live_photo"}, {"id": 201, "name": "老照片动起来", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/laozhaopian_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/0325_cn/laozhaopian_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "live_memory"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/0325_cn/laozhaopian_upload.webp"], "person_count": "支持单一或多个商品图", "attention": "当人物为正面、动作较为简单、且照片不模糊时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "会有可能出现人物仅眨眼微笑、没有运动幅度的情况；且当人物带有道具时，会有可能出现道具变形的情况"}, "prompt": "Video content\\\n 视频中的人物们，简单互动，微笑看着屏幕。\\\n Requirements: \\\n1.严格根据图片判断有多少个人物，精准简要的描述人物外观。\\\n2.Motion Level ：Middle", "template": "live_memory"}, {"id": 202, "name": "转身热舞", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/zhuanshenrewu_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/zhuanshenrewu_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "bodyshake"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/zhuanshenrewu_upload.png"], "person_count": "支持写实风或二次元风格的单人照片", "attention": "当人物为单人且画面露出大腿以上时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当人物在转身向后过程中，有时会出现肢体结构崩坏或手部穿模的情况；当执行抖臀类动作时，可能会出现动作不连贯或视觉效果不自然的情况"}, "prompt": "Video content\\\nThe character performs a rhythmic dance sequence in an indoor environment. She starts by twisting her hips, then turns to orhe side, briefly shaking her hips in a playful manner. Her movements are smooth and confident, consistently emphasizing body rhythm and expressiveness.\\\n Requirement:\\\n Motion_level:Large", "template": "bodyshake"}, {"id": 203, "name": "360度转转转", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/360xuanzhuan_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/360xuanzhuan_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "spin360"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/360xuanzhuan_upload.PNG"], "person_count": "仅支持人物/动物的单主体", "attention": "当人物为单人全身或半身照、动物为全身照时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当画面中出现双人或多人时，可能会出现角色错位或穿模的情况；可能会出现背部区域生成不准确的情况；有时可能会出现旋转不足 360 度的情况"}, "prompt": "Video content\\\nThe subject in the image smoothly rotates 360° in place.\\\n Requirements:\\\n1.A<PERSON>ura<PERSON>y identify the number of subjects.\\\n Camera Movement:Static shot", "template": "spin360"}, {"id": 204, "name": "镜头环绕", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0324/jingtouhuanrao_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0324/jingtouhuanrao_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "orbit"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0324/jingtouhuanrao_upload.jpeg"], "person_count": "支持单一或多个商品图", "attention": "当商品图为近景、中景或远景拍摄，且旁边有参照物时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当照片为白底商品图，旁边无参考物时，会出现镜头运动相对较慢的情况；也有可能出现非环绕镜头或镜头运动较快的情况"}, "prompt": "The product in the image remains stationary while the camera moves counterclockwise around it.", "template": "orbit"}, {"id": 205, "name": "镜头推进", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0324/jingtoutuijin_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0324/jingtoutuijin_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "zoom_in"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0324/jingtoutuijin_upload.jpeg"], "person_count": "支持单一或多个商品图", "attention": "当商品图为近景、中景或远景拍摄，且旁边有参照物时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当照片中的主体为近景拍摄时，会有可能出现旋转推进的情况"}, "prompt": "Video content\\\n The product remains completely stationary while the camera zooms in to showcase it.\\\nRequirement:\\\n1. Camera Movement: Zoom in", "template": "zoom_in"}, {"id": 206, "name": "虚拟试衣", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0324/shiyi-nei/xunishiyi_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0324/shiyi-nei/xunishiyi_cover.png", "isHot": true, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "9:16", "credit_cost": "6", "scene": "ai_outfit"}, "input_instruction": {"image_count": "必传，支持上传2～3张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0324/shiyi-nei/xunishiyi_renwu.png", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0324/shiyi-nei/xunishiyi_fuzhuang.png", "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0324/shiyi-nei/xunishiyi_beijing.png"], "person_count": "支持单一或多个商品图", "attention": "当上传的人物图片为大头照，环境图片背景干净、服装图片背景干净时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能会出现服装的颜色、长短和上传图片不一致的情况"}, "prompt": "Video content\\\n 一个模特穿着服装自信的走向镜头，脚步稳健，手臂自然摆动。\\\n Requirements: \\\n1. 对服装配饰的各种细节要尽量精准详细的描述，对角色的面部细节详细描述。\\\n2.如果图片展示了[服装、手提包、配饰、帽子、鞋子]，需要出现在对人物的服装描述中。 \\\n3.如果图片没有展示所有服装，则针对性以提供服装设计合适的搭配服装，例如只提供了上衣，则要设计对应的裤子、裙子等。 \\\n4.如果有环境图，则设定为背景。如果没有环境图片，提供一个简洁白色背景，不要出现白布之类的描述。 \\\n5.只有1个角色，不要出现任何复数代词。 \\\n6.Shot Size 为：Wide Shot.\\\n7.camera movement：zoom out", "template": "ai_outfit"}, {"id": 207, "name": "180度转身", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/180xuanzhuan_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/180xuanzhuan_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "spin180"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Dynamic/Dynamic/180xuanzhuan_upload.jpg"], "person_count": "仅支持单人照片", "attention": "当人物为半身照(上半身或下半身)，距离为中景、远景时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入图为大头近景时，可能会出现面部比例不协调或生成效果不佳的情况；当人物手部抬起或位于身体背后时，可能会出现手部模糊或结构异常的情况；当输入图为双人或多人时，可能会出现角色错位、主体缺失或穿模的情况；当输入图仅为小腿（含鞋），缺失上半身时，可能无法完成转身动作；当输入图为背面照时，可能无法实现从背面转向正面的效果"}, "prompt": "Video content\\\nThe model turns 180 degrees clockwise to showcase the clothing details, smiling gently.\\\n Requirement:\\\n Camera Movement:Static shot", "template": "spin180"}, {"id": 208, "name": "环绕推进", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0425/orbitdolly_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0425/orbitdolly_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "orbit_dolly"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0425/orbitdolly_upload.png"], "person_count": "适用于商品图、建筑等物体", "attention": "当主体为商品，距离处于近景、中景、远景时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入图为白底图时，镜头无法环绕，只能推进；当输入图为人物时，人物不动或仅微动，效果较差；在少数情况下，可能出现主体偏离环绕中心的情况"}, "prompt": "The camera zooms in very slowly and smoothly orbits clockwise around the subject, showcasing intricate details.\\\nRequirements:\\\n1.If the image includes natural elements such as water or leaves, the description in the output must mention their subtle, natural movements.\\\n2.If the subject is a person, the output description must mention actions such as blinking.\\\n3. Motion Level: Middle\\\n4. Camera Movement: 'Zoom In, Clockwise Rotation' ", "template": "orbit_dolly"}, {"id": 209, "name": "快速环绕推进", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0425/orbitdollyfast_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0425/orbitdollyfast_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "orbit_dolly_fast"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0425/orbitdollyfast_upload.jpg"], "person_count": "适用于商品图、建筑等物体", "attention": "当主体为商品，距离处于近景、中景、远景时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "可能出现背景中其他物体未随自然属性发生微动的情况；当运镜速度过快时，主体可能发生歪斜，或主体中的部分物体有概率消失"}, "prompt": "The camera zooms in very slowly and smoothly orbits clockwise around the subject, showcasing intricate details.\\\nRequirements:\\\n1.If the image includes natural elements such as water or leaves, the description in the output must mention their subtle, natural movements.\\\n2.If the subject is a person, the output description must mention actions such as blinking.\\\n3. Motion Level: Middle\\\n4. Camera Movement: 'Zoom In, Clockwise Rotation' ", "template": "orbit_dolly_fast"}, {"id": 210, "name": "旋转", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0425/autospin_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0425/autospin_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "4s", "aspect_ratio": "与输入图比例相同", "credit_cost": "4", "scene": "auto_spin"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Ecommerce/0425/autospin_upload.jpg"], "person_count": "仅支持商品图", "attention": "当主体为商品，距离处于近景、中景、远景时，效果更好", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "当输入图为白背景时，商品可能无法实现自转；若旋转速度过快，可能导致主体崩坏；若旋转速度过慢，整体表现可能不理想"}, "prompt": "The subject in the photo slowly rotates counterclockwise at a steady and fluid pace, with even lighting illuminating the surface. The camera remains fixed, highlighting the dynamic beauty of the rotation.\\\n# Requirement\\\n- Camera Movement must be 'Static Shot' ", "template": "auto_spin"}, {"id": 211, "name": "牛马的一周", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Template+Story/workdayfeels_0512/workdayfeels_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Template+Story/workdayfeels_0512/workdayfeels_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "19s", "aspect_ratio": "仅支持输入9:16", "credit_cost": "24", "scene": "workday_feels"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Template+Story/workdayfeels_0512/workdayfeels_upload.png"], "person_count": "仅支持单人照片", "attention": "建议使用单人全身照，效果更好；该玩法仅支持上传一张 9:16 比例的图片", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "若上传的是近景大头照、多人照片或宠物照片，可能导致部分场景效果不佳或生成失败"}, "prompt": null, "template": null}, {"id": 212, "name": "爱情五重奏", "videoUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Template+Story/lovestory_0512/lovestory_video.mp4", "posterUrl": "https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Template+Story/lovestory_0512/lovestory_cover.png", "isHot": false, "isNew": false, "category": "Others", "urlType": "template", "provider": "vidu", "detail": {"resolution": "720p", "duration": "22s", "aspect_ratio": "仅支持输入9:16", "credit_cost": "46", "scene": "love_story"}, "input_instruction": {"image_count": "必传，仅支持上传一张图片", "image_url": ["https://image01.vidu.zone/vidu-maas/scene-template/Template_material/Template+Story/lovestory_0512/lovestory_upload.png"], "person_count": "仅支持双人照片", "attention": "两个人物为全身，且能露出脚和大腿时候，效果更好；该玩法仅支持上传一张 9:16 比例的图片", "prompt_instruction": "必传，格式尽量不改动，内容详见请求示例 \"prompt\" 参数；若有个性需求，可在不改变结构和大多内容前提下，对提示词进行改动", "effect_boundary": "若上传的是近景大头照、单人照片或宠物照片，可能导致部分场景效果不佳或生成失败"}, "prompt": null, "template": null}]}