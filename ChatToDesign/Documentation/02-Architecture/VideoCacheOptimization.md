# 视频缓存优化方案

## 概述

本方案实现了一个高效的视频缓存系统，专门针对 ChatToDesign 应用中的 3-5MB 视频文件进行优化。

## 核心组件

### 1. VideoCacheManager
- **位置**: `ChatToDesign/Infrastructure/Cache/CacheManager.swift`
- **功能**: 
  - 内存缓存 (50MB) + 磁盘缓存 (150MB)
  - LRU (最近最少使用) 清理策略
  - 自动过期清理 (7天)
  - SHA256 URL 哈希避免文件名冲突

### 2. VideoPreloadService
- **位置**: `ChatToDesign/Infrastructure/Services/VideoPreloadService.swift`
- **功能**:
  - 智能预加载相邻视频
  - 最大3个并发下载
  - 优先级队列 (距离可见区域越近优先级越高)
  - 防抖机制避免快速滚动时频繁预加载

### 3. 优化的 VideoEffectCardView
- **位置**: `ChatToDesign/Presentation/VideoEffects/VideoEffectCardView.swift`
- **改进**:
  - 移除了复杂的临时文件操作
  - 使用新的缓存管理器
  - 异步下载和缓存
  - 更好的错误处理

## 缓存策略

### 内存缓存
- **大小限制**: 50MB
- **数量限制**: 50个视频
- **用途**: 快速访问最近使用的视频

### 磁盘缓存
- **大小限制**: 150MB (约35个视频)
- **清理策略**: LRU + 过期时间
- **存储位置**: `~/Library/Caches/VideoCache/`

### 预加载策略
- **水平滚动**: 预加载前后2个视频
- **网格布局**: 预加载上下左右相邻视频
- **并发限制**: 最多3个同时下载
- **延迟执行**: 0.5秒防抖，避免快速滚动时频繁触发

## 性能优化

### 内存优化
- 磁盘缓存不占用内存
- 内存缓存使用 NSCache 自动管理
- LRU 策略确保内存使用可控

### 网络优化
- 并发下载限制避免网络拥塞
- 优先级队列确保重要视频优先下载
- 重复下载检测避免浪费带宽

### 用户体验优化
- 缓存命中时瞬间播放
- 智能预加载减少等待时间
- 后台下载不影响 UI 响应

## 使用方法

### 基本使用
```swift
// 获取缓存的视频 URL
if let cachedURL = await VideoCacheManager.shared.getCachedVideoURL(for: videoURL) {
    // 使用缓存的视频
    createPlayer(with: cachedURL)
} else {
    // 下载并缓存视频
    let (data, _) = try await URLSession.shared.data(from: videoURL)
    if let cachedURL = await VideoCacheManager.shared.cacheVideo(url: videoURL, data: data) {
        createPlayer(with: cachedURL)
    }
}
```

### 预加载
```swift
// 预加载单个视频
await VideoPreloadService.shared.preloadVideo(url: videoURL)

// 预加载视频列表
VideoPreloadService.shared.preloadVideos(from: videoEffects, visibleRange: 0..<10)
```

### 缓存管理
```swift
// 获取缓存统计
let stats = await VideoCacheManager.shared.getCacheStats()
print("磁盘缓存: \(stats.diskSize) bytes, 文件数: \(stats.totalFiles)")

// 清除所有缓存
await VideoCacheManager.shared.clearAllCache()
```

## 调试工具

### VideoCacheDebugView
- **访问方式**: 在 Debug 模式下，点击 AI Video Effects 页面右上角的齿轮图标
- **功能**:
  - 查看缓存统计信息
  - 监控预加载状态
  - 清除缓存和取消预加载
  - 查看配置信息

## 配置参数

### VideoCacheManager 配置
```swift
private struct Configuration {
    static let maxMemoryCacheSize = 50 * 1024 * 1024  // 50MB
    static let maxDiskCacheSize = 150 * 1024 * 1024   // 150MB
    static let maxCacheAge: TimeInterval = 7 * 24 * 60 * 60  // 7天
}
```

### VideoPreloadService 配置
```swift
private struct Configuration {
    static let maxConcurrentDownloads = 3  // 最大并发下载数
    static let preloadDistance = 5         // 预加载距离
    static let preloadDelay: TimeInterval = 0.5  // 预加载延迟
}
```

## 监控和维护

### 缓存健康检查
- 定期检查缓存大小是否在限制内
- 监控 LRU 清理频率
- 观察缓存命中率

### 性能指标
- 视频加载时间
- 缓存命中率
- 内存使用情况
- 网络流量消耗

## 故障排除

### 常见问题
1. **视频加载慢**: 检查网络连接和预加载是否正常工作
2. **内存占用高**: 检查内存缓存配置和清理策略
3. **存储空间不足**: 检查磁盘缓存大小限制和清理机制

### 日志监控
- 使用 Logger 查看缓存操作日志
- 监控下载失败和缓存错误
- 观察预加载队列状态

## 未来优化方向

1. **智能预测**: 基于用户行为预测需要预加载的视频
2. **网络感知**: 根据网络状况调整预加载策略
3. **压缩优化**: 对缓存的视频进行压缩以节省空间
4. **分级缓存**: 根据视频重要性设置不同的缓存策略
