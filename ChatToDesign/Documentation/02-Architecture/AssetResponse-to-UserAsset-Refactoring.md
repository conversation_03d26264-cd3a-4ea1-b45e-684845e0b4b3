# AssetResponse 重构为 UserAsset 实体技术方案

## 📋 概述

本文档详细描述了将 `AssetResponse` 从 API 响应模型重构为领域实体 `UserAsset` 的技术方案，以符合 DDD（领域驱动设计）架构原则。

## 🎯 重构目标

### 主要目标

- 将 `AssetResponse` 重构为符合领域语义的 `UserAsset` 实体
- 实现清晰的职责分离：API 传输对象 vs 领域实体
- 统一项目中实体的命名和结构模式
- 提升代码的可维护性和可扩展性

### 预期收益

- **架构清晰**：明确区分传输对象和领域实体
- **类型安全**：使用正确的数据类型（如 Date 而非 String）
- **业务语义**：实体名称更符合业务领域
- **可扩展性**：易于添加业务逻辑和行为

## 📊 现状分析

### 当前 AssetResponse 的问题

1. **职责混乱**

   - 既作为 API 响应模型使用
   - 又被当作领域对象在业务逻辑中使用
   - 违反了单一职责原则

2. **命名语义问题**

   - `Response` 后缀表明它是传输对象
   - 在领域层使用时语义不清晰
   - 与其他实体命名不一致

3. **架构层次混乱**

   - 位于 `Domain/Models/FileUploadModels.swift`
   - 应该在 `Domain/Entities/` 目录下
   - 与其他实体文件组织不一致

4. **缺乏业务行为**
   - 仅包含数据字段
   - 缺少业务逻辑方法
   - 不符合 DDD 实体设计原则

### 项目现有实体模式分析

通过分析项目中现有实体，发现以下模式：

#### User 实体模式

```swift
public struct User: Identifiable, Equatable, Codable {
    // 核心字段
    public let id: String
    public let email: String

    // 业务逻辑方法
    public var isVerified: Bool { return status == .verified }
    public var isActive: Bool { return status == .active || status == .verified }
    public func isActivated() -> Bool { /* 实现 */ }
}
```

#### Message 实体模式

```swift
public struct Message: Identifiable, Codable, Equatable {
    // 核心字段
    public var id: String

    // 计算属性
    public var hasMedia: Bool { /* 实现 */ }
    public var hasImage: Bool { /* 实现 */ }

    // 工厂方法
    public static func createUserMessage(...) -> Message { /* 实现 */ }
}
```

#### Chat 实体模式

```swift
public struct Chat: Identifiable, Codable, Hashable {
    // 核心字段
    public var id: String?

    // 业务逻辑
    public var isEditable: Bool { return status == .active }

    // 工厂方法
    public static func create(...) -> Chat { /* 实现 */ }
}
```

## 🔍 **数据流分析**

通过分析代码发现，当前的数据流是：

```
Firestore → FirestoreAssetRepository.mapToAssetResponse() → AssetResponse → 业务层
```

**关键发现**：

- 数据直接来自 Firestore，不是 HTTP API 响应
- `observeUserAssets()` 直接观察 Firestore 变化
- `AssetResponse` 实际上是从 Firestore 数据映射而来，不是真正的 API 响应

**结论**：不需要保留 `AssetResponse` 作为 API 传输对象，应该直接重构为 `UserAsset` 实体！

## 🏗️ 简化设计方案

### 1. UserAsset 实体设计

#### 1.1 核心结构

```swift
// ChatToDesign/Domain/Entities/UserAsset.swift
import Foundation

/// 用户资产实体
/// 表示用户拥有的数字资产（图片、视频等）
public struct UserAsset: Identifiable, Codable, Equatable {
    // MARK: - 核心标识

    /// 资产唯一标识符
    public let id: String

    /// 所属用户ID
    public let userId: String

    // MARK: - 存储信息

    /// 缩略图URL
    public let thumbnailUrl: String?

    /// 存储路径
    public let path: String

    /// 存储桶名称
    public let bucket: String

    // MARK: - 文件属性

    /// 文件名
    public let name: String

    /// 文件大小（字节）
    public let size: Int

    /// MIME 类型
    public let type: String

    /// 访问 URL
    public let url: String

    // MARK: - 业务属性

    /// 来源类型：AI生成 或 用户上传
    public let sourceType: SourceType

    /// 关联的任务ID（仅AI生成内容）
    public let sourceTaskId: String?

    /// AI生成的prompt（仅AI生成内容）
    public let generationPrompt: String?

    // MARK: - 社交统计

    /// 点赞数
    public let likeCount: Int

    /// 收藏数
    public let favoriteCount: Int

    // MARK: - 元数据

    /// 标签数组
    public let tags: [String]?

    /// 资产描述
    public let description: String?

    /// 扩展元数据
    public let metadata: [String: AnyCodable]?

    // MARK: - 时间戳

    /// 创建时间
    public let createdAt: Date

    /// 更新时间
    public let updatedAt: Date

    // MARK: - 状态管理

    /// 资产状态
    public let status: AssetStatus?

    /// 是否公开访问
    public let isPublic: Bool?
}
```

#### 1.2 业务逻辑方法

```swift
// MARK: - 业务逻辑
extension UserAsset {
    /// 是否为图片资产
    public var isImage: Bool {
        return type.hasPrefix("image/")
    }

    /// 是否为视频资产
    public var isVideo: Bool {
        return type.hasPrefix("video/")
    }

    /// 是否为AI生成内容
    public var isAIGenerated: Bool {
        return sourceType == .aiGenerated
    }

    /// 是否可以重新创建（基于模板）
    public var canRecreate: Bool {
        guard isAIGenerated,
              let metadata = metadata,
              let originalTaskData = metadata["originalTaskData"] else {
            return false
        }
        // 检查是否包含模板信息
        return originalTaskData.value != nil
    }

    /// 是否为活跃状态
    public var isActive: Bool {
        return status == .active || status == nil
    }

    /// 获取文件扩展名
    public var fileExtension: String {
        return URL(fileURLWithPath: name).pathExtension.lowercased()
    }

    /// 获取格式化的文件大小
    public var formattedSize: String {
        return ByteCountFormatter.string(fromByteCount: Int64(size), countStyle: .file)
    }
}
```

#### 1.3 便利属性

```swift
// MARK: - 便利属性
extension UserAsset {
    /// 获取访问URL对象
    public var urlObject: URL? {
        return URL(string: url)
    }

    /// 获取缩略图URL对象
    public var thumbnailUrlObject: URL? {
        guard let thumbnailUrl = thumbnailUrl else { return nil }
        return URL(string: thumbnailUrl)
    }

    /// 获取显示名称（优先使用描述，否则使用文件名）
    public var displayName: String {
        return description?.isEmpty == false ? description! : name
    }
}
```

#### 1.4 工厂方法

```swift
// MARK: - 工厂方法
extension UserAsset {
    /// 创建用户上传的资产
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - name: 文件名
    ///   - size: 文件大小
    ///   - type: MIME类型
    ///   - url: 访问URL
    ///   - path: 存储路径
    ///   - bucket: 存储桶
    ///   - thumbnailUrl: 缩略图URL
    ///   - tags: 标签
    ///   - description: 描述
    /// - Returns: UserAsset 实例
    public static func createUserUpload(
        userId: String,
        name: String,
        size: Int,
        type: String,
        url: String,
        path: String,
        bucket: String,
        thumbnailUrl: String? = nil,
        tags: [String]? = nil,
        description: String? = nil
    ) -> UserAsset {
        return UserAsset(
            id: UUID().uuidString,
            userId: userId,
            thumbnailUrl: thumbnailUrl,
            path: path,
            bucket: bucket,
            name: name,
            size: size,
            type: type,
            url: url,
            sourceType: .userUpload,
            sourceTaskId: nil,
            generationPrompt: nil,
            likeCount: 0,
            favoriteCount: 0,
            tags: tags,
            description: description,
            metadata: nil,
            createdAt: Date(),
            updatedAt: Date(),
            status: .active,
            isPublic: false
        )
    }

    /// 创建AI生成的资产
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - name: 文件名
    ///   - size: 文件大小
    ///   - type: MIME类型
    ///   - url: 访问URL
    ///   - path: 存储路径
    ///   - bucket: 存储桶
    ///   - sourceTaskId: 关联任务ID
    ///   - generationPrompt: 生成提示词
    ///   - thumbnailUrl: 缩略图URL
    ///   - metadata: 元数据
    /// - Returns: UserAsset 实例
    public static func createAIGenerated(
        userId: String,
        name: String,
        size: Int,
        type: String,
        url: String,
        path: String,
        bucket: String,
        sourceTaskId: String,
        generationPrompt: String?,
        thumbnailUrl: String? = nil,
        metadata: [String: AnyCodable]? = nil
    ) -> UserAsset {
        return UserAsset(
            id: UUID().uuidString,
            userId: userId,
            thumbnailUrl: thumbnailUrl,
            path: path,
            bucket: bucket,
            name: name,
            size: size,
            type: type,
            url: url,
            sourceType: .aiGenerated,
            sourceTaskId: sourceTaskId,
            generationPrompt: generationPrompt,
            likeCount: 0,
            favoriteCount: 0,
            tags: nil,
            description: nil,
            metadata: metadata,
            createdAt: Date(),
            updatedAt: Date(),
            status: .active,
            isPublic: false
        )
    }
}
```

### 2. Firestore 映射重构

#### 2.1 直接映射到 UserAsset

```swift
// ChatToDesign/Infrastructure/ThirdParty/Firebase/Database/FirestoreAssetRepository.swift

/// 将 Firestore 数据直接转换为 UserAsset 实体
/// - Parameters:
///   - id: 资产ID
///   - data: Firestore 数据
/// - Returns: UserAsset 实体对象
/// - Throws: 如果数据格式不正确
private func mapToUserAsset(id: String, data: [String: Any]) throws -> UserAsset {
    guard let userId = data[Field.userId] as? String,
          let path = data[Field.path] as? String,
          let bucket = data[Field.bucket] as? String,
          let name = data[Field.name] as? String,
          let size = data[Field.size] as? Int,
          let type = data[Field.type] as? String,
          let url = data[Field.url] as? String,
          let sourceTypeString = data[Field.sourceType] as? String,
          let sourceType = SourceType(rawValue: sourceTypeString),
          let likeCount = data[Field.likeCount] as? Int,
          let favoriteCount = data[Field.favoriteCount] as? Int,
          let createdAtTimestamp = data[Field.createdAt] as? Timestamp,
          let updatedAtTimestamp = data[Field.updatedAt] as? Timestamp else {
        throw AssetRepositoryError.databaseError(
            NSError(domain: "FirestoreMapping", code: -1,
                   userInfo: [NSLocalizedDescriptionKey: "Invalid asset data structure"])
        )
    }

    // 可选字段
    let thumbnailUrl = data[Field.thumbnailUrl] as? String
    let sourceTaskId = data[Field.sourceTaskId] as? String
    let generationPrompt = data[Field.generationPrompt] as? String
    let tags = data[Field.tags] as? [String]
    let description = data[Field.description] as? String
    let metadata = data[Field.metadata] as? [String: AnyCodable]
    let statusString = data[Field.status] as? String
    let status = statusString != nil ? AssetStatus(rawValue: statusString!) : nil
    let isPublic = data[Field.isPublic] as? Bool

    return UserAsset(
        id: id,
        userId: userId,
        thumbnailUrl: thumbnailUrl,
        path: path,
        bucket: bucket,
        name: name,
        size: size,
        type: type,
        url: url,
        sourceType: sourceType,
        sourceTaskId: sourceTaskId,
        generationPrompt: generationPrompt,
        likeCount: likeCount,
        favoriteCount: favoriteCount,
        tags: tags,
        description: description,
        metadata: metadata,
        createdAt: createdAtTimestamp.dateValue(),
        updatedAt: updatedAtTimestamp.dateValue(),
        status: status,
        isPublic: isPublic
    )
}
```

## 🔄 简化迁移策略

### 阶段 1：创建 UserAsset 实体（第 1 周）

#### 1.1 创建新文件结构

- [ ] 创建 `ChatToDesign/Domain/Entities/UserAsset.swift`
- [ ] 删除 `ChatToDesign/Domain/Models/FileUploadModels.swift` 中的 `AssetResponse`

#### 1.2 更新结果类型

```swift
// ChatToDesign/Domain/Models/AssetModels.swift
/// 用户资产查询结果
public struct UserAssetsResult {
    /// 资产列表
    public let assets: [UserAsset]

    /// 分页信息
    public let pagination: PaginationInfo

    public init(assets: [UserAsset], pagination: PaginationInfo) {
        self.assets = assets
        self.pagination = pagination
    }
}
```

**注意**：将 `UserAssetsResponse` 重命名为 `UserAssetsResult` 以保持命名一致性

### 阶段 2：仓储层重构（第 2 周）

#### 2.1 更新仓储接口

```swift
// ChatToDesign/Domain/Interfaces/AssetRepository.swift (重命名为 UserAssetRepository.swift)
public protocol UserAssetRepository {
    func getUserAssets(userId: String, query: AssetListQuery) async throws -> UserAssetsResult
    func searchAssets(userId: String, searchTerm: String, type: String?, limit: Int) async throws -> [UserAsset]
    func getAssetsByType(userId: String, type: String, limit: Int) async throws -> [UserAsset]
    func getAssetsByTags(userId: String, tags: [String], limit: Int) async throws -> [UserAsset]
    func deleteAsset(id: String, userId: String) async throws -> AssetDeleteResponse
    func observeUserAssets(userId: String, query: AssetListQuery) -> AnyPublisher<UserAssetsResult, Error>
}
```

#### 2.2 更新 Firestore 仓储实现

直接在 `FirestoreAssetRepository` 中映射到 `UserAsset`：

```swift
// 替换所有 mapToAssetResponse 调用为 mapToUserAsset
// 更新所有方法返回类型从 AssetResponse 到 UserAsset
// 更新 UserAssetsResponse 到 UserAssetsResult

// 示例：getUserAssets 方法
public func getUserAssets(
    userId: String,
    query: AssetListQuery
) async throws -> UserAssetsResult {
    // ... 查询逻辑保持不变 ...

    // 转换数据 - 直接映射到 UserAsset
    var assets: [UserAsset] = []
    for document in documents {
        let asset = try mapToUserAsset(id: document.documentID, data: document.data())
        assets.append(asset)
    }

    // 返回 UserAssetsResult
    return UserAssetsResult(
        assets: assets,
        pagination: PaginationInfo(/* ... */)
    )
}
```

### 阶段 3：应用层重构（第 3 周）

#### 3.1 更新应用服务接口

```swift
// ChatToDesign/Application/Asset/DI/AssetApplicationService.swift
public protocol AssetApplicationService {
    func getUserAssets(query: AssetListQuery) async throws -> UserAssetsResult
    func getUserImages(limit: Int) async throws -> [UserAsset]
    func getUserVideos(limit: Int) async throws -> [UserAsset]
    func searchAssets(searchTerm: String, type: String?, limit: Int) async throws -> [UserAsset]
    func searchImages(searchTerm: String, limit: Int) async throws -> [UserAsset]
    func deleteAsset(id: String) async throws -> AssetDeleteResponse
    func observeUserAssets(query: AssetListQuery) -> AnyPublisher<UserAssetsResult, Error>
}
```

#### 3.2 更新 UseCase 类

**需要更新的文件**：

- `GetUserAssetsUseCase.swift`: 返回 `UserAssetsResult`
- `SearchAssetsUseCase.swift`: 返回 `[UserAsset]`
- `ObserveUserAssetsUseCase.swift`: 返回 `AnyPublisher<UserAssetsResult, Error>`

**示例更新**：

```swift
// ChatToDesign/Application/Asset/UseCases/SearchAssetsUseCase.swift
func execute(searchTerm: String, type: String?, limit: Int) async throws -> [UserAsset] {
    // 实现保持不变，只是返回类型改变
    let assets = try await assetRepository.searchAssets(
        userId: currentUser.id,
        searchTerm: searchTerm,
        type: type,
        limit: limit
    )
    return assets // 现在是 [UserAsset] 而不是 [AssetResponse]
}
```

### 阶段 4：表示层重构（第 4 周）

#### 4.1 更新 ViewModel

```swift
// ChatToDesign/Presentation/App/HomePage/PromptFooter/UserAssetSelectionViewModel.swift
@Published var selectedAssets: [UserAsset] = []
@Published private(set) var userAssets: [UserAsset] = []
```

#### 4.2 更新适配器

```swift
// ChatToDesign/Domain/Adapters/AssetToTemplateAdapter.swift
public static func convert(from asset: UserAsset) -> CreateVideoFromTemplateParams {
    // 更新实现以使用 UserAsset
}
```

## ✅ 验证和测试

### 单元测试

- [ ] UserAsset 实体业务逻辑测试
- [ ] AssetResponseAdapter 转换测试
- [ ] 仓储层转换测试

### 集成测试

- [ ] API 到实体的完整转换流程测试
- [ ] ViewModel 与新实体的集成测试

### 回归测试

- [ ] 现有功能完整性测试
- [ ] 性能基准测试

## 📈 预期收益

### 架构改进

- **清晰的职责分离**：API 传输 vs 领域逻辑
- **符合 DDD 原则**：实体包含业务行为
- **统一的命名规范**：与其他实体保持一致

### 开发体验

- **类型安全**：正确的数据类型使用
- **智能提示**：更好的 IDE 支持
- **可维护性**：清晰的代码结构

### 业务价值

- **可扩展性**：易于添加新的业务逻辑
- **一致性**：统一的实体处理方式
- **可测试性**：更好的单元测试支持

## 🚨 风险和注意事项

### 技术风险

- **性能影响**：适配器转换的开销
- **内存使用**：Date 对象 vs String 的内存差异
- **兼容性**：确保现有 API 契约不变

### 迁移风险

- **功能回归**：需要充分的测试覆盖
- **团队协调**：需要团队成员理解新架构
- **时间成本**：分阶段迁移需要合理安排

### 缓解措施

- **渐进式迁移**：分阶段进行，降低风险
- **充分测试**：每个阶段都有完整的测试
- **文档更新**：及时更新相关文档
- **团队培训**：确保团队理解新架构

## 📝 最佳实践

### 代码规范

1. **命名一致性**：确保所有相关类型都使用 `UserAsset` 而非 `AssetResponse`
2. **错误处理**：适配器转换失败时提供清晰的错误信息
3. **文档注释**：为所有公共方法和属性添加详细注释
4. **单元测试**：每个业务逻辑方法都要有对应的测试

### 性能优化

1. **直接映射**：从 Firestore 直接映射到 UserAsset，避免中间转换
2. **缓存策略**：考虑在适当位置缓存 UserAsset 对象
3. **懒加载**：对于计算属性使用懒加载模式
4. **内存管理**：及时释放不需要的对象引用
5. **批量处理**：在处理大量资产时使用批量操作

### 架构原则

1. **单一职责**：每个类型只负责一个职责
2. **开闭原则**：对扩展开放，对修改封闭
3. **依赖倒置**：依赖抽象而非具体实现
4. **接口隔离**：提供最小化的接口

## 🔍 代码审查清单

### UserAsset 实体

- [ ] 所有字段都有适当的访问控制
- [ ] 业务逻辑方法实现正确
- [ ] 工厂方法参数验证充分
- [ ] 计算属性性能合理
- [ ] 时间字段使用 Date 类型而非 String

### Firestore 映射层

- [ ] `mapToUserAsset` 方法正确处理所有字段
- [ ] 时间戳转换正确（Timestamp → Date）
- [ ] 可选字段处理合理
- [ ] 错误处理完整

### 仓储层更新

- [ ] 接口定义清晰，返回 UserAsset 类型
- [ ] 所有方法都直接返回 UserAsset
- [ ] UserAssetsResult 替换 UserAssetsResponse
- [ ] 错误传播合理
- [ ] 异步操作处理正确

### 应用层更新

- [ ] 服务接口更新完整
- [ ] UseCase 返回类型正确
- [ ] 依赖注入配置更新
- [ ] 错误处理一致

### 表示层更新

- [ ] ViewModel 类型从 AssetResponse 更新为 UserAsset
- [ ] UI 绑定正确
- [ ] 用户交互逻辑完整
- [ ] 错误显示合理

### 适配器更新

- [ ] AssetToTemplateAdapter 更新为使用 UserAsset
- [ ] 所有相关适配器都更新类型引用

## 📚 相关文档

### 项目文档

- [Cache-Architecture-Design.md](./Cache-Architecture-Design.md) - 缓存系统架构
- [AssetModuleRefactoringPlan.md](../Documentation/AssetModuleRefactoringPlan.md) - 资产模块重构计划
- [CreatePage-API-Reference.md](./CreatePage-API-Reference.md) - CreatePage API 参考

### 外部参考

- [Domain-Driven Design](https://martinfowler.com/bliki/DomainDrivenDesign.html)
- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [Swift API Design Guidelines](https://swift.org/documentation/api-design-guidelines/)

---

**文档版本**: v1.0
**创建日期**: 2025-01-07
**最后更新**: 2025-01-07
**负责人**: 开发团队
**审核人**: 架构师团队
