# SWR (Stale-While-Revalidate) 实现指南

## 概述

本项目实现了基于 Combine 的 SWR 模式，提供了类似 TanStack Query 的功能，包括：

- ✅ **Stale-while-revalidate** - 返回缓存数据的同时后台刷新
- ✅ **请求去重** - 相同的请求会被合并
- ✅ **内存缓存** - 高效的内存缓存策略
- ✅ **Mutation 后失效** - 变更操作后自动失效相关缓存
- ✅ **后台刷新** - 数据过期时自动后台更新
- ✅ **与现有架构完美集成** - 无需重构现有代码

## 核心组件

### 1. QueryManager

核心 SWR 管理器，负责查询和缓存管理。

### 2. CacheManager

内存缓存管理器，专注于高效的内存缓存存储。

### 3. SWRHook

便捷的 Hook 类，简化在 ViewModel 中的使用。

## 使用方式

### 方式一：直接使用 QueryManager（推荐）

```swift
class HomePageViewModel: ObservableObject {
  @Published var cmsCategories: [CMSCategory] = []
  @Published var isLoadingCMS: Bool = false
  @Published var cmsError: Error? = nil

  private let queryManager = QueryManager.shared
  private var cancellables = Set<AnyCancellable>()

  private func loadCMSDataWithSWR() {
    queryManager
      .query(
        key: "cms_data",
        maxAge: 300,  // 5分钟后触发后台刷新
        staleTime: 3600,  // 1小时后数据完全过期
        networkCall: { [weak self] in
          guard let self = self else { throw APIServiceError.unknown(...) }
          return try await self.apiService.fetchImageUseCaseCMS()
        }
      )
      .receive(on: DispatchQueue.main)
      .sink { [weak self] (result: QueryResult<[ImageTemplateItem]>) in
        self?.handleQueryResult(result)
      }
      .store(in: &cancellables)
  }

  private func handleQueryResult(_ result: QueryResult<[ImageTemplateItem]>) {
    switch result {
    case .cached(let data):
      // 来自缓存的新鲜数据
      processCMSData(data)
      isLoadingCMS = false
      cmsError = nil

    case .stale(let data):
      // 来自缓存的过期数据，正在后台刷新
      processCMSData(data)
      isLoadingCMS = cmsCategories.isEmpty  // 如果已有数据，不显示加载状态
      cmsError = nil

    case .fresh(let data):
      // 来自网络的新数据
      processCMSData(data)
      isLoadingCMS = false
      cmsError = nil

    case .error(let error):
      cmsError = error
      isLoadingCMS = false
    }
  }
}
```

### 方式二：使用 SWRHook（简化版）

```swift
class UserAssetsViewModel: ObservableObject {
  @Published var assets: [AssetResponse] = []
  @Published var isLoading: Bool = false
  @Published var error: Error? = nil

  private var assetsHook: SWRHook<UserAssetsResponse>?
  private var cancellables = Set<AnyCancellable>()

  init() {
    setupAssetsHook()
  }

  private func setupAssetsHook() {
    assetsHook = SWRHook<UserAssetsResponse>(
      key: "user_assets",
      maxAge: 180,  // 3分钟
      staleTime: 600,  // 10分钟
      networkCall: { [weak self] in
        guard let self = self else { throw APIServiceError.unknown(...) }
        let query = AssetListQuery(page: 1, limit: 20)
        return try await self.apiService.getUserAssets(query: query)
      }
    )

    // 绑定状态
    assetsHook?.$data
      .compactMap { $0?.assets }
      .assign(to: \.assets, on: self)
      .store(in: &cancellables)

    assetsHook?.$isLoading
      .assign(to: \.isLoading, on: self)
      .store(in: &cancellables)

    assetsHook?.$error
      .assign(to: \.error, on: self)
      .store(in: &cancellables)

    // 开始获取数据
    assetsHook?.fetch()
  }
}
```

### 方式三：Mutation 操作

```swift
class AssetManagementViewModel: ObservableObject {
  private let queryManager = QueryManager.shared
  private var cancellables = Set<AnyCancellable>()

  func deleteAsset(id: String) {
    queryManager
      .mutate(
        key: "delete_asset_\(id)",
        invalidates: ["user_assets", "cms_data"],  // 删除后失效相关缓存
        networkCall: { [weak self] in
          guard let self = self else { throw APIServiceError.unknown(...) }
          return try await self.apiService.deleteAsset(id: id)
        }
      )
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { completion in
          if case .failure(let error) = completion {
            // 处理错误
          }
        },
        receiveValue: { result in
          // 处理成功结果
        }
      )
      .store(in: &cancellables)
  }
}
```

## 配置参数

### maxAge

数据的最大有效期（秒）。超过此时间后，数据被认为是"过期"的，会触发后台刷新。

- 默认值：300 秒（5 分钟）
- 推荐值：根据数据更新频率调整

### staleTime

数据的完全过期时间（秒）。超过此时间后，数据被认为完全无效，会直接发起网络请求。

- 默认值：3600 秒（1 小时）
- 推荐值：通常设置为 maxAge 的 2-4 倍

## 缓存策略

### 内存缓存

- 限制：100MB 或 2000 个条目
- 自动清理：收到内存警告时清空
- 优势：快速访问，无磁盘 IO 开销，适合 iOS 应用

## 最佳实践

### 1. 缓存键命名

使用有意义的缓存键，包含必要的参数：

```swift
// ✅ 好的命名
"cms_data"
"user_assets_page_1_limit_20"
"task_list_user_123"

// ❌ 避免的命名
"data"
"api_call"
"fetch"
```

### 2. 错误处理

始终处理错误情况：

```swift
case .error(let error):
  self.error = error
  self.isLoading = false
  Logger.error("查询失败: \(error.localizedDescription)")
```

### 3. 内存管理

使用 weak self 避免循环引用：

```swift
networkCall: { [weak self] in
  guard let self = self else { throw ... }
  return try await self.apiService.fetchData()
}
```

### 4. 状态管理

合理设置加载状态：

```swift
case .stale(let data):
  processData(data)
  // 如果已有数据，不显示加载状态
  isLoading = existingData.isEmpty
```

## 迁移指南

### 从现有代码迁移到 SWR

1. **保持现有 API 接口不变**
2. **替换数据获取逻辑**
3. **添加缓存失效逻辑**
4. **测试缓存行为**

### 示例迁移

**迁移前：**

```swift
func fetchData() async {
  isLoading = true
  do {
    let data = try await apiService.fetchData()
    self.data = data
  } catch {
    self.error = error
  }
  isLoading = false
}
```

**迁移后：**

```swift
private func loadDataWithSWR() {
  queryManager
    .query(key: "data", networkCall: {
      try await self.apiService.fetchData()
    })
    .receive(on: DispatchQueue.main)
    .sink { result in
      // 处理结果
    }
    .store(in: &cancellables)
}
```

## 性能优化

1. **合理设置缓存时间** - 根据数据更新频率调整
2. **使用请求去重** - 避免重复请求
3. **及时失效缓存** - 数据变更后失效相关缓存
4. **监控缓存大小** - 定期清理过期缓存

## 调试技巧

1. **查看日志** - 所有缓存操作都有详细日志
2. **检查缓存状态** - 使用 `getCacheSize()` 方法
3. **手动清理缓存** - 使用 `clearAllCache()` 方法
4. **监控网络请求** - 观察请求去重效果
