# 缓存系统架构设计

## 🎯 设计原则

### 1. 清晰的职责分离

- **CacheManager**: 负责内存缓存存储和管理
- **QueryManager**: 负责 SWR 逻辑和请求管理
- **SWRHook**: 提供 ViewModel 友好的接口
- **CacheFactory**: 统一的创建和获取入口

### 2. 简化的架构设计

- **仅内存缓存**: 专注于内存缓存，避免磁盘 IO 复杂性
- **共享实例**: 用于大多数场景，减少内存占用
- **自定义实例**: 用于特殊需求（测试、隔离等）
- **工厂模式**: 统一管理实例创建

## 🏗️ 架构层次

```
┌─────────────────┐
│   SWRHook       │  ← ViewModel 层接口
├─────────────────┤
│  QueryManager   │  ← SWR 逻辑层
├─────────────────┤
│  CacheManager   │  ← 内存缓存层
├─────────────────┤
│ CacheFactory    │  ← 工厂创建层
└─────────────────┘
```

## 📦 实例管理策略

### CacheManager 实例

```swift
public actor CacheManager {
  // 共享实例
  public static let shared = CacheManager(configuration: .default)

  // 自定义实例
  public init(configuration: CacheConfiguration)
}
```

**设计理念**：

- 提供默认共享实例，减少内存占用
- 支持创建自定义实例，满足特殊需求
- 每个实例都有独立的配置和内存缓存空间
- 专注于内存缓存，简化架构复杂度

### QueryManager 实例

```swift
@MainActor
public class QueryManager: ObservableObject {
  // 共享实例
  public static let shared = QueryManager(cacheManager: CacheManager.shared)

  // 自定义实例
  public init(cacheManager: CacheManager)
}
```

**设计理念**：

- 依赖注入 CacheManager，支持不同的缓存策略
- 共享实例使用默认缓存管理器
- 支持创建使用自定义缓存的实例

## 🏭 工厂模式设计

### CacheFactory 职责

```swift
public class CacheFactory {
  // CacheManager 工厂方法
  public static var defaultCache: CacheManager
  public static func createCache(configuration: CacheConfiguration) -> CacheManager
  public static func createTestCache() -> CacheManager

  // QueryManager 工厂方法
  @MainActor public static var defaultQueryManager: QueryManager
  @MainActor public static func createMemoryOnlyQueryManager() -> QueryManager
  @MainActor public static func createQueryManager(cacheManager: CacheManager) -> QueryManager
  @MainActor public static func createTestQueryManager() -> QueryManager
}
```

**优势**：

- 统一的创建入口，降低使用复杂度
- 隐藏实例创建的细节
- 提供语义化的方法名
- 支持测试场景的特殊需求
- 简化的 API，专注于内存缓存管理

## 🎭 使用场景分类

### 1. 默认场景（90% 的使用情况）

```swift
// 直接使用共享实例
let queryManager = QueryManager.shared

// 或通过工厂获取
let queryManager = await CacheFactory.defaultQueryManager
```

**适用于**：

- 大部分业务逻辑
- 标准的数据缓存需求
- 不需要特殊配置的场景

### 2. 自定义配置场景

```swift
// 创建自定义配置
let config = CacheConfiguration(
  maxMemoryCacheSize: 200 * 1024 * 1024,  // 200MB
  maxCacheAge: 24 * 60 * 60,  // 1天
  memoryCacheCountLimit: 10000,
  namespace: "large_data"
)

let cache = CacheFactory.createCache(configuration: config)
let queryManager = await CacheFactory.createQueryManager(cacheManager: cache)
```

**适用于**：

- 大数据量缓存需求
- 特殊的过期策略
- 模块隔离需求
- 性能优化场景

### 3. 测试场景

```swift
// 创建测试专用实例
let testQueryManager = await CacheFactory.createTestQueryManager()
```

**适用于**：

- 单元测试
- 集成测试
- 避免测试数据污染

## 🔄 生命周期管理

### 共享实例

- **创建时机**: 应用启动时
- **生命周期**: 与应用生命周期一致
- **内存管理**: 自动内存警告处理

### 自定义实例

- **创建时机**: 按需创建
- **生命周期**: 由使用者管理
- **内存管理**: 支持手动清理

### 测试实例

- **创建时机**: 每个测试用例
- **生命周期**: 测试用例结束时销毁
- **内存管理**: 自动清理，避免测试间干扰

## 🔧 配置管理

### 预设配置

```swift
// 默认配置：内存缓存，适合大多数场景
CacheConfiguration.default
```

### 自定义配置

```swift
CacheConfiguration(
  maxMemoryCacheSize: Int,      // 内存缓存大小限制
  maxCacheAge: TimeInterval,    // 缓存最大保存时间
  memoryCacheCountLimit: Int,   // 内存缓存项数量限制
  namespace: String             // 缓存命名空间
)
```

## 📊 性能考虑

### 内存使用优化

- 共享实例减少重复的缓存管理器
- 自动内存警告处理
- 可配置的内存限制
- 专注内存缓存，避免磁盘 IO 开销

### 并发安全

- Actor 模式保证 CacheManager 的线程安全
- MainActor 确保 QueryManager 在主线程操作
- 类型安全的请求去重

### 简化架构优势

- 减少代码复杂度，提高可维护性
- 避免磁盘缓存的一致性问题
- 更适合 iOS 应用的使用场景

## 🧪 测试支持

### 隔离性

- 每个测试用例使用独立的缓存实例
- 唯一的命名空间避免冲突
- 自动清理避免测试间影响

### 可控性

- 可配置的缓存行为
- 可预测的过期时间
- 可观察的缓存状态

## 📈 扩展性

### 新增缓存策略

- 通过 CacheConfiguration 添加新配置
- 在 CacheFactory 中添加新的工厂方法
- 保持向后兼容

### 新增存储策略

- CacheManager 的模块化设计支持扩展
- 通过依赖注入支持不同的缓存策略

### 新增查询模式

- QueryManager 的可扩展设计
- 支持新的缓存失效策略
- 支持新的数据同步模式

这种简化的架构设计专注于内存缓存管理，在保持简单易用的同时，提供了高效的性能和足够的灵活性来满足 iOS 应用的各种使用场景需求。
