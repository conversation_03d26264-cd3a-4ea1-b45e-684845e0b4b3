# Media Download & Save Architecture Design

## 概述

本文档描述了 ChatToDesign 应用中媒体（图片/视频）下载和保存到相册功能的通用架构设计方案。该方案提供了一个独立的、可复用的媒体处理模块，不与具体业务对象耦合，可以在应用的多个场景中使用。

## 问题分析

### 当前架构问题

1. **职责混乱**: CreateDetailPage 承担了过多责任
   - UI 展示逻辑
   - 下载逻辑
   - 缓存检查逻辑
   - 文件系统操作
   - 权限管理

2. **代码重复**: 下载逻辑在多个地方重复实现
   - CreateDetailPage 中的 downloadImage()
   - VideoSaver 中的 downloadAndSaveVideo()
   - 相似的错误处理和临时文件管理

3. **测试困难**: 业务逻辑与 UI 紧耦合，难以单独测试

4. **维护性差**: 修改下载逻辑需要改动 UI 层代码

### 用户体验问题

用户看到视频/图片时，媒体文件已经缓存在应用目录中，但保存到相册时仍需重新下载，造成：
- 不必要的网络流量消耗
- 用户等待时间过长
- 重复的网络请求

### 架构耦合问题

现有实现将媒体保存功能与具体业务对象（如 Asset）耦合，导致：
- 功能无法在其他场景复用（聊天图片、用户头像等）
- 业务逻辑与媒体处理逻辑混合
- 测试和维护困难

## 通用媒体模块架构设计

### 设计原则

1. **通用性**: 设计独立的媒体模块，不与具体业务对象耦合
2. **简洁性**: 避免过度设计，保持代码简单直接
3. **单一职责**: 每个组件只负责一个明确的功能
4. **可复用性**: 支持多个场景使用（Asset、Chat、Profile等）
5. **遵循现有架构**: 与项目现有的 Module + Service 模式保持一致
6. **可测试性**: 业务逻辑与 UI 分离，便于测试

### 架构层次

```
┌─────────────────────────────────────────┐
│              Presentation               │
│         CreateDetailPage.swift          │
│    (只负责 UI 展示和用户交互)              │
└─────────────────────────────────────────┘
                    │
                    ▼
┌─────────────────────────────────────────┐
│              ViewModel                  │
│       CreateDetailPageViewModel         │
│      (管理 UI 状态和调用 Service)         │
└─────────────────────────────────────────┘
                    │
                    ▼
┌─────────────────────────────────────────┐
│              Application                │
│            MediaService                 │
│      (通用媒体处理服务)                   │
└─────────────────────────────────────────┘
                    │
                    ▼
┌─────────────────────────────────────────┐
│              Use Cases                  │
│  SaveMediaToPhotosUseCase               │
│  DownloadMediaUseCase                   │
│      (具体媒体处理逻辑)                   │
└─────────────────────────────────────────┘
                    │
                    ▼
┌─────────────────────────────────────────┐
│            Infrastructure               │
│  VideoCacheManager / PhotoLibraryHelper │
│      (底层技术实现)                       │
└─────────────────────────────────────────┘
```

## 核心组件设计

### 1. MediaService 通用媒体服务

```swift
// MediaService.swift
/// 媒体处理服务协议 - 通用的媒体处理接口
public protocol MediaService {
    /// 保存媒体到相册（从 URL）
    func saveToPhotos(from url: URL, mediaType: MediaType) async -> Result<Void, MediaError>
    
    /// 保存媒体到相册（从数据）
    func saveToPhotos(data: Data, mediaType: MediaType) async -> Result<Void, MediaError>
    
    /// 下载媒体
    func downloadMedia(from url: URL) async -> Result<Data, MediaError>
    
    /// 检查媒体是否已缓存
    func isCached(url: URL, mediaType: MediaType) -> Bool
}

/// 媒体服务实现
final class MediaServiceImpl: MediaService {
    
    private let saveMediaToPhotosUseCase: SaveMediaToPhotosUseCase
    private let downloadMediaUseCase: DownloadMediaUseCase
    
    init(
        saveMediaToPhotosUseCase: SaveMediaToPhotosUseCase,
        downloadMediaUseCase: DownloadMediaUseCase
    ) {
        self.saveMediaToPhotosUseCase = saveMediaToPhotosUseCase
        self.downloadMediaUseCase = downloadMediaUseCase
    }
    
    func saveToPhotos(from url: URL, mediaType: MediaType) async -> Result<Void, MediaError> {
        return await saveMediaToPhotosUseCase.execute(from: url, mediaType: mediaType)
    }
    
    func saveToPhotos(data: Data, mediaType: MediaType) async -> Result<Void, MediaError> {
        return await saveMediaToPhotosUseCase.execute(data: data, mediaType: mediaType)
    }
    
    func downloadMedia(from url: URL) async -> Result<Data, MediaError> {
        return await downloadMediaUseCase.execute(from: url)
    }
    
    func isCached(url: URL, mediaType: MediaType) -> Bool {
        switch mediaType {
        case .video:
            return VideoCacheManager.shared.isCached(url: url)
        case .image:
            return ImageCache.default.isCached(forKey: url.absoluteString)
        }
    }
}

/// 媒体类型
public enum MediaType {
    case image
    case video
}
```

### 2. SaveMediaToPhotosUseCase

```swift
import Foundation
import Photos

/// 保存媒体到相册的业务逻辑
final class SaveMediaToPhotosUseCase {
    
    // MARK: - Dependencies
    
    private let photoLibraryHelper: PhotoLibraryHelper
    private let downloadMediaUseCase: DownloadMediaUseCase
    
    // MARK: - Initialization
    
    init(
        photoLibraryHelper: PhotoLibraryHelper = PhotoLibraryHelper(),
        downloadMediaUseCase: DownloadMediaUseCase = DownloadMediaUseCase()
    ) {
        self.photoLibraryHelper = photoLibraryHelper
        self.downloadMediaUseCase = downloadMediaUseCase
    }
    
    // MARK: - Public Methods
    
    /// 执行保存媒体到相册（从 URL）
    func execute(from url: URL, mediaType: MediaType) async -> Result<Void, MediaError> {
        do {
            // 1. 检查相册权限
            let hasPermission = await photoLibraryHelper.requestPermission()
            guard hasPermission else {
                return .failure(.photoLibraryPermissionDenied)
            }
            
            // 2. 获取媒体数据
            let mediaData = try await getMediaData(from: url, mediaType: mediaType)
            
            // 3. 保存到相册
            try await photoLibraryHelper.saveToPhotos(data: mediaData, mediaType: mediaType)
            
            // 4. 记录分析事件
            Logger.info("Media saved to photos from URL: \(url)")
            
            return .success(())
            
        } catch let error as MediaError {
            return .failure(error)
        } catch {
            return .failure(.unknown(error))
        }
    }
    
    /// 执行保存媒体到相册（从数据）
    func execute(data: Data, mediaType: MediaType) async -> Result<Void, MediaError> {
        do {
            // 1. 检查相册权限
            let hasPermission = await photoLibraryHelper.requestPermission()
            guard hasPermission else {
                return .failure(.photoLibraryPermissionDenied)
            }
            
            // 2. 保存到相册
            try await photoLibraryHelper.saveToPhotos(data: data, mediaType: mediaType)
            
            // 3. 记录分析事件
            Logger.info("Media saved to photos from data")
            
            return .success(())
            
        } catch let error as MediaError {
            return .failure(error)
        } catch {
            return .failure(.unknown(error))
        }
    }
    
    // MARK: - Private Methods
    
    private func getMediaData(from url: URL, mediaType: MediaType) async throws -> Data {
        // 优先使用缓存
        if mediaType == .video {
            if let cachedData = VideoCacheManager.shared.getCachedData(for: url) {
                Logger.info("Using cached video data for: \(url)")
                return cachedData
            }
        }
        
        if mediaType == .image {
            if let cachedData = ImageCache.default.retrieveImageInDiskCache(forKey: url.absoluteString)?.pngData() {
                Logger.info("Using cached image data for: \(url)")
                return cachedData
            }
        }
        
        // 无缓存：下载
        Logger.info("Downloading media from: \(url)")
        let result = await downloadMediaUseCase.execute(from: url)
        
        switch result {
        case .success(let data):
            return data
        case .failure(let error):
            throw error
        }
    }
}

/// 下载媒体的业务逻辑
final class DownloadMediaUseCase {
    
    /// 执行下载媒体
    func execute(from url: URL) async -> Result<Data, MediaError> {
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            return .success(data)
        } catch {
            return .failure(.downloadFailed(error.localizedDescription))
        }
    }
}

/// 媒体错误类型
public enum MediaError: LocalizedError {
    case invalidURL
    case downloadFailed(String)
    case photoLibraryPermissionDenied
    case photoLibrarySaveFailed(String)
    case unknown(Error)
    
    public var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid media URL"
        case .downloadFailed(let message):
            return "Download failed: \(message)"
        case .photoLibraryPermissionDenied:
            return "Photo library access denied"
        case .photoLibrarySaveFailed(let message):
            return "Failed to save: \(message)"
        case .unknown(let error):
            return error.localizedDescription
        }
    }
}
```

### 3. PhotoLibraryHelper

```swift
import Photos
import UIKit

/// 相册操作辅助类
final class PhotoLibraryHelper {
    
    /// 请求相册权限
    func requestPermission() async -> Bool {
        let status = PHPhotoLibrary.authorizationStatus(for: .addOnly)
        
        switch status {
        case .authorized, .limited:
            return true
        case .notDetermined:
            return await PHPhotoLibrary.requestAuthorization(for: .addOnly) == .authorized
        default:
            return false
        }
    }
    
    /// 保存媒体到相册
    func saveToPhotos(data: Data, mediaType: MediaType) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges({
                if mediaType == .video {
                    // 保存视频
                    let tempURL = self.createTempFile(data: data, fileExtension: "mp4")
                    PHAssetChangeRequest.creationRequestForAssetFromVideo(atFileURL: tempURL)
                } else {
                    // 保存图片
                    if let image = UIImage(data: data) {
                        PHAssetChangeRequest.creationRequestForAsset(from: image)
                    }
                }
            }) { success, error in
                if success {
                    continuation.resume()
                } else {
                    let message = error?.localizedDescription ?? "Unknown error"
                    continuation.resume(throwing: MediaError.photoLibrarySaveFailed(message))
                }
            }
        }
    }
    
    private func createTempFile(data: Data, fileExtension: String) -> URL {
        let tempDir = FileManager.default.temporaryDirectory
        let fileName = UUID().uuidString + "." + fileExtension
        let fileURL = tempDir.appendingPathComponent(fileName)
        try? data.write(to: fileURL)
        return fileURL
    }
}

public enum MediaType {
    case image
    case video
}
```

### 4. CreateDetailPageViewModel

```swift
@MainActor
final class CreateDetailPageViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var isDownloading = false
    @Published var showingDownloadAlert = false
    @Published var downloadMessage = ""
    @Published var isPlaying = false
    @Published var showRecreateView = false
    @Published var likeCount: Int = 0
    @Published var isLiked = false
    
    // MARK: - Private Properties
    
    private let asset: UserAsset
    private let mediaService: MediaService  // ✅ 使用通用的 MediaService
    private var player: AVPlayer?
    
    // MARK: - Initialization
    
    init(
        asset: UserAsset,
        mediaService: MediaService = AppDependencyContainer.shared.mediaModule.mediaService
    ) {
        self.asset = asset
        self.mediaService = mediaService
        self.likeCount = asset.likeCount
    }
    
    // MARK: - Public Methods
    
    func handleDownload() {
        Task {
            isDownloading = true
            
            guard let url = URL(string: asset.url) else {
                downloadMessage = "Invalid URL"
                showingDownloadAlert = true
                isDownloading = false
                return
            }
            
            let mediaType: MediaType = asset.type == .video ? .video : .image
            let result = await mediaService.saveToPhotos(from: url, mediaType: mediaType)
            
            isDownloading = false
            
            switch result {
            case .success:
                downloadMessage = "Successfully saved to Photos"
            case .failure(let error):
                downloadMessage = error.localizedDescription
            }
            
            showingDownloadAlert = true
        }
    }
    
    func handleLike() {
        // Toggle like state
        isLiked.toggle()
        likeCount += isLiked ? 1 : -1
        
        // TODO: Call like API through UseCase
        Logger.info("Like tapped for asset: \(asset.id), new count: \(likeCount)")
    }
    
    func handleShare() {
        // TODO: Implement share functionality through UseCase
        Logger.info("Share tapped for asset: \(asset.id)")
    }
    
    func handleRecreate() {
        Logger.info("Recreate tapped for asset: \(asset.id)")
        showRecreateView = true
    }
    
    // ... 视频播放相关方法保持不变 ...
}
```

### 5. 更新后的 CreateDetailPage

```swift
struct CreateDetailPage: View {
    let asset: UserAsset
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel: CreateDetailPageViewModel
    
    init(asset: UserAsset) {
        self.asset = asset
        self._viewModel = StateObject(
            wrappedValue: CreateDetailPageViewModel(asset: asset)
        )
    }
    
    var body: some View {
        // UI 代码保持不变，但现在调用 viewModel 的方法
        // 例如：
        // - 下载按钮调用 viewModel.handleDownload()
        // - 点赞按钮调用 viewModel.handleLike()
        // - 分享按钮调用 viewModel.handleShare()
    }
}
```

## 依赖注入设计

### MediaModule 设计

```swift
// MediaModule.swift
public final class MediaModule {
    
    private let dependencies: ModuleDependencies
    
    public init(dependencies: ModuleDependencies) {
        self.dependencies = dependencies
    }
    
    /// 媒体服务 - ViewModel 使用的接口
    public var mediaService: MediaService {
        MediaServiceImpl(
            saveMediaToPhotosUseCase: saveMediaToPhotosUseCase,
            downloadMediaUseCase: downloadMediaUseCase
        )
    }
    
    // MARK: - Private Use Cases
    
    private var saveMediaToPhotosUseCase: SaveMediaToPhotosUseCase {
        SaveMediaToPhotosUseCase(
            photoLibraryHelper: PhotoLibraryHelper(),
            downloadMediaUseCase: downloadMediaUseCase
        )
    }
    
    private var downloadMediaUseCase: DownloadMediaUseCase {
        DownloadMediaUseCase()
    }
}

// AppDependencyContainer.swift
extension AppDependencyContainer {
    
    /// 媒体模块
    public var mediaModule: MediaModule {
        MediaModule(dependencies: moduleDependencies)
    }
}
```

### 使用场景示例

```swift
// 1. 在 Asset 详情页中使用
class CreateDetailPageViewModel: ObservableObject {
    private let mediaService: MediaService
    
    init(mediaService: MediaService = AppDependencyContainer.shared.mediaModule.mediaService) {
        self.mediaService = mediaService
    }
    
    func saveAssetToPhotos(asset: UserAsset) {
        let url = URL(string: asset.url)!
        let mediaType: MediaType = asset.type == .video ? .video : .image
        await mediaService.saveToPhotos(from: url, mediaType: mediaType)
    }
}

```

## 错误处理设计

错误处理保持简洁，只定义必要的错误类型：

```swift
public enum MediaError: LocalizedError {
    case invalidURL
    case downloadFailed(String)
    case photoLibraryPermissionDenied
    case photoLibrarySaveFailed(String)
    case unknown(Error)
}
```

## 测试策略

### 单元测试

1. **SaveMediaToPhotosUseCase 测试**
   - 缓存命中场景
   - 缓存未命中场景
   - 权限拒绝场景
   - 下载失败场景

2. **DownloadMediaUseCase 测试**
   - 成功下载场景
   - 网络错误场景
   - 无效 URL 场景

3. **CreateDetailPageViewModel 测试**
   - 下载功能测试
   - 状态管理测试
   - 错误处理测试

4. **MediaService 测试**
   - 接口正确性测试
   - 不同媒体类型处理测试
   - 缓存检查功能测试


## 性能优化

1. **智能缓存利用**
   - 视频：优先使用 VideoCacheManager 缓存
   - 图片：优先使用 Kingfisher 缓存
   - 避免重复下载

2. **内存管理**
   - 及时清理临时文件
   - 使用 autoreleasepool 处理大文件

## 实施计划

### Phase 1: 基础实现 ✅
- [x] 创建 SaveMediaToPhotosUseCase
- [x] 创建 DownloadMediaUseCase
- [x] 创建 PhotoLibraryHelper
- [ ] 创建 MediaModule 和 MediaService

### Phase 2: UI 重构 📋
- [ ] 创建 CreateDetailPageViewModel
- [ ] 重构 CreateDetailPage 使用 ViewModel
- [ ] 移除 UI 层的下载逻辑

### Phase 3: 测试完善 🧪
- [ ] 编写单元测试
- [ ] 集成测试
- [ ] 性能测试

## 总结

这个通用媒体模块架构设计方案：

### 优点
- ✅ **通用性强**: 独立的媒体模块，可在多个场景使用
- ✅ **简洁性**: 只有必要的层次，避免过度设计
- ✅ **清晰性**: 职责分明，易于理解
- ✅ **可复用性**: 支持 Asset、Chat、Profile 等多种场景
- ✅ **可测试性**: 业务逻辑独立，便于测试
- ✅ **性能优化**: 智能利用缓存，减少网络请求
- ✅ **符合现有架构**: 遵循项目的 Module + Service 模式

### 关键改进
1. **解耦业务对象**: 不再与 Asset 等具体对象耦合
2. **UI 层纯净**: CreateDetailPage 只负责展示
3. **ViewModel 专注**: 管理 UI 状态，调用 MediaService
4. **服务独立**: MediaService 提供通用的媒体处理功能
5. **缓存优先**: 避免重复下载

### 使用场景
该通用媒体模块可以在以下场景中使用：
- **Asset 详情页**: 保存生成的图片/视频到相册
- **聊天页面**: 保存聊天中的图片到相册
- **用户头像**: 保存头像图片到相册
- **模板预览**: 保存模板预览图到相册
- **任意媒体下载**: 从 URL 下载媒体文件

这个方案既保持了架构的清晰性和可维护性，又提供了优秀的通用性和可复用性，是一个实用且高效的解决方案。
- ✅ **性能优化**: 智能利用缓存，减少网络请求
- ✅ **符合现有架构**: 遵循项目的 UseCase 模式

### 关键改进
1. **UI 层纯净**: CreateDetailPage 只负责展示
2. **ViewModel 专注**: 管理 UI 状态，调用 UseCase
3. **UseCase 独立**: 封装完整的业务逻辑
4. **缓存优先**: 避免重复下载

这个方案保持了架构的清晰性和可维护性，同时避免了不必要的复杂性，是一个实用且高效的解决方案。