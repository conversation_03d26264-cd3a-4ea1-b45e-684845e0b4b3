# 缓存系统使用示例

## 🎯 概述

本文档展示了如何使用简化后的 SWR 缓存系统，专注于高效的内存缓存管理。

## 📋 主要特性

### 1. 简化的架构

- ✅ 专注于内存缓存，避免磁盘 IO 复杂性
- ✅ 支持自定义缓存配置
- ✅ 支持命名空间隔离
- ✅ 并发安全的 Actor 模式

### 2. 请求去重优化

- ✅ 类型安全的请求去重
- ✅ 自动清理完成的查询
- ✅ 避免内存泄漏

### 3. 状态管理改进

- ✅ 精确的加载状态控制
- ✅ 更好的错误处理
- ✅ 自动取消之前的请求

## 🚀 使用示例

### 1. 获取和创建缓存管理器

```swift
// 使用默认共享实例（内存缓存）
let defaultCache = CacheFactory.defaultCache

// 创建自定义配置的缓存管理器
let customConfig = CacheConfiguration(
  maxMemoryCacheSize: 200 * 1024 * 1024, // 200MB
  maxCacheAge: 30 * 60, // 30分钟
  memoryCacheCountLimit: 5000,
  namespace: "custom_app"
)
let customCache = CacheFactory.createCache(configuration: customConfig)

// 创建测试用缓存（每次都是新实例）
let testCache = CacheFactory.createTestCache()
```

### 1.1 获取和创建查询管理器

```swift
// 使用默认查询管理器
let defaultQueryManager = await CacheFactory.defaultQueryManager

// 创建使用自定义缓存的查询管理器
let customQueryManager = await CacheFactory.createQueryManager(cacheManager: customCache)

// 创建测试用查询管理器
let testQueryManager = await CacheFactory.createTestQueryManager()
```

### 2. 在 ViewModel 中使用 SWRHook

```swift
@MainActor
class UserProfileViewModel: ObservableObject {
  @Published var user: User?
  @Published var isLoading = false
  @Published var error: Error?

  private var userHook: SWRHook<User>?
  private let apiService: APIService

  init(apiService: APIService, userId: String) {
    self.apiService = apiService
    setupUserHook(userId: userId)
  }

  private func setupUserHook(userId: String) {
    userHook = SWRHook.create(
      key: "user_\(userId)",
      maxAge: 5 * 60,    // 5分钟后触发后台刷新
      staleTime: 30 * 60, // 30分钟后数据完全过期
      networkCall: { [weak self] in
        guard let self = self else {
          throw APIServiceError.unknown(NSError(domain: "ViewModel", code: -1))
        }
        return try await self.apiService.getUser(id: userId)
      },
      autoFetch: true
    )

    // 监听状态变化
    observeUserHookChanges()
  }

  private func observeUserHookChanges() {
    guard let userHook = userHook else { return }

    userHook.$data
      .receive(on: DispatchQueue.main)
      .assign(to: &$user)

    userHook.$isLoading
      .receive(on: DispatchQueue.main)
      .assign(to: &$isLoading)

    userHook.$error
      .receive(on: DispatchQueue.main)
      .assign(to: &$error)
  }

  func refreshUser() {
    userHook?.refresh()
  }

  func updateUser(_ newUser: User) {
    // 乐观更新
    userHook?.mutate(newUser)
  }
}
```

### 3. 直接使用 QueryManager

```swift
@MainActor
class DataService: ObservableObject {
  private let queryManager: QueryManager
  private var cancellables = Set<AnyCancellable>()

  init(queryManager: QueryManager = QueryManager.shared) {
    self.queryManager = queryManager
  }

  func loadData<T: Codable>(
    key: String,
    type: T.Type,
    networkCall: @escaping () async throws -> T
  ) -> AnyPublisher<QueryResult<T>, Never> {
    return queryManager.query(
      key: key,
      maxAge: 300,     // 5分钟
      staleTime: 1800, // 30分钟
      networkCall: networkCall
    )
  }

  func mutateData<T: Codable>(
    key: String,
    invalidates: [String] = [],
    networkCall: @escaping () async throws -> T
  ) -> AnyPublisher<T, Error> {
    return queryManager.mutate(
      key: key,
      invalidates: invalidates,
      networkCall: networkCall
    )
  }
}
```

### 4. 使用自定义配置的场景

```swift
// 适用于需要特殊配置的数据
@MainActor
class CustomDataViewModel: ObservableObject {
  private let customQueryManager: QueryManager

  init() async {
    // 创建短期缓存配置
    let shortTermConfig = CacheConfiguration(
      maxMemoryCacheSize: 50 * 1024 * 1024, // 50MB
      maxCacheAge: 60, // 1分钟
      memoryCacheCountLimit: 500,
      namespace: "short_term"
    )
    let customCache = CacheFactory.createCache(configuration: shortTermConfig)
    self.customQueryManager = await CacheFactory.createQueryManager(cacheManager: customCache)
  }

  func loadShortTermData() {
    customQueryManager
      .query(
        key: "short_term_data",
        maxAge: 60,      // 1分钟
        staleTime: 300,  // 5分钟
        networkCall: {
          return try await self.loadFromAPI()
        }
      )
      .receive(on: DispatchQueue.main)
      .sink { result in
        // 处理结果
      }
      .store(in: &cancellables)
  }
}
```

### 5. 缓存失效和清理

```swift
class CacheManagementService {
  private let queryManager: QueryManager

  init(queryManager: QueryManager = QueryManager.shared) {
    self.queryManager = queryManager
  }

  // 失效特定缓存
  func invalidateUserData(userId: String) async {
    await queryManager.invalidateQueries(keys: [
      "user_\(userId)",
      "user_profile_\(userId)",
      "user_settings_\(userId)"
    ])
  }

  // 清除所有缓存
  func clearAllCache() async {
    await queryManager.clearAllCache()
  }

  // 获取缓存大小
  func getCacheSize() async -> Int {
    return await CacheManager.shared.getCacheSize()
  }
}
```

## 🔧 配置选项说明

### CacheConfiguration 参数

- `maxMemoryCacheSize`: 内存缓存大小限制（字节）
- `maxCacheAge`: 缓存最大保存时间（秒）
- `memoryCacheCountLimit`: 内存缓存项数量限制
- `namespace`: 缓存命名空间，用于隔离不同模块的缓存

### 预设配置

- `.default`: 默认配置，100MB 内存缓存，2000 项限制，1 天过期

## 📊 性能优化建议

1. **选择合适的缓存策略**

   - 所有数据都使用内存缓存，快速高效
   - 临时数据使用较短的过期时间
   - 重要数据使用较长的过期时间

2. **合理设置缓存时间**

   - `maxAge`: 控制后台刷新频率
   - `staleTime`: 控制数据完全过期时间
   - 根据数据更新频率调整

3. **使用命名空间**

   - 不同模块使用不同命名空间
   - 避免缓存键冲突
   - 便于模块化管理

4. **及时清理缓存**
   - 在适当时机失效相关缓存
   - 监听内存警告自动清理
   - 合理设置缓存大小限制

## 🧪 测试支持

```swift
class CacheTests: XCTestCase {
  var testCache: CacheManager!
  var testQueryManager: QueryManager!

  override func setUp() {
    super.setUp()
    testCache = CacheFactory.createForTesting()
    testQueryManager = QueryManager(cacheManager: testCache)
  }

  func testCacheHitAndMiss() async {
    // 测试缓存命中和未命中的情况
  }

  func testSWRBehavior() async {
    // 测试 stale-while-revalidate 行为
  }
}
```

这个简化后的缓存系统专注于内存缓存管理，提供了更好的性能、简洁性和可靠性，同时保持了简单易用的 API。
