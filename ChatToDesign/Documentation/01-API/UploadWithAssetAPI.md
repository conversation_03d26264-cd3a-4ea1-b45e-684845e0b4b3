\*\*\*\*# 上传文件并创建资产 API 使用指南

本文档介绍如何在 ChatToDesign 应用中使用上传文件并创建资产 API 接口。

## 概述

上传文件并创建资产 API 提供了将文件上传到远程服务器并同时创建资产记录的功能，支持多种文件类型包括图片、视频、音频和文档。与普通文件上传不同，此 API 会在上传文件的同时创建一个资产记录，包含元数据、标签、描述等信息。

### API 端点

```
POST https://api-test.picadabra.ai/api/v1/file-upload/with-asset
```

### 请求格式

```json
{
  "mimeType": "image/jpeg",
  "base64Data": "UklGRthTAABXRUJQVlA4IMxTAACQWgKdASoABAAEPm02mUkkKTMsozOYamANiWdu+8oNFA3EgUr+7ODbihOQptsjDcn5dXMZCxcyBQK/ewABQ8C9LC0eLwt1HT3V/btHKcPHnj6ZSUhZA7AdwAAAAA/IL9AAAAAAuknMTOC/3pvPcmEuAAAAAA==",
  "prefix": "uploads/images",
  "fileName": "profile-picture",
  "tags": ["profile", "user", "avatar"],
  "description": "User profile picture",
  "metadata": {
    "camera": "iPhone 14",
    "location": "Office"
  },
  "isPublic": false
}
```

### 响应格式

```json
{
  "id": "asset_123456789",
  "userId": "user_987654321",
  "storageUrl": "https://twitter-r2.a1d.ai/uploads/images/profile-picture.jpeg",
  "thumbnailUrl": "https://twitter-r2.a1d.ai/uploads/images/profile-picture_thumb.jpeg",
  "path": "uploads/images/profile-picture.jpeg",
  "bucket": "twitter-r2",
  "originalFilename": "profile-picture.jpeg",
  "fileSize": 1024000,
  "mimeType": "image/jpeg",
  "fileType": "image",
  "tags": ["profile", "user", "avatar"],
  "description": "User profile picture",
  "metadata": {
    "camera": "iPhone 14",
    "location": "Office"
  },
  "isPublic": false,
  "status": "active",
  "createdAt": "2025-03-20T10:30:00.000Z"
}
```

## 架构组件

### 1. 数据模型

#### UploadWithAssetRequest

```swift
public struct UploadWithAssetRequest: Codable {
    public let mimeType: String         // MIME类型
    public let base64Data: String       // Base64编码的文件数据
    public let prefix: String           // 文件路径前缀
    public let fileName: String         // 文件名
    public let tags: [String]?          // 标签（可选）
    public let description: String?     // 描述（可选）
    public let metadata: [String: AnyCodable]? // 元数据（可选）
    public let isPublic: Bool?          // 是否公开（可选）
}
```

#### AssetResponse

```swift
public struct AssetResponse: Codable {
    public let id: String               // 资产ID
    public let userId: String           // 用户ID
    public let storageUrl: String       // 存储URL
    public let thumbnailUrl: String?    // 缩略图URL
    public let path: String             // 存储路径
    public let bucket: String           // 存储桶
    public let originalFilename: String // 原始文件名
    public let fileSize: Int            // 文件大小
    public let mimeType: String         // MIME类型
    public let fileType: String         // 文件类型
    public let tags: [String]?          // 标签
    public let description: String?     // 描述
    public let metadata: [String: AnyCodable]? // 元数据
    public let isPublic: Bool           // 是否公开
    public let status: String           // 状态
    public let createdAt: String        // 创建时间
}
```

### 2. 服务层

#### APIService

低级 API 服务，直接与 HTTP 端点交互：

```swift
func uploadFileWithAsset(request: UploadWithAssetRequest) async throws -> AssetResponse
```

#### AssetUploadService

高级资产上传服务，提供验证、错误处理和便利方法：

```swift
func uploadFileWithAsset(
    data: Data,
    mimeType: String,
    fileName: String,
    prefix: String?,
    tags: [String]?,
    description: String?,
    metadata: [String: AnyCodable]?,
    isPublic: Bool?
) async throws -> AssetResponse

func uploadImageWithAsset(
    image: UIImage,
    fileName: String,
    quality: CGFloat,
    prefix: String?,
    tags: [String]?,
    description: String?,
    metadata: [String: AnyCodable]?,
    isPublic: Bool?
) async throws -> AssetResponse
```

## 使用方法

### 1. 基本文件上传并创建资产

```swift
// 创建资产上传服务
let apiService = DefaultAPIService()
let assetUploadService = DefaultAssetUploadService(apiService: apiService)

// 上传文件并创建资产
do {
    let asset = try await assetUploadService.uploadFileWithAsset(
        data: fileData,
        mimeType: "image/jpeg",
        fileName: "my-image",
        prefix: "uploads/images",
        tags: ["photo", "user"],
        description: "User uploaded photo",
        metadata: ["source": AnyCodable("mobile_app")],
        isPublic: false
    )
    print("资产创建成功: \(asset.storageUrl)")
    print("资产ID: \(asset.id)")
} catch {
    print("上传失败: \(error)")
}
```

### 2. 上传图片并创建资产

```swift
// 上传UIImage并创建资产
let image = UIImage(named: "photo")!
do {
    let asset = try await assetUploadService.uploadImageWithAsset(
        image: image,
        fileName: "user-avatar",
        quality: 0.8,
        prefix: "uploads/avatars",
        tags: ["avatar", "profile"],
        description: "User profile avatar",
        metadata: ["device": AnyCodable("iPhone")],
        isPublic: true
    )
    print("图片资产创建成功: \(asset.storageUrl)")
    if let thumbnailUrl = asset.thumbnailUrl {
        print("缩略图URL: \(thumbnailUrl)")
    }
} catch {
    print("图片上传失败: \(error)")
}
```

### 3. 直接使用 API 服务

```swift
// 创建上传请求
let request = UploadWithAssetRequest.from(
    data: imageData,
    mimeType: "image/jpeg",
    prefix: "uploads/images",
    fileName: "profile-picture",
    tags: ["profile", "user"],
    description: "User profile picture",
    metadata: ["camera": AnyCodable("iPhone 14")],
    isPublic: false
)

// 执行上传
do {
    let asset = try await apiService.uploadFileWithAsset(request: request)
    print("资产URL: \(asset.storageUrl)")
    print("资产ID: \(asset.id)")
} catch {
    print("上传失败: \(error)")
}
```

## 特性

### 1. 自动资产管理

- 自动创建资产记录
- 支持标签和描述
- 支持自定义元数据
- 支持公开/私有设置

### 2. 文件类型支持

- 图片：JPEG、PNG、GIF、WebP
- 视频：MP4、QuickTime
- 音频：MP3、WAV
- 文档：PDF、文本文件

### 3. 元数据支持

- 支持任意键值对元数据
- 使用 AnyCodable 支持多种数据类型
- 自动合并上传元数据和用户元数据

### 4. 错误处理

- 详细的错误信息
- 网络错误重试机制
- 文件验证错误提示

## 配置选项

使用与 FileUploadService 相同的配置：

```swift
let config = FileUploadConfiguration(
    maxFileSize: 10 * 1024 * 1024,  // 10MB
    supportedMimeTypes: [
        "image/jpeg",
        "image/png",
        "video/mp4",
        "application/pdf"
    ],
    defaultPrefix: "uploads/images"
)

let assetUploadService = DefaultAssetUploadService(
    apiService: apiService,
    configuration: config
)
```

## 获取用户资产 API

### API 端点

```
GET https://api-test.picadabra.ai/api/v1/assets
```

### 查询参数

| 参数     | 类型   | 必需 | 默认值 | 描述                                                 |
| -------- | ------ | ---- | ------ | ---------------------------------------------------- |
| page     | number | 否   | 1      | 页码（从 1 开始）                                    |
| limit    | number | 否   | 20     | 每页数量（1-100）                                    |
| fileType | string | 否   | -      | 文件类型过滤（image, video, audio, document, other） |
| status   | string | 否   | -      | 状态过滤（active, deleted）                          |
| tags     | string | 否   | -      | 标签过滤（逗号分隔）                                 |
| search   | string | 否   | -      | 搜索关键词（在文件名或描述中搜索）                   |

### 响应格式

```json
{
  "assets": [
    {
      "id": "asset_123456789",
      "userId": "user_987654321",
      "storageUrl": "https://twitter-r2.a1d.ai/uploads/images/profile-picture.jpeg",
      "thumbnailUrl": "https://twitter-r2.a1d.ai/uploads/images/profile-picture_thumb.jpeg",
      "path": "uploads/images/profile-picture.jpeg",
      "bucket": "twitter-r2",
      "originalFilename": "profile-picture.jpeg",
      "fileSize": 1024000,
      "mimeType": "image/jpeg",
      "fileType": "image",
      "tags": ["profile", "user", "avatar"],
      "description": "User profile picture",
      "metadata": {
        "camera": "iPhone 14",
        "location": "Office"
      },
      "isPublic": false,
      "status": "active",
      "createdAt": "2025-03-20T10:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### 使用示例

```swift
// 获取用户资产列表
let query = AssetListQuery(
    page: 1,
    limit: 20,
    fileType: "image",
    status: "active",
    tags: "profile,avatar",
    search: "profile"
)

do {
    let response = try await apiService.getUserAssets(query: query)
    print("获取到 \(response.assets.count) 个资产")
    print("总共 \(response.pagination.total) 个资产")
} catch {
    print("获取失败: \(error)")
}

// 使用AssetManagementService
let assetService = DefaultAssetManagementService(apiService: apiService)

// 获取用户的所有图片
let images = try await assetService.getUserImages(limit: 50)

// 搜索资产
let searchResults = try await assetService.searchAssets(
    searchTerm: "profile",
    fileType: "image",
    limit: 20
)

// 按标签获取资产
let taggedAssets = try await assetService.getAssetsByTags(
    tags: ["avatar", "profile"],
    limit: 30
)
```

## 注意事项

1. **认证要求**：此端点需要 Firebase ID token 认证
2. **文件大小限制**：默认最大 10MB，可通过配置调整
3. **元数据限制**：元数据应保持合理大小，避免过大的 JSON 对象
4. **标签规范**：建议使用小写字母和连字符的标签格式
5. **公开设置**：谨慎设置 isPublic 为 true，确保文件适合公开访问
6. **分页性能**：建议使用合理的页面大小（10-50），避免一次性加载过多数据
7. **搜索优化**：搜索功能在服务端实现，支持文件名和描述的模糊匹配
8. **标签过滤**：多个标签使用逗号分隔，支持 AND 逻辑（资产必须包含所有指定标签）
