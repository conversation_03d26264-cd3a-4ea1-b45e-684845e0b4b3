# 删除资产 API 使用指南

本文档介绍如何在 ChatToDesign 应用中使用删除资产 API 接口。

## 概述

删除资产 API 提供了软删除用户资产的功能。用户只能删除自己的资产，删除操作会将资产状态设置为 'deleted' 而不是物理删除。

### API 端点

```
DELETE https://api-test.picadabra.ai/api/v1/assets/{id}
```

### 请求参数

#### 路径参数

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| id | string | 是 | 要删除的资产ID |

### 请求头

```
Authorization: Bearer <Firebase_ID_Token>
Content-Type: application/json
```

### 响应格式

#### 成功响应 (200)

```json
{
  "message": "Asset deleted successfully",
  "id": "asset_id_here"
}
```

#### 错误响应

**401 - 认证错误**
```json
{
  "error": "Authentication required"
}
```

**404 - 资产未找到或无权限**
```json
{
  "error": "Asset not found or access denied"
}
```

**500 - 服务器错误**
```json
{
  "error": "Failed to delete asset"
}
```

## 使用示例

### 1. 使用 AssetManagementService

```swift
// 通过资产ID删除
do {
    let response = try await assetManagementService.deleteAsset(id: "asset_id")
    print("删除成功: \(response.message)")
    print("删除的资产ID: \(response.id)")
} catch {
    print("删除失败: \(error)")
}

// 通过资产对象删除（便捷方法）
let asset: AssetResponse = // ... 获取的资产对象
do {
    let response = try await assetManagementService.deleteAsset(asset)
    print("删除成功: \(response.message)")
} catch {
    print("删除失败: \(error)")
}
```

### 2. 在 ViewModel 中使用

```swift
class AssetViewModel: ObservableObject {
    @Published var assets: [AssetResponse] = []
    @Published var isDeleting = false
    
    private let assetManagementService: AssetManagementService
    
    func deleteAsset(_ asset: AssetResponse) {
        Task {
            await MainActor.run {
                self.isDeleting = true
            }
            
            do {
                let response = try await assetManagementService.deleteAsset(asset)
                await MainActor.run {
                    // 从本地列表中移除已删除的资产
                    self.assets.removeAll { $0.id == asset.id }
                    self.isDeleting = false
                }
            } catch {
                await MainActor.run {
                    self.isDeleting = false
                    // 处理错误
                }
            }
        }
    }
}
```

### 3. 在 SwiftUI 视图中使用

```swift
struct AssetListView: View {
    @StateObject private var viewModel = AssetViewModel()
    
    var body: some View {
        List(viewModel.assets, id: \.id) { asset in
            HStack {
                AsyncImage(url: URL(string: asset.thumbnailUrl ?? asset.storageUrl))
                    .frame(width: 50, height: 50)
                
                VStack(alignment: .leading) {
                    Text(asset.originalFilename)
                    Text(asset.createdAt)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Button("删除") {
                    viewModel.deleteAsset(asset)
                }
                .foregroundColor(.red)
                .disabled(viewModel.isDeleting)
            }
        }
        .overlay(
            Group {
                if viewModel.isDeleting {
                    ProgressView("删除中...")
                }
            }
        )
    }
}
```

## 错误处理

删除资产时可能遇到以下错误：

### AssetManagementError

- `networkError`: 网络连接错误
- `apiError`: API 返回的错误（包含状态码和错误消息）
- `parsingError`: 响应解析错误
- `unknown`: 未知错误

### 错误处理示例

```swift
do {
    let response = try await assetManagementService.deleteAsset(id: assetId)
    // 处理成功
} catch let error as AssetManagementError {
    switch error {
    case .networkError(let networkError):
        print("网络错误: \(networkError.localizedDescription)")
    case .apiError(let statusCode, let message):
        if statusCode == 404 {
            print("资产不存在或无权限删除")
        } else {
            print("API错误 (\(statusCode)): \(message)")
        }
    case .parsingError(let parsingError):
        print("解析错误: \(parsingError.localizedDescription)")
    case .unknown(let unknownError):
        print("未知错误: \(unknownError.localizedDescription)")
    }
} catch {
    print("其他错误: \(error.localizedDescription)")
}
```

## 注意事项

1. **权限控制**: 用户只能删除自己的资产
2. **软删除**: 资产不会被物理删除，只是状态变为 'deleted'
3. **认证要求**: 需要有效的 Firebase ID Token
4. **错误处理**: 建议实现完整的错误处理逻辑
5. **UI 反馈**: 删除操作应提供适当的用户反馈（加载状态、确认对话框等）

## 相关 API

- [获取用户资产列表 API](./UserAssetsAPI.md)
- [上传文件并创建资产 API](./UploadWithAssetAPI.md)
- [文件上传 API](./FileUploadAPI.md)
