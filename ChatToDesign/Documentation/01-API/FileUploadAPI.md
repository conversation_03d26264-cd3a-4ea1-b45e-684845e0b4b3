# 文件上传API使用指南

本文档介绍如何在ChatToDesign应用中使用文件上传API接口。

## 概述

文件上传API提供了将文件上传到远程服务器的功能，支持多种文件类型包括图片、视频、音频和文档。

### API端点

```
POST https://pic-api.a1d.ai/api/v1/file-upload
```

### 请求格式

```json
{
  "mimeType": "image/jpeg",
  "base64Data": "UklGRthTAABXRUJQVlA4IMxTAACQWgKdASoABAAEPm02mUkkKTMsozOYamANiWdu+8oNFA3EgUr+7ODbihOQptsjDcn5dXMZCxcyBQK/ewABQ8C9LC0eLwt1HT3V/btHKcPHnj6ZSUhZA7AdwAAAAA/IL9AAAAAAuknMTOC/3pvPcmEuAAAAAA==",
  "prefix": "uploads/images",
  "fileName": "profile-picture"
}
```

### 响应格式

```json
{
  "url": "https://twitter-r2.a1d.ai/uploads/images/profile-picture.jpeg"
}
```

## 架构组件

### 1. 数据模型

#### FileUploadRequest
```swift
public struct FileUploadRequest: Codable {
    public let mimeType: String      // MIME类型
    public let base64Data: String    // Base64编码的文件数据
    public let prefix: String        // 文件路径前缀
    public let fileName: String      // 文件名
}
```

#### FileUploadResponse
```swift
public struct FileUploadResponse: Codable {
    public let url: String          // 上传后的文件URL
}
```

### 2. 服务层

#### APIService
低级API服务，直接与HTTP端点交互：
```swift
func uploadFile(request: FileUploadRequest) async throws -> FileUploadResponse
```

#### FileUploadService
高级文件上传服务，提供验证、错误处理和便利方法：
```swift
func uploadFile(data: Data, mimeType: String, fileName: String, prefix: String?) async throws -> URL
func uploadImage(image: UIImage, fileName: String, quality: CGFloat, prefix: String?) async throws -> URL
```

## 使用方法

### 1. 基本文件上传

```swift
// 创建文件上传服务
let apiService = DefaultAPIService()
let fileUploadService = DefaultFileUploadService(apiService: apiService)

// 上传文件
do {
    let url = try await fileUploadService.uploadFile(
        data: fileData,
        mimeType: "image/jpeg",
        fileName: "my-image"
    )
    print("上传成功: \(url)")
} catch {
    print("上传失败: \(error)")
}
```

### 2. 上传图片

```swift
// 上传UIImage
let image = UIImage(named: "photo")!
do {
    let url = try await fileUploadService.uploadImage(
        image: image,
        fileName: "user-avatar",
        quality: 0.8,
        prefix: "uploads/avatars"
    )
    print("图片上传成功: \(url)")
} catch {
    print("图片上传失败: \(error)")
}
```

### 3. 直接使用API服务

```swift
// 创建上传请求
let request = FileUploadRequest.from(
    data: imageData,
    mimeType: "image/jpeg",
    prefix: "uploads/images",
    fileName: "profile-picture"
)

// 执行上传
do {
    let response = try await apiService.uploadFile(request: request)
    print("文件URL: \(response.url)")
} catch {
    print("上传失败: \(error)")
}
```

## 配置选项

### FileUploadConfiguration

```swift
let config = FileUploadConfiguration(
    maxFileSize: 10 * 1024 * 1024,  // 10MB
    supportedMimeTypes: [
        "image/jpeg",
        "image/png",
        "video/mp4",
        "application/pdf"
    ],
    defaultPrefix: "uploads/images"
)

let fileUploadService = DefaultFileUploadService(
    apiService: apiService,
    configuration: config
)
```

## 错误处理

### FileUploadError类型

```swift
public enum FileUploadError: Error {
    case invalidFileData              // 无效的文件数据
    case invalidMimeType             // 无效的MIME类型
    case fileTooLarge(maxSize: Int)  // 文件太大
    case unsupportedFileType(String) // 不支持的文件类型
    case networkError(Error)         // 网络错误
    case apiError(statusCode: Int, message: String) // API错误
    case parsingError(Error)         // 解析错误
    case unknown(Error)              // 未知错误
}
```

### 错误处理示例

```swift
do {
    let url = try await fileUploadService.uploadFile(...)
} catch let error as FileUploadError {
    switch error {
    case .fileTooLarge(let maxSize):
        showAlert("文件太大，最大支持 \(maxSize) 字节")
    case .unsupportedFileType(let type):
        showAlert("不支持的文件类型: \(type)")
    case .networkError(let networkError):
        showAlert("网络错误: \(networkError.localizedDescription)")
    case .apiError(let statusCode, let message):
        showAlert("服务器错误 (\(statusCode)): \(message)")
    default:
        showAlert("上传失败: \(error.localizedDescription)")
    }
}
```

## 依赖注入

### 在StorageModule中使用

```swift
// 创建存储模块
let storageService = FirebaseStorageAdapter()
let apiService = DefaultAPIService()
let storageModule = StorageModule(
    storageService: storageService,
    apiService: apiService
)

// 使用文件上传服务
let fileUploadService = storageModule.fileUploadService
```

## 支持的文件类型

默认配置支持以下文件类型：

- **图片**: `image/jpeg`, `image/png`, `image/gif`, `image/webp`
- **视频**: `video/mp4`, `video/quicktime`
- **音频**: `audio/mpeg`, `audio/wav`
- **文档**: `application/pdf`, `text/plain`

## 最佳实践

1. **文件大小限制**: 默认最大10MB，可根据需要调整
2. **图片压缩**: 上传图片时建议使用适当的压缩质量（0.7-0.9）
3. **错误处理**: 始终处理可能的上传错误
4. **文件验证**: 在上传前验证文件类型和大小
5. **进度反馈**: 对于大文件，考虑显示上传进度

## 示例代码

完整的使用示例请参考 `ChatToDesign/Examples/FileUploadExample.swift` 文件。
