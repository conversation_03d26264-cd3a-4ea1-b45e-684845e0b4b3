# 套餐选择弹窗实现方案

## 概述

本文档详细说明了在 PaywallFooterView 中点击"annul"按钮时弹出套餐选择弹窗的技术实现方案。该弹窗将根据当前 Figma 设计显示订阅套餐选项。

## 现状分析

### 现有组件

- **PaywallFooterView**: 包含定价信息、购买按钮和法律链接
- **PaywallView**: 主要付费墙容器，包含头部、功能对比和底部
- **PaywallViewModel**: 处理付费墙状态管理和业务逻辑
- **ProductInfo**: 产品信息模型（已替代 PaywallPricing）

### 现有数据模型

- `ProductInfo`: 产品信息模型，包含完整的产品数据和结构化的周期信息
- `ProductInfo`: RevenueCat 产品信息
- `UserTier`: 免费版和专业版层级定义
- `EntitlementType`: 专业版权限类型

## 设计需求（基于 Figma）

### 视觉设计

- **底部弹窗**: 深色背景 (#333333)，顶部圆角 (24px)
- **头部**: 拖拽指示器（深灰色 #252525）
- **套餐卡片**: 两个并排卡片，圆角 (16px)
  - 周套餐: $2.99，3 天免费试用
  - 年套餐: $49.99，3 天免费试用（推荐，蓝色边框和勾选标记）
- **字体**: Inter 字体系列，特定字重和尺寸
- **颜色**: 深色主题，白色文字，蓝色强调色 (#536db0) 用于选中套餐

### 交互设计

- 点击"annul"按钮时显示弹窗
- 年套餐预选中（推荐）
- 用户可以点击选择不同套餐
- 可通过向下拖拽或点击外部区域关闭弹窗

## Technical Implementation Plan

### 1. Create Plan Selection Models

#### SubscriptionPlan Model

```swift
public struct SubscriptionPlan: Identifiable, Codable, Equatable {
    public let id: String
    public let productId: String
    public let title: String           // "Weekly", "Annual"
    public let price: String           // "$2.99", "$49.99"
    public let period: String          // "Weekly", "yearly"
    public let trialInfo: String       // "3-day free trial, then 2.99/Weekly"
    public let isRecommended: Bool     // true for Annual
    public let badge: String?          // "Destructive" for Annual plan
}
```

#### PlanSelectionViewModel

```swift
@MainActor
public final class PlanSelectionViewModel: ObservableObject {
    @Published public var availablePlans: [SubscriptionPlan] = []
    @Published public var selectedPlan: SubscriptionPlan?
    @Published public var isLoading: Bool = false

    public func loadPlans()
    public func selectPlan(_ plan: SubscriptionPlan)
    public func getRecommendedPlan() -> SubscriptionPlan?
}
```

### 2. Create Plan Selection Sheet Component

#### PlanSelectionSheet

```swift
public struct PlanSelectionSheet: View {
    @ObservedObject var viewModel: PlanSelectionViewModel
    @Binding var isPresented: Bool
    let onPlanSelected: (SubscriptionPlan) -> Void

    // UI Components:
    // - Sheet header with drag indicator
    // - Plan cards grid (2 columns)
    // - Plan card with selection state
    // - Recommended badge for Annual plan
}
```

#### PlanCard Component

```swift
private struct PlanCard: View {
    let plan: SubscriptionPlan
    let isSelected: Bool
    let onTap: () -> Void

    // Features:
    // - Border color changes based on selection
    // - Recommended badge overlay
    // - Check mark for selected plan
    // - Proper typography and spacing
}
```

### 3. Integrate with PaywallFooterView

#### Add State Management

```swift
// In PaywallFooterView
@State private var showPlanSelection = false
@StateObject private var planSelectionViewModel = PlanSelectionViewModel()
```

#### Add Annul Button

```swift
// Replace or add alongside legal link
private var annulButton: some View {
    Button("Annul") {
        showPlanSelection = true
    }
    .foregroundColor(PaywallDesignSystem.Colors.mutedForeground)
    .font(PaywallDesignSystem.Typography.textSmallNormal)
}
```

#### Add Sheet Presentation

```swift
// In body view
.sheet(isPresented: $showPlanSelection) {
    PlanSelectionSheet(
        viewModel: planSelectionViewModel,
        isPresented: $showPlanSelection,
        onPlanSelected: { plan in
            handlePlanSelection(plan)
        }
    )
    .presentationDetents([.medium])
    .presentationDragIndicator(.visible)
}
```

### 4. Update PaywallViewModel Integration

#### Extend PaywallViewModel

```swift
// Add plan selection support
@Published public var availableSubscriptionPlans: [SubscriptionPlan] = []
@Published public var selectedSubscriptionPlan: SubscriptionPlan?

public func loadSubscriptionPlans() async
public func selectSubscriptionPlan(_ plan: SubscriptionPlan)
```

#### Update Product Loading

```swift
// Convert ProductInfo to SubscriptionPlan
private func convertToSubscriptionPlans(_ products: [ProductInfo]) -> [SubscriptionPlan]
```

### 5. Design System Updates

#### Add New Design Tokens

```swift
extension PaywallDesignSystem {
    enum PlanSelection {
        static let cardBackgroundColor = Color(hex: "#27272a") // Muted
        static let selectedBorderColor = Color(hex: "#536db0") // Brand
        static let unselectedBorderColor = Color.white
        static let sheetBackgroundColor = Color(hex: "#333333")
        static let dragIndicatorColor = Color(hex: "#252525")
    }
}
```

### 6. Data Flow Architecture

```
User clicks "Annul"
    ↓
PaywallFooterView shows sheet
    ↓
PlanSelectionSheet loads plans from PlanSelectionViewModel
    ↓
User selects plan
    ↓
PaywallFooterView.handlePlanSelection() called
    ↓
Update PaywallViewModel.selectedSubscriptionPlan
    ↓
Update pricing display in footer
    ↓
Sheet dismisses
```

## Implementation Steps

### Phase 1: Models and ViewModels

1. Create `SubscriptionPlan` model
2. Create `PlanSelectionViewModel`
3. Add plan selection properties to `PaywallViewModel`

### Phase 2: UI Components

1. Create `PlanCard` component
2. Create `PlanSelectionSheet` component
3. Add design system tokens

### Phase 3: Integration

1. Add annul button to `PaywallFooterView`
2. Add sheet presentation logic
3. Implement plan selection handling
4. Update pricing display logic

### Phase 4: Testing and Polish

1. Test sheet presentation and dismissal
2. Test plan selection and state updates
3. Verify design matches Figma specifications
4. Add accessibility support

## File Structure

```
ChatToDesign/
├── Presentation/Subscription/
│   ├── Models/
│   │   ├── PaywallModels.swift (existing)
│   │   └── SubscriptionPlan.swift (new)
│   ├── Components/
│   │   ├── PaywallFooterView.swift (modify)
│   │   ├── PlanSelectionSheet.swift (new)
│   │   └── PlanCard.swift (new)
│   └── ViewModels/
│       ├── PaywallViewModel.swift (modify)
│       └── PlanSelectionViewModel.swift (new)
```

## Dependencies

### Existing Dependencies

- SwiftUI for UI components
- RevenueCat for subscription management
- Existing PaywallDesignSystem

### New Dependencies

- None required (using existing SwiftUI sheet presentation)

## Testing Considerations

### Unit Tests

- `SubscriptionPlan` model validation
- `PlanSelectionViewModel` state management
- Plan selection logic

### UI Tests

- Sheet presentation and dismissal
- Plan selection interaction
- Visual regression testing against Figma design

### Integration Tests

- End-to-end plan selection flow
- Integration with RevenueCat products
- Pricing update after plan selection

## Accessibility

### VoiceOver Support

- Proper labels for plan cards
- Selection state announcements
- Sheet dismissal instructions

### Dynamic Type

- Support for larger text sizes
- Maintain layout integrity with different font sizes

## Performance Considerations

### Memory Management

- Proper cleanup of sheet state
- Efficient plan data loading
- Avoid retain cycles in closures

### Animation Performance

- Smooth sheet transitions
- Efficient plan card selection animations
- Proper state update batching

## Future Enhancements

### Potential Features

- Plan comparison table
- Discount badges for limited-time offers
- Custom trial periods per plan
- Localized pricing display
- A/B testing for plan presentation

### Extensibility

- Support for more than 2 plans
- Custom plan card layouts
- Dynamic plan loading from server
- Plan recommendation engine
