# Paywall 套餐选择功能实现

## 功能概述

实现了用户在 PlanSelectionSheet 中选择套餐后，PaywallFooterView 中的 `plan.period` 会自动更新的功能。

## 实现细节

### 1. PaywallFooterView 修改

**文件**: `ChatToDesign/Presentation/Subscription/Components/PaywallFooterView.swift`

**主要变更**:

- 添加了 `onPlanSelected: ((SubscriptionPlan) -> Void)?` 回调参数
- 更新了初始化方法，添加 `onPlanSelected` 参数（默认值为 `nil`）
- 修改了 `handlePlanSelection` 方法，调用 `onPlanSelected?(plan)` 通知父视图

```swift
/// 套餐选择回调
let onPlanSelected: ((SubscriptionPlan) -> Void)?

/// 处理套餐选择
private func handlePlanSelection(_ plan: SubscriptionPlan) {
  Logger.info("PaywallFooterView: 用户选择套餐 - \(plan.title)")

  // 通过回调通知父视图更新定价信息
  onPlanSelected?(plan)
}
```

### 2. PaywallViewModel 扩展

**文件**: `ChatToDesign/Presentation/Subscription/PaywallViewModel.swift`

**主要变更**:

- 添加了 `updateSelectedProduct(_ product: ProductInfo)` 方法
- 该方法直接使用 ProductInfo 更新配置
- 保留了兼容性方法 `updatePricing(with plan: SubscriptionPlan)`

```swift
/// 更新选中的产品
public func updateSelectedProduct(_ product: ProductInfo) {
  // 更新配置中的产品信息
  configuration = PaywallConfiguration(
    title: configuration.title,
    subtitle: configuration.subtitle,
    backgroundImageUrl: configuration.backgroundImageUrl,
    features: configuration.features,
    selectedProduct: product,
    showCloseButton: configuration.showCloseButton,
    purchaseButtonText: configuration.purchaseButtonText,
    legalLinkText: configuration.legalLinkText
  )

  Logger.info("PaywallViewModel: 更新选中产品 - \(product.displayName), 周期: \(product.formattedPeriod)")
}
```

### 3. PaywallView 集成

**文件**: `ChatToDesign/Presentation/Subscription/PaywallView.swift`

**主要变更**:

- 在 `PaywallFooterView` 调用中添加了 `onPlanSelected` 回调
- 添加了 `handlePlanSelection(_ plan: SubscriptionPlan)` 方法
- 该方法调用 `viewModel.updatePricing(with: plan)` 更新定价
- 记录分析事件和日志

```swift
PaywallFooterView(
  pricing: viewModel.currentPricing,
  purchaseButtonText: "Start free trial",
  legalLinkText: "How your free trial works",
  isPurchasing: viewModel.isPurchasing,
  onPurchase: {
    handlePurchase()
  },
  onLegalLinkTap: {
    handleLegalLinkTap()
  },
  onPlanSelected: { plan in
    handlePlanSelection(plan)
  },
  enableAnimation: true
)

/// 处理套餐选择
private func handlePlanSelection(_ plan: SubscriptionPlan) {
  // 更新视图模型中的定价信息
  viewModel.updatePricing(with: plan)

  // 记录分析事件
  analyticsTracker.trackProductSelected(
    productId: plan.productId,
    productName: plan.title,
    price: plan.price
  )

  Logger.info("PaywallView: 用户选择套餐 - \(plan.title)")
}
```

## 工作流程

1. 用户点击 PaywallFooterView 中的 `pricing.period` 按钮
2. 显示 `PlanSelectionSheet`
3. 用户在 sheet 中选择一个套餐
4. `PlanSelectionSheet` 调用 `onPlanSelected` 回调
5. `PaywallFooterView` 的 `handlePlanSelection` 被调用
6. `PaywallFooterView` 通过 `onPlanSelected` 回调通知 `PaywallView`
7. `PaywallView` 的 `handlePlanSelection` 被调用
8. `PaywallViewModel.updatePricing` 被调用，更新定价信息
9. 由于 `@Published` 属性，UI 自动刷新显示新的 `plan.period`

## 兼容性

- 所有修改都是向后兼容的
- `onPlanSelected` 参数有默认值 `nil`，不会影响现有的 Preview 和其他调用
- 现有的 PlanSelectionSheet 功能保持不变

## 测试建议

1. 验证点击 period 按钮能正确显示 PlanSelectionSheet
2. 验证选择不同套餐后 footerView 中的 period 文本会更新
3. 验证选择套餐后 sheet 会自动关闭
4. 验证分析事件和日志记录正常工作
