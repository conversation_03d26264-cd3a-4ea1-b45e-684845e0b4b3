# Paywall Legal Sheet 技术方案

## 概述

本文档描述了在 PaywallView 中实现点击 legalButton 展示 sheet 的技术方案。该 sheet 将展示"How your free trial works"的详细信息，设计基于当前 Figma 选择的内容。

## 设计分析

根据 Figma 设计，Legal Sheet 包含以下元素：

### 1. 头部区域
- 标题："How your free trial works"
- 字体：Inter Semi Bold, 16px, 白色
- 居中对齐

### 2. 内容区域
包含三个时间线步骤，每个步骤包含：

#### 步骤 1: Today: Unlock all features
- 图标：绿色圆形背景 (#536db0) + 白色对勾图标
- 标题：白色文字，Inter Medium, 14px
- 描述：灰色文字 (#a1a1aa)，Inter Regular, 14px
- 连接线：深灰色竖线连接到下一步

#### 步骤 2: Day 2: Trail reminder  
- 图标：灰色圆形背景 (#f4f4f5) + 白色铃铛图标
- 标题：白色文字，Inter Medium, 14px
- 描述：灰色文字，Inter Regular, 14px
- 连接线：深灰色竖线连接到下一步

#### 步骤 3: Day 3: Trail ends
- 图标：灰色圆形背景 + 白色星星图标
- 标题：白色文字，Inter Medium, 14px
- 描述：灰色文字，包含日期和"Cancel before"链接

### 3. 样式规范
- 背景：深色主题
- 间距：使用设计系统定义的 spacing 值
- 圆角：使用 radius/full (9999) 和 radius/2xl (16)
- 颜色：使用 brand (#536db0)、foreground (#fafafa)、muted (#27272a) 等

## 技术实现方案

### 1. 创建 LegalSheetView 组件

**文件路径**: `ChatToDesign/Presentation/Subscription/Components/LegalSheetView.swift`

**主要功能**:
- 展示免费试用工作流程的时间线
- 使用 SwiftUI 实现响应式布局
- 支持深色主题
- 包含关闭按钮

**组件结构**:
```swift
public struct LegalSheetView: View {
  @Binding var isPresented: Bool
  
  public var body: some View {
    // 主要内容布局
  }
  
  private var headerSection: some View { }
  private var timelineSection: some View { }
  private func timelineStep() -> some View { }
}
```

### 2. 创建时间线步骤数据模型

**文件路径**: `ChatToDesign/Presentation/Subscription/Models/TrialTimelineStep.swift`

**数据结构**:
```swift
public struct TrialTimelineStep {
  let id: String
  let iconName: String
  let iconBackgroundColor: Color
  let title: String
  let description: String
  let isCompleted: Bool
  let showConnector: Bool
}
```

### 3. 修改 PaywallView

**文件路径**: `ChatToDesign/Presentation/Subscription/PaywallView.swift`

**主要变更**:
- 添加 `@State private var showLegalSheet = false` 状态变量
- 修改 `handleLegalLinkTap()` 方法，设置 `showLegalSheet = true`
- 添加 `.sheet(isPresented: $showLegalSheet)` 修饰符

**代码示例**:
```swift
// 在 body 中添加
.sheet(isPresented: $showLegalSheet) {
  LegalSheetView(isPresented: $showLegalSheet)
    .presentationDetents([.height(400)])
    .presentationDragIndicator(.visible)
}

// 修改 handleLegalLinkTap 方法
private func handleLegalLinkTap() {
  analyticsTracker.trackPaywallDismissed(reason: .userAction)
  showLegalSheet = true
}
```

### 4. 设计系统扩展

**文件路径**: `ChatToDesign/Presentation/Subscription/DesignSystem/PaywallDesignSystem.swift`

**新增内容**:
- LegalSheet 相关的颜色定义
- 时间线组件的样式规范
- 图标尺寸和间距定义

## 实现步骤

### 第一阶段：基础组件创建
1. 创建 `TrialTimelineStep` 数据模型
2. 创建 `LegalSheetView` 基础结构
3. 实现头部区域布局

### 第二阶段：时间线内容实现
1. 实现时间线步骤组件
2. 添加图标和连接线
3. 实现文本样式和布局

### 第三阶段：集成和优化
1. 在 PaywallView 中集成 LegalSheetView
2. 添加动画效果
3. 测试和调优

### 第四阶段：完善和测试
1. 添加分析事件跟踪
2. 完善错误处理
3. 进行全面测试

## 文件结构

```
ChatToDesign/Presentation/Subscription/
├── Components/
│   ├── LegalSheetView.swift          # 新增：法律条款弹窗
│   └── TimelineStepView.swift        # 新增：时间线步骤组件
├── Models/
│   └── TrialTimelineStep.swift       # 新增：时间线数据模型
├── PaywallView.swift                 # 修改：添加弹窗状态管理
└── DesignSystem/
    └── PaywallDesignSystem.swift     # 修改：添加相关样式定义
```

## 注意事项

1. **设计一致性**: 严格按照 Figma 设计实现，确保颜色、字体、间距完全匹配
2. **响应式布局**: 支持不同屏幕尺寸的适配
3. **性能优化**: 使用 LazyVStack 等优化长列表性能
4. **可访问性**: 添加适当的 accessibility 标签
5. **动画效果**: 添加平滑的展示和隐藏动画
6. **错误处理**: 处理可能的布局异常情况

## 测试计划

1. **单元测试**: 测试数据模型和组件逻辑
2. **UI 测试**: 验证布局和交互功能
3. **集成测试**: 测试与 PaywallView 的集成
4. **设备测试**: 在不同设备上验证显示效果

## 后续优化

1. 支持本地化多语言
2. 添加更多动画效果
3. 支持自定义主题
4. 添加更详细的分析跟踪
