# RevenueCat 支付系统集成文档

## 概述

本文档描述了 ChatToDesign 应用中 RevenueCat 支付系统的完整工作流程，包括架构设计、核心组件、数据流转和使用方式。

## 系统架构

### 分层架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   PaywallView   │  │SubscriptionView │  │  ViewModels  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │SubscriptionState│  │   Use Cases     │  │Subscription  │ │
│  │    Manager      │  │                 │  │   Module     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Subscription   │  │  Entitlement    │  │    User      │ │
│  │    Entity       │  │    Entity       │  │   Entity     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 Infrastructure Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ RevenueCat      │  │     Logger      │  │   Config     │ │
│  │   Adapter       │  │                 │  │   Service    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. Domain Entities (领域实体)

#### Subscription (订阅实体)

```swift
public struct Subscription {
    public let id: String                    // 订阅唯一标识
    public let productId: String             // 产品标识符
    public let status: SubscriptionStatus    // 订阅状态
    public let tier: SubscriptionTier        // 订阅层级 (free/premium/pro)
    public let purchaseDate: Date            // 购买日期
    public let expirationDate: Date?         // 到期日期
    public let willRenew: Bool               // 是否自动续费
    public let isInTrialPeriod: Bool         // 是否在试用期
}
```

#### Entitlement (权限实体)

```swift
public struct Entitlement {
    public let id: String                    // 权限唯一标识
    public let type: EntitlementType         // 权限类型
    public let isActive: Bool                // 是否激活
    public let expirationDate: Date?         // 到期日期
}
```

### 2. Application Services (应用服务)

#### SubscriptionStateManager (订阅状态管理器)

- **职责**: 管理应用中的订阅状态，提供响应式状态更新
- **特性**:
  - 使用`@MainActor`确保 UI 线程安全
  - 通过`@Published`属性提供响应式状态
  - 集成 Combine 进行状态同步

#### Use Cases (用例)

- **CheckSubscriptionStatusUseCase**: 检查订阅状态和权限
- **PurchaseSubscriptionUseCase**: 处理购买流程
- **RestorePurchasesUseCase**: 恢复购买记录

### 3. Infrastructure Layer (基础设施层)

#### RevenueCatAdapter

- **职责**: 封装 RevenueCat SDK，实现 SubscriptionService 协议
- **功能**:
  - SDK 配置和初始化
  - 购买流程处理
  - 订阅状态同步
  - 错误处理和重试

## 工作流程

### 1. 系统初始化流程

```
AppDelegate → DependencyContainer → SubscriptionModule → RevenueCatAdapter → RevenueCat SDK
    ↓
1. 应用启动时初始化依赖容器
2. 创建订阅模块和RevenueCat适配器
3. 配置RevenueCat SDK (API Key, User ID)
4. 刷新用户订阅信息
5. 设置状态监听和同步
```

### 2. 用户登录流程

```
用户登录 → 认证系统 → SubscriptionModule → RevenueCat
    ↓
1. 用户完成登录认证
2. 调用 userDidLogin(user)
3. 同步用户ID到RevenueCat
4. 刷新订阅状态
5. 更新UI显示
```

### 3. 购买流程

```
用户点击购买 → PaywallView → ViewModel → StateManager → UseCase → RevenueCatAdapter → RevenueCat SDK → App Store
    ↓
1. 用户在付费墙点击购买按钮
2. ViewModel调用StateManager.purchaseSubscription()
3. StateManager委托给PurchaseUseCase处理
4. UseCase通过RevenueCatAdapter调用SDK
5. SDK与App Store交互完成购买
6. 购买结果逐层返回并更新UI
```

### 4. 权限检查流程

```
功能模块 → StateManager → 本地缓存检查 → (如需要)UseCase → RevenueCatAdapter
    ↓
1. 功能模块请求权限检查
2. StateManager首先检查本地缓存
3. 如缓存有效直接返回，否则查询远程
4. 通过UseCase和Adapter获取最新状态
5. 更新缓存并返回结果
```

## 数据流转

### 1. 状态管理

```
RevenueCat SDK → RevenueCatAdapter → SubscriptionStateManager → ViewModels → Views
                      ↓
                 数据映射和转换
                      ↓
              Domain Entities (Subscription, Entitlement)
```

### 2. 响应式更新

```swift
// RevenueCat状态变化
RevenueCat.delegate.receivedUpdated(customerInfo)
    ↓
// 适配器处理
RevenueCatAdapter.refreshCustomerInfo()
    ↓
// 发布状态更新
subscriptionStatusSubject.send(subscription)
entitlementsSubject.send(entitlements)
    ↓
// 状态管理器接收
SubscriptionStateManager.setupSubscriptions()
    ↓
// UI自动更新
@Published properties trigger SwiftUI updates
```

## 错误处理策略

### 1. 错误类型分类

```swift
public enum SubscriptionServiceError: Error {
    case notConfigured           // 服务未配置
    case userNotAuthenticated    // 用户未认证
    case purchaseInProgress      // 购买进行中
    case purchaseCancelled       // 购买被取消
    case purchaseFailed(String)  // 购买失败
    case networkError           // 网络错误
    case unknownError(Error)    // 未知错误
}
```

### 2. 错误处理流程

```
操作发起 → 网络检查 → 执行操作 → 结果处理
    ↓
- 网络不可用: 显示网络错误
- 用户取消: 静默处理
- 网络错误: 重试机制(指数退避)
- 其他错误: 显示用户友好的错误信息
```

## 使用示例

### 1. 检查订阅状态

```swift
class FeatureViewModel: ObservableObject {
    private let subscriptionStateManager: SubscriptionStateManager

    var canAccessFeature: Bool {
        return subscriptionStateManager.canAccessPremiumFeatures
    }

    func checkAccess() {
        if !canAccessFeature {
            // 显示付费墙
            showPaywall = true
        }
    }
}
```

### 2. 购买订阅

```swift
class PaywallViewModel: ObservableObject {
    @Published var purchaseState: PurchaseState = .idle

    func purchaseSubscription(productId: String) async {
        purchaseState = .loading

        let result = await subscriptionStateManager.purchaseSubscription(productId: productId)

        if result.isSuccessful {
            purchaseState = .completed
        } else {
            purchaseState = .failed(result.error)
        }
    }
}
```

### 3. 权限控制

```swift
struct PremiumFeatureView: View {
    @EnvironmentObject var subscriptionState: SubscriptionStateManager

    var body: some View {
        if subscriptionState.canAccessPremiumFeatures {
            PremiumContentView()
        } else {
            PaywallView()
        }
    }
}
```

## 配置和部署

### 1. RevenueCat 配置

```swift
// AppDelegate中的配置
func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
    // 配置订阅模块
    Task {
        await container.subscriptionModule.configure()
        Logger.info("订阅模块初始化完成")
    }
    return true
}
```

### 2. 产品配置

在 RevenueCat Dashboard 中配置：

- **产品标识符 (Product IDs)**: 对应 App Store Connect 中的产品
- **权限标识符 (Entitlement IDs)**: 定义用户可获得的权限
- **产品包装 (Packages)**: 将产品组织成不同的套餐
- **价格和试用期设置**: 配置定价策略和免费试用

### 3. 环境配置

```swift
private func getRevenueCatAPIKey() -> String {
    #if DEBUG
    return "appl_debug_api_key_placeholder"  // 开发环境API密钥
    #else
    return "appl_release_api_key_placeholder" // 生产环境API密钥
    #endif
}
```

### 4. 依赖注入配置

```swift
// 在AppDependencyContainer中集成
public lazy var subscriptionModule: SubscriptionModule = {
    let subscriptionDependencies = SubscriptionModuleDependencies(
        configService: configService,
        userService: userModule.userService
    )
    return SubscriptionModule(dependencies: subscriptionDependencies)
}()
```

## 测试策略

### 1. 单元测试

```swift
class SubscriptionStateManagerTests: XCTestCase {
    func testPurchaseSubscription() async {
        // Mock RevenueCat服务
        let mockService = MockSubscriptionService()
        let stateManager = SubscriptionStateManager(subscriptionService: mockService)

        // 测试购买流程
        let result = await stateManager.purchaseSubscription(productId: "premium_monthly")

        XCTAssertTrue(result.isSuccessful)
        XCTAssertNotNil(result.subscription)
    }
}
```

### 2. 集成测试

```swift
class SubscriptionIntegrationTests: XCTestCase {
    func testEndToEndPurchaseFlow() async throws {
        // 测试完整的购买流程
        // 1. 配置RevenueCat
        // 2. 获取产品列表
        // 3. 执行购买
        // 4. 验证订阅状态
    }
}
```

### 3. UI 测试

```swift
class PaywallUITests: XCTestCase {
    func testPurchaseButtonTap() throws {
        let app = XCUIApplication()
        app.launch()

        // 导航到付费墙
        app.buttons["upgrade_button"].tap()

        // 点击购买按钮
        app.buttons["purchase_premium"].tap()

        // 验证购买流程启动
        XCTAssertTrue(app.staticTexts["processing_purchase"].exists)
    }
}
```

## 监控和分析

### 1. 关键指标

- **转化率 (Conversion Rate)**: 从免费用户到付费用户的转化比例
- **流失率 (Churn Rate)**: 订阅取消的比例
- **平均收入 (ARPU)**: 每用户平均收入
- **试用转化率**: 试用用户转为付费用户的比例
- **生命周期价值 (LTV)**: 用户在整个生命周期内的价值

### 2. 日志记录

```swift
// 购买事件
Logger.info("购买开始 - 产品: \(productId), 用户: \(userId)")
Logger.info("购买成功 - 产品: \(productId), 订阅层级: \(tier)")
Logger.error("购买失败 - 产品: \(productId), 错误: \(error)")

// 状态变化
Logger.debug("订阅状态更新 - 层级: \(subscription.tier), 状态: \(subscription.status)")
Logger.info("权限激活 - 类型: \(entitlement.type), 到期: \(entitlement.expirationDate)")

// 用户行为
Logger.info("付费墙展示 - 触发功能: \(feature)")
Logger.info("恢复购买 - 结果: \(success ? "成功" : "失败")")
```

### 3. 错误监控

```swift
// 集成Sentry或其他错误监控服务
func trackPurchaseError(_ error: SubscriptionServiceError, productId: String) {
    let context = [
        "product_id": productId,
        "error_type": String(describing: error),
        "user_id": currentUserId ?? "anonymous"
    ]

    ErrorReporting.captureError(error, context: context)
}
```

## 安全考虑

### 1. 客户端验证限制

```swift
// 重要功能需要服务端验证
public func performCriticalAction() async throws {
    // 1. 客户端快速检查
    guard canAccessPremiumFeatures else {
        throw SubscriptionError.subscriptionRequired
    }

    // 2. 服务端验证（对于重要操作）
    let serverResult = try await serverValidator.validateSubscription(userId: currentUserId)
    guard serverResult.isValid else {
        throw SubscriptionError.subscriptionExpired
    }

    // 3. 执行实际操作
    await performAction()
}
```

### 2. 数据保护

- 订阅状态缓存加密存储
- 敏感信息不在客户端长期保存
- 定期与服务端同步验证

## 故障排除

### 1. 常见问题

**问题**: 购买成功但订阅状态未更新
**解决**: 检查 RevenueCat webhook 配置，确保状态同步正常

**问题**: 恢复购买失败
**解决**: 验证用户 Apple ID 与原购买账户一致

**问题**: 权限检查不准确
**解决**: 清除本地缓存，强制从服务端获取最新状态

### 2. 调试工具

```swift
#if DEBUG
extension SubscriptionStateManager {
    func debugPrintState() {
        print("=== 订阅状态调试信息 ===")
        print("配置状态: \(isConfigured)")
        print("当前订阅: \(currentSubscription?.tier.displayName ?? "无")")
        print("激活权限: \(entitlements.activeEntitlements.map { $0.type.displayName })")
        print("========================")
    }
}
#endif
```

## 总结

RevenueCat 支付系统通过清晰的分层架构、响应式状态管理和完善的错误处理，为 ChatToDesign 应用提供了可靠的订阅服务。系统具有以下特点：

### 核心优势

1. **架构清晰**: 遵循 Clean Architecture 原则，职责分离明确
2. **类型安全**: 使用强类型和协议确保代码安全
3. **响应式**: 基于 Combine 的响应式状态管理
4. **可测试**: 通过依赖注入支持单元测试
5. **可扩展**: 模块化设计便于功能扩展
6. **用户友好**: 完善的错误处理和用户体验

### 技术特色

- **状态管理**: 使用`@MainActor`和`@Published`确保线程安全的响应式更新
- **错误处理**: 分层的错误处理机制，提供用户友好的错误信息
- **缓存策略**: 智能缓存减少网络请求，提升用户体验
- **日志记录**: 详细的日志记录便于问题排查和性能优化

### 扩展方向

1. **A/B 测试**: 支持不同定价策略的 A/B 测试
2. **本地化**: 支持多地区定价和货币
3. **推广码**: 支持优惠券和推广码功能
4. **家庭共享**: 支持 Apple 家庭共享功能
5. **企业订阅**: 支持企业级订阅管理

这个设计为后续的功能扩展和维护奠定了坚实的基础，能够满足应用在不同发展阶段的需求。

```

```
