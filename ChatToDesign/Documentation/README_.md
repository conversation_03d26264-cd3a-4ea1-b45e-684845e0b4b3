# ChatToDesign 项目文档

## 📚 文档概览

本目录包含 ChatToDesign 项目的技术文档，现已按照功能模块重新整理，便于查找和维护。

## 🗂️ 文档分类 (新结构)

### 📡 01-API - API 接口文档

- **[FileUploadAPI.md](./01-API/FileUploadAPI.md)** - 基础文件上传 API 使用指南
- **[UploadWithAssetAPI.md](./01-API/UploadWithAssetAPI.md)** - 上传文件并创建资产 API 使用指南
- **[DeleteAssetAPI.md](./01-API/DeleteAssetAPI.md)** - 删除资产 API 使用指南

### 🏗️ 02-Architecture - 系统架构文档

- **[Cache-System-Overview.md](./02-Architecture/Cache-System-Overview.md)** - 缓存系统的整体概览和核心特性
- **[Cache-Architecture-Design.md](./02-Architecture/Cache-Architecture-Design.md)** - 缓存系统的详细架构设计
- **[SWR-Implementation-Guide.md](./02-Architecture/SWR-Implementation-Guide.md)** - SWR (Stale-While-Revalidate) 模式的实现指南
- **[Cache-Usage-Examples.md](./02-Architecture/Cache-Usage-Examples.md)** - 缓存系统的具体使用示例
- **[VideoCacheOptimization.md](./02-Architecture/VideoCacheOptimization.md)** - 视频缓存优化系统
- **[Video-Loading-Performance-Optimization.md](./02-Architecture/Video-Loading-Performance-Optimization.md)** ⭐ - 视频加载性能优化技术方案
- **[Video-Loading-Implementation-Guide.md](./02-Architecture/Video-Loading-Implementation-Guide.md)** ⭐ - 视频加载优化实施指南
- **[Video-Performance-Monitoring.md](./02-Architecture/Video-Performance-Monitoring.md)** ⭐ - 视频性能监控与配置指南
- **[AssetResponse-to-UserAsset-Refactoring.md](./02-Architecture/AssetResponse-to-UserAsset-Refactoring.md)** - API 模型到领域实体的重构计划
- **[MediaDownloadSaveArchitecture.md](./02-Architecture/MediaDownloadSaveArchitecture.md)** - 媒体下载和保存架构设计

### 🎨 03-DesignSystem - 设计系统文档

- **[DesignSystem-ComponentSystem-Architecture.md](./03-DesignSystem/DesignSystem-ComponentSystem-Architecture.md)** - 设计系统组件架构
- **[Create-Module-DesignSystem-Integration-Plan.md](./03-DesignSystem/Create-Module-DesignSystem-Integration-Plan.md)** - Create 模块设计系统集成计划
- **[Explore-DesignSystem-Integration-Plan.md](./03-DesignSystem/Explore-DesignSystem-Integration-Plan.md)** - Explore 模块设计系统集成计划
- **[Profile-DesignSystem-Integration-Plan.md](./03-DesignSystem/Profile-DesignSystem-Integration-Plan.md)** - Profile 模块设计系统集成计划
- **[Design-Tokens-Migration-Plan.md](./03-DesignSystem/Design-Tokens-Migration-Plan.md)** - 设计令牌迁移计划
- **[Design-Tokens-Mapping-Example.md](./03-DesignSystem/Design-Tokens-Mapping-Example.md)** - 设计令牌映射示例
- **[Design-Token-System-Architecture.md](./03-DesignSystem/Design-Token-System-Architecture.md)** - 设计令牌系统架构

### ⚡ 04-Features - 功能实现文档

- **[RecreateButtonImplementation.md](./04-Features/RecreateButtonImplementation.md)** - 重新创建按钮实现
- **[CreatePage-VideoTemplate-Integration.md](./04-Features/CreatePage-VideoTemplate-Integration.md)** - Create 页面视频模板集成
- **[CreatePage-Implementation-Guide.md](./04-Features/CreatePage-Implementation-Guide.md)** - Create 页面实现指南
- **[CreatePage-API-Reference.md](./04-Features/CreatePage-API-Reference.md)** - Create 页面 API 参考
- **[VideoGenerationHandler-Integration.md](./04-Features/VideoGenerationHandler-Integration.md)** - 视频生成处理器集成
- **[ExploreItemDetail-Implementation-Summary.md](./04-Features/ExploreItemDetail-Implementation-Summary.md)** - Explore 项目详情实现总结
- **[CreationsDisplayEnhancement.md](./04-Features/CreationsDisplayEnhancement.md)** - 创作显示增强
- **[Credits-Feature-Technical-Plan.md](./04-Features/Credits-Feature-Technical-Plan.md)** - 积分功能技术计划

### 💳 05-Subscriptions - 订阅支付文档

- **[RevenueCat-Payment-System.md](./05-Subscriptions/RevenueCat-Payment-System.md)** - RevenueCat 支付系统集成
- **[RevenueCat-Paywall-Integration.md](./05-Subscriptions/RevenueCat-Paywall-Integration.md)** - RevenueCat 付费墙集成
- **[PaywallLegalSheet.md](./05-Subscriptions/PaywallLegalSheet.md)** - 付费墙法律条款页面
- **[PaywallView-Refactoring-Plan.md](./05-Subscriptions/PaywallView-Refactoring-Plan.md)** - 付费墙视图重构计划
- **[PaywallPlanSelection.md](./05-Subscriptions/PaywallPlanSelection.md)** - 付费墙计划选择
- **[plan-selection-sheet-implementation.md](./05-Subscriptions/plan-selection-sheet-implementation.md)** - 计划选择页面实现

### 📊 06-Data - 数据集成文档

- **[Explore-API-Integration-Plan.md](./06-Data/Explore-API-Integration-Plan.md)** - Explore API 集成计划

### 📢 07-Marketing - 营销文档

- **[mkt/](./07-Marketing/mkt/)** - 营销相关文档和资源

## 🚀 快速开始

### 🔥 视频性能优化 (最新更新)

如果你遇到视频加载相关的性能问题，建议按以下顺序阅读：

1. **[Video-Loading-Performance-Optimization.md](./02-Architecture/Video-Loading-Performance-Optimization.md)** - 了解问题分析和整体解决方案
2. **[Video-Loading-Implementation-Guide.md](./02-Architecture/Video-Loading-Implementation-Guide.md)** - 获取具体实施步骤和代码示例
3. **[Video-Performance-Monitoring.md](./02-Architecture/Video-Performance-Monitoring.md)** - 配置性能监控和调优

**预期效果**: CPU 占用降低 60-70%，内存占用降低 40-50%，滚动性能提升至 60fps

### 1. 了解缓存系统

如果你是第一次接触项目的缓存系统，建议按以下顺序阅读：

1. [Cache-System-Overview.md](./02-Architecture/Cache-System-Overview.md) - 获得整体认识
2. [SWR-Implementation-Guide.md](./02-Architecture/SWR-Implementation-Guide.md) - 学习具体使用
3. [Cache-Usage-Examples.md](./Cache-Usage-Examples.md) - 查看实际示例

### 2. 集成到项目

在 ViewModel 中使用缓存系统的基本步骤：

```swift
// 1. 获取 QueryManager
private let queryManager = QueryManager.shared

// 2. 使用 SWR 模式获取数据
queryManager
  .query(
    key: "your_data_key",
    maxAge: 300,      // 5分钟
    staleTime: 3600,  // 1小时
    networkCall: { try await self.apiService.fetchData() }
  )
  .receive(on: DispatchQueue.main)
  .sink { result in /* 处理结果 */ }
  .store(in: &cancellables)
```

### 3. 使用 API 接口

根据需要选择合适的 API：

- **简单文件上传**: 使用 [FileUploadAPI](./FileUploadAPI.md)
- **创建资产记录**: 使用 [UploadWithAssetAPI](./UploadWithAssetAPI.md)
- **删除资产**: 使用 [DeleteAssetAPI](./DeleteAssetAPI.md)

## 🔧 开发指南

### 缓存策略选择

| 场景           | 推荐配置          | 说明               |
| -------------- | ----------------- | ------------------ |
| 频繁更新的数据 | maxAge: 60-300s   | 短期缓存，频繁刷新 |
| 相对稳定的数据 | maxAge: 300-1800s | 中期缓存，适度刷新 |
| 静态配置数据   | maxAge: 3600s+    | 长期缓存，很少刷新 |
| 敏感临时数据   | 自定义短期配置    | 使用独立缓存实例   |

### 缓存键命名规范

```swift
// ✅ 推荐的命名方式
"cms_data"                    // 简单数据
"user_assets_page_1_limit_20" // 分页数据
"task_list_user_123"          // 用户相关数据
"search_results_keyword_abc"  // 搜索结果

// ❌ 避免的命名方式
"data"                        // 太泛化
"api_call"                    // 不具体
"temp"                        // 不明确
```

### 错误处理模式

```swift
.sink { result in
  switch result {
  case .cached(let data):
    // 处理新鲜缓存数据
    self.updateUI(with: data)
    self.isLoading = false

  case .stale(let data):
    // 处理过期缓存数据（正在后台刷新）
    self.updateUI(with: data)
    self.isLoading = self.data.isEmpty // 有数据时不显示加载状态

  case .fresh(let data):
    // 处理网络新数据
    self.updateUI(with: data)
    self.isLoading = false

  case .error(let error):
    // 处理错误
    self.error = error
    self.isLoading = false
    Logger.error("数据获取失败: \(error)")
  }
}
```

## 📊 性能优化建议

1. **合理设置缓存时间** - 根据数据更新频率调整 maxAge 和 staleTime
2. **使用命名空间** - 不同模块使用不同的缓存命名空间
3. **及时失效缓存** - 数据变更后主动失效相关缓存
4. **监控内存使用** - 定期检查缓存大小，避免内存压力

## 🧪 测试支持

项目提供了完整的测试支持：

```swift
// 创建测试专用的缓存实例
let testCache = CacheFactory.createTestCache()
let testQueryManager = QueryManager(cacheManager: testCache)

// 每个测试用例都有独立的缓存空间
// 避免测试间的数据污染
```

## 📝 贡献指南

### 更新文档

- 新增功能时，请同步更新相关文档
- 修改 API 时，请更新对应的 API 文档
- 重大架构变更时，请更新架构设计文档

### 文档规范

- 使用清晰的标题层次结构
- 提供具体的代码示例
- 包含错误处理和最佳实践
- 保持文档的时效性和准确性

## 🔗 相关链接

- [项目主页](../README.md)
- [API 文档](https://api-docs.example.com)
- [设计规范](https://design.example.com)

---

📅 **最后更新**: 2025 年 1 月
👥 **维护者**: ChatToDesign 开发团队
