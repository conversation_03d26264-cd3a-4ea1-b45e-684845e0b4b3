# Design Token System Architecture

## Executive Summary

This document outlines the ideal architecture for a modern design token system that provides a single source of truth from design to implementation. The system automates the entire workflow from Figma to Swift code, ensuring perfect design-developer alignment with zero manual synchronization.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Implementation Guide](#implementation-guide)
4. [Token Structure](#token-structure)
5. [Automation Pipeline](#automation-pipeline)
6. [Swift Integration](#swift-integration)
7. [Theme System](#theme-system)
8. [Best Practices](#best-practices)
9. [Migration Strategy](#migration-strategy)

## Overview

### Goals

- **Single Source of Truth**: Figma serves as the only source for design tokens
- **Full Automation**: Zero manual steps from design to code
- **Type Safety**: Strongly typed Swift code with compile-time validation
- **Scalability**: Support for multiple themes, platforms, and brands
- **Developer Experience**: IntelliSense support and clear API design

### Key Benefits

1. **Design-Code Parity**: 100% consistency between designs and implementation
2. **Efficiency**: Design changes propagate automatically
3. **Reduced Errors**: No manual copying or interpretation
4. **Better Collaboration**: Shared language between designers and developers
5. **Version Control**: Full history and rollback capabilities

## Architecture

### High-Level Flow

```
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│   Figma File    │     │  Tokens Studio   │     │   GitHub/Git    │
│  (Design Tool)  │ --> │    (Plugin)      │ --> │  (tokens.json)  │
└─────────────────┘     └──────────────────┘     └─────────────────┘
                                                          |
                                                          v
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│   Swift Code    │ <-- │ Style Dictionary │ <-- │  CI/CD Pipeline │
│  (Generated)    │     │  (Transform)     │     │   (Automated)   │
└─────────────────┘     └──────────────────┘     └─────────────────┘
```

### Component Details

#### 1. Figma + Tokens Studio
- Designers define tokens in Figma using Tokens Studio plugin
- Supports color, typography, spacing, shadows, and custom tokens
- Exports tokens in W3C Design Token Format

#### 2. Version Control
- Tokens stored as `tokens.json` in repository
- Enables code review for design changes
- Provides audit trail and rollback capability

#### 3. Style Dictionary
- Industry-standard token transformation tool
- Converts platform-agnostic tokens to Swift code
- Extensible with custom transforms and formats

#### 4. Generated Swift Code
- Type-safe enums and structs
- Zero runtime overhead
- Full IDE support with autocompletion

## Implementation Guide

### Step 1: Setup Figma Tokens

Install and configure Tokens Studio in Figma:

```json
// Example token structure in Figma
{
  "global": {
    "color": {
      "primary": {
        "100": { "value": "#e0e7ff", "type": "color" },
        "500": { "value": "#536db0", "type": "color" },
        "900": { "value": "#1e2a4a", "type": "color" }
      }
    },
    "spacing": {
      "xs": { "value": "4", "type": "spacing" },
      "sm": { "value": "8", "type": "spacing" },
      "md": { "value": "16", "type": "spacing" }
    }
  }
}
```

### Step 2: Configure Style Dictionary

Create `style-dictionary.config.js`:

```javascript
const StyleDictionary = require('style-dictionary');

// Custom transform for Swift color
StyleDictionary.registerTransform({
  name: 'color/swift',
  type: 'value',
  matcher: (token) => token.type === 'color',
  transformer: (token) => {
    const hex = token.value;
    return `Color(hex: "${hex}")`;
  }
});

// Custom format for Swift enums
StyleDictionary.registerFormat({
  name: 'swift/enum',
  formatter: ({ dictionary }) => {
    return `
import SwiftUI

public enum DesignTokens {
    ${generateTokenTree(dictionary.tokens)}
}
    `;
  }
});

module.exports = {
  source: ['tokens/**/*.json'],
  platforms: {
    ios: {
      transformGroup: 'ios-swift',
      transforms: ['color/swift', 'name/ti/camel', 'size/swift'],
      buildPath: 'ChatToDesign/DesignSystem/Generated/',
      files: [{
        destination: 'DesignTokens.swift',
        format: 'swift/enum',
        options: {
          imports: ['SwiftUI', 'UIKit'],
          objectType: 'enum',
          accessControl: 'public'
        }
      }]
    }
  }
};
```

### Step 3: Setup Automation Pipeline

GitHub Actions workflow (`.github/workflows/update-tokens.yml`):

```yaml
name: Update Design Tokens

on:
  push:
    paths:
      - 'tokens/**'
  repository_dispatch:
    types: [tokens-updated]

jobs:
  generate-tokens:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build tokens
        run: npm run build:tokens
        
      - name: Run Swift tests
        run: swift test
        
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          title: 'Update design tokens'
          commit-message: 'chore: update design tokens from Figma'
          branch: update-tokens
          delete-branch: true
```

### Step 4: Figma Webhook Integration

Configure Tokens Studio to trigger GitHub Actions:

```javascript
// tokens-studio-config.json
{
  "version": "2",
  "storageType": "github",
  "github": {
    "repository": "org/repo",
    "branch": "main",
    "filePath": "tokens/tokens.json"
  },
  "webhooks": {
    "onPush": {
      "url": "https://api.github.com/repos/org/repo/dispatches",
      "headers": {
        "Authorization": "token $GITHUB_TOKEN",
        "Accept": "application/vnd.github.v3+json"
      },
      "body": {
        "event_type": "tokens-updated"
      }
    }
  }
}
```

## Token Structure

### Naming Convention

Tokens follow a hierarchical naming pattern:

```
{category}.{property}.{variant}.{state}
```

Examples:
- `color.text.primary.default`
- `spacing.component.button.horizontal`
- `typography.heading.large`

### Token Categories

#### 1. Primitive Tokens (Foundation)

```swift
// Generated from tokens.json
public enum Primitives {
    public enum Colors {
        // Neutral colors
        public static let neutral50 = Color(hex: "#fafafa")
        public static let neutral100 = Color(hex: "#f5f5f5")
        public static let neutral900 = Color(hex: "#171717")
        
        // Brand colors
        public static let brand50 = Color(hex: "#e0e7ff")
        public static let brand500 = Color(hex: "#536db0")
        public static let brand900 = Color(hex: "#1e2a4a")
    }
    
    public enum Spacing {
        public static let space4 = 4.0
        public static let space8 = 8.0
        public static let space16 = 16.0
        public static let space24 = 24.0
    }
}
```

#### 2. Semantic Tokens (Meaning)

```swift
// Maps to theme-aware values
public enum Semantic {
    public enum Colors {
        @Environment(\.colorScheme) static var colorScheme
        
        public static var textPrimary: Color {
            colorScheme == .dark ? Primitives.Colors.neutral50 : Primitives.Colors.neutral900
        }
        
        public static var backgroundPrimary: Color {
            colorScheme == .dark ? Primitives.Colors.neutral900 : Primitives.Colors.neutral50
        }
    }
}
```

#### 3. Component Tokens (Context)

```swift
public enum Components {
    public enum Button {
        public enum Primary {
            public static let background = Semantic.Colors.interactive
            public static let text = Semantic.Colors.textOnInteractive
            public static let paddingHorizontal = Primitives.Spacing.space16
            public static let paddingVertical = Primitives.Spacing.space8
            public static let cornerRadius = 8.0
        }
    }
}
```

## Automation Pipeline

### Build Script

`package.json`:

```json
{
  "scripts": {
    "build:tokens": "node scripts/build-tokens.js",
    "watch:tokens": "chokidar 'tokens/**/*.json' -c 'npm run build:tokens'",
    "validate:tokens": "node scripts/validate-tokens.js",
    "sync:figma": "node scripts/sync-figma.js"
  }
}
```

### Token Validation

`scripts/validate-tokens.js`:

```javascript
const Ajv = require('ajv');
const schema = require('./token-schema.json');

function validateTokens(tokens) {
  const ajv = new Ajv();
  const validate = ajv.compile(schema);
  const valid = validate(tokens);
  
  if (!valid) {
    console.error('Token validation failed:', validate.errors);
    process.exit(1);
  }
  
  // Additional business logic validation
  validateColorContrast(tokens);
  validateTypographyScale(tokens);
  validateSpacingScale(tokens);
}
```

### Custom Transforms

```javascript
// transforms/spacing-ios.js
module.exports = {
  type: 'value',
  matcher: (token) => token.type === 'spacing',
  transformer: (token) => {
    const value = parseFloat(token.value);
    return `CGFloat(${value})`;
  }
};

// transforms/typography-ios.js
module.exports = {
  type: 'value',
  matcher: (token) => token.type === 'typography',
  transformer: (token) => {
    const { fontFamily, fontWeight, fontSize, lineHeight, letterSpacing } = token.value;
    return `Typography(
      fontFamily: "${fontFamily}",
      fontWeight: .${fontWeight},
      fontSize: ${fontSize},
      lineHeight: ${lineHeight},
      letterSpacing: ${letterSpacing}
    )`;
  }
};
```

## Swift Integration

### Generated Token File Structure

```swift
// DesignSystem/Generated/DesignTokens.swift
// WARNING: This file is auto-generated. Do not edit manually.

import SwiftUI

public enum DesignTokens {
    
    // MARK: - Colors
    public enum Color {
        public enum Primitive {
            public static let neutral50 = SwiftUI.Color(hex: "#fafafa")
            public static let neutral900 = SwiftUI.Color(hex: "#171717")
            public static let brand500 = SwiftUI.Color(hex: "#536db0")
        }
        
        public enum Semantic {
            public static let textPrimary = Color.Primitive.neutral900
            public static let textSecondary = Color.Primitive.neutral600
            public static let backgroundPrimary = Color.Primitive.neutral50
        }
    }
    
    // MARK: - Typography
    public struct TypographyStyle {
        let font: Font
        let size: CGFloat
        let lineHeight: CGFloat
        let letterSpacing: CGFloat
        let weight: Font.Weight
    }
    
    public enum Typography {
        public static let headingLarge = TypographyStyle(
            font: .custom("Inter", size: 32),
            size: 32,
            lineHeight: 40,
            letterSpacing: -0.8,
            weight: .semibold
        )
        
        public static let bodyRegular = TypographyStyle(
            font: .custom("Inter", size: 16),
            size: 16,
            lineHeight: 24,
            letterSpacing: 0,
            weight: .regular
        )
    }
    
    // MARK: - Spacing
    public enum Spacing {
        public static let xs: CGFloat = 4
        public static let sm: CGFloat = 8
        public static let md: CGFloat = 16
        public static let lg: CGFloat = 24
        public static let xl: CGFloat = 32
        public static let xxl: CGFloat = 48
    }
    
    // MARK: - Shadows
    public struct ShadowStyle {
        let color: SwiftUI.Color
        let opacity: Double
        let x: CGFloat
        let y: CGFloat
        let blur: CGFloat
        let spread: CGFloat
    }
    
    public enum Shadow {
        public static let small = ShadowStyle(
            color: .black,
            opacity: 0.1,
            x: 0,
            y: 1,
            blur: 3,
            spread: 0
        )
    }
}
```

### Usage in Components

```swift
// Components/DSButton.swift
import SwiftUI

struct DSButton: View {
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(DesignTokens.Typography.button.font)
                .foregroundColor(DesignTokens.Color.Component.Button.textColor)
                .padding(.horizontal, DesignTokens.Spacing.md)
                .padding(.vertical, DesignTokens.Spacing.sm)
        }
        .background(DesignTokens.Color.Component.Button.background)
        .cornerRadius(DesignTokens.Component.Button.cornerRadius)
        .shadow(
            color: DesignTokens.Shadow.small.color.opacity(DesignTokens.Shadow.small.opacity),
            radius: DesignTokens.Shadow.small.blur,
            x: DesignTokens.Shadow.small.x,
            y: DesignTokens.Shadow.small.y
        )
    }
}
```

### Type Extensions

```swift
// Extensions/Color+Hex.swift
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 0, 0, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}
```

## Theme System

### Theme Protocol

```swift
// Protocols/Theme.swift
protocol Theme {
    // Colors
    var textPrimary: Color { get }
    var textSecondary: Color { get }
    var backgroundPrimary: Color { get }
    var backgroundSecondary: Color { get }
    var interactive: Color { get }
    
    // Typography
    var headingLarge: TypographyStyle { get }
    var bodyRegular: TypographyStyle { get }
    
    // Spacing
    var spacingUnit: CGFloat { get }
}
```

### Theme Implementation

```swift
// Themes/LightTheme.swift
struct LightTheme: Theme {
    var textPrimary = DesignTokens.Color.Primitive.neutral900
    var textSecondary = DesignTokens.Color.Primitive.neutral600
    var backgroundPrimary = DesignTokens.Color.Primitive.neutral50
    var backgroundSecondary = DesignTokens.Color.Primitive.neutral100
    var interactive = DesignTokens.Color.Primitive.brand500
    
    var headingLarge = DesignTokens.Typography.headingLarge
    var bodyRegular = DesignTokens.Typography.bodyRegular
    
    var spacingUnit: CGFloat = 4
}

// Themes/DarkTheme.swift
struct DarkTheme: Theme {
    var textPrimary = DesignTokens.Color.Primitive.neutral50
    var textSecondary = DesignTokens.Color.Primitive.neutral400
    var backgroundPrimary = DesignTokens.Color.Primitive.neutral900
    var backgroundSecondary = DesignTokens.Color.Primitive.neutral800
    var interactive = DesignTokens.Color.Primitive.brand400
    
    var headingLarge = DesignTokens.Typography.headingLarge
    var bodyRegular = DesignTokens.Typography.bodyRegular
    
    var spacingUnit: CGFloat = 4
}
```

### Theme Manager

```swift
// Managers/ThemeManager.swift
class ThemeManager: ObservableObject {
    @Published var currentTheme: Theme
    
    @AppStorage("selectedTheme") private var selectedThemeId: String = "system"
    
    init() {
        self.currentTheme = Self.getTheme(for: selectedThemeId)
    }
    
    static func getTheme(for themeId: String) -> Theme {
        switch themeId {
        case "light":
            return LightTheme()
        case "dark":
            return DarkTheme()
        case "system":
            return UITraitCollection.current.userInterfaceStyle == .dark ? DarkTheme() : LightTheme()
        default:
            return LightTheme()
        }
    }
    
    func setTheme(_ themeId: String) {
        selectedThemeId = themeId
        currentTheme = Self.getTheme(for: themeId)
    }
}
```

### Environment Integration

```swift
// Views/ContentView.swift
struct ContentView: View {
    @StateObject private var themeManager = ThemeManager()
    
    var body: some View {
        NavigationView {
            // Content
        }
        .environmentObject(themeManager)
        .environment(\.theme, themeManager.currentTheme)
    }
}

// Extensions/EnvironmentValues+Theme.swift
private struct ThemeKey: EnvironmentKey {
    static let defaultValue: Theme = LightTheme()
}

extension EnvironmentValues {
    var theme: Theme {
        get { self[ThemeKey.self] }
        set { self[ThemeKey.self] = newValue }
    }
}
```

## Best Practices

### 1. Token Naming

- **Be Descriptive**: `color.text.primary` not `color.gray.900`
- **Use Hierarchy**: Group related tokens together
- **Avoid Abbreviations**: `background` not `bg`
- **Be Consistent**: Use the same naming pattern throughout

### 2. Token Organization

```
tokens/
├── global/
│   ├── color.json
│   ├── typography.json
│   ├── spacing.json
│   └── shadow.json
├── semantic/
│   ├── light.json
│   └── dark.json
└── components/
    ├── button.json
    ├── card.json
    └── input.json
```

### 3. Version Control

- **Semantic Versioning**: Tag token releases (v1.0.0)
- **Changelog**: Document all token changes
- **Breaking Changes**: Major version for incompatible changes
- **Review Process**: Require approval for token changes

### 4. Documentation

Generate documentation from tokens:

```javascript
// scripts/generate-docs.js
function generateDocs(tokens) {
  const markdown = `
# Design Tokens Documentation

Generated on: ${new Date().toISOString()}

## Colors

${generateColorDocs(tokens.color)}

## Typography

${generateTypographyDocs(tokens.typography)}

## Spacing

${generateSpacingDocs(tokens.spacing)}
  `;
  
  fs.writeFileSync('docs/tokens.md', markdown);
}
```

### 5. Testing

```swift
// Tests/TokenTests.swift
import XCTest
@testable import DesignSystem

class TokenTests: XCTestCase {
    
    func testColorContrast() {
        let textColor = DesignTokens.Color.Semantic.textPrimary
        let backgroundColor = DesignTokens.Color.Semantic.backgroundPrimary
        
        let contrastRatio = calculateContrast(textColor, backgroundColor)
        XCTAssertGreaterThanOrEqual(contrastRatio, 4.5, "Text must meet WCAG AA standards")
    }
    
    func testSpacingScale() {
        let spacings = [
            DesignTokens.Spacing.xs,
            DesignTokens.Spacing.sm,
            DesignTokens.Spacing.md,
            DesignTokens.Spacing.lg
        ]
        
        for i in 1..<spacings.count {
            XCTAssertGreaterThan(spacings[i], spacings[i-1], "Spacing scale must be incremental")
        }
    }
}
```

## Migration Strategy

### Phase 1: Setup Infrastructure
1. Install and configure Style Dictionary
2. Setup CI/CD pipeline
3. Create initial token structure

### Phase 2: Token Migration
1. Export existing colors from code to tokens
2. Map semantic meanings
3. Generate Swift code
4. Validate output matches existing values

### Phase 3: Component Migration
1. Update components to use generated tokens
2. Remove hardcoded values
3. Test thoroughly

### Phase 4: Full Adoption
1. Train team on new workflow
2. Deprecate old token system
3. Remove legacy code

## Conclusion

This design token system provides a robust, scalable foundation for maintaining design consistency across your iOS application. By automating the entire workflow from Figma to Swift code, you eliminate manual errors and ensure perfect alignment between design and implementation.

The investment in setting up this system pays dividends through:
- Faster design iterations
- Reduced bugs from design mismatches  
- Better designer-developer collaboration
- Easier theme and brand management
- Improved code maintainability

For questions or support, please refer to the internal wiki or contact the Design System team.