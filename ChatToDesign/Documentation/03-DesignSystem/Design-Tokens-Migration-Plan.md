# Design Tokens Migration Plan

## Current Situation

The codebase currently has two parallel token systems:

1. **Existing System (In Use)**:
   - `ColorPalette` → `ColorTokens` → Components
   - ColorTokens provides semantic naming (textPrimary, backgroundPrimary, etc.)
   - Used in 16 files throughout the codebase
   - ColorPalette also used directly in 10 files

2. **New System (Not Integrated)**:
   - `FigmaTokens` - comprehensive tokens exported from Figma
   - Contains colors, spacing, typography, and other design tokens
   - Currently not imported or used anywhere
   - Has different color values than the existing system

## Problem Statement

- Designers use FigmaTokens values in their designs
- Developers use ColorTokens/ColorPalette values in code
- This disconnect causes visual inconsistencies between designs and implementation
- FigmaTokens is more comprehensive but requires migration effort

## Proposed Solution

### Option 3: Semantic Bridge Approach (Recommended)

Refactor ColorTokens to use FigmaTokens as its source while maintaining the semantic API. This provides the best balance of:
- Maintaining existing semantic naming conventions
- Using actual Figma design values
- Minimal breaking changes to existing code
- Clear migration path

#### Implementation Steps:

1. **Phase 1: Create Bridge Layer**
   ```swift
   // ColorTokens.swift
   public struct ColorTokens {
     // Text Colors - now referencing FigmaTokens
     public static let textPrimary = FigmaTokens.foreground
     public static let textSecondary = FigmaTokens.mutedForeground
     public static let textTertiary = FigmaTokens.mutedForeground.opacity(0.7)
     public static let textInverse = FigmaTokens.background
     
     // Background Colors
     public static let backgroundPrimary = FigmaTokens.background
     public static let backgroundSecondary = FigmaTokens.muted
     // ... etc
   }
   ```

2. **Phase 2: Add Missing Semantic Mappings**
   - Audit all ColorTokens to find appropriate FigmaTokens mappings
   - For any missing colors in FigmaTokens, either:
     - Request designers to add them to Figma
     - Keep minimal ColorPalette for app-specific colors

3. **Phase 3: Migrate Direct ColorPalette Usage**
   - Replace direct ColorPalette usage with appropriate semantic tokens
   - Update module-specific tokens (CreateTokens, ExploreTokens)

4. **Phase 4: Extend Token System**
   - Create semantic wrappers for spacing, typography, etc.
   ```swift
   public struct SpacingTokens {
     public static let small = FigmaTokens.spacing2
     public static let medium = FigmaTokens.spacing4
     public static let large = FigmaTokens.spacing8
   }
   ```

### Alternative Options Considered

#### Option 1: Direct FigmaTokens Usage
- ❌ Requires updating all 16+ files
- ❌ Loses semantic naming
- ❌ Major breaking change

#### Option 2: Dual System
- ❌ Maintains inconsistency
- ❌ Increases complexity
- ❌ No clear source of truth

#### Option 4: Full Token Pipeline
- ❌ Requires tooling setup (Style Dictionary, etc.)
- ❌ May be overengineering for current needs
- ✅ Consider for future if token management becomes complex

## Benefits of Recommended Approach

1. **Immediate Alignment**: UI will match Figma designs
2. **Minimal Disruption**: Existing code continues to work
3. **Progressive Enhancement**: Can migrate incrementally
4. **Type Safety**: Maintains compile-time checking
5. **Semantic Clarity**: Preserves meaningful token names

## Migration Timeline

- **Week 1**: Implement bridge layer for colors
- **Week 2**: Test and verify color consistency
- **Week 3**: Migrate direct ColorPalette usage
- **Week 4**: Add spacing and typography tokens

## Risks and Mitigation

1. **Risk**: Color value changes may alter UI appearance
   - **Mitigation**: Visual regression testing, phased rollout

2. **Risk**: Missing mappings between systems
   - **Mitigation**: Audit script to find unmapped tokens

3. **Risk**: Performance impact from additional indirection
   - **Mitigation**: Static properties, no runtime overhead

## Success Criteria

- [ ] All ColorTokens map to FigmaTokens values
- [ ] No direct ColorPalette usage remains
- [ ] UI matches Figma designs pixel-perfect
- [ ] No breaking changes to component APIs
- [ ] Documentation updated

## Next Steps

1. Review and approve this plan
2. Create mapping audit spreadsheet
3. Implement Phase 1 bridge layer
4. Test in sample components
5. Roll out incrementally