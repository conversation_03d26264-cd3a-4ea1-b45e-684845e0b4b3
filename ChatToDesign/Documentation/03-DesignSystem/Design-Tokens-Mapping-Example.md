# Design Tokens Mapping Example

## Color Token Mappings

This document shows how we would map existing ColorTokens to FigmaTokens values:

### Text Colors
| Current ColorToken | Current Value | FigmaToken Mapping | Notes |
|-------------------|---------------|-------------------|--------|
| textPrimary | ColorPalette.gray950 | FigmaTokens.foreground | Main text color |
| textSecondary | ColorPalette.gray600 | FigmaTokens.mutedForeground | Secondary text |
| textTertiary | ColorPalette.gray400 | FigmaTokens.mutedForeground.opacity(0.6) | May need opacity |
| textInverse | ColorPalette.gray50 | FigmaTokens.primaryForeground | Used on dark backgrounds |

### Background Colors
| Current ColorToken | Current Value | FigmaToken Mapping | Notes |
|-------------------|---------------|-------------------|--------|
| backgroundPrimary | ColorPalette.gray50 | FigmaTokens.white | Main background |
| backgroundSecondary | ColorPalette.gray100 | FigmaTokens.muted | Secondary surfaces |
| backgroundTertiary | ColorPalette.gray200 | FigmaTokens.sidebarBackground | Sidebar/tertiary |
| backgroundInverse | ColorPalette.gray950 | FigmaTokens.background | Dark mode background |

### Interactive Colors
| Current ColorToken | Current Value | FigmaToken Mapping | Notes |
|-------------------|---------------|-------------------|--------|
| interactive | ColorPalette.brand500 | FigmaTokens.brand | Primary brand color |
| interactiveHover | ColorPalette.brand600 | FigmaTokens.brand.opacity(0.8) | May need adjustment |
| interactiveActive | ColorPalette.brand700 | FigmaTokens.brand.opacity(0.6) | May need adjustment |

### Status Colors
| Current ColorToken | Current Value | FigmaToken Mapping | Notes |
|-------------------|---------------|-------------------|--------|
| statusSuccess | ColorPalette.success500 | FigmaTokens.colorsGreen | Success states |
| statusWarning | ColorPalette.warning500 | FigmaTokens.colorsRed | Warning states |
| statusError | ColorPalette.error500 | FigmaTokens.colorsRed | Error states |

## Direct ColorPalette Usage Migration

### Components Currently Using ColorPalette Directly

1. **AddImageButton.swift**
   ```swift
   // Current
   ColorPalette.gray800
   ColorPalette.gray750
   
   // Proposed
   ColorTokens.backgroundInverse
   ColorTokens.backgroundTertiary
   ```

2. **CreateTokens.swift**
   ```swift
   // Current
   static let overlayBackground = ColorPalette.gray800.opacity(0.4)
   
   // Proposed
   static let overlayBackground = FigmaTokens.overlaysDefault
   ```

3. **ExploreTokens.swift**
   ```swift
   // Current
   static let gridBackground = ColorPalette.gray150
   
   // Proposed
   static let gridBackground = FigmaTokens.muted
   ```

## New Token Categories to Add

### Spacing Tokens (Semantic Layer)
```swift
public struct SpacingTokens {
    // Component spacing
    static let componentSmall = FigmaTokens.spacing2    // 8pt
    static let componentMedium = FigmaTokens.spacing4   // 16pt
    static let componentLarge = FigmaTokens.spacing6    // 24pt
    
    // Layout spacing
    static let layoutSmall = FigmaTokens.spacing4      // 16pt
    static let layoutMedium = FigmaTokens.spacing8     // 32pt
    static let layoutLarge = FigmaTokens.spacing16     // 64pt
}
```

### Typography Tokens (Semantic Layer)
```swift
public struct TypographyTokens {
    // Headings
    static let headingLarge = FigmaTokens.text2xlMedium
    static let headingMedium = FigmaTokens.textXlSemibold
    
    // Body
    static let bodyRegular = FigmaTokens.textBaseMedium
    static let bodySmall = FigmaTokens.textSmMedium
    
    // UI Elements
    static let button = FigmaTokens.componentButton
    static let caption = FigmaTokens.textXsNormal
}
```

## Validation Checklist

- [ ] All current ColorTokens have FigmaToken equivalents
- [ ] Color values are visually similar (test in UI)
- [ ] No tokens are missing from FigmaTokens
- [ ] Module-specific tokens are mapped correctly
- [ ] Typography and spacing tokens are comprehensive