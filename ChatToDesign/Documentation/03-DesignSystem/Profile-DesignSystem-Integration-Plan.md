# Profile 模块接入 Design System 技术方案

## 项目概述

本文档描述了将 `Presentation/Profile` 目录中的组件集成到现有 Design System 体系的技术方案。通过统一设计规范和组件库，提升代码可维护性、视觉一致性，并促进组件复用。

## 现状分析

### 现有 Design System 架构

ChatToDesign 项目已建立了完善的设计系统架构：

#### 1. Foundation Layer (基础层)
- **Colors**: `ColorPalette` 提供统一的颜色调色板，从 gray50 到 gray950，以及品牌色和语义色
- **Typography**: `Typography` 提供层次化字体系统，包括 Display、Headline、Body、Label 等
- **Spacing**: `Spacing` 提供标准化间距系统，从 2px 到 128px
- **Animation**: `AnimationTokens` 提供统一的动画配置

#### 2. Tokens Layer (令牌层)
- **ColorTokens**: 语义化颜色令牌，如 `textPrimary`、`interactive`、`backgroundPrimary` 等
- **ComponentTokens**: 组件专用令牌，目前包含 `DesignSystem.Button.*` 配置

#### 3. Components Layer (组件层)
- **DSButton**: 成熟的按钮组件，支持多种样式、尺寸、状态和配置选项

### Profile 模块现状问题

通过代码审查发现以下问题：

#### 1. 硬编码设计值
```swift
// ConnectedActionButton.swift - 硬编码颜色
.foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
.background(Color(red: 0.157, green: 0.157, blue: 0.188))      // #27272a

// UserInfoView.swift - 硬编码字体
.font(.custom("Inter", size: 14).weight(.semibold))
```

#### 2. 设计不一致性
- 相同语义色在不同组件中使用不同的硬编码值
- 字体大小、间距缺乏标准化
- 组件间缺乏统一的视觉语言

#### 3. 缺乏复用性
- `ConnectedActionButton` 与 `DSButton` 功能重叠但实现不同
- 自定义组件未遵循 Design System 的架构模式

## 技术方案

### 阶段 1: 设计令牌扩展 (参考 Create 模块方案)

#### 1.1 扩展 ColorTokens (添加 Profile 模块色彩)
参考 Create 模块的实践，在 `ColorTokens.swift` 中为 Profile 模块添加专用颜色：

```swift
// ChatToDesign/DesignSystem/Tokens/ColorTokens.swift
public struct ColorTokens {
    // 现有颜色...
    
    // MARK: - Profile Module Colors
    public static let profileBackground = Color(red: 0.035, green: 0.035, blue: 0.043) // #09090b
    public static let profileCardBackground = ColorPalette.gray800 // #27272a
    public static let profileActionBackground = ColorPalette.gray800 // #27272a  
    public static let profileActionIcon = ColorPalette.gray400 // #a1a1aa
    public static let profileUsernameText = ColorPalette.gray50 // #fafafa
    public static let profilePromptText = ColorPalette.gray400 // #a1a1aa
    public static let profileOverlay = Color.black.opacity(0.4)
    public static let profileRetryBackground = ColorPalette.error500.opacity(0.8)
    public static let profileShadow = Color.black.opacity(0.5)
}
```

#### 1.2 扩展 ComponentTokens (添加 Profile 组件配置)
在 `ComponentTokens.swift` 中添加 Profile 模块的组件令牌：

```swift
// ChatToDesign/DesignSystem/Tokens/ComponentTokens.swift
public struct DesignSystem {
    // 现有组件...
    
    // MARK: - Profile Module Components
    public struct Profile {
        // MARK: - Page Layout
        public struct Page {
            public struct Layout {
                public static let headerPadding: CGFloat = 24
                public static let sectionSpacing: CGFloat = 24
                public static let bottomSpacing: CGFloat = 120
            }
            
            public struct Colors {
                public static let background = ColorTokens.profileBackground
            }
        }
        
        // MARK: - Action Button Component
        public struct ActionButton {
            public struct Layout {
                public static let height: CGFloat = 80
                public static let iconSize: CGFloat = 24
                public static let cornerRadius: CGFloat = 16
                public static let spacing: CGFloat = 8
            }
            
            public struct Colors {
                public static let background = ColorTokens.profileActionBackground
                public static let icon = ColorTokens.profileActionIcon
                public static let text = ColorTokens.profileUsernameText
            }
            
            public struct Typography {
                public static let label = Typography.labelMedium.weight(.semibold)
            }
        }
        
        // MARK: - User Info Component
        public struct UserInfo {
            public struct Layout {
                public static let verticalSpacing: CGFloat = 8
                public static let horizontalPadding: CGFloat = 24
                public static let bottomPadding: CGFloat = 16
            }
            
            public struct Colors {
                public static let username = ColorTokens.profileUsernameText
                public static let prompt = ColorTokens.profilePromptText
            }
            
            public struct Typography {
                public static let username = Typography.labelMedium.weight(.semibold)
                public static let prompt = Typography.bodyMedium
            }
        }
        
        // MARK: - Social Actions Component
        public struct SocialActions {
            public struct Layout {
                public static let iconSize: CGFloat = 24
                public static let itemSpacing: CGFloat = 24
                public static let iconToTextSpacing: CGFloat = 8
                public static let trailingPadding: CGFloat = 24
                public static let verticalPadding: CGFloat = 44
            }
            
            public struct Colors {
                public static let icon = ColorTokens.textInverse
                public static let text = ColorTokens.textInverse
                public static let shadow = ColorTokens.profileShadow
            }
            
            public struct Typography {
                public static let label = Typography.labelMedium.weight(.semibold)
            }
        }
        
        // MARK: - Creation Item Component
        public struct CreationItem {
            public struct Layout {
                public static let cornerRadius: CGFloat = 16
                public static let progressSize: CGFloat = 30
                public static let progressLineWidth: CGFloat = 3
                public static let retryButtonSize: CGFloat = 32
                public static let retryButtonPadding: CGFloat = 8
            }
            
            public struct Colors {
                public static let placeholder = ColorTokens.backgroundTertiary
                public static let placeholderIcon = ColorTokens.textSecondary
                public static let overlay = ColorTokens.profileOverlay
                public static let progressTrack = ColorTokens.textInverse.opacity(0.3)
                public static let progressFill = ColorTokens.textInverse
                public static let statusBackground = Color.black.opacity(0.6)
                public static let statusText = ColorTokens.textInverse
                public static let retryBackground = ColorTokens.profileRetryBackground
                public static let retryIcon = ColorTokens.textInverse
            }
            
            public struct Typography {
                public static let status = Typography.labelSmall
            }
        }
    }
}
```

### 阶段 2: 组件重构

#### 2.1 重构 ConnectedActionButton
将现有的 `ConnectedActionButton` 重构为使用 DesignSystem.Profile 令牌：

```swift
struct ConnectedActionButton: View {
    // 保持现有接口...
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: DesignSystem.Profile.ActionButton.Layout.spacing) {
                // 图标部分
                Group {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Profile.ActionButton.Colors.icon))
                            .scaleEffect(0.8)
                    } else if let iconName = iconName {
                        Image(iconName)
                            .resizable()
                            .renderingMode(.template)
                            .foregroundColor(DesignSystem.Profile.ActionButton.Colors.icon)
                            .frame(width: DesignSystem.Profile.ActionButton.Layout.iconSize, 
                                   height: DesignSystem.Profile.ActionButton.Layout.iconSize)
                    } else if let icon = icon {
                        Image(systemName: icon)
                            .font(.system(size: DesignSystem.Profile.ActionButton.Layout.iconSize))
                            .foregroundColor(DesignSystem.Profile.ActionButton.Colors.icon)
                    }
                }
                .frame(width: DesignSystem.Profile.ActionButton.Layout.iconSize, 
                       height: DesignSystem.Profile.ActionButton.Layout.iconSize)
                
                // 标题
                Text(title)
                    .font(DesignSystem.Profile.ActionButton.Typography.label)
                    .foregroundColor(DesignSystem.Profile.ActionButton.Colors.text)
            }
            .frame(maxWidth: .infinity)
            .frame(height: DesignSystem.Profile.ActionButton.Layout.height)
            .background(DesignSystem.Profile.ActionButton.Colors.background)
            .clipShape(
                RoundedCorners(
                    topLeft: position == .leading ? DesignSystem.Profile.ActionButton.Layout.cornerRadius : 0,
                    topRight: position == .trailing ? DesignSystem.Profile.ActionButton.Layout.cornerRadius : 0,
                    bottomLeft: position == .leading ? DesignSystem.Profile.ActionButton.Layout.cornerRadius : 0,
                    bottomRight: position == .trailing ? DesignSystem.Profile.ActionButton.Layout.cornerRadius : 0
                )
            )
        }
        .disabled(isLoading)
    }
}
```

#### 2.2 重构 UserInfoView
标准化用户信息展示组件：

```swift
struct UserInfoView: View {
    // 保持现有接口...
    
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Profile.UserInfo.Layout.verticalSpacing) {
            // 用户名
            HStack {
                Text("@\(username)")
                    .font(DesignSystem.Profile.UserInfo.Typography.username)
                    .foregroundColor(DesignSystem.Profile.UserInfo.Colors.username)
                Spacer()
            }
            
            // 提示文本
            Text(displayPrompt)
                .font(DesignSystem.Profile.UserInfo.Typography.prompt)
                .foregroundColor(DesignSystem.Profile.UserInfo.Colors.prompt)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
        }
        .padding(.horizontal, DesignSystem.Profile.UserInfo.Layout.horizontalPadding)
        .padding(.bottom, DesignSystem.Profile.UserInfo.Layout.bottomPadding)
    }
    
    private var displayPrompt: String {
        if let prompt = prompt, !prompt.isEmpty {
            return prompt
        } else {
            return "Visualize a futuristic city skyline with flying cars, neon lights, and advanced technology, sho..."
        }
    }
}
```

#### 2.3 创建通用进度组件
将 `CircularProgressView` 提升为可复用的 Design System 组件：

```swift
// DesignSystem/Components/DSProgress.swift
public struct DSProgress: View {
    public enum Style {
        case circular
        case linear
    }
    
    public enum Size {
        case small   // 20x20
        case medium  // 30x30
        case large   // 40x40
    }
    
    let progress: Double?  // nil 表示无限加载
    let style: Style
    let size: Size
    
    public var body: some View {
        // 实现标准化进度组件...
    }
}
```

### 阶段 3: 组件整合优化

#### 3.1 评估按钮组件整合
分析 `ConnectedActionButton` 与 `DSButton` 的差异：

**ConnectedActionButton 特点:**
- 垂直布局（图标上，文字下）
- 连接样式（可设置特定圆角）
- Profile 页面专用语义

**整合策略:**
1. **保留专用组件**: 由于 UI 模式差异较大，建议保留 `ConnectedActionButton` 作为 Profile 模块专用组件
2. **内部使用 DSButton 原则**: 使用 Design System 的设计令牌，但保持独特的布局逻辑
3. **未来考虑**: 可在 DSButton 中增加垂直布局选项，但当前阶段风险较大

#### 3.2 重构 LikeShareActionsView
重构社交动作组件使用 DesignSystem.Profile 令牌：

```swift
struct LikeShareActionsView: View {
    let likeCount: Int
    let onLike: () -> Void
    let onShare: () -> Void

    var body: some View {
        VStack(spacing: DesignSystem.Profile.SocialActions.Layout.itemSpacing) {
            // 点赞按钮
            VStack(spacing: DesignSystem.Profile.SocialActions.Layout.iconToTextSpacing) {
                Button(action: onLike) {
                    Image(systemName: "heart")
                        .font(.system(size: DesignSystem.Profile.SocialActions.Layout.iconSize, weight: .medium))
                        .foregroundColor(DesignSystem.Profile.SocialActions.Colors.icon)
                        .shadow(color: DesignSystem.Profile.SocialActions.Colors.shadow, radius: 1, x: 0, y: 1)
                }
                .frame(width: DesignSystem.Profile.SocialActions.Layout.iconSize, 
                       height: DesignSystem.Profile.SocialActions.Layout.iconSize)

                Text("\(likeCount)")
                    .font(DesignSystem.Profile.SocialActions.Typography.label)
                    .foregroundColor(DesignSystem.Profile.SocialActions.Colors.text)
                    .shadow(color: DesignSystem.Profile.SocialActions.Colors.shadow, radius: 1, x: 0, y: 1)
            }

            // 分享按钮
            VStack(spacing: DesignSystem.Profile.SocialActions.Layout.iconToTextSpacing) {
                Button(action: onShare) {
                    Image(systemName: "arrowshape.turn.up.right")
                        .font(.system(size: DesignSystem.Profile.SocialActions.Layout.iconSize, weight: .medium))
                        .foregroundColor(DesignSystem.Profile.SocialActions.Colors.icon)
                        .shadow(color: DesignSystem.Profile.SocialActions.Colors.shadow, radius: 1, x: 0, y: 1)
                }
                .frame(width: DesignSystem.Profile.SocialActions.Layout.iconSize, 
                       height: DesignSystem.Profile.SocialActions.Layout.iconSize)

                Text("Share")
                    .font(DesignSystem.Profile.SocialActions.Typography.label)
                    .foregroundColor(DesignSystem.Profile.SocialActions.Colors.text)
                    .shadow(color: DesignSystem.Profile.SocialActions.Colors.shadow, radius: 1, x: 0, y: 1)
            }
        }
        .padding(.trailing, DesignSystem.Profile.SocialActions.Layout.trailingPadding)
        .padding(.vertical, DesignSystem.Profile.SocialActions.Layout.verticalPadding)
    }
}
```

#### 3.3 重构 CreationItemView
将缩略图展示组件标准化：

```swift
struct CreationItemView: View {
    // 保持现有接口...

    var body: some View {
        Button(action: {
            if item.isCompleted {
                showDetailPage = true
            } else if item.canRetry {
                onFailedTaskTap?(item)
            }
        }) {
            ZStack {
                // 缩略图
                thumbnailImageView
                
                // 状态覆盖层
                if !item.isCompleted {
                    statusOverlayView
                }
                
                // 重试按钮
                if item.canRetry {
                    retryButtonView
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .fullScreenCover(isPresented: $showDetailPage) {
            if case .asset(let asset) = item {
                CreateDetailPage(asset: asset)
            }
        }
    }
    
    private var thumbnailImageView: some View {
        // 使用 DesignSystem.Profile.CreationItem 令牌...
        Group {
            if let thumbnailUrl = item.thumbnailUrl, !thumbnailUrl.isEmpty {
                KFImage(URL(string: thumbnailUrl))
                    .placeholder {
                        Rectangle()
                            .fill(DesignSystem.Profile.CreationItem.Colors.placeholder)
                            .overlay(
                                Image(systemName: "photo")
                                    .foregroundColor(DesignSystem.Profile.CreationItem.Colors.placeholderIcon)
                                    .font(.title2)
                            )
                    }
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: width, height: randomHeight())
                    .clipped()
            } else {
                Rectangle()
                    .fill(DesignSystem.Profile.CreationItem.Colors.placeholder)
                    .frame(width: width, height: randomHeight())
                    .overlay(
                        Image(systemName: "photo")
                            .foregroundColor(DesignSystem.Profile.CreationItem.Colors.placeholderIcon)
                            .font(.title2)
                    )
            }
        }
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Profile.CreationItem.Layout.cornerRadius))
    }
    
    private var statusOverlayView: some View {
        ZStack {
            DesignSystem.Profile.CreationItem.Colors.overlay
            
            VStack(spacing: Spacing.space8) {
                // 进度指示器使用标准化配置
                if case .processing(let progress) = item.status {
                    if let progress = progress {
                        CircularProgressView(
                            progress: Double(progress) / 100.0,
                            size: DesignSystem.Profile.CreationItem.Layout.progressSize,
                            lineWidth: DesignSystem.Profile.CreationItem.Layout.progressLineWidth,
                            trackColor: DesignSystem.Profile.CreationItem.Colors.progressTrack,
                            fillColor: DesignSystem.Profile.CreationItem.Colors.progressFill
                        )
                    } else {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Profile.CreationItem.Colors.progressFill))
                    }
                }
                
                Text(item.status.displayText)
                    .font(DesignSystem.Profile.CreationItem.Typography.status)
                    .foregroundColor(DesignSystem.Profile.CreationItem.Colors.statusText)
                    .padding(.horizontal, Spacing.space8)
                    .padding(.vertical, Spacing.space4)
                    .background(DesignSystem.Profile.CreationItem.Colors.statusBackground)
                    .clipShape(Capsule())
            }
        }
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Profile.CreationItem.Layout.cornerRadius))
    }
    
    private var retryButtonView: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                Button(action: { onRetry?(item) }) {
                    Image(systemName: "arrow.clockwise")
                        .foregroundColor(DesignSystem.Profile.CreationItem.Colors.retryIcon)
                        .font(.title3)
                        .padding(DesignSystem.Profile.CreationItem.Layout.retryButtonPadding)
                        .background(DesignSystem.Profile.CreationItem.Colors.retryBackground)
                        .clipShape(Circle())
                }
                .padding(.trailing, DesignSystem.Profile.CreationItem.Layout.retryButtonPadding)
                .padding(.bottom, DesignSystem.Profile.CreationItem.Layout.retryButtonPadding)
            }
        }
    }
}

### 阶段 4: 主题和暗色模式支持

#### 4.1 主题支持
Profile 模块通过 ColorTokens 自动获得主题支持，无需额外配置。所有颜色都基于 ColorPalette 和现有的语义化令牌，确保在不同主题下的一致性。

## 实施计划

### 第一阶段 (1-2 天): 设计令牌扩展
1. **扩展 ColorTokens**
   - 在 `ColorTokens.swift` 中添加 Profile 模块专用颜色
   - 映射现有硬编码颜色到标准 ColorPalette
   
2. **扩展 ComponentTokens**
   - 在 `ComponentTokens.swift` 中添加 `DesignSystem.Profile` 组件配置
   - 定义 ActionButton、UserInfo、SocialActions、CreationItem 组件令牌

### 第二阶段 (2-3 天): 组件重构
3. **重构核心组件**
   - 重构 `ConnectedActionButton` 使用 `DesignSystem.Profile.ActionButton`
   - 重构 `UserInfoView` 使用 `DesignSystem.Profile.UserInfo`
   - 重构 `LikeShareActionsView` 使用 `DesignSystem.Profile.SocialActions`
   - 重构 `CreationItemView` 使用 `DesignSystem.Profile.CreationItem`

### 第三阶段 (1-2 天): 标准化组件
4. **通用组件提升**
   - 评估 `CircularProgressView` 是否提升为 `DSProgress`
   - 确保 Profile 页面布局使用 `DesignSystem.Profile.Page` 配置
   - 验证组件间的视觉一致性

### 第四阶段 (1 天): 验证和优化
5. **质量保证**
   - 运行应用验证视觉一致性
   - 检查主题支持 (继承 ColorTokens)
   - 性能验证和代码审查
   - 文档更新

## 预期收益

### 技术收益
1. **设计系统标准化**: 遵循 Create 模块的成功模式，在 ComponentTokens 中建立模块化令牌体系
2. **可维护性提升**: 统一的设计令牌便于全局样式调整和维护
3. **类型安全**: 减少硬编码值，通过设计令牌提升代码安全性
4. **模块化架构**: 为其他模块接入 DesignSystem 提供参考模式

### 业务收益
1. **视觉一致性**: Profile 模块组件间保持统一的视觉语言
2. **开发效率**: 新功能开发直接使用 `DesignSystem.Profile` 配置
3. **设计协作**: 设计师和开发者基于统一的令牌体系协作
4. **品牌连贯性**: 与其他已接入 DesignSystem 的模块保持一致

### 用户收益
1. **体验一致性**: Profile 功能的视觉和交互保持统一
2. **主题兼容**: 自动支持应用的主题切换功能
3. **性能稳定**: 标准化组件减少渲染异常和性能问题

## 风险评估与缓解

### 技术风险
1. **视觉回归**: 
   - **缓解**: 在开发环境中对比重构前后的视觉效果
   - **监控**: 使用 SwiftUI Preview 确保一致性

2. **性能影响**: 
   - **缓解**: Design System 通常会优化性能，风险较低
   - **验证**: 在设备上测试滑动性能

### 业务风险
1. **开发时间**: 
   - **缓解**: 分阶段实施，每个阶段都能独立交付价值
   - **预估**: 总计 6-8 天开发时间

## 后续发展

### 短期规划 (接下来 2-4 周)
1. **其他模块接入**: 将 Create、Explore 等模块也接入 Design System
2. **组件库扩展**: 根据需求添加更多标准化组件

### 中期规划 (1-3 个月)
1. **设计工具集成**: 考虑与 Figma 等设计工具的集成
2. **文档完善**: 建立完整的 Design System 文档站点

### 长期规划 (3-6 个月)
1. **自动化测试**: 建立视觉回归测试
2. **主题系统**: 支持多套主题和个性化定制

---

*本方案基于对现有代码的深入分析制定，旨在在保持向后兼容的前提下，逐步提升代码质量和用户体验。*