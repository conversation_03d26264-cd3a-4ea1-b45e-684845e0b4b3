# Explore 接口集成技术方案

## 📋 概述

本文档描述了如何在 Explore 页面集成 Explore 接口，使用项目现有的 SWR 架构模式来实现数据获取和缓存管理。

## 🎯 目标

1. 接入 Explore 接口：`https://api-test.picadabra.ai/api/v1/explore?page=1&limit=20&sourceType=all&sortBy=latest`
2. 在 ExplorePageView 中使用 SWR 方式调用该接口
3. 遵循项目现有架构模式和最佳实践

## 🏗️ 现有架构分析

### API 服务架构

项目使用以下架构模式：

```
APIEndpoint (Moya) → DefaultAPIService → ViewModel (SWRHook) → View
```

**核心组件**：
- `APIEndpoint`: 定义 API 端点和请求配置
- `DefaultAPIService`: 实现具体的网络请求逻辑
- `SWRHook`: 提供缓存和状态管理
- `ViewModel`: 业务逻辑和状态管理

### SWR 使用模式

项目中 SWR 的典型使用模式：

```swift
// 1. 在 ViewModel 中创建 SWRHook
private var exploreHook: SWRHook<ExploreResponse>?

// 2. 设置 Hook
private func setupExploreHook() {
    exploreHook = SWRHook.create(
        key: "explore_data",
        maxAge: 5 * 60,      // 5分钟后触发后台刷新
        staleTime: 30 * 60,  // 30分钟后数据完全过期
        networkCall: { [weak self] in
            guard let self = self else { throw APIServiceError.unknown(...) }
            return try await self.apiService.fetchExploreData(query: query)
        },
        autoFetch: true
    )
    
    observeExploreHookChanges()
}

// 3. 监听状态变化
private func observeExploreHookChanges() {
    exploreHook?.$data
        .compactMap { $0 }
        .assign(to: \.exploreData, on: self)
        .store(in: &cancellables)
        
    exploreHook?.$isLoading
        .assign(to: \.isLoading, on: self)
        .store(in: &cancellables)
        
    exploreHook?.$error
        .assign(to: \.error, on: self)
        .store(in: &cancellables)
}
```

## 📊 API 接口分析

### 接口信息

- **URL**: `https://api-test.picadabra.ai/api/v1/explore`
- **方法**: GET
- **参数**:
  - `page`: 页码 (默认: 1)
  - `limit`: 每页数量 (默认: 20)
  - `sourceType`: 来源类型 (all/ai_generated/user_uploaded)
  - `sortBy`: 排序方式 (latest/popular/trending)

### 响应数据结构

根据 curl 测试结果，响应包含：

```json
{
  "items": [
    {
      "id": "a2ccc6d4-8573-4b97-9d2c-3c64c72ef939",
      "type": "video/mp4",
      "sourceType": "ai_generated", 
      "userDisplayName": "Deniffer 003",
      "userPhotoURL": "https://handsome.guy/",
      "url": "https://file.302.ai/gpt/imgs/20240716800b1680b4de1.mp4",
      "thumbnailUrl": "https://file.302.ai/gpt/imgs/20240716800b1680b4de1.jpeg",
      "size": 5,
      "generationPrompt": "Video content\\n\\nThe person in Figure One is dressed in the outfit from Figure Two, holding an Oscar statuette, standing at the center of the Oscar awards stage, smiling at the camera while delivering an acceptance speech.\\n\\nA precise description of the person in Figure One, including their facial features, expression, hairstyle, and the age and gender of the character based on the gender or the appearance of the character. If the character is female, she wears the gown from Figure Two. If the character is male, he wears a stylish suit.\\n\\nAdditional Information as 'None': .",
      "likeCount": 0,
      "favoriteCount": 0,
      "createdAt": "2025-07-07T13:48:50.152Z"
    }
  ]
}
```

## 🔧 实现方案

### 1. 数据模型定义

创建 Explore 相关的数据模型：

```swift
// ChatToDesign/Domain/Models/ExploreModels.swift

/// Explore 查询参数
public struct ExploreQuery: Codable {
    public let page: Int
    public let limit: Int
    public let sourceType: String
    public let sortBy: String
    
    public init(
        page: Int = 1,
        limit: Int = 20,
        sourceType: String = "all",
        sortBy: String = "latest"
    ) {
        self.page = page
        self.limit = limit
        self.sourceType = sourceType
        self.sortBy = sortBy
    }
}

/// Explore 响应数据
public struct ExploreResponse: Codable {
    public let items: [ExploreItem]
    
    public init(items: [ExploreItem]) {
        self.items = items
    }
}

/// Explore 项目数据
public struct ExploreItem: Identifiable, Codable {
    public let id: String
    public let type: String
    public let sourceType: String
    public let userDisplayName: String?
    public let userPhotoURL: String?
    public let url: String
    public let thumbnailUrl: String?
    public let size: Int
    public let generationPrompt: String?
    public let likeCount: Int
    public let favoriteCount: Int
    public let createdAt: String
    
    // 计算属性
    public var isVideo: Bool {
        return type.hasPrefix("video/")
    }
    
    public var isAIGenerated: Bool {
        return sourceType == "ai_generated"
    }
}
```

### 2. API 端点扩展

在 `DefaultAPIService.swift` 中添加 Explore 端点：

```swift
// 在 APIEndpoint 枚举中添加
case fetchExploreData(query: ExploreQuery)

// 在 path 中添加
case .fetchExploreData:
    return "/api/v1/explore"

// 在 task 中添加
case .fetchExploreData(let query):
    return .requestParameters(
        parameters: [
            "page": query.page,
            "limit": query.limit,
            "sourceType": query.sourceType,
            "sortBy": query.sortBy
        ],
        encoding: URLEncoding.queryString
    )
```

### 3. API 服务接口扩展

在 `APIService.swift` 协议中添加方法：

```swift
/// 获取 Explore 数据
/// - Parameter query: 查询参数
/// - Returns: Explore 响应数据
func fetchExploreData(query: ExploreQuery) async throws -> ExploreResponse
```

### 4. ExplorePageViewModel 实现

创建专门的 ViewModel：

```swift
// ChatToDesign/Presentation/Explore/ExplorePageViewModel.swift

@MainActor
class ExplorePageViewModel: ObservableObject {
    // MARK: - Published Properties
    
    @Published var exploreItems: [ExploreItem] = []
    @Published var isLoading: Bool = false
    @Published var error: Error? = nil
    
    // Filter state
    @Published var selectedSourceType: String = "all"
    @Published var selectedSortBy: String = "latest"
    
    // MARK: - Private Properties
    
    private var exploreHook: SWRHook<ExploreResponse>?
    private let apiService: APIService
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(apiService: APIService) {
        self.apiService = apiService
        setupExploreHook()
    }
    
    // MARK: - Public Methods
    
    func refresh() {
        exploreHook?.refresh()
    }
    
    func updateFilters(sourceType: String, sortBy: String) {
        selectedSourceType = sourceType
        selectedSortBy = sortBy
        setupExploreHook() // 重新设置 Hook 以应用新的过滤条件
    }
    
    // MARK: - Private Methods
    
    private func setupExploreHook() {
        let query = ExploreQuery(
            page: 1,
            limit: 20,
            sourceType: selectedSourceType,
            sortBy: selectedSortBy
        )
        
        exploreHook = SWRHook.create(
            key: "explore_data_\(selectedSourceType)_\(selectedSortBy)",
            maxAge: 5 * 60,      // 5分钟后触发后台刷新
            staleTime: 30 * 60,  // 30分钟后数据完全过期
            networkCall: { [weak self] in
                guard let self = self else {
                    throw APIServiceError.unknown(NSError(domain: "ViewModel", code: -1))
                }
                return try await self.apiService.fetchExploreData(query: query)
            },
            autoFetch: true
        )
        
        observeExploreHookChanges()
    }
    
    private func observeExploreHookChanges() {
        exploreHook?.$data
            .compactMap { $0?.items }
            .assign(to: \.exploreItems, on: self)
            .store(in: &cancellables)
            
        exploreHook?.$isLoading
            .assign(to: \.isLoading, on: self)
            .store(in: &cancellables)
            
        exploreHook?.$error
            .assign(to: \.error, on: self)
            .store(in: &cancellables)
    }
}
```

### 5. ExplorePageView 更新

更新视图以使用 ViewModel：

```swift
// ChatToDesign/Presentation/Explore/ExplorePageView.swift

struct ExplorePageView: View {
    @StateObject private var viewModel: ExplorePageViewModel
    
    init(viewModel: ExplorePageViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }
    
    var body: some View {
        ZStack {
            Color(red: 0.035, green: 0.035, blue: 0.043)
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                navigationHeader
                
                if viewModel.isLoading && viewModel.exploreItems.isEmpty {
                    loadingView
                } else if viewModel.exploreItems.isEmpty {
                    emptyStateView
                } else {
                    exploreContentView
                }
                
                Spacer()
                    .frame(height: 120)
            }
        }
        .refreshable {
            viewModel.refresh()
        }
    }
    
    // MARK: - Content Views
    
    private var exploreContentView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(viewModel.exploreItems) { item in
                    ExploreItemCard(item: item)
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    private var loadingView: some View {
        VStack {
            Spacer()
            ProgressView()
                .scaleEffect(1.2)
                .tint(.white)
            Spacer()
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "photo.on.rectangle.angled")
                .font(.system(size: 64))
                .foregroundColor(.white.opacity(0.6))
            
            Text("No content found")
                .font(.custom("Inter", size: 20).weight(.semibold))
                .foregroundColor(.white)
            
            Text("Try adjusting your filters or check back later")
                .font(.custom("Inter", size: 14))
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
        }
    }
}
```

## 🔄 集成步骤

1. **创建数据模型** - 定义 ExploreModels.swift
2. **扩展 API 服务** - 添加 Explore 端点和服务方法
3. **实现 ViewModel** - 创建 ExplorePageViewModel 使用 SWR
4. **更新视图** - 修改 ExplorePageView 集成 ViewModel
5. **依赖注入** - 在 AppDependencyContainer 中配置依赖
6. **测试验证** - 编写单元测试和集成测试

## 📝 注意事项

1. **缓存策略**: 使用 5 分钟的 maxAge 和 30 分钟的 staleTime，适合内容发现场景
2. **错误处理**: 遵循项目现有的错误处理模式
3. **分页支持**: 当前实现支持基础分页，后续可扩展无限滚动
4. **过滤功能**: 支持按来源类型和排序方式过滤
5. **性能优化**: 使用 LazyVGrid 和图片缓存优化性能

## 🧪 测试计划

1. **单元测试**: 测试 ViewModel 的状态管理和数据转换
2. **集成测试**: 测试 API 调用和 SWR 缓存行为
3. **UI 测试**: 测试用户交互和状态展示
4. **性能测试**: 测试大量数据的渲染性能

这个方案遵循了项目现有的架构模式，使用 SWR 进行数据管理，确保了代码的一致性和可维护性。
