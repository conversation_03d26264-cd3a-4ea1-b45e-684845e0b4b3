# Creations Display Enhancement - 在My Creations区域展示进行中的任务

## 概述

当前系统只在任务成功完成后才在"My Creations"区域显示对应的UserAsset，但用户需要感知正在生成的资产。本文档描述了如何在creations区域展示非succeeded状态的imageTask/videoTask的技术方案。

## 问题分析

### 当前架构限制
1. **数据流断层**：只有succeeded状态的task才会生成UserAsset
2. **用户体验缺失**：用户无法在Profile页面看到正在进行的生成任务
3. **状态感知不足**：用户需要通过其他途径（如MyCreationsBanner）才能了解任务进度

### 现有相关组件
- `UserAsset`: 已完成的资产实体
- `ImageGenerationTask`/`VideoGenerationTask`: 任务实体，包含完整状态管理
- `ProfilePageViewModel`: 当前只观察UserAsset
- `MyCreationsBanner`: 

## 技术方案

### 1. 统一数据模型设计

#### 1.1 CreationItem枚举
创建统一的创作内容表示模型：

```swift
// ChatToDesign/Domain/Entities/CreationItem.swift
public enum CreationItem: Identifiable, Equatable {
  case asset(UserAsset)
  case imageTask(ImageGenerationTask)
  case videoTask(VideoGenerationTask)
  
  public var id: String {
    switch self {
    case .asset(let asset): return asset.id
    case .imageTask(let task): return task.taskId
    case .videoTask(let task): return task.taskId
    }
  }
}
```

#### 1.2 CreationStatus枚举
统一的状态表示：

```swift
public enum CreationStatus: Equatable {
  case pending
  case processing(progress: Int?)
  case completed
  case failed(error: String?)
  
  public var displayText: String {
    switch self {
    case .pending: return "排队中"
    case .processing(let progress):
      if let progress = progress {
        return "生成中 \(progress)%"
      }
      return "生成中"
    case .completed: return "已完成"
    case .failed: return "生成失败"
    }
  }
}
```

#### 1.3 CreationItem扩展
提供统一的显示属性：

```swift
extension CreationItem {
  public var thumbnailUrl: String? {
    switch self {
    case .asset(let asset):
      return asset.thumbnailUrl ?? asset.url
    case .imageTask(let task):
      return task.inputImageUrls?.first
    case .videoTask(let task):
      return task.inputImageUrl ?? task.coverImageUrl
    }
  }
  
  public var status: CreationStatus {
    switch self {
    case .asset: return .completed
    case .imageTask(let task): return mapTaskStatus(task.status, progress: task.progress)
    case .videoTask(let task): return mapTaskStatus(task.status, progress: task.progress)
    }
  }
  
  public var createdAt: Date? {
    switch self {
    case .asset(let asset): return asset.createdAt?.dateValue()
    case .imageTask(let task): return task.createdAt?.dateValue()
    case .videoTask(let task): return task.createdAt?.dateValue()
    }
  }
  
  public var isCompleted: Bool {
    switch self {
    case .asset: return true
    case .imageTask(let task): return task.status == .succeeded
    case .videoTask(let task): return task.status == .succeeded
    }
  }
  
  public var canRetry: Bool {
    switch self {
    case .asset: return false
    case .imageTask(let task): return task.status == .failed
    case .videoTask(let task): return task.status == .failed
    }
  }
}
```

### 2. ViewModel架构调整

#### 2.1 ProfilePageViewModel扩展
修改ProfilePageViewModel以支持统一的创作内容管理：

```swift
// ChatToDesign/Presentation/Profile/ProfilePageViewModel.swift
class ProfilePageViewModel: ObservableObject {
  @Published var creationItems: [CreationItem] = []
  @Published var isLoadingCreations = false
  @Published var errorMessage: String?
  
  // 分离的数据源
  private var userAssets: [UserAsset] = []
  private var imageTasks: [ImageGenerationTask] = []
  private var videoTasks: [VideoGenerationTask] = []
  
  // Combine subscriptions
  private var assetSubscription: AnyCancellable?
  private var imageTaskSubscription: AnyCancellable?
  private var videoTaskSubscription: AnyCancellable?
  
  func loadUserCreations() {
    observeUserAssets()
    observeImageTasks()
    observeVideoTasks()
  }
  
  private func mergeAndSortCreations() {
    var items: [CreationItem] = []
    
    // 添加已完成的assets
    items.append(contentsOf: userAssets.filter { $0.isAIGenerated }.map { .asset($0) })
    
    // 添加非succeeded的imageTasks（避免重复）
    let succeededImageTaskIds = Set(userAssets.compactMap { $0.sourceTaskId })
    let activeImageTasks = imageTasks.filter { task in
      task.status != .succeeded || !succeededImageTaskIds.contains(task.taskId)
    }
    items.append(contentsOf: activeImageTasks.map { .imageTask($0) })
    
    // 添加非succeeded的videoTasks（避免重复）
    let succeededVideoTaskIds = Set(userAssets.compactMap { $0.sourceTaskId })
    let activeVideoTasks = videoTasks.filter { task in
      task.status != .succeeded || !succeededVideoTaskIds.contains(task.taskId)
    }
    items.append(contentsOf: activeVideoTasks.map { .videoTask($0) })
    
    // 按创建时间排序，进行中的任务优先
    creationItems = items.sorted { item1, item2 in
      let date1 = item1.createdAt ?? Date.distantPast
      let date2 = item2.createdAt ?? Date.distantPast
      
      // 进行中的任务优先显示
      if !item1.isCompleted && item2.isCompleted { return true }
      if item1.isCompleted && !item2.isCompleted { return false }
      
      return date1 > date2
    }
  }
}
```

#### 2.2 数据观察方法
```swift
extension ProfilePageViewModel {
  private func observeImageTasks() {
    guard let userId = userService.currentUser?.id else { return }
    
    imageTaskSubscription = imageTaskRepository.observeTaskList(userId: userId, limit: 50)
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          if case .failure(let error) = completion {
            self?.errorMessage = "Failed to load image tasks: \(error.localizedDescription)"
          }
        },
        receiveValue: { [weak self] tasks in
          self?.imageTasks = tasks
          self?.mergeAndSortCreations()
        }
      )
  }
  
  private func observeVideoTasks() {
    guard let userId = userService.currentUser?.id else { return }
    
    videoTaskSubscription = videoTaskRepository.observeTaskList(userId: userId, limit: 50)
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          if case .failure(let error) = completion {
            self?.errorMessage = "Failed to load video tasks: \(error.localizedDescription)"
          }
        },
        receiveValue: { [weak self] tasks in
          self?.videoTasks = tasks
          self?.mergeAndSortCreations()
        }
      )
  }
}
```

### 3. UI组件设计

#### 3.1 CreationItemView
创建统一的创作内容展示组件：

```swift
// ChatToDesign/Presentation/Profile/Components/CreationItemView.swift
struct CreationItemView: View {
  let item: CreationItem
  let width: CGFloat
  @State private var showDetailPage = false
  
  var body: some View {
    Button(action: {
      if item.isCompleted {
        showDetailPage = true
      }
    }) {
      ZStack {
        // 缩略图
        thumbnailImageView
        
        // 状态覆盖层
        if !item.isCompleted {
          statusOverlayView
        }
        
        // 重试按钮
        if item.canRetry {
          retryButtonView
        }
      }
    }
    .buttonStyle(PlainButtonStyle())
    .fullScreenCover(isPresented: $showDetailPage) {
      if case .asset(let asset) = item {
        CreateDetailPage(asset: asset)
      }
    }
  }
}
```

#### 3.2 状态指示器设计
```swift
extension CreationItemView {
  private var statusOverlayView: some View {
    ZStack {
      // 半透明遮罩
      Color.black.opacity(0.4)
      
      VStack(spacing: 8) {
        // 进度指示器
        if case .processing(let progress) = item.status {
          if let progress = progress {
            CircularProgressView(progress: Double(progress) / 100.0)
          } else {
            ProgressView()
              .progressViewStyle(CircularProgressViewStyle(tint: .white))
          }
        }
        
        // 状态文本
        Text(item.status.displayText)
          .font(.caption)
          .foregroundColor(.white)
          .padding(.horizontal, 8)
          .padding(.vertical, 4)
          .background(Color.black.opacity(0.6))
          .clipShape(Capsule())
      }
    }
    .clipShape(RoundedRectangle(cornerRadius: 16))
  }
}
```

## 实施计划

### 阶段1：核心数据模型 (1-2天)
- [ ] 创建CreationItem和CreationStatus枚举
- [ ] 实现CreationItem扩展方法
- [ ] 添加单元测试

### 阶段2：ViewModel集成 (2-3天)
- [ ] 扩展ProfilePageViewModel
- [ ] 实现多数据源观察和合并逻辑
- [ ] 处理数据去重和排序

### 阶段3：UI组件开发 (2-3天)
- [ ] 创建CreationItemView组件
- [ ] 实现状态指示器和进度显示
- [ ] 集成到ProfilePageView

### 阶段4：交互功能 (1-2天)
- [ ] 实现重试功能
- [ ] 添加错误处理
- [ ] 优化用户体验

### 阶段5：测试和优化 (1-2天)
- [ ] 集成测试
- [ ] 性能优化
- [ ] UI/UX调优

## 技术考虑

### 性能优化
1. **数据去重**：避免succeeded任务和对应asset重复显示
2. **内存管理**：合理管理Combine订阅生命周期
3. **UI更新**：使用@Published属性确保UI及时更新

### 用户体验
1. **状态清晰**：不同状态使用不同的视觉表现
2. **交互反馈**：进行中的任务不可点击，失败任务可重试
3. **实时更新**：任务状态变化实时反映在UI上

### 错误处理
1. **网络异常**：处理观察数据流中断
2. **数据异常**：处理解析错误和缺失数据
3. **用户反馈**：提供清晰的错误信息

## 风险评估

### 技术风险
- **数据一致性**：多数据源可能导致状态不一致
- **性能影响**：同时观察多个数据源可能影响性能

### 缓解措施
- 实现严格的数据去重逻辑
- 使用合适的数据结构优化查询性能
- 添加完善的错误处理和日志记录

## 总结

本方案通过创建统一的CreationItem模型，扩展ProfilePageViewModel的数据观察能力，并设计相应的UI组件，实现了在My Creations区域展示进行中任务的需求。方案保持了与现有架构的兼容性，提供了良好的用户体验，并考虑了性能和错误处理等技术细节。
