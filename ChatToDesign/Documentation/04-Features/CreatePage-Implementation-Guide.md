# CreatePage VideoTemplate 实现指南

## 快速开始

本指南提供了将 VideoEffect 数据集成到 CreatePageView 的详细实现步骤。

## 前置条件

- 熟悉项目的 SWR Hook 架构
- 了解 VideoEffects 模块的实现
- 掌握 SwiftUI 和 Combine 基础

## 实现步骤

### Step 1: 更新 CreatePageViewModel

#### 1.1 添加依赖注入

```swift
// ChatToDesign/Presentation/Create/CreatePageViewModel.swift

@MainActor
public class CreatePageViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 当前选中的标签页
    @Published public var selectedTab: TabType = .videoTemplate
    
    /// 视频效果分类数据
    @Published public var videoEffectCategories: [VideoEffectCategory] = []
    
    /// 加载状态
    @Published public var isLoading: Bool = false
    
    /// 错误信息
    @Published public var error: Error?
    
    // MARK: - Private Properties
    
    private let videoEffectService: VideoEffectService
    private var videoEffectsHook: SWRHook<[VideoEffect]>?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    public init(videoEffectService: VideoEffectService = AppDependencyContainer.shared.videoEffectService) {
        self.videoEffectService = videoEffectService
        setupVideoEffectsHook()
    }
}
```

#### 1.2 实现 SWR Hook

```swift
// MARK: - Private Methods

private func setupVideoEffectsHook() {
    videoEffectsHook = SWRHook.create(
        key: "create_page_video_effects",
        maxAge: 60 * 60,  // 1 hour cache
        staleTime: 24 * 60 * 60,  // 24 hours stale time
        networkCall: { [weak self] in
            guard let self = self else {
                throw VideoEffectServiceError.invalidData
            }
            return try await self.videoEffectService.fetchVideoEffects()
        },
        autoFetch: true
    )
    
    observeVideoEffectsHookChanges()
}

private func observeVideoEffectsHookChanges() {
    guard let hook = videoEffectsHook else { return }
    
    // Observe data changes
    hook.$data
        .receive(on: DispatchQueue.main)
        .sink { [weak self] videoEffects in
            if let effects = videoEffects {
                self?.processVideoEffectsData(effects)
            }
        }
        .store(in: &cancellables)
    
    // Observe loading state
    hook.$isLoading
        .receive(on: DispatchQueue.main)
        .sink { [weak self] isLoading in
            self?.isLoading = isLoading
        }
        .store(in: &cancellables)
    
    // Observe error state
    hook.$error
        .receive(on: DispatchQueue.main)
        .sink { [weak self] error in
            self?.error = error
        }
        .store(in: &cancellables)
}

private func processVideoEffectsData(_ videoEffects: [VideoEffect]) {
    // Filter out pollo effects (if needed)
    let filteredEffects = videoEffects.filter { $0.provider != .polloAI }
    
    // Group effects by category
    let groupedEffects = Dictionary(grouping: filteredEffects) { $0.category }
    
    // Convert to VideoEffectCategory objects and sort by category name
    let categories = groupedEffects.map { (categoryName, effects) in
        VideoEffectCategory(name: categoryName, effects: effects)
    }.sorted { $0.name < $1.name }
    
    self.videoEffectCategories = categories
    Logger.info("Successfully processed \(categories.count) video effect categories for CreatePage")
}
```

#### 1.3 添加计算属性

```swift
// MARK: - Computed Properties

/// Interactive 分类的视频效果
public var interactiveVideoEffects: [VideoEffect] {
    videoEffectCategories.first { $0.name == "Interaction" }?.effects ?? []
}

/// Entertainment 分类的视频效果
public var entertainmentVideoEffects: [VideoEffect] {
    videoEffectCategories.first { $0.name == "Entertainment" }?.effects ?? []
}

/// 刷新数据
public func refresh() {
    videoEffectsHook?.refresh()
}
```

### Step 2: 更新 CreatePageView

#### 2.1 修改状态变量

```swift
// ChatToDesign/Presentation/Create/CreatePageView.swift

public struct CreatePageView: View {
    
    // MARK: - Properties
    
    @StateObject private var viewModel = CreatePageViewModel()
    @Environment(\.colorScheme) private var colorScheme
    @State private var showCreateVideo = false
    @State private var selectedVideoEffect: VideoEffect?  // 改为 VideoEffect
    
    // 移除 selectedTemplate: TemplateItem?
}
```

#### 2.2 更新导航逻辑

```swift
// 更新 fullScreenCover
.fullScreenCover(item: $selectedVideoEffect) { effect in
    CreateVideoFromTemplatePage(
        templateId: effect.template ?? effect.id,
        demoUrl: effect.videoUrl,
        inputImageUrls: [],
        inputImageLimits: effect.imageCount,
        inputGuide: effect.inputInstruction ?? "Upload images to create your video using this template",
        demoPrompt: effect.prompt ?? "Create a video using \(effect.name) template"
    )
}
```

#### 2.3 重构模板网格

```swift
// MARK: - Template Grids

private var templateGrids: some View {
    VStack(spacing: 32) {
        if viewModel.selectedTab == .videoTemplate {
            videoEffectGrids
        } else {
            // 保持现有的图片模板逻辑
            imageTemplateGrids
        }
    }
}

private var videoEffectGrids: some View {
    Group {
        if viewModel.isLoading && viewModel.videoEffectCategories.isEmpty {
            loadingView
        } else if let error = viewModel.error, viewModel.videoEffectCategories.isEmpty {
            errorView(error)
        } else {
            VStack(spacing: 32) {
                // Interactive 视频效果组
                if !viewModel.interactiveVideoEffects.isEmpty {
                    VideoEffectTemplateGroup(
                        title: "Interactive",
                        videoEffects: viewModel.interactiveVideoEffects,
                        onEffectTap: { effect in
                            selectedVideoEffect = effect
                        }
                    )
                }
                
                // Entertainment 视频效果组
                if !viewModel.entertainmentVideoEffects.isEmpty {
                    VideoEffectTemplateGroup(
                        title: "Entertainment",
                        videoEffects: viewModel.entertainmentVideoEffects,
                        onEffectTap: { effect in
                            selectedVideoEffect = effect
                        }
                    )
                }
            }
        }
    }
}

// 保持现有的图片模板网格
private var imageTemplateGrids: some View {
    VStack(spacing: 32) {
        // 现有的 TemplateGroup 实现
        TemplateGroup(
            title: "Interactive",
            templates: viewModel.interactiveTemplates,
            onTemplateTap: { template in
                // 处理图片模板选择
            }
        )
        
        TemplateGroup(
            title: "Funny", 
            templates: viewModel.funnyTemplates,
            onTemplateTap: { template in
                // 处理图片模板选择
            }
        )
    }
}
```

### Step 3: 创建 VideoEffectTemplateGroup 组件

```swift
// MARK: - Video Effect Template Group

struct VideoEffectTemplateGroup: View {
    let title: String
    let videoEffects: [VideoEffect]
    let onEffectTap: (VideoEffect) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 组标题
            HStack {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            // 视频效果网格
            LazyVGrid(
                columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 2),
                spacing: 16
            ) {
                ForEach(videoEffects) { effect in
                    VideoEffectCardView(
                        videoEffect: effect,
                        onTap: { onEffectTap(effect) },
                        isInteractive: true
                    )
                    .frame(width: 160, height: 200)
                }
            }
        }
    }
}
```

### Step 4: 添加加载和错误状态

```swift
// MARK: - Loading and Error Views

private var loadingView: some View {
    VStack(spacing: 16) {
        ProgressView()
            .scaleEffect(1.2)
        
        Text("Loading Templates...")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.secondary)
    }
    .frame(maxWidth: .infinity)
    .frame(height: 200)
}

private func errorView(_ error: Error) -> some View {
    VStack(spacing: 16) {
        Image(systemName: "exclamationmark.triangle")
            .font(.system(size: 32))
            .foregroundColor(.orange)
        
        Text("Failed to Load Templates")
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(.primary)
        
        Text(error.localizedDescription)
            .font(.system(size: 14))
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
            .padding(.horizontal, 32)
        
        Button("Retry") {
            viewModel.refresh()
        }
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.white)
        .padding(.horizontal, 20)
        .padding(.vertical, 8)
        .background(Color.blue)
        .cornerRadius(8)
    }
    .frame(maxWidth: .infinity)
    .frame(height: 200)
}
```

### Step 5: 清理旧代码

#### 5.1 移除 TemplateItem 定义

```swift
// 从 CreatePageView.swift 中移除
// public struct TemplateItem: Identifiable, Equatable { ... }
```

#### 5.2 清理 CreatePageViewModel

```swift
// 移除以下属性和方法：
// @Published public var interactiveTemplates: [TemplateItem] = []
// @Published public var funnyTemplates: [TemplateItem] = []
// private func setupMockData()
```

## 测试验证

### 单元测试

```swift
// Tests/CreatePageViewModelTests.swift

@MainActor
class CreatePageViewModelTests: XCTestCase {
    
    func testVideoEffectsLoading() async {
        // 测试视频效果加载
        let mockService = MockVideoEffectService()
        let viewModel = CreatePageViewModel(videoEffectService: mockService)
        
        // 验证加载状态
        XCTAssertTrue(viewModel.isLoading)
        
        // 等待数据加载完成
        await waitForExpectation { viewModel.videoEffectCategories.count > 0 }
        
        // 验证数据正确性
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertNil(viewModel.error)
        XCTAssertGreaterThan(viewModel.videoEffectCategories.count, 0)
    }
    
    func testCategoryFiltering() {
        // 测试分类筛选逻辑
        let viewModel = CreatePageViewModel()
        // ... 测试实现
    }
}
```

### UI 测试

```swift
// UITests/CreatePageUITests.swift

class CreatePageUITests: XCTestCase {
    
    func testVideoTemplateTabSelection() {
        // 测试视频模板标签页选择
        let app = XCUIApplication()
        app.launch()
        
        // 点击 Video Template 标签
        app.buttons["Video Template"].tap()
        
        // 验证视频效果卡片显示
        XCTAssertTrue(app.scrollViews.otherElements.containing(.staticText, identifier: "Interactive").exists)
    }
}
```

## 常见问题

### Q: VideoEffect 数据为空怎么办？
A: 检查网络连接和 API 服务状态，实现优雅的错误处理和重试机制。

### Q: 如何处理不同分类的映射？
A: 在 `processVideoEffectsData` 方法中添加分类名称映射逻辑。

### Q: 性能优化建议？
A: 使用 SWR Hook 的缓存机制，实现视频预加载，考虑虚拟化长列表。

## 下一步

1. 实现图片模板的类似集成
2. 添加搜索和筛选功能
3. 优化加载性能和用户体验
4. 添加分析和监控
