# CreatePage VideoTemplate Integration

## 概述

本文档描述了如何在 CreatePageView 中选择 VideoTemplate 标签页时展示 VideoEffect 数据的完整实现方案。该方案将统一数据模型，复用现有的 VideoEffects 架构，提供一致的用户体验。

## 背景

### 当前状态
- `CreatePageView` 使用硬编码的 `TemplateItem` 模拟数据
- `VideoEffects` 模块已有完整的架构和真实 API 数据
- 两个模块使用不同的数据模型，造成数据不一致

### 目标
- 统一使用 `VideoEffect` 数据模型
- 复用 VideoEffects 模块的组件和服务
- 保持 CreatePageView 的整体 UI 布局
- 提供真实的 API 数据而非模拟数据

## 架构设计

### 数据流架构

```
API Service → VideoEffectService → SWR Hook → CreatePageViewModel → CreatePageView
```

### 核心组件关系

```
CreatePageView
├── CreatePageViewModel (集成 VideoEffectService)
├── VideoEffectCardView (复用现有组件)
├── TemplateGroup (重构为支持 VideoEffect)
└── Navigation → CreateVideoFromTemplatePage
```

## 实现方案

### 1. 数据模型统一

#### 移除 TemplateItem
- 删除 `CreatePageView.swift` 中的 `TemplateItem` 定义
- 统一使用 `VideoEffect` 和 `VideoEffectCategory`

#### VideoEffect 映射
```swift
// VideoEffect 已包含所需字段：
- id: String
- name: String  
- videoUrl: String
- posterUrl: String
- category: String
- isHot: Bool
- isNew: Bool
- template: String? (用于 templateId)
```

### 2. CreatePageViewModel 重构

#### 依赖注入
```swift
@MainActor
public class CreatePageViewModel: ObservableObject {
    private let videoEffectService: VideoEffectService
    private var videoEffectsHook: SWRHook<[VideoEffect]>?
    
    init(videoEffectService: VideoEffectService = AppDependencyContainer.shared.videoEffectService)
}
```

#### 状态管理
```swift
// 替换现有属性
@Published public var videoEffectCategories: [VideoEffectCategory] = []
@Published public var isLoading: Bool = false
@Published public var error: Error? = nil

// 计算属性
var interactiveVideoEffects: [VideoEffect] {
    videoEffectCategories.first { $0.name == "Interactive" }?.effects ?? []
}

var funnyVideoEffects: [VideoEffect] {
    videoEffectCategories.first { $0.name == "Entertainment" }?.effects ?? []
}
```

#### SWR Hook 集成
```swift
private func setupVideoEffectsHook() {
    videoEffectsHook = SWRHook.create(
        key: "create_page_video_effects",
        maxAge: 60 * 60,  // 1 hour cache
        staleTime: 24 * 60 * 60,  // 24 hours stale time
        networkCall: { [weak self] in
            guard let self = self else {
                throw VideoEffectServiceError.invalidData
            }
            return try await self.videoEffectService.fetchVideoEffects()
        },
        autoFetch: true
    )
    
    observeVideoEffectsHookChanges()
}
```

### 3. CreatePageView UI 更新

#### 模板组件重构
```swift
// 更新 TemplateGroup 以支持 VideoEffect
struct TemplateGroup: View {
    let title: String
    let videoEffects: [VideoEffect]  // 改为 VideoEffect
    let onEffectTap: (VideoEffect) -> Void  // 改为 VideoEffect
    
    var body: some View {
        // 使用 VideoEffectCardView 替代 TemplateCard
        ForEach(videoEffects) { effect in
            VideoEffectCardView(
                videoEffect: effect,
                onTap: { onEffectTap(effect) },
                isInteractive: true
            )
            .frame(width: 160, height: 200)
        }
    }
}
```

#### 导航逻辑更新
```swift
.fullScreenCover(item: $selectedVideoEffect) { effect in
    CreateVideoFromTemplatePage(
        templateId: effect.template ?? effect.id,
        demoUrl: effect.videoUrl,
        inputImageUrls: [],
        inputImageLimits: effect.imageCount,
        inputGuide: effect.inputInstruction ?? "Upload images to create your video",
        demoPrompt: effect.prompt ?? "Create a video using \(effect.name)"
    )
}
```

### 4. 标签页逻辑处理

#### 条件渲染
```swift
private var templateGrids: some View {
    VStack(spacing: 32) {
        if viewModel.selectedTab == .videoTemplate {
            // 显示 VideoEffect 数据
            if viewModel.isLoading {
                loadingView
            } else if let error = viewModel.error {
                errorView(error)
            } else {
                videoEffectGrids
            }
        } else {
            // 显示 ImageTemplate 数据（保持现有逻辑）
            imageTemplateGrids
        }
    }
}
```

#### VideoEffect 网格
```swift
private var videoEffectGrids: some View {
    VStack(spacing: 32) {
        // Interactive 视频效果组
        if !viewModel.interactiveVideoEffects.isEmpty {
            TemplateGroup(
                title: "Interactive",
                videoEffects: viewModel.interactiveVideoEffects,
                onEffectTap: { effect in
                    selectedVideoEffect = effect
                }
            )
        }
        
        // Entertainment 视频效果组  
        if !viewModel.funnyVideoEffects.isEmpty {
            TemplateGroup(
                title: "Entertainment", 
                videoEffects: viewModel.funnyVideoEffects,
                onEffectTap: { effect in
                    selectedVideoEffect = effect
                }
            )
        }
    }
}
```

## 实现步骤

### Phase 1: 数据层重构
1. 修改 `CreatePageViewModel`
   - 集成 `VideoEffectService`
   - 实现 SWR Hook
   - 添加状态管理

2. 更新数据绑定
   - 移除 `TemplateItem` 相关代码
   - 使用 `VideoEffect` 和 `VideoEffectCategory`

### Phase 2: UI 层更新
1. 重构 `TemplateGroup` 组件
   - 支持 `VideoEffect` 数据类型
   - 集成 `VideoEffectCardView`

2. 更新 `CreatePageView`
   - 修改数据绑定
   - 更新导航逻辑
   - 添加加载和错误状态

### Phase 3: 测试和优化
1. 单元测试
   - ViewModel 逻辑测试
   - 数据转换测试

2. UI 测试
   - 标签页切换测试
   - 导航流程测试

3. 性能优化
   - 预加载策略
   - 缓存优化

## 技术考虑

### 性能优化
- 使用 SWR Hook 的缓存机制
- 实现视频预加载（参考 VideoEffects 模块）
- 懒加载非当前标签页的数据

### 错误处理
- 网络错误重试机制
- 优雅的错误状态展示
- 降级到本地缓存数据

### 用户体验
- 加载状态指示器
- 平滑的标签页切换动画
- 保持滚动位置

## 依赖关系

### 新增依赖
- `VideoEffectService` (已存在)
- `SWRHook` (已存在)
- `VideoEffectCardView` (已存在)

### 移除依赖
- 硬编码的 `TemplateItem` 数据
- 模拟数据生成逻辑

## 风险评估

### 低风险
- 复用现有成熟组件
- 遵循现有架构模式
- 向后兼容的数据结构

### 中等风险
- UI 布局可能需要微调
- 导航参数映射需要验证

### 缓解措施
- 渐进式重构
- 充分的测试覆盖
- 保留回滚机制

## 后续扩展

### ImageTemplate 集成
- 类似方式集成图片模板数据
- 统一模板管理架构

### 搜索和筛选
- 添加模板搜索功能
- 分类筛选优化

### 个性化推荐
- 基于用户行为的模板推荐
- 热门模板排序

## 总结

该实现方案通过统一数据模型、复用现有组件、遵循现有架构模式，能够有效地将 VideoEffect 数据集成到 CreatePageView 中。方案具有良好的可维护性、扩展性和用户体验。
