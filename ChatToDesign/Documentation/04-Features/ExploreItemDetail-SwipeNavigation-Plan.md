# ExploreItemDetail 上下滑动切换功能技术方案

## 概述

在 `ExploreItemDetailView` 中实现类似 TikTok/Instagram Stories 的上下滑动切换不同 item 的功能。

## 当前架构分析

### 现有实现
- **ExplorePageView**: 瀑布流布局，使用 `fullScreenCover` 打开详情页
- **ExploreItemDetailView**: 单个 item 详情展示，接收 `ExploreItem` 参数
- **ExploreItemDetailViewModel**: 管理单个 item 的详情数据加载
- **导航方式**: `selectedDetailItem` 触发 `fullScreenCover`

### 现有限制
1. 详情页只接收单个 `ExploreItem`，无集合上下文
2. 无当前索引跟踪机制
3. 无滑动手势处理
4. 无前后item的预加载机制

## 业界最佳实践分析

### TikTok/抖音等头部应用实现
- **核心组件**: UICollectionView + 垂直分页布局
- **预加载策略**: 当前±2个item智能预加载
- **内存管理**: 严格的视频播放器生命周期控制
- **性能优化**: 精确的视图复用和资源管理

### 技术选型对比
| 方案 | 性能 | 可控性 | 复杂度 | 业界采用 |
|------|------|--------|--------|----------|
| UICollectionView | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ✅ 主流 |
| UIPageViewController | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐ 简单场景 |
| SwiftUI TabView | ⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ | ❌ 不成熟 |

**选择 UICollectionView 的原因**：
1. **精确预加载控制**：适配项目现有的SWR缓存策略
2. **视频内容优化**：精确的播放器生命周期管理
3. **业界验证**：TikTok、Instagram Reels的实际选择
4. **扩展性强**：支持未来功能扩展需求

## 技术方案设计

### 1. 数据架构改进

#### 1.1 新增导航参数模型
```swift
struct ExploreDetailNavigation {
    let items: [ExploreItem]
    let initialIndex: Int
}
```

#### 1.2 ExplorePageViewModel 改进
- 添加 `@Published var detailNavigation: ExploreDetailNavigation?`
- 替换原有 `selectedDetailItem`
- 添加 `handleItemTap(item, at index)` 方法

#### 1.3 ExploreItemDetailView 参数调整
- 修改初始化参数从 `exploreItem` 到 `navigation: ExploreDetailNavigation`
- 添加 `@State private var currentIndex: Int`

### 2. UI架构设计

#### 2.1 核心组件：VerticalPagingCollectionView
```swift
struct VerticalPagingCollectionView: UIViewRepresentable {
    let items: [ExploreItem]
    @Binding var currentIndex: Int
    let onPageChanged: (Int) -> Void
    let viewModelProvider: (Int) -> ExploreItemDetailViewModel?
    
    func makeUIView(context: Context) -> UICollectionView {
        let layout = createPagingLayout()
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        
        // 垂直分页配置
        collectionView.isPagingEnabled = true
        collectionView.showsVerticalScrollIndicator = false
        collectionView.contentInsetAdjustmentBehavior = .never
        
        // 预加载优化
        collectionView.prefetchDataSource = context.coordinator
        
        return collectionView
    }
    
    private func createPagingLayout() -> UICollectionViewFlowLayout {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        layout.itemSize = CGSize(
            width: UIScreen.main.bounds.width,
            height: UIScreen.main.bounds.height
        )
        return layout
    }
}
```

#### 2.2 SwiftUI集成设计
```swift
struct ExploreItemDetailCollectionView: View {
    let navigation: ExploreDetailNavigation
    @StateObject private var viewModel: ExploreCollectionDetailViewModel
    
    var body: some View {
        VerticalPagingCollectionView(
            items: navigation.items,
            currentIndex: $viewModel.currentIndex,
            onPageChanged: viewModel.onIndexChanged,
            viewModelProvider: viewModel.getViewModel
        )
        .background(Color.black)
        .ignoresSafeArea()
    }
}
```

#### 2.3 UICollectionViewCell 设计
```swift
class ExploreItemDetailCell: UICollectionViewCell {
    private var hostingController: UIHostingController<ExploreItemDetailContentView>?
    
    func configure(with item: ExploreItem, viewModel: ExploreItemDetailViewModel?) {
        let contentView = ExploreItemDetailContentView(
            item: item,
            viewModel: viewModel
        )
        
        if let hostingController = hostingController {
            hostingController.rootView = contentView
        } else {
            let newController = UIHostingController(rootView: contentView)
            newController.view.backgroundColor = .clear
            self.hostingController = newController
            
            addSubview(newController.view)
            newController.view.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                newController.view.topAnchor.constraint(equalTo: topAnchor),
                newController.view.leadingAnchor.constraint(equalTo: leadingAnchor),
                newController.view.trailingAnchor.constraint(equalTo: trailingAnchor),
                newController.view.bottomAnchor.constraint(equalTo: bottomAnchor)
            ])
        }
    }
}
```

### 3. ViewModel架构重构

#### 3.1 ExploreCollectionDetailViewModel设计
```swift
@MainActor
final class ExploreCollectionDetailViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var currentIndex: Int
    @Published var itemViewModels: [Int: ExploreItemDetailViewModel] = [:]
    
    // MARK: - Private Properties
    private let items: [ExploreItem]
    private let initialIndex: Int
    private let preloadRange = 1 // 前后预加载1个item
    private let apiService: APIService
    
    // MARK: - Initialization
    init(items: [ExploreItem], initialIndex: Int, apiService: APIService) {
        self.items = items
        self.initialIndex = initialIndex
        self.currentIndex = initialIndex
        self.apiService = apiService
        
        // 初始化时预加载当前和相邻item
        preloadViewModelsForIndex(initialIndex)
    }
    
    // MARK: - Public Methods
    func onIndexChanged(_ newIndex: Int) {
        guard newIndex != currentIndex else { return }
        
        currentIndex = newIndex
        preloadViewModelsForIndex(newIndex)
        
        // 通知当前item准备显示
        itemViewModels[newIndex]?.prepareForDisplay()
        
        // 暂停非当前item的重资源操作
        pauseInactiveItems(except: newIndex)
    }
    
    func getViewModel(for index: Int) -> ExploreItemDetailViewModel? {
        return itemViewModels[index]
    }
    
    // MARK: - Private Methods
    private func preloadViewModelsForIndex(_ index: Int) {
        let preloadIndices = getPreloadIndices(for: index)
        
        // 清理超出范围的ViewModels
        cleanupOutOfRangeViewModels(keeping: preloadIndices)
        
        // 创建新的ViewModels
        createViewModelsForIndices(preloadIndices)
    }
    
    private func pauseInactiveItems(except activeIndex: Int) {
        itemViewModels.forEach { index, viewModel in
            if index != activeIndex {
                viewModel.pauseHeavyOperations()
            }
        }
    }
}
```

#### 3.2 ExploreItemDetailViewModel 生命周期扩展
```swift
extension ExploreItemDetailViewModel {
    /// 暂停重资源操作（视频播放、网络请求等）
    func pauseHeavyOperations() {
        // 实现细节在UI层处理
        Logger.debug("ExploreItemDetailViewModel: 暂停重资源操作 - \(exploreItem.id)")
    }
    
    /// 恢复操作
    func resumeOperations() {
        // 实现细节在UI层处理
        Logger.debug("ExploreItemDetailViewModel: 恢复操作 - \(exploreItem.id)")
    }
}
```

### 4. 性能优化策略

#### 4.1 智能预加载机制
```swift
private func getPreloadIndices(for currentIndex: Int) -> Set<Int> {
    let start = max(0, currentIndex - preloadRange)
    let end = min(items.count - 1, currentIndex + preloadRange)
    return Set(start...end)
}

private func cleanupOutOfRangeViewModels(keeping preloadIndices: Set<Int>) {
    let indicesToRemove = Set(itemViewModels.keys).subtracting(preloadIndices)
    
    indicesToRemove.forEach { index in
        // 先清理媒体资源
        itemViewModels[index]?.cleanup()
        itemViewModels.removeValue(forKey: index)
    }
}
```

#### 4.2 UICollectionView 预加载优化
```swift
extension VerticalPagingCollectionView.Coordinator: UICollectionViewDataSourcePrefetching {
    func collectionView(_ collectionView: UICollectionView, prefetchItemsAt indexPaths: [IndexPath]) {
        // 预加载数据
        indexPaths.forEach { indexPath in
            parent.onPreloadRequest(indexPath.item)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cancelPrefetchingForItemsAt indexPaths: [IndexPath]) {
        // 取消预加载
        indexPaths.forEach { indexPath in
            parent.onCancelPreload(indexPath.item)
        }
    }
}
```

#### 4.3 内存管理策略
- **视频播放器管理**: 只有当前可见item播放，其他暂停
- **图片缓存控制**: 配合Kingfisher的缓存策略
- **网络请求管理**: 根据滚动状态智能控制请求优先级

### 5. 用户体验优化

#### 5.1 分页手势优化
```swift
// 在UICollectionViewFlowLayout子类中实现
class VerticalPagingFlowLayout: UICollectionViewFlowLayout {
    override func targetContentOffset(forProposedContentOffset proposedContentOffset: CGPoint, withScrollingVelocity velocity: CGPoint) -> CGPoint {
        guard let collectionView = collectionView else {
            return super.targetContentOffset(forProposedContentOffset: proposedContentOffset, withScrollingVelocity: velocity)
        }
        
        let pageHeight = itemSize.height + minimumLineSpacing
        let currentPage = collectionView.contentOffset.y / pageHeight
        
        // 根据滑动速度决定翻页方向
        let targetPage: CGFloat
        if velocity.y > 0.5 {
            targetPage = ceil(currentPage)
        } else if velocity.y < -0.5 {
            targetPage = floor(currentPage)
        } else {
            targetPage = round(currentPage)
        }
        
        return CGPoint(x: 0, y: targetPage * pageHeight)
    }
}
```

#### 5.2 滚动状态管理
```swift
func scrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
    let pageHeight = UIScreen.main.bounds.height
    let newIndex = Int(round(scrollView.contentOffset.y / pageHeight))
    
    if newIndex != currentIndex {
        onPageChanged(newIndex)
    }
}
```

### 6. 实现步骤

#### Phase 1: 基础架构
1. 实现 `VerticalPagingCollectionView` 核心组件
2. 创建 `ExploreItemDetailCell` 桥接层
3. 基础的分页滚动功能

#### Phase 2: 集成现有系统
1. 修改 `ExploreCollectionDetailViewModel` 适配 CollectionView
2. 更新 `ExplorePageView` 使用新组件
3. 确保预加载机制正常工作

#### Phase 3: 性能优化
1. 实现智能预加载和清理
2. 优化滚动手势和分页体验
3. 添加性能监控

#### Phase 4: 用户体验
1. 细化滚动动画和反馈
2. 添加边界处理和错误状态
3. 性能调优和压力测试

### 7. 风险评估与缓解

#### 7.1 技术风险
- **UIKit-SwiftUI桥接复杂性**: 通过充分测试和封装降低风险
- **内存管理**: 严格的资源清理机制和监控

#### 7.2 性能风险
- **大量视频内容**: 智能预加载和及时清理
- **滚动流畅度**: 使用系统优化的UICollectionView组件

#### 7.3 兼容性风险
- **iOS版本支持**: UICollectionView成熟稳定，支持所有目标版本
- **设备性能差异**: 自适应的预加载策略

## 总结

该方案基于业界主流的UICollectionView实现，结合项目现有架构特点，实现高性能的垂直分页体验。核心优势：

- **业界验证**: TikTok等头部应用的成熟方案
- **高性能**: 精确的资源管理和预加载控制
- **可扩展**: 支持未来功能需求扩展
- **兼容性**: 稳定的系统组件支持

通过分阶段实施，确保功能稳定可靠，用户体验达到业界领先水平。