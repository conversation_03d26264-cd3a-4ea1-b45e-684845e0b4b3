# CreatePage API 参考文档

## 概述

本文档详细描述了 CreatePage VideoTemplate 集成所涉及的 API 接口、数据结构和服务层实现。

## 数据模型

### VideoEffect

视频效果的核心数据模型，用于表示可用的视频模板。

```swift
public struct VideoEffect: Identifiable, Codable, Hashable {
    public let id: String                    // 唯一标识符
    public let name: String                  // 效果名称
    public let videoUrl: String              // 视频预览 URL
    public let posterUrl: String             // 封面图片 URL
    public let isHot: Bool                   // 是否为热门
    public let isNew: Bool                   // 是否为新增
    public let category: String              // 分类名称
    public let urlType: String               // URL 类型 ("video")
    public let provider: VideoEffectProvider // 提供商
    public let imageCount: Int               // 所需图片数量
    

    public let detail: String?               // 详细描述
    public let inputInstruction: String?     // 输入指导
    public let prompt: String?               // 提示词
    public let template: String?             // 模板 ID
}
```

### VideoEffectCategory

视频效果分类，用于组织和展示视频效果。

```swift
public struct VideoEffectCategory: Identifiable, Hashable {
    public let id: String                    // 分类 ID (等于 name)
    public let name: String                  // 分类名称
    public let effects: [VideoEffect]        // 该分类下的效果列表
    public let count: Int                    // 效果数量
}
```

### VideoEffectProvider

视频效果提供商枚举。

```swift
public enum VideoEffectProvider: String, Codable, CaseIterable {
    case polloAI = "pollo_ai"               // Pollo AI 提供商
    case vidu = "vidu"                      // Vidu 提供商
}
```

## API 服务

### VideoEffectService 协议

```swift
public protocol VideoEffectService {
    /// 获取所有视频效果
    /// - Returns: VideoEffect 数组
    /// - Throws: VideoEffectServiceError
    func fetchVideoEffects() async throws -> [VideoEffect]
    
    /// 获取按分类组织的视频效果
    /// - Returns: VideoEffectCategory 数组
    /// - Throws: VideoEffectServiceError
    func fetchVideoEffectCategories() async throws -> [VideoEffectCategory]
}
```

### DefaultVideoEffectService 实现

```swift
public class DefaultVideoEffectService: VideoEffectService {
    private let apiService: APIService
    
    public init(apiService: APIService) {
        self.apiService = apiService
    }
    
    public func fetchVideoEffects() async throws -> [VideoEffect] {
        do {
            let videoUseCases = try await apiService.fetchVideoUseCaseCMS()
            let videoEffects = videoUseCases.map { convertToVideoEffect(from: $0) }
            return videoEffects
        } catch {
            throw VideoEffectServiceError.apiError(error)
        }
    }
    
    public func fetchVideoEffectCategories() async throws -> [VideoEffectCategory] {
        let videoEffects = try await fetchVideoEffects()
        let groupedEffects = Dictionary(grouping: videoEffects) { $0.category }
        let categories = groupedEffects.map { (categoryName, effects) in
            VideoEffectCategory(name: categoryName, effects: effects)
        }.sorted { $0.name < $1.name }
        return categories
    }
}
```

## API 端点

### 视频用例 CMS 接口

**端点**: `GET /api/v1/video-usecase-cms`

**响应格式**:
```json
[
    {
        "id": "effect_001",
        "name": "AI Kissing Video Generator",
        "category": "Interaction",
        "provider": "vidu",
        "videoUrl": "https://example.com/video.mp4",
        "coverImgUrl": "https://example.com/cover.jpg",
        "isHot": true,
        "isNew": false,
        "image_count": 2,
        "detail": "Create romantic kissing videos",
        "input_instruction": "Upload two portrait photos",
        "prompt": "Generate a romantic kissing scene",
        "template": "kissing_template_v1"
    }
]
```

### VideoUseCaseResponse 数据结构

```swift
public struct VideoUseCaseResponse: Codable {
    public let id: String
    public let name: String
    public let category: String
    public let provider: String
    public let videoUrl: String
    public let coverImgUrl: String
    public let isHot: Bool
    public let isNew: Bool
    public let imageCount: Int
    public let detail: String?
    public let inputInstruction: String?
    public let prompt: String?
    public let template: String?
    
    enum CodingKeys: String, CodingKey {
        case id, name, category, provider, prompt, template
        case videoUrl = "videoUrl"
        case coverImgUrl = "coverImgUrl"
        case isHot = "isHot"
        case isNew = "isNew"
        case imageCount = "image_count"
        case detail = "detail"
        case inputInstruction = "input_instruction"
    }
}
```

## SWR Hook 集成

### SWRHook 配置

```swift
// CreatePageViewModel 中的 SWR Hook 配置
private func setupVideoEffectsHook() {
    videoEffectsHook = SWRHook.create(
        key: "create_page_video_effects",        // 缓存键
        maxAge: 60 * 60,                         // 1小时缓存时间
        staleTime: 24 * 60 * 60,                 // 24小时过期时间
        networkCall: { [weak self] in
            guard let self = self else {
                throw VideoEffectServiceError.invalidData
            }
            return try await self.videoEffectService.fetchVideoEffects()
        },
        autoFetch: true                          // 自动获取数据
    )
}
```

### 缓存策略

- **maxAge**: 1小时 - 数据在1小时内被认为是新鲜的
- **staleTime**: 24小时 - 数据在24小时后被认为是过期的
- **autoFetch**: true - 组件初始化时自动获取数据

## 错误处理

### VideoEffectServiceError

```swift
public enum VideoEffectServiceError: Error, LocalizedError {
    case apiError(Error)                     // API 请求错误
    case invalidData                         // 无效数据格式
    case parsingError(Error)                 // 数据解析错误
    
    public var errorDescription: String? {
        switch self {
        case .apiError(let error):
            return "Failed to fetch video effects: \(error.localizedDescription)"
        case .invalidData:
            return "Invalid video effects data format"
        case .parsingError(let error):
            return "Failed to parse video effects: \(error.localizedDescription)"
        }
    }
}
```

### 错误处理策略

1. **网络错误**: 显示重试按钮，允许用户手动重试
2. **数据解析错误**: 记录错误日志，显示通用错误信息
3. **服务不可用**: 显示服务维护信息

## 数据转换

### API 响应到 VideoEffect 转换

```swift
private func convertToVideoEffect(from response: VideoUseCaseResponse) -> VideoEffect {
    let provider = VideoEffectProvider(rawValue: response.provider) ?? .vidu
    
    return VideoEffect(
        id: response.id,
        name: response.name,
        videoUrl: response.videoUrl,
        posterUrl: response.coverImgUrl,
        isHot: response.isHot,
        isNew: response.isNew,
        category: response.category,
        urlType: "video",
        provider: provider,
        imageCount: response.imageCount,
        detail: response.detail,
        inputInstruction: response.inputInstruction,
        prompt: response.prompt,
        template: response.template
    )
}
```

## 分类映射

### 标准分类

- **Interaction**: 互动类效果 (如接吻、拥抱等)
- **Appearance**: 外观变化效果 (如换装、美颜等)
- **Emotions**: 情感表达效果 (如笑容、哭泣等)
- **Entertainment**: 娱乐效果 (如舞蹈、表演等)
- **Hero/Villain**: 英雄/反派效果
- **Horror/Fantasy**: 恐怖/奇幻效果
- **Xmas**: 圣诞节主题效果

### 分类过滤

```swift
private func processVideoEffectsData(_ videoEffects: [VideoEffect]) {
    // 过滤掉 pollo 效果 (如果需要)
    let filteredEffects = videoEffects.filter { $0.provider != .polloAI }
    
    // 按分类分组
    let groupedEffects = Dictionary(grouping: filteredEffects) { $0.category }
    
    // 转换为 VideoEffectCategory 并排序
    let categories = groupedEffects.map { (categoryName, effects) in
        VideoEffectCategory(name: categoryName, effects: effects)
    }.sorted { $0.name < $1.name }
    
    self.videoEffectCategories = categories
}
```

## 性能优化

### 预加载策略

```swift
// 预加载相邻视频
private func preloadAdjacentVideos(currentIndex: Int, effects: [VideoEffect]) {
    let preloadRange = max(0, currentIndex - 2)...min(effects.count - 1, currentIndex + 2)
    
    Task {
        for index in preloadRange {
            if let url = URL(string: effects[index].videoUrl) {
                await VideoPreloadService.shared.preloadVideo(url: url)
            }
        }
    }
}
```

### 内存管理

- 使用 `weak self` 避免循环引用
- 及时释放不需要的资源
- 合理设置缓存大小限制

## 监控和日志

### 关键指标

- API 请求成功率
- 数据加载时间
- 缓存命中率
- 用户交互行为

### 日志记录

```swift
Logger.info("Successfully processed \(categories.count) video effect categories")
Logger.error("Failed to fetch video effects: \(error)")
Logger.debug("Video effects cache hit for key: \(cacheKey)")
```

## 测试支持

### Mock 服务

```swift
class MockVideoEffectService: VideoEffectService {
    func fetchVideoEffects() async throws -> [VideoEffect] {
        // 返回测试数据
        return [
            VideoEffect(
                id: "test_001",
                name: "Test Effect",
                videoUrl: "https://test.com/video.mp4",
                posterUrl: "https://test.com/poster.jpg",
                isHot: true,
                isNew: false,
                category: "Test",
                provider: .vidu,
                imageCount: 1
            )
        ]
    }
    
    func fetchVideoEffectCategories() async throws -> [VideoEffectCategory] {
        let effects = try await fetchVideoEffects()
        return [VideoEffectCategory(name: "Test", effects: effects)]
    }
}
```

## 版本兼容性

- **最低 iOS 版本**: iOS 15.0
- **Swift 版本**: Swift 5.7+
- **依赖框架**: SwiftUI, Combine, Foundation

## 安全考虑

- 验证 API 响应数据格式
- 过滤恶意内容
- 限制网络请求频率
- 保护用户隐私数据
