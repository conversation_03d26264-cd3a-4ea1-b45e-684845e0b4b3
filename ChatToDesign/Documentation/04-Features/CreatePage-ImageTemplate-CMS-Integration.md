# CreatePage 图片模板 CMS 集成技术方案

## 背景

将 CreatePageViewModel 中的硬编码图片模板替换为从 CMS 系统获取的真实数据，参考 HomePageViewModel 的实现方式。

## 现状分析

### HomePageViewModel 的 CMS 数据获取方式

- 使用 `SWRHook<[ImageTemplateItem]>` 管理数据获取和缓存
- 通过 `apiService.fetchImageUseCaseCMS()` 获取 CMS 数据
- 将 ImageTemplateItem 按 category 分组转换为 CMSCategory 对象
- 自动处理加载状态、错误处理和数据缓存

### CreatePageViewModel 的当前实现

- 定义了 `TemplateItem` 结构体用于图片模板
- 使用模板数组存储模板数据
- 通过 `setupImageTemplateMockData()` 设置 mock 数据

## 技术方案

### 1. 直接使用 ImageTemplateItem 模型

移除 `TemplateItem` 结构体，直接使用现有的 `ImageTemplateItem` 模型：

### 2. 架构设计

#### 2.1 更新数据模型

```swift

private var imageTemplatesHook: SWRHook<[ImageTemplateItem]>?
```


### 3. 实施步骤

#### 步骤 1：删除旧模型

- 删除 `TemplateItem` 结构体定义
- 删除 `setupImageTemplateMockData()` 方法
- 更新所有相关属性为 `[ImageTemplateItem]` 类型

#### 步骤 2：添加依赖注入

```swift
init(apiService: APIService = AppDependencyContainer.shared.coreModule.apiService) {
    self.apiService = apiService
    setupImageTemplatesHook()
}
```

#### 步骤 3：创建 SWRHook

```swift
private func setupImageTemplatesHook() {
    imageTemplatesHook = SWRHook.create(
        key: "create_page_image_templates",
        maxAge: 30 * 60,     // 30分钟缓存
        staleTime: 60 * 60,  // 1小时过期
        networkCall: { [weak self] in
            try await self?.apiService.fetchImageUseCaseCMS() ?? []
        },
        autoFetch: true
    )
    
    imageTemplatesHook?.dataPublisher
        .receive(on: DispatchQueue.main)
        .sink { [weak self] imageTemplateItems in
            self?.processImageTemplateData(imageTemplateItems)
        }
        .store(in: &cancellables)
}
```

#### 步骤 4：数据处理逻辑

```swift
private func processImageTemplateData(_ imageTemplateItems: [ImageTemplateItem]) {
    // 直接更新模板数据
    self.imageTemplates = imageTemplateItems
}
```

#### 步骤 5：更新 UI 代码

更新视图中的属性访问：

```swift
// 原来：templateItem.coverImageURL
// 现在：imageTemplateItem.thumbnail

// 原来：templateItem.videoURL  
// 现在：imageTemplateItem.videoUrl

// 其他属性名称保持一致
```

### 4. 注意事项

#### 4.1 CMS 接口确认

需要与后端确认：
- CMS 接口返回的图片模板数据格式

#### 4.2 错误处理

- CMS 数据获取失败时显示空状态
- 网络错误时的重试机制
- 加载状态的 UI 反馈

### 5. 测试要点

1. **数据获取**：验证 CMS 数据正确获取和过滤
2. **UI 显示**：确保模板列表正常显示
3. **缓存机制**：验证离线和重新打开应用的表现
4. **错误处理**：测试网络异常情况

## 实施时间表

1. **第一阶段**：删除 TemplateItem，更新数据模型（1 小时）
2. **第二阶段**：添加 SWRHook 和数据获取（1.5 小时）
3. **第三阶段**：更新 UI 代码使用新模型（1 小时）
4. **第四阶段**：测试和调试（1 小时）

总计预估时间：4.5 小时