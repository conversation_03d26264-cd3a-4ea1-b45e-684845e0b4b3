# Recreate Button Implementation

## 概述

本文档描述了在 `CreateDetailPage` 中实现 recreate 按钮功能的完整方案。当用户点击 recreate 按钮时，系统会将当前 `AssetResponse` 的数据适配到 `CreateVideoFromTemplatePage`，让用户基于原始内容重新创建视频。

## 架构设计

### 1. 数据流程

```
AssetResponse → AssetToTemplateAdapter → CreateVideoFromTemplatePage
```

### 2. 核心组件

#### 2.1 AssetToTemplateAdapter

负责将 `AssetResponse` 转换为 `CreateVideoFromTemplatePage` 所需的参数。

#### 2.2 CreateDetailPage 导航状态

管理到 `CreateVideoFromTemplatePage` 的导航。

#### 2.3 Metadata 数据结构

从 `AssetResponse.metadata.originalTaskData` 中提取模板信息。

## 数据映射规则

### 3.1 AssetResponse Metadata 结构

根据项目架构，`AssetResponse.metadata` 包含以下结构：

```swift
metadata: {
  "originalTaskData": {
    "inputData": {
      "template": String,           // 模板ID
      "inputImageUrl": [String],    // 输入图片URL数组
      "prompt": String,             // 原始提示词
      "aspectRatio": String?,       // 宽高比（可选）
      "imageCount": Int?            // 图片数量（可选）
    }
  }
}
```

### 3.2 参数映射表

| CreateVideoFromTemplatePage 参数 | AssetResponse 来源                                  | 备用方案                   |
| -------------------------------- | --------------------------------------------------- | -------------------------- |
| `templateId`                     | `metadata.originalTaskData.inputData.template`      | `"recreate_\(asset.id)"`   |
| `demoUrl`                        | `asset.url`                                         | `asset.thumbnailUrl ?? ""` |
| `inputImageUrls`                 | `metadata.originalTaskData.inputData.inputImageUrl` | `[asset.url]`              |
| `inputImageLimits`               | `metadata.originalTaskData.inputData.imageCount`    | `4` (默认值)               |
| `inputGuide`                     | 基于资产类型生成                                    | 默认指导文本               |
| `demoPrompt`                     | `metadata.originalTaskData.inputData.prompt`        | `asset.generationPrompt`   |

## 实现细节

### 4.1 AssetToTemplateAdapter 实现

```swift
struct AssetToTemplateAdapter {

  /// 将 AssetResponse 转换为 CreateVideoFromTemplatePage 所需参数
  static func convert(from asset: AssetResponse) -> CreateVideoFromTemplateParams {

    // 1. 提取 metadata 中的 originalTaskData
    let originalTaskData = extractOriginalTaskData(from: asset.metadata)

    // 2. 映射各个参数
    let templateId = originalTaskData?.inputData.template ?? generateFallbackTemplateId(for: asset)
    let demoUrl = asset.url
    let inputImageUrls = originalTaskData?.inputData.inputImageUrl ?? [asset.url]
    let inputImageLimits = originalTaskData?.inputData.imageCount ?? getDefaultImageLimits(for: asset)
    let inputGuide = generateInputGuide(for: asset)
    let demoPrompt = originalTaskData?.inputData.prompt ?? asset.generationPrompt ?? generateDefaultPrompt(for: asset)

    return CreateVideoFromTemplateParams(
      templateId: templateId,
      demoUrl: demoUrl,
      inputImageUrls: inputImageUrls,
      inputImageLimits: inputImageLimits,
      inputGuide: inputGuide,
      demoPrompt: demoPrompt
    )
  }
}
```

### 4.2 CreateDetailPage 导航实现

```swift
struct CreateDetailPage: View {
  let asset: AssetResponse
  @State private var showRecreateView = false

  private func handleRecreate() {
    showRecreateView = true
  }

  var body: some View {
    // ... 现有内容
    .fullScreenCover(isPresented: $showRecreateView) {
      let params = AssetToTemplateAdapter.convert(from: asset)
      CreateVideoFromTemplatePage(
        templateId: params.templateId,
        demoUrl: params.demoUrl,
        inputImageUrls: params.inputImageUrls,
        inputImageLimits: params.inputImageLimits,
        inputGuide: params.inputGuide,
        demoPrompt: params.demoPrompt
      )
    }
  }
}
```

## 错误处理和边界情况

### 5.1 Metadata 缺失处理

- **完全缺失 metadata**: 使用资产基本信息生成默认参数
- **部分缺失 inputData**: 使用备用数据源和默认值
- **无效的 template**: 生成基于资产 ID 的唯一模板 ID

### 5.2 资产类型处理

#### 图片资产 (image/\*)

- `demoUrl`: 使用 `asset.url`
- `inputImageUrls`: 优先使用 metadata，备用为 `[asset.url]`
- `inputGuide`: "基于此图片重新创建视频内容"

#### 视频资产 (video/\*)

- `demoUrl`: 使用 `asset.url`
- `inputImageUrls`: 优先使用 metadata，备用为 `[asset.thumbnailUrl ?? asset.url]`
- `inputGuide`: "基于此视频重新创建类似内容"

### 5.3 数据验证

```swift
private static func validateAndSanitize(_ params: CreateVideoFromTemplateParams) -> CreateVideoFromTemplateParams {
  // 验证 templateId 不为空
  let validTemplateId = params.templateId.isEmpty ? "default_template" : params.templateId

  // 验证 inputImageUrls 不为空且URL有效
  let validImageUrls = params.inputImageUrls.filter { !$0.isEmpty && URL(string: $0) != nil }
  let finalImageUrls = validImageUrls.isEmpty ? [] : validImageUrls

  // 验证 inputImageLimits 在合理范围内
  let validLimits = max(1, min(params.inputImageLimits, 10))

  return CreateVideoFromTemplateParams(
    templateId: validTemplateId,
    demoUrl: params.demoUrl,
    inputImageUrls: finalImageUrls,
    inputImageLimits: validLimits,
    inputGuide: params.inputGuide,
    demoPrompt: params.demoPrompt
  )
}
```

## 用户体验考虑

### 6.1 预填充策略

- **图片预填充**: 将原始输入图片预填充到新的创建页面
- **提示词预填充**: 使用原始提示词作为起点，用户可以修改
- **模板保持**: 尽可能使用相同的模板以保持一致性

### 6.2 导航体验

- 使用 `fullScreenCover` 提供沉浸式体验
- 保持与其他创建页面一致的导航模式
- 支持用户取消操作返回详情页

### 6.3 加载状态

- 在数据适配过程中显示适当的加载指示
- 处理网络图片加载的异步情况

## 测试策略

### 7.1 单元测试

- `AssetToTemplateAdapter.convert()` 方法的各种输入情况
- Metadata 解析的边界情况
- 参数验证逻辑

### 7.2 集成测试

- 完整的 recreate 流程测试
- 不同资产类型的适配测试
- 导航状态管理测试

### 7.3 用户测试场景

- 有完整 metadata 的资产
- 缺失 metadata 的资产
- 不同类型资产（图片/视频）
- 网络异常情况

## 性能考虑

### 8.1 内存管理

- 避免在适配过程中创建大量临时对象
- 合理处理图片 URL 的缓存

### 8.2 网络优化

- 预加载演示内容的缩略图
- 避免重复请求相同的资源

## 扩展性设计

### 9.1 支持更多创建页面

适配器模式可以轻松扩展支持其他创建页面：

- `CreateImageFromTemplatePage`
- `CreateAudioFromTemplatePage`

### 9.2 自定义适配规则

支持基于用户偏好或资产特征的自定义适配规则。

## 代码实现示例

### 10.1 CreateVideoFromTemplateParams 数据结构

```swift
/// CreateVideoFromTemplatePage 参数封装
public struct CreateVideoFromTemplateParams {
  let templateId: String
  let demoUrl: String
  let inputImageUrls: [String]
  let inputImageLimits: Int
  let inputGuide: String
  let demoPrompt: String
}
```

### 10.2 OriginalTaskData 数据结构

```swift
/// 原始任务数据结构（从 metadata 中解析）
public struct OriginalTaskData: Codable {
  let inputData: InputData

  public struct InputData: Codable {
    let template: String?
    let inputImageUrl: [String]?
    let prompt: String?
    let aspectRatio: String?
    let imageCount: Int?
  }
}
```

### 10.3 完整的 AssetToTemplateAdapter 实现

```swift
import Foundation

/// 资产到模板页面参数的适配器
public struct AssetToTemplateAdapter {

  // MARK: - Public Methods

  /// 将 AssetResponse 转换为 CreateVideoFromTemplatePage 所需参数
  public static func convert(from asset: AssetResponse) -> CreateVideoFromTemplateParams {
    let originalTaskData = extractOriginalTaskData(from: asset.metadata)

    let params = CreateVideoFromTemplateParams(
      templateId: extractTemplateId(from: originalTaskData, asset: asset),
      demoUrl: extractDemoUrl(from: asset),
      inputImageUrls: extractInputImageUrls(from: originalTaskData, asset: asset),
      inputImageLimits: extractInputImageLimits(from: originalTaskData, asset: asset),
      inputGuide: generateInputGuide(for: asset),
      demoPrompt: extractDemoPrompt(from: originalTaskData, asset: asset)
    )

    return validateAndSanitize(params)
  }

  // MARK: - Private Methods

  /// 从 metadata 中提取原始任务数据
  private static func extractOriginalTaskData(from metadata: [String: AnyCodable]?) -> OriginalTaskData? {
    guard let metadata = metadata,
          let originalTaskDataValue = metadata["originalTaskData"]?.value,
          let originalTaskDataDict = originalTaskDataValue as? [String: Any],
          let inputDataDict = originalTaskDataDict["inputData"] as? [String: Any] else {
      return nil
    }

    let inputData = OriginalTaskData.InputData(
      template: inputDataDict["template"] as? String,
      inputImageUrl: inputDataDict["inputImageUrl"] as? [String],
      prompt: inputDataDict["prompt"] as? String,
      aspectRatio: inputDataDict["aspectRatio"] as? String,
      imageCount: inputDataDict["imageCount"] as? Int
    )

    return OriginalTaskData(inputData: inputData)
  }

  /// 提取模板ID
  private static func extractTemplateId(from taskData: OriginalTaskData?, asset: AssetResponse) -> String {
    if let template = taskData?.inputData.template, !template.isEmpty {
      return template
    }
    return "recreate_\(asset.id)"
  }

  /// 提取演示URL
  private static func extractDemoUrl(from asset: AssetResponse) -> String {
    return asset.url
  }

  /// 提取输入图片URLs
  private static func extractInputImageUrls(from taskData: OriginalTaskData?, asset: AssetResponse) -> [String] {
    if let inputImageUrls = taskData?.inputData.inputImageUrl, !inputImageUrls.isEmpty {
      return inputImageUrls
    }

    // 备用方案：根据资产类型决定
    if asset.type.hasPrefix("image/") {
      return [asset.url]
    } else if asset.type.hasPrefix("video/") {
      return [asset.thumbnailUrl ?? asset.url]
    }

    return [asset.url]
  }

  /// 提取输入图片限制数量
  private static func extractInputImageLimits(from taskData: OriginalTaskData?, asset: AssetResponse) -> Int {
    if let imageCount = taskData?.inputData.imageCount, imageCount > 0 {
      return imageCount
    }

    // 根据资产类型提供默认值
    return asset.type.hasPrefix("video/") ? 4 : 1
  }

  /// 生成输入指导文本
  private static func generateInputGuide(for asset: AssetResponse) -> String {
    if asset.type.hasPrefix("image/") {
      return "基于此图片重新创建视频内容，您可以上传新的图片或使用原始图片进行修改"
    } else if asset.type.hasPrefix("video/") {
      return "基于此视频重新创建类似内容，上传相关图片来生成新的视频"
    }

    return "重新创建内容，上传图片来生成新的视频"
  }

  /// 提取演示提示词
  private static func extractDemoPrompt(from taskData: OriginalTaskData?, asset: AssetResponse) -> String {
    // 优先使用原始任务数据中的提示词
    if let prompt = taskData?.inputData.prompt, !prompt.isEmpty {
      return prompt
    }

    // 备用：使用资产的生成提示词
    if let generationPrompt = asset.generationPrompt, !generationPrompt.isEmpty {
      return generationPrompt
    }

    // 最后备用：生成默认提示词
    return generateDefaultPrompt(for: asset)
  }

  /// 生成默认提示词
  private static func generateDefaultPrompt(for asset: AssetResponse) -> String {
    if asset.type.hasPrefix("image/") {
      return "基于上传的图片创建一个有趣的视频，添加动态效果和转场"
    } else if asset.type.hasPrefix("video/") {
      return "重新创建类似风格的视频内容，保持原有的视觉特色"
    }

    return "创建一个引人注目的视频内容"
  }

  /// 验证和清理参数
  private static func validateAndSanitize(_ params: CreateVideoFromTemplateParams) -> CreateVideoFromTemplateParams {
    let validTemplateId = params.templateId.isEmpty ? "default_template" : params.templateId

    let validImageUrls = params.inputImageUrls.compactMap { url -> String? in
      guard !url.isEmpty, URL(string: url) != nil else { return nil }
      return url
    }

    let validLimits = max(1, min(params.inputImageLimits, 10))

    let validDemoUrl = params.demoUrl.isEmpty ? "" : params.demoUrl
    let validInputGuide = params.inputGuide.isEmpty ? "上传图片来创建视频" : params.inputGuide
    let validDemoPrompt = params.demoPrompt.isEmpty ? "创建视频内容" : params.demoPrompt

    return CreateVideoFromTemplateParams(
      templateId: validTemplateId,
      demoUrl: validDemoUrl,
      inputImageUrls: validImageUrls,
      inputImageLimits: validLimits,
      inputGuide: validInputGuide,
      demoPrompt: validDemoPrompt
    )
  }
}
```

## 实现优先级

1. **P0**: 基础适配器和导航功能
2. **P1**: 错误处理和数据验证
3. **P2**: 用户体验优化
4. **P3**: 性能优化和扩展功能

## 部署和监控

### 11.1 日志记录

- 记录适配过程中的关键信息
- 监控 metadata 解析失败的情况
- 跟踪用户使用 recreate 功能的频率

### 11.2 错误监控

- 监控适配过程中的异常
- 跟踪导航失败的情况
- 收集用户反馈数据

### 11.3 性能指标

- 适配器转换耗时
- 页面加载时间
- 用户完成 recreate 流程的成功率
