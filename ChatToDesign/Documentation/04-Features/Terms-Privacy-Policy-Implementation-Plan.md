# Terms of Service & Privacy Policy 实现技术方案

## 概述

为 Picadabra AI 应用添加 Terms of Service (服务条款) 和 Privacy Policy (隐私政策) 功能，在设置页面提供入口，用户可以查看相关法律文档。

## 当前架构分析

### 现有设置页面结构

- **位置**: `ChatToDesign/Presentation/Profile/ProfileSettingsView.swift`
- **架构**: MVVM 模式，使用 `ProfileSettingsViewModel`
- **UI 结构**: 分组设置项 (Account, Subscription, Support)
- **导航**: 使用 NavigationView 和 fullScreenCover 模式

### 项目架构特点

- **Clean Architecture**: Domain/Infrastructure/Presentation 分层
- **依赖注入**: 使用 `AppDependencyContainer`
- **设计系统**: 自定义 DesignSystem 组件
- **导航模式**: 偏好使用 fullScreenCover 而非 sheet

## 技术方案设计

### 1. 方案选择：WebView + 原生混合方案

#### 1.1 为什么选择 WebView 方案

- **架构匹配**: 符合项目现有的复杂内容处理模式 (图片/视频)
- **设计一致**: 支持自定义样式，与设计系统完美集成
- **扩展性强**: 支持富文本、多语言、动态更新
- **用户体验**: 使用 fullScreenCover 保持应用内体验

#### 1.2 性能优化策略

- **预编译**: 应用启动时 Markdown → HTML 预处理
- **缓存机制**: 利用现有缓存系统存储渲染结果
- **懒加载**: 用户点击时才初始化 WebView
- **原生备选**: 如遇性能问题可降级到原生 ScrollView

### 2. 文档内容准备

#### 2.1 创建 Picadabra AI 专用文档

基于现有的 `pollo-ai-tos.md` 模板，创建：

- `picadabra-ai-tos.md` - 服务条款
- `picadabra-ai-privacy-policy.md` - 隐私政策

#### 2.2 文档内容适配

- 品牌名称: Pollo.ai → Picadabra AI
- 域名和联系方式更新
- 功能描述适配 (AI 图像/视频生成)
- 支持深色模式的 CSS 样式

### 3. UI 组件实现

#### 3.1 WebView 组件架构

创建通用的文档查看组件：

```
ChatToDesign/Presentation/Common/Components/
├── DocumentWebView.swift          # WebView 封装组件
├── DocumentViewerSheet.swift      # 文档查看器容器
└── DocumentRenderer.swift         # Markdown → HTML 渲染器
```

**核心技术栈**:

- `WKWebView`: 主要内容渲染
- `SafariServices`: 外部链接处理
- JavaScript (marked.js): Markdown 解析
- 自定义 CSS: 匹配应用设计系统

#### 3.2 设置页面集成

在 `ProfileSettingsView.swift` 的 "Support" 分组中添加：

- Terms of Service 入口
- Privacy Policy 入口
- 复用现有的 `settingsRow` 组件和 `fullScreenCover` 导航

### 3. 数据层实现

#### 3.1 文档服务

```
ChatToDesign/Domain/Services/
└── DocumentService.swift          # 文档内容管理服务
```

#### 3.2 文档实体

```
ChatToDesign/Domain/Entities/
└── LegalDocument.swift           # 法律文档实体定义
```

### 4. 架构设计

#### 4.1 组件层次结构

```
ProfileSettingsView
├── Support Section
│   ├── Terms of Service Row
│   └── Privacy Policy Row
└── DocumentViewerSheet (fullScreenCover)
    └── DocumentWebView
        └── WKWebView / SFSafariViewController
```

#### 4.2 数据流

```
User Tap → ProfileSettingsView → DocumentService → DocumentWebView
                ↓
        Load Markdown Content → Render HTML → Display
```

### 5. 实现细节

#### 5.1 文档加载策略

- **本地优先**: 从 Bundle 加载 Markdown 文件
- **在线备份**: 支持从远程 URL 加载最新版本
- **缓存机制**: 使用现有的缓存系统

#### 5.2 Markdown 渲染

- 使用 JavaScript 库 (如 marked.js) 在 WKWebView 中渲染
- 自定义 CSS 样式匹配应用设计系统
- 支持深色模式

#### 5.3 用户体验优化

- **加载状态**: 显示加载指示器
- **错误处理**: 网络错误时显示友好提示
- **导航**: 支持返回按钮和关闭手势
- **可访问性**: 支持 VoiceOver 和动态字体

### 6. 文件结构

```
ChatToDesign/
├── Documentation/
│   ├── picadabra-ai-tos.md                    # 服务条款
│   └── picadabra-ai-privacy-policy.md         # 隐私政策
├── Domain/
│   ├── Entities/
│   │   └── LegalDocument.swift
│   └── Services/
│       └── DocumentService.swift
├── Infrastructure/
│   └── Services/
│       └── DocumentServiceImpl.swift
└── Presentation/
    ├── Common/
    │   └── Components/
    │       ├── DocumentWebView.swift
    │       └── DocumentViewerSheet.swift
    └── Profile/
        └── ProfileSettingsView.swift (修改)
```

### 7. 开发步骤

#### Phase 1: 核心功能实现 (优先级: 高)

1. **文档内容准备**

   - 创建 Picadabra AI 版本的 TOS 和 Privacy Policy
   - 准备 HTML 模板和深色模式 CSS 样式
   - 集成 marked.js 用于 Markdown 渲染

2. **WebView 组件开发**

   - 实现 `DocumentWebView` 核心组件
   - 实现 `DocumentViewerSheet` 全屏容器
   - 创建 `DocumentRenderer` 处理 Markdown → HTML

3. **设置页面集成**
   - 修改 `ProfileSettingsView` 添加 TOS/Privacy 入口
   - 使用现有的 `settingsRow` 和 `fullScreenCover` 模式
   - 基础导航和用户体验测试

#### Phase 2: 优化和完善 (优先级: 中)

4. **服务层实现**

   - 创建 `DocumentService` 接口和实现
   - 集成到 `AppDependencyContainer` 依赖注入
   - 添加缓存和错误处理机制

5. **性能优化**
   - 实现预编译和缓存策略
   - 添加加载状态和错误处理
   - 可访问性和多设备适配测试

#### Phase 3: 高级功能 (可选)

6. **扩展功能**
   - 多语言支持预留
   - 远程内容更新机制
   - A/B 测试集成

### 8. 技术考虑

#### 8.1 性能优化

- Markdown 预编译为 HTML
- 图片和样式资源优化
- WebView 内存管理

#### 8.2 安全考虑

- 内容安全策略 (CSP)
- 防止 XSS 攻击
- 外部链接安全检查

#### 8.3 维护性

- 文档版本管理
- 多语言支持预留
- A/B 测试支持

## 总结

该方案遵循项目现有的 Clean Architecture 模式，使用 MVVM 架构，集成到现有的设置页面中。通过 WebView 组件提供良好的文档阅读体验，同时保持与应用整体设计的一致性。

实现后用户可以在设置页面方便地访问服务条款和隐私政策，满足应用商店审核要求和法律合规需求。
