# Credits Feature Technical Plan

## Overview
This document outlines the technical implementation plan for adding a credits field to the user repository in the Picadabra iOS application.

## 1. Requirements Analysis

### 1.1 Functional Requirements
- Add a credits field to store user's credit balance
- Support reading credits from server (read-only for client)
- Display credits in the UI when needed
- Credits can only be updated via server-side operations

### 1.2 Non-functional Requirements
- Maintain data consistency across the application
- Ensure secure handling of credits data
- Provide smooth migration for existing users

## 2. Data Model Design

### 2.1 Field Definition
```swift
credits: Int  // User's credit balance
```

**Design Considerations:**
- Use `Int` type to support positive values (typically non-negative)
- Default value: 0 for new users (configurable)
- Non-nullable field to avoid complexity of handling nil values
- Client-side read-only: no update operations from mobile app

### 2.2 Database Schema
Firestore document structure:
```json
{
  "id": "userId",
  "email": "<EMAIL>",
  "displayName": "User Name",
  "photoURL": "https://...",
  "credits": 100,  // New field
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

## 3. Implementation Architecture

### 3.1 Domain Layer
**File:** `ChatToDesign/Domain/Entities/User.swift`

Changes required:
- Add `credits: Int` property to User struct
- Update `create()` factory method to initialize credits
- Update `CodingKeys` enum for proper serialization
- Consider adding computed properties for credit-related logic

### 3.2 Infrastructure Layer
**File:** `ChatToDesign/Infrastructure/ThirdParty/Firebase/Database/FirestoreUserRepository.swift`

Changes required:
- Add `credits = "credits"` to the Field enum
- Update `mapToUser()` method to parse credits from Firestore data
- Handle missing credits field for backward compatibility
- Remove any client-side update operations for credits field
- Keep read-only access to credits via `getUser()` method

### 3.3 Application Layer
**File:** `ChatToDesign/Application/Services/UserStateManager.swift`

Changes required:
- Ensure `currentUser` properly reflects credits when user data is refreshed
- Implement credit refresh mechanism to get latest credits from server
- Remove any local credit modification methods
- Implement credit change notifications when data is refreshed from server

### 3.4 Repository Interface (Optional)
**File:** `ChatToDesign/Domain/Repositories/UserRepository.swift`

Read-only methods to implement:
```swift
func getUserCredits(userId: String) async throws -> Int
func refreshUserData(userId: String) async throws -> User
```

**Note:** No update methods for credits as all modifications must be done server-side

## 4. Business Logic Extension

### 4.1 Use Cases
Client-side use cases to implement:
- `GetUserCreditsUseCase` - Retrieve user's current credit balance
- `RefreshUserDataUseCase` - Refresh user data including latest credits
- `ValidateCreditsUseCase` - Check if user has sufficient credits for client-side validation

**Server-side only operations (not implemented in client):**
- Credit modifications (add/subtract/set)
- Credit transactions
- Credit purchases

### 4.2 Business Rules
Client-side validation rules:
- Display credits accurately from server data
- Show appropriate UI for insufficient credits
- Validate user actions against current credit balance

**Server-side rules (for reference):**
- Minimum credit balance enforcement
- Maximum credit balance limits
- Credit transaction validation
- Credit expiration handling

### 4.3 Credit Operations
**Client-side operations:**
- Display current credit balance
- Show credit requirements for features
- Trigger server-side credit operations via API calls
- Refresh credit data after operations

**Server-side operations (API endpoints needed):**
- Purchase credits
- Consume credits for features
- Reward credits for actions
- Transfer credits (if applicable)

## 5. UI Integration

### 5.1 Display Locations
Potential UI locations for credits:
- User profile view
- Main navigation bar
- Settings page
- Dedicated credits/wallet page
- Feature usage points (show cost)

### 5.2 UI Components
Components to create:
- `CreditsIndicator` - Simple credit display (read-only)
- `CreditsDetailView` - Detailed credit information (read-only)
- `CreditsPurchaseView` - Purchase flow (triggers server API)
- `CreditsHistoryView` - Transaction history (read-only, if provided by server)
- `InsufficientCreditsView` - Warning when credits are insufficient

### 5.3 Design System Integration
- Use existing design tokens for consistency
- Follow established color and typography systems
- Consider creating specific tokens for credits UI

## 6. Data Migration Strategy

### 6.1 Existing Users
Options for handling existing users:
1. **Default Value Approach**: Set credits = 0 for all existing users
2. **Initial Grant Approach**: Give existing users initial credits
3. **Calculated Approach**: Calculate initial credits based on user activity

### 6.2 Implementation Methods
1. **Server-side migration**: Use Firebase Cloud Functions (recommended)
2. **Database migration**: Direct Firestore batch updates
3. **Lazy migration**: Server updates when user data is accessed

**Note:** No client-side migration for credits as client cannot modify this field

### 6.3 Firestore Security Rules
Update rules to prevent client modifications of credits field:
```javascript
// Prevent any client-side updates to credits field
allow update: if request.auth.uid == userId 
  && !request.resource.data.diff(resource.data).affectedKeys().hasAny(['credits']);

// Allow read access to credits
allow read: if request.auth.uid == userId;
```

## 7. Error Handling

### 7.1 Common Scenarios
- Missing credits field in legacy data
- Insufficient credits for operation
- Network errors during credit data refresh
- Stale credit data on client
- Server-side credit operation failures

### 7.2 Error Recovery
- Provide default value (0) for missing credits
- Clear error messages for insufficient credits
- Retry mechanism for network failures when refreshing data
- Force refresh credit data after failed operations
- Show loading states during server operations

## 8. Testing Strategy

### 8.1 Unit Tests
- User entity credits initialization
- Credits calculation methods
- Business rule validation

### 8.2 Integration Tests
- Repository read operations with credits
- UserStateManager credit data refresh
- Use case implementations (read-only)
- Server API integration for credit operations

### 8.3 UI Tests
- Credits display accuracy
- Purchase flow completion
- Error state handling

## 9. Security Considerations

### 9.1 Client Security
- Client cannot modify credits (enforced by read-only implementation)
- All credit operations validated server-side
- Client only displays and validates against current credit balance
- Implement rate limiting for credit refresh requests

### 9.2 Firebase Security
- Completely restrict client updates to credits field
- Use Cloud Functions exclusively for credit modifications
- Implement audit logging for all credit changes
- Monitor for any attempted client-side credit modifications

### 9.3 Data Integrity
- Use transactions for credit updates
- Implement idempotency for credit operations
- Regular data consistency checks

## 10. Performance Considerations

### 10.1 Caching
- Cache user credits in UserStateManager for display
- Use SWR pattern for credit queries
- Implement cache invalidation after server operations
- No optimistic updates for credits (wait for server confirmation)

### 10.2 Database Optimization
- Index credits field if server needs to query by credit ranges
- Consider denormalizing for frequently accessed data
- Server-side batch operations for credit updates
- Minimize client refresh frequency to reduce read operations

## 11. Future Enhancements

### 11.1 Advanced Features
- Credit history/transaction log
- Credit expiration system
- Credit tiers/levels
- Credit gifting between users
- Subscription-based credit allocation

### 11.2 Analytics
- Track credit usage patterns
- Monitor credit purchase conversions
- Analyze feature usage by credit cost

## 12. Implementation Timeline

### Phase 1: Core Implementation (Week 1)
- Domain entity updates
- Repository implementation
- Basic UI display

### Phase 2: Business Logic (Week 2)
- Read-only use case implementation
- Client-side validation logic
- Error handling for display and refresh operations

### Phase 3: UI Enhancement (Week 3)
- Complete read-only UI integration
- Server API integration for credit operations
- Testing and refinement

## 13. Rollback Plan

In case of issues:
1. Feature flag to disable credits UI
2. Database field remains but unused
3. Revert to previous version without data loss
4. Clear communication to users

## 14. Success Metrics

- Successful migration of all users
- No data loss or corruption
- Credits display accurately
- Credit operations complete successfully
- User satisfaction with credit system

---

This technical plan provides a comprehensive roadmap for implementing the credits feature. Each section should be reviewed and adjusted based on specific business requirements and technical constraints.