# FeedbackForm Contact Support 实现技术方案

## 概述

为 Picadabra AI 应用实现用户反馈表单功能，作为 Contact Support 的主要实现方式。用户可以通过表单提交问题、建议或错误报告，系统将反馈信息发送到后端进行处理。

## 当前架构分析

### 现有设置页面结构

- **位置**: `ChatToDesign/Presentation/Profile/ProfileSettingsView.swift`
- **架构**: MVVM 模式，使用 `ProfileSettingsViewModel`
- **UI 结构**: 分组设置项 (Account, Subscription, Support)
- **导航**: 使用 NavigationView 和 fullScreenCover 模式

### 项目架构特点

- **Clean Architecture**: Domain/Infrastructure/Presentation 分层
- **依赖注入**: 使用 `AppDependencyContainer`
- **设计系统**: 自定义 DesignSystem 组件
- **导航模式**: 偏好使用 fullScreenCover 而非 sheet
- **API 服务**: 现有 APIService 基础设施
- **用户认证**: Firebase Auth 集成

## 技术方案设计

### 1. 功能需求

#### 1.1 核心功能

- 用户可以选择反馈类型（Bug 报告、功能建议、使用问题、其他）
- 用户可以输入详细的反馈内容
- 自动收集设备信息和应用版本
- 支持上传截图附件（可选）
- 表单验证和提交状态管理
- 提交成功后的确认反馈

#### 1.2 用户体验要求

- 界面简洁直观，符合应用设计风格
- 支持键盘自动调整
- 提交过程有明确的加载状态
- 错误处理和重试机制
- 提交成功后的感谢页面

### 2. 架构设计

#### 2.1 组件层次结构

```
ChatToDesign/Presentation/Support/
├── FeedbackFormView.swift            # 反馈表单主视图
├── FeedbackFormViewModel.swift       # 反馈表单ViewModel
├── Components/
│   ├── FeedbackTypeSelector.swift   # 反馈类型选择器
│   ├── FeedbackTextEditor.swift     # 反馈内容编辑器
│   ├── AttachmentUploader.swift     # 附件上传组件
│   └── SubmitButton.swift           # 提交按钮组件
└── Models/
    └── FeedbackModels.swift         # 反馈相关数据模型
```

#### 2.2 数据层设计

```
ChatToDesign/Domain/Services/
└── FeedbackService.swift            # 反馈服务接口

ChatToDesign/Infrastructure/Services/
└── DefaultFeedbackService.swift     # 反馈服务实现

ChatToDesign/Domain/Entities/
└── Feedback.swift                   # 反馈实体定义
```

#### 2.3 API 集成

```
ChatToDesign/Infrastructure/API/
└── FeedbackAPI.swift               # 反馈API端点定义
```

### 3. 数据模型设计

#### 3.1 反馈实体

```swift
// Feedback.swift
struct Feedback {
    let id: String
    let userId: String?
    let type: FeedbackType
    let subject: String
    let content: String
    let deviceInfo: DeviceInfo
    let appVersion: String
    let attachments: [FeedbackAttachment]
    let createdAt: Date
    let status: FeedbackStatus
}

enum FeedbackType: String, CaseIterable {
    case bug = "bug"
    case feature = "feature"
    case question = "question"
    case other = "other"

    var displayName: String {
        switch self {
        case .bug: return "Bug Report"
        case .feature: return "Feature Request"
        case .question: return "Question"
        case .other: return "Other"
        }
    }
}

struct FeedbackAttachment: Identifiable, Codable {
    let id: String        // 用于UI中的删除操作
    let url: String       // 图片URL
    let filename: String  // 文件名，用于UI显示
}

struct DeviceInfo {
    let deviceModel: String
    let systemVersion: String
    let appVersion: String
    let buildNumber: String
}

enum FeedbackStatus {
    case draft
    case submitting
    case submitted
    case failed(Error)
}
```

### 4. API 设计

#### 4.1 提交反馈 API

**Endpoint**: `POST /api/v1/feedback`

**Request Body**:

```json
{
  "type": "bug",
  "subject": "App crashes when uploading image",
  "content": "Detailed description of the issue...",
  "deviceInfo": {
    "deviceModel": "iPhone 15 Pro",
    "systemVersion": "iOS 17.0",
    "appVersion": "1.0.0",
    "buildNumber": "100"
  },
  "attachments": [
    {
      "id": "att_123",
      "url": "https://cdn.example.com/feedback/feedback_uuid.jpg",
      "filename": "screenshot.jpg"
    }
  ]
}
```

**Response**:

```json
{
  "success": true,
  "feedbackId": "fb_123456789",
  "message": "Thank you for your feedback!"
}
```

### 5. UI/UX 设计

#### 5.1 页面布局

```
┌─────────────────────────────────┐
│ ← Contact Support              │
├─────────────────────────────────┤
│ What can we help you with?      │
│                                 │
│ ○ Bug Report                    │
│ ○ Feature Request               │
│ ○ Question                      │
│ ○ Other                         │
│                                 │
│ Subject                         │
│ ┌─────────────────────────────┐ │
│ │ Brief description...        │ │
│ └─────────────────────────────┘ │
│                                 │
│ Details                         │
│ ┌─────────────────────────────┐ │
│ │ Please provide more         │ │
│ │ details about your issue... │ │
│ │                             │ │
│ └─────────────────────────────┘ │
│                                 │
│ 📎 Add Screenshot (Optional)    │
│                                 │
│ [Submit Feedback]               │
└─────────────────────────────────┘
```

#### 5.2 设计系统集成

- 使用现有的 FigmaTokens 颜色系统
- 复用现有的表单组件样式
- 遵循应用的圆角、间距等设计规范
- 使用统一的字体系统 (Inter)

### 6. 实施步骤

#### Phase 1: 基础架构搭建

1. 创建数据模型和实体
2. 实现 FeedbackService 接口
3. 添加 API 端点定义
4. 集成到依赖注入系统

#### Phase 2: UI 组件开发

1. 实现 FeedbackFormView 主视图
2. 开发 FeedbackFormViewModel
3. 创建子组件 (类型选择器、文本编辑器等)
4. 集成到 ProfileSettingsView

#### Phase 3: 功能完善

1. 添加表单验证逻辑
2. 实现附件上传功能
3. 添加设备信息收集
4. 完善错误处理和重试机制

#### Phase 4: 测试和优化

1. 单元测试覆盖
2. UI 测试验证
3. 性能优化
4. 用户体验优化

### 7. 技术实现细节

#### 7.1 表单验证

```swift
// FeedbackFormViewModel.swift
class FeedbackFormViewModel: ObservableObject {
    @Published var selectedType: FeedbackType = .question
    @Published var subject: String = ""
    @Published var content: String = ""
    @Published var isSubmitting: Bool = false
    @Published var submitError: Error?

    var isFormValid: Bool {
        !subject.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        content.count >= 10
    }
}
```

#### 7.2 附件上传实现

```swift
// FeedbackFormViewModel.swift - 附件上传方法
func addImageAttachment(_ image: UIImage) async {
    isUploadingAttachment = true

    do {
        let filename = "feedback_\(UUID().uuidString).jpg"

        // 使用简单的图片上传服务
        let url = try await uploadService.uploadImage(
            image: image,
            fileName: filename,
            quality: 0.8,
            prefix: "feedback"
        )

        let attachment = FeedbackAttachment(
            id: UUID().uuidString,
            url: url.absoluteString,
            filename: filename
        )

        attachments.append(attachment)
        Logger.info("Feedback attachment uploaded: \(filename)")

    } catch {
        Logger.error("Failed to upload feedback attachment: \(error)")
        errorMessage = "Failed to upload image. Please try again."
        showErrorAlert = true
    }

    isUploadingAttachment = false
}

func removeAttachment(_ attachmentId: String) {
    attachments.removeAll { $0.id == attachmentId }
}
```

#### 7.3 设备信息收集

```swift
// DeviceInfoCollector.swift
struct DeviceInfoCollector {
    static func collect() -> DeviceInfo {
        return DeviceInfo(
            deviceModel: UIDevice.current.model,
            systemVersion: UIDevice.current.systemVersion,
            appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown",
            buildNumber: Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown"
        )
    }
}
```

### 8. 错误处理策略

#### 8.1 网络错误处理

- 网络连接失败：显示重试选项
- 服务器错误：显示友好错误信息
- 超时处理：自动重试机制

#### 8.2 表单验证错误

- 实时验证反馈
- 明确的错误提示信息
- 防止重复提交

### 9. 性能考虑

#### 9.1 内存管理

- 图片附件压缩处理
- 及时释放不需要的资源
- 避免内存泄漏

#### 9.2 网络优化

- 请求超时设置
- 重试机制实现
- 上传进度显示

### 10. 安全考虑

#### 10.1 数据保护

- 用户隐私信息脱敏
- 敏感信息加密传输
- 符合数据保护法规

#### 10.2 防滥用机制

- 提交频率限制
- 内容长度限制
- 垃圾信息过滤

## 成功标准

1. **功能完整性**: 用户能够成功提交各类型反馈
2. **用户体验**: 界面友好，操作流畅，错误处理完善
3. **性能表现**: 提交响应时间 < 3 秒，界面流畅无卡顿
4. **稳定性**: 错误率 < 1%，崩溃率 < 0.1%
5. **可维护性**: 代码结构清晰，测试覆盖率 > 80%

## 风险评估

### 技术风险

- **中等**: API 集成复杂度
- **低**: UI 组件开发难度
- **低**: 现有架构集成风险

### 业务风险

- **低**: 用户接受度风险
- **中等**: 客服工作量增加
- **低**: 数据隐私合规风险

## 后续扩展

1. **多语言支持**: 国际化反馈表单
2. **智能分类**: AI 自动分类反馈类型
3. **实时聊天**: 集成在线客服功能
4. **反馈跟踪**: 用户可查看反馈处理状态
5. **数据分析**: 反馈数据统计和分析面板

## 附录：核心代码示例

### A.1 FeedbackFormView 核心实现

```swift
// FeedbackFormView.swift
struct FeedbackFormView: View {
    @StateObject private var viewModel: FeedbackFormViewModel
    @Environment(\.dismiss) private var dismiss

    init(feedbackService: FeedbackService) {
        self._viewModel = StateObject(wrappedValue: FeedbackFormViewModel(feedbackService: feedbackService))
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    feedbackTypeSection
                    subjectSection
                    contentSection
                    attachmentSection
                    submitSection
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 16)
            }
            .navigationTitle("Contact Support")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
        .alert("Feedback Submitted", isPresented: $viewModel.showSuccessAlert) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Thank you for your feedback! We'll get back to you soon.")
        }
        .alert("Error", isPresented: $viewModel.showErrorAlert) {
            Button("Retry") {
                Task {
                    await viewModel.submitFeedback()
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text(viewModel.errorMessage)
        }
    }
}
```

### A.2 FeedbackFormViewModel 核心逻辑

```swift
// FeedbackFormViewModel.swift
@MainActor
class FeedbackFormViewModel: ObservableObject {
    @Published var selectedType: FeedbackType = .question
    @Published var subject: String = ""
    @Published var content: String = ""
    @Published var attachments: [FeedbackAttachment] = []
    @Published var isSubmitting: Bool = false
    @Published var isUploadingAttachment: Bool = false
    @Published var showSuccessAlert: Bool = false
    @Published var showErrorAlert: Bool = false
    @Published var errorMessage: String = ""

    private let feedbackService: FeedbackService
    private let uploadService: FileUploadService

    init(feedbackService: FeedbackService, uploadService: FileUploadService) {
        self.feedbackService = feedbackService
        self.uploadService = uploadService
    }

    var isFormValid: Bool {
        !subject.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        content.count >= 10
    }

    func addImageAttachment(_ image: UIImage) async {
        isUploadingAttachment = true

        do {
            let filename = "feedback_\(UUID().uuidString).jpg"

            let url = try await uploadService.uploadImage(
                image: image,
                fileName: filename,
                quality: 0.8,
                prefix: "feedback"
            )

            let attachment = FeedbackAttachment(
                id: UUID().uuidString,
                url: url.absoluteString,
                filename: filename
            )

            attachments.append(attachment)

        } catch {
            errorMessage = "Failed to upload image. Please try again."
            showErrorAlert = true
            Logger.error("Failed to upload feedback attachment: \(error)")
        }

        isUploadingAttachment = false
    }

    func removeAttachment(_ attachmentId: String) {
        attachments.removeAll { $0.id == attachmentId }
    }

    func submitFeedback() async {
        guard isFormValid else { return }

        isSubmitting = true

        do {
            let feedback = Feedback(
                id: UUID().uuidString,
                userId: UserStateManager.shared.currentUser?.id,
                type: selectedType,
                subject: subject.trimmingCharacters(in: .whitespacesAndNewlines),
                content: content.trimmingCharacters(in: .whitespacesAndNewlines),
                deviceInfo: DeviceInfoCollector.collect(),
                appVersion: Bundle.main.appVersion,
                attachments: attachments,
                createdAt: Date(),
                status: .submitting
            )

            try await feedbackService.submitFeedback(feedback)

            showSuccessAlert = true
            Logger.info("Feedback submitted successfully")

        } catch {
            errorMessage = error.localizedDescription
            showErrorAlert = true
            Logger.error("Failed to submit feedback: \(error)")
        }

        isSubmitting = false
    }
}
```

### A.3 FeedbackService 接口定义

```swift
// FeedbackService.swift
protocol FeedbackService {
    func submitFeedback(_ feedback: Feedback) async throws
}

// DefaultFeedbackService.swift
class DefaultFeedbackService: FeedbackService {
    private let apiService: APIService

    init(apiService: APIService) {
        self.apiService = apiService
    }

    func submitFeedback(_ feedback: Feedback) async throws {
        let request = SubmitFeedbackRequest(
            type: feedback.type.rawValue,
            subject: feedback.subject,
            content: feedback.content,
            deviceInfo: feedback.deviceInfo,
            appVersion: feedback.appVersion,
            attachments: feedback.attachments
        )

        let _: SubmitFeedbackResponse = try await apiService.request(
            endpoint: .submitFeedback,
            method: .post,
            body: request
        )
    }
}
```

### A.4 API 端点定义

```swift
// APIEndpoint+Feedback.swift
extension APIEndpoint {
    static let submitFeedback = APIEndpoint(
        path: "/api/v1/feedback",
        method: .post,
        requiresAuth: false
    )
}

struct SubmitFeedbackRequest: Codable {
    let type: String
    let subject: String
    let content: String
    let deviceInfo: DeviceInfo
    let appVersion: String
    let attachments: [FeedbackAttachment]
}

struct SubmitFeedbackResponse: Codable {
    let success: Bool
    let feedbackId: String
    let message: String
}
```

### A.5 依赖注入集成

```swift
// AppDependencyContainer+Support.swift
extension AppDependencyContainer {
    /// 支持模块
    public lazy var supportModule: SupportModule = {
        return SupportModule(dependencies: moduleDependencies)
    }()
}

// SupportModule.swift
public final class SupportModule {
    private let dependencies: ModuleDependencies

    public init(dependencies: ModuleDependencies) {
        self.dependencies = dependencies
    }

    public var feedbackService: FeedbackService {
        DefaultFeedbackService(apiService: dependencies.apiService)
    }

    public var uploadService: FileUploadService {
        DefaultFileUploadService(
            apiService: dependencies.apiService,
            configuration: FileUploadConfiguration.feedback
        )
    }
}
```

### A.6 ProfileSettingsView 集成

```swift
// ProfileSettingsView.swift 修改
settingsRow(
    icon: "envelope",
    title: "Contact Support",
    action: {
        viewModel.showFeedbackForm()
    }
)

// ProfileSettingsViewModel.swift 添加
@Published var showingFeedbackForm: Bool = false

func showFeedbackForm() {
    showingFeedbackForm = true
}

// ProfileSettingsView.swift 添加 fullScreenCover
.fullScreenCover(isPresented: $viewModel.showingFeedbackForm) {
    FeedbackFormView(
        feedbackService: AppDependencyContainer.shared.supportModule.feedbackService,
        uploadService: AppDependencyContainer.shared.supportModule.uploadService
    )
}
```

### A.7 文件上传配置

```swift
// FileUploadConfiguration+Feedback.swift
extension FileUploadConfiguration {
    static let feedback = FileUploadConfiguration(
        maxFileSize: 5 * 1024 * 1024,  // 5MB
        allowedMimeTypes: ["image/jpeg", "image/png"],
        defaultPrefix: "feedback",
        compressionQuality: 0.8
    )
}
```

### A.8 AttachmentUploader UI 组件

```swift
// AttachmentUploader.swift
struct AttachmentUploader: View {
    @ObservedObject var viewModel: FeedbackFormViewModel
    @State private var showImagePicker = false

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Add Screenshot (Optional)")
                .font(.custom("Inter", size: 16).weight(.medium))
                .foregroundColor(.primary)

            if viewModel.attachments.isEmpty {
                Button(action: { showImagePicker = true }) {
                    HStack(spacing: 8) {
                        Image(systemName: "camera")
                        Text("Add Screenshot")
                    }
                    .foregroundColor(.blue)
                    .padding(.vertical, 8)
                }
            } else {
                // 显示已上传的附件
                ForEach(viewModel.attachments) { attachment in
                    AttachmentRow(attachment: attachment) {
                        viewModel.removeAttachment(attachment.id)
                    }
                }

                if viewModel.attachments.count < 3 {
                    Button("Add Another Screenshot") {
                        showImagePicker = true
                    }
                    .foregroundColor(.blue)
                    .padding(.top, 4)
                }
            }

            if viewModel.isUploadingAttachment {
                HStack(spacing: 8) {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("Uploading...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 4)
            }
        }
        .sheet(isPresented: $showImagePicker) {
            ImagePicker { image in
                Task {
                    await viewModel.addImageAttachment(image)
                }
            }
        }
    }
}

struct AttachmentRow: View {
    let attachment: FeedbackAttachment
    let onRemove: () -> Void

    var body: some View {
        HStack {
            Image(systemName: "photo")
                .foregroundColor(.blue)

            Text(attachment.filename)
                .font(.caption)
                .foregroundColor(.primary)

            Spacer()

            Button(action: onRemove) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.red)
            }
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}
```
