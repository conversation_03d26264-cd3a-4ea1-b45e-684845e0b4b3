# VideoGenerationHandler 集成实现文档

## 概述

本文档详细说明如何将 `VideoGenerationHandler` 集成到 `CreateVideoFromTemplateViewModel` 中，实现真实的视频生成流程，替换现有的模拟实现。

## 架构分析

### 现有架构

- **CreateVideoFromTemplateViewModel**: 当前使用模拟的异步延迟来模拟视频生成过程
- **VideoCreationLoadingState**: 定义了完整的加载状态枚举
- **LoadingOverlayView**: 支持详细的加载状态展示
- **CreateButtonView**: 简单的加载状态显示

### VideoGenerationHandler 架构

- **职责**: 管理视频生成的完整流程，包括图片上传、任务创建和状态观察
- **状态管理**: 通过 `@Published` 属性提供响应式状态更新
- **核心方法**: `startGenerationWithImageUrls()` - 使用图片 URLs 开始生成

## 集成方案

### 1. 状态映射策略

将 `VideoGenerationHandler` 的状态映射到 `VideoCreationLoadingState`:

```swift
VideoGenerationHandler状态 -> VideoCreationLoadingState映射：
- isLoading=true + taskCreatedSuccessfully=false -> .creatingTask
- isLoading=true + taskCreatedSuccessfully=true -> .generating
- generatedVideoUrls != nil -> .completed
- errorMessage != nil -> .failed(errorMessage)
- 默认状态 -> .idle
```

### 2. 依赖注入

在 `CreateVideoFromTemplateViewModel` 中注入 `VideoGenerationHandler`:

```swift
private let videoGenerationHandler: VideoGenerationHandler

init(...) {
    // 现有初始化代码

    // 初始化 VideoGenerationHandler
    let container = AppDependencyContainer.shared
    self.videoGenerationHandler = VideoGenerationHandler(
        videoGenerationService: container.videoModule.videoGenerationService,
        assetUploadService: container.storageModule.assetUploadService
    )

    // 设置状态观察
    setupVideoGenerationObservers()
}
```

### 3. 状态观察设置

```swift
private func setupVideoGenerationObservers() {
    // 观察 VideoGenerationHandler 的状态变化
    videoGenerationHandler.$isLoading
        .combineLatest(
            videoGenerationHandler.$taskCreatedSuccessfully,
            videoGenerationHandler.$generatedVideoUrls,
            videoGenerationHandler.$errorMessage
        )
        .receive(on: DispatchQueue.main)
        .sink { [weak self] (isLoading, taskCreated, videoUrls, errorMessage) in
            self?.updateLoadingState(
                isLoading: isLoading,
                taskCreated: taskCreated,
                videoUrls: videoUrls,
                errorMessage: errorMessage
            )
        }
        .store(in: &cancellables)
}
```

### 4. 状态更新逻辑

```swift
private func updateLoadingState(
    isLoading: Bool,
    taskCreated: Bool,
    videoUrls: [String]?,
    errorMessage: String?
) {
    // 更新基础加载状态
    self.isLoading = isLoading
    self.errorMessage = errorMessage

    // 映射到 VideoCreationLoadingState
    if let errorMessage = errorMessage {
        self.loadingState = .failed(errorMessage)
    } else if let videoUrls = videoUrls, !videoUrls.isEmpty {
        self.generatedVideoUrl = videoUrls.first
        self.loadingState = .completed
    } else if isLoading && taskCreated {
        self.loadingState = .generating
    } else if isLoading && !taskCreated {
        self.loadingState = .creatingTask
    } else {
        self.loadingState = .idle
    }
}
```

## 实现步骤

### 第一步: 修改 CreateVideoFromTemplateViewModel

1. **添加依赖和属性**:

   - 添加 `VideoGenerationHandler` 属性
   - 添加 `Combine` 相关的 `cancellables`

2. **修改初始化方法**:

   - 注入 `VideoGenerationHandler`
   - 设置状态观察

3. **替换 startVideoCreation() 方法**:

   - 移除模拟的异步延迟逻辑
   - 调用 `VideoGenerationHandler.startGenerationWithImageUrls()`

4. **更新取消逻辑**:
   - 调用 `VideoGenerationHandler.cancelGeneration()`

### 第二步: 升级 CreateButtonView

为了支持三状态显示（Create → Creating Task → Generating），需要升级 `CreateButtonView`:

1. **添加状态枚举**:

```swift
public enum CreateButtonState {
    case idle
    case creatingTask
    case generating
}
```

2. **修改 CreateButtonView 接口**:

```swift
struct CreateButtonView: View {
    let credits: Int
    let state: CreateButtonState
    let action: () -> Void

    // 根据状态显示不同内容
}
```

### 第三步: 错误处理和用户体验优化

1. **完善错误处理**:

   - 处理网络错误
   - 处理上传失败
   - 处理生成失败

2. **用户体验优化**:
   - 添加重试机制
   - 优化加载状态显示
   - 添加取消确认对话框

## 关键代码实现

### VideoGenerationHandler 调用

```swift
public func startVideoCreation() {
    guard !selectedImages.isEmpty else {
        errorMessage = "Please select at least one image to create your video."
        return
    }

    // 使用 VideoGenerationHandler 开始生成
    videoGenerationHandler.startGenerationWithImageUrls(
        prompt: demoPrompt,
        imageUrls: selectedImages,
        template: templateId,
        chatId: nil
    )
}
```

### 取消操作

```swift
public func cancelVideoCreation() {
    videoGenerationHandler.cancelGeneration()
}
```

### 重试操作

```swift
public func retryVideoCreation() {
    // 清除错误状态
    errorMessage = nil

    // 重新开始生成
    startVideoCreation()
}
```

## 测试策略

### 单元测试

1. 测试状态映射逻辑
2. 测试错误处理
3. 测试取消操作

### 集成测试

1. 测试完整的视频生成流程
2. 测试网络异常情况
3. 测试用户交互场景

### UI 测试

1. 测试加载状态显示
2. 测试按钮状态变化
3. 测试错误提示显示

## 风险控制

### 向后兼容性

- 保持现有的公共 API 不变
- 确保现有的 UI 组件正常工作

### 错误处理

- 完善的网络错误处理
- 用户友好的错误提示
- 自动重试机制

### 性能考虑

- 避免内存泄漏
- 及时取消不需要的任务
- 优化状态更新频率

## 部署计划

### 阶段一: 核心集成

- 集成 VideoGenerationHandler
- 实现基础状态映射
- 替换模拟逻辑

### 阶段二: UI 优化

- 升级 CreateButtonView
- 优化加载状态显示
- 完善错误处理

### 阶段三: 测试和优化

- 完整测试覆盖
- 性能优化
- 用户体验优化

## 详细代码实现

### CreateVideoFromTemplateViewModel 完整实现

```swift
import Combine
import Foundation
import AVFoundation

@MainActor
public class CreateVideoFromTemplateViewModel: ObservableObject {

    // MARK: - Published Properties

    @Published public var isLoading: Bool = false
    @Published public var loadingState: VideoCreationLoadingState = .idle
    @Published public var errorMessage: String?
    @Published public var selectedImages: [String] = []
    @Published public var isUserAssetSelectionPresented: Bool = false
    @Published public var generatedVideoUrl: String?
    @Published public var demoPlayer: AVPlayer?

    // MARK: - Private Properties

    private let templateId: String
    private let demoUrl: String
    private let inputImageUrls: [String]
    private let inputImageLimits: Int
    private let inputGuide: String
    private let demoPrompt: String

    // VideoGenerationHandler 集成
    private let videoGenerationHandler: VideoGenerationHandler
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization

    public init(
        templateId: String,
        demoUrl: String,
        inputImageUrls: [String],
        inputImageLimits: Int,
        inputGuide: String,
        demoPrompt: String
    ) {
        self.templateId = templateId
        self.demoUrl = demoUrl
        self.inputImageUrls = inputImageUrls
        self.inputImageLimits = inputImageLimits
        self.inputGuide = inputGuide
        self.demoPrompt = demoPrompt

        // 初始化 VideoGenerationHandler
        let container = AppDependencyContainer.shared
        self.videoGenerationHandler = VideoGenerationHandler(
            videoGenerationService: container.videoModule.videoGenerationService,
            assetUploadService: container.storageModule.assetUploadService
        )

        // Pre-load input images if provided
        loadInputImages()

        // 设置状态观察
        setupVideoGenerationObservers()
    }

    // MARK: - Private Methods

    private func setupVideoGenerationObservers() {
        // 观察 VideoGenerationHandler 的状态变化
        videoGenerationHandler.$isLoading
            .combineLatest(
                videoGenerationHandler.$taskCreatedSuccessfully,
                videoGenerationHandler.$generatedVideoUrls,
                videoGenerationHandler.$errorMessage
            )
            .receive(on: DispatchQueue.main)
            .sink { [weak self] (isLoading, taskCreated, videoUrls, errorMessage) in
                self?.updateLoadingState(
                    isLoading: isLoading,
                    taskCreated: taskCreated,
                    videoUrls: videoUrls,
                    errorMessage: errorMessage
                )
            }
            .store(in: &cancellables)
    }

    private func updateLoadingState(
        isLoading: Bool,
        taskCreated: Bool,
        videoUrls: [String]?,
        errorMessage: String?
    ) {
        // 更新基础加载状态
        self.isLoading = isLoading
        self.errorMessage = errorMessage

        // 映射到 VideoCreationLoadingState
        if let errorMessage = errorMessage {
            self.loadingState = .failed(errorMessage)
        } else if let videoUrls = videoUrls, !videoUrls.isEmpty {
            self.generatedVideoUrl = videoUrls.first
            self.loadingState = .completed
        } else if isLoading && taskCreated {
            self.loadingState = .generating
        } else if isLoading && !taskCreated {
            self.loadingState = .creatingTask
        } else {
            self.loadingState = .idle
        }
    }

    // MARK: - Public Methods

    /// Start video creation process - 使用 VideoGenerationHandler
    public func startVideoCreation() {
        guard !selectedImages.isEmpty else {
            errorMessage = "Please select at least one image to create your video."
            return
        }

        // 使用 VideoGenerationHandler 开始生成
        videoGenerationHandler.startGenerationWithImageUrls(
            prompt: demoPrompt,
            imageUrls: selectedImages,
            template: templateId,
            chatId: nil
        )
    }

    /// Cancel video creation
    public func cancelVideoCreation() {
        videoGenerationHandler.cancelGeneration()
    }

    /// Retry video creation
    public func retryVideoCreation() {
        // 清除错误状态
        errorMessage = nil

        // 重新开始生成
        startVideoCreation()
    }

    // ... 其他现有方法保持不变
}
```

### CreateButtonView 升级实现

```swift
import SwiftUI

// MARK: - Create Button State

public enum CreateButtonState {
    case idle
    case creatingTask
    case generating
}

// MARK: - Enhanced Create Button View

struct EnhancedCreateButtonView: View {
    let credits: Int
    let state: CreateButtonState
    let action: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            // Shadow/blur effect
            Rectangle()
                .fill(Color.black.opacity(0.3))
                .frame(height: 35)
                .blur(radius: 35)
                .offset(y: -31)

            // Button container
            VStack(spacing: 10) {
                Button(action: action) {
                    HStack(spacing: 8) {
                        // 根据状态显示不同内容
                        switch state {
                        case .idle:
                            // 正常状态
                            Image(systemName: "sparkles")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white)

                            Text("\(credits)")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white)

                            Text("Create")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white)

                        case .creatingTask:
                            // 创建任务状态
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)

                            Text("Creating Task...")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white)

                        case .generating:
                            // 生成中状态
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)

                            Text("Generating...")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 40)
                    .background(buttonBackgroundColor)
                    .cornerRadius(.infinity)
                    .shadow(color: Color.black.opacity(0.1), radius: 6, x: 0, y: 4)
                }
                .buttonStyle(PlainButtonStyle())
                .disabled(state != .idle)
                .opacity(buttonOpacity)
                .padding(.horizontal, 24)
            }
            .background(Color.black)
        }
    }

    // MARK: - Computed Properties

    private var buttonBackgroundColor: Color {
        switch state {
        case .idle:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.33, green: 0.43, blue: 0.69),
                    Color(red: 0.33, green: 0.43, blue: 0.69),
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case .creatingTask, .generating:
            return Color.gray.opacity(0.6)
        }
    }

    private var buttonOpacity: Double {
        switch state {
        case .idle:
            return 1.0
        case .creatingTask, .generating:
            return 0.6
        }
    }
}
```

### CreateVideoFromTemplatePage 更新

```swift
// 在 CreateVideoFromTemplatePage 中使用新的按钮
private var createButtonSection: some View {
    EnhancedCreateButtonView(
        credits: 35,
        state: createButtonState,
        action: {
            viewModel.startVideoCreation()
        }
    )
}

// 计算按钮状态
private var createButtonState: CreateButtonState {
    switch viewModel.loadingState {
    case .idle:
        return .idle
    case .creatingTask:
        return .creatingTask
    case .queued, .generating:
        return .generating
    case .completed, .failed:
        return .idle
    }
}
```

## 总结

通过将 `VideoGenerationHandler` 集成到 `CreateVideoFromTemplateViewModel` 中，我们可以：

1. **实现真实的视频生成流程**，替换模拟实现
2. **保持现有的 UI 架构**，最小化改动影响
3. **提供一致的用户体验**，与其他视频生成页面保持一致
4. **确保代码的可维护性**，清晰的职责分离

这个集成方案充分利用了现有的架构设计，通过最小化的改动实现了功能升级，同时保持了代码的一致性和可维护性。

## 实现注意事项

### 1. 内存管理

- 确保在 `deinit` 中正确清理 `cancellables`
- `VideoGenerationHandler` 使用 `weak self` 避免循环引用
- 及时取消不需要的任务和订阅

### 2. 错误处理

- 网络错误：显示用户友好的错误信息
- 上传失败：提供重试选项
- 生成失败：允许用户重新尝试
- 参数验证：确保必要参数不为空

### 3. 状态同步

- 使用 `@MainActor` 确保 UI 更新在主线程
- 通过 `Combine` 确保状态变化及时反映到 UI
- 避免状态不一致的情况

### 4. 用户体验

- 提供清晰的加载状态指示
- 支持取消操作
- 错误时提供重试选项
- 保持按钮状态与实际操作同步

## 迁移检查清单

### 代码修改

- [ ] 在 `CreateVideoFromTemplateViewModel` 中添加 `VideoGenerationHandler`
- [ ] 添加 `Combine` 导入和 `cancellables` 属性
- [ ] 实现 `setupVideoGenerationObservers()` 方法
- [ ] 实现 `updateLoadingState()` 方法
- [ ] 修改 `startVideoCreation()` 方法
- [ ] 修改 `cancelVideoCreation()` 方法
- [ ] 添加 `retryVideoCreation()` 方法

### UI 组件升级

- [ ] 创建 `CreateButtonState` 枚举
- [ ] 实现 `EnhancedCreateButtonView`
- [ ] 在 `CreateVideoFromTemplatePage` 中使用新按钮
- [ ] 实现按钮状态计算逻辑

### 测试验证

- [ ] 测试正常的视频生成流程
- [ ] 测试错误处理和重试
- [ ] 测试取消操作
- [ ] 测试状态同步
- [ ] 测试内存管理

### 性能优化

- [ ] 确保状态更新不会过于频繁
- [ ] 优化图片上传性能
- [ ] 避免不必要的 UI 重绘

## 常见问题解决

### Q: 状态更新不及时怎么办？

A: 确保所有状态更新都在 `@MainActor` 上下文中进行，使用 `.receive(on: DispatchQueue.main)` 确保 Combine 订阅在主线程执行。

### Q: 内存泄漏怎么处理？

A: 在所有闭包中使用 `[weak self]`，在 `deinit` 中清理 `cancellables`，及时取消不需要的任务。

### Q: 如何处理网络错误？

A: 在 `updateLoadingState` 中检查 `errorMessage`，显示用户友好的错误信息，提供重试选项。

### Q: 按钮状态不正确怎么办？

A: 检查 `createButtonState` 计算逻辑，确保与 `VideoCreationLoadingState` 的映射正确。

## 后续优化建议

### 1. 添加进度指示

- 显示上传进度
- 显示生成进度
- 提供更详细的状态信息

### 2. 优化用户体验

- 添加生成预估时间
- 提供生成队列状态
- 支持后台生成

### 3. 错误处理增强

- 分类错误类型
- 提供具体的解决建议
- 支持自动重试

### 4. 性能优化

- 图片压缩优化
- 网络请求优化
- 内存使用优化
