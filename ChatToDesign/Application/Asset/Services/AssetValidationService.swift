//
//  AssetValidationService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/7.
//

import Foundation

// MARK: - Asset Validation Service

/// 资产验证服务
/// 负责验证文件是否符合上传要求
final class AssetValidationService {

  // MARK: - 属性

  /// 上传配置
  let configuration: FileUploadConfiguration

  // MARK: - 初始化

  /// 初始化资产验证服务
  /// - Parameter configuration: 上传配置
  init(configuration: FileUploadConfiguration) {
    self.configuration = configuration
  }

  // MARK: - 验证方法

  /// 验证文件是否符合上传要求
  /// - Parameters:
  ///   - data: 文件数据
  ///   - mimeType: MIME类型
  /// - Throws: AssetError 如果文件不符合要求
  func validateFile(data: Data, mimeType: String) throws {
    // 1. 验证文件大小
    try validateFileSize(data: data, mimeType: mimeType)

    // 2. 验证MIME类型
    try validateMimeType(mimeType)

    // 3. 验证文件内容
    try validateFileContent(data: data, mimeType: mimeType)

    Logger.debug("File validation passed for MIME type: \(mimeType), size: \(data.count) bytes")
  }

  // MARK: - 私有验证方法

  /// 验证文件大小
  /// - Parameters:
  ///   - data: 文件数据
  ///   - mimeType: MIME类型
  /// - Throws: AssetError.validationFailed 如果文件大小不符合要求
  private func validateFileSize(data: Data, mimeType: String) throws {
    let fileSize = data.count

    // 检查最大文件大小
    if fileSize > configuration.maxFileSize {
      let maxSizeMB = Double(configuration.maxFileSize) / (1024 * 1024)
      let currentSizeMB = Double(fileSize) / (1024 * 1024)
      throw AssetError.validationFailed(
        "文件大小 \(String(format: "%.2f", currentSizeMB))MB 超过最大限制 \(String(format: "%.2f", maxSizeMB))MB"
      )
    }

    // 检查最小文件大小
    if fileSize < configuration.minFileSize {
      throw AssetError.validationFailed("文件大小不能小于 \(configuration.minFileSize) 字节")
    }

    // 检查特定类型的大小限制
    if mimeType.hasPrefix("image/") && fileSize > configuration.maxImageSize {
      let maxSizeMB = Double(configuration.maxImageSize) / (1024 * 1024)
      let currentSizeMB = Double(fileSize) / (1024 * 1024)
      throw AssetError.validationFailed(
        "图片大小 \(String(format: "%.2f", currentSizeMB))MB 超过最大限制 \(String(format: "%.2f", maxSizeMB))MB"
      )
    }

    if mimeType.hasPrefix("video/") && fileSize > configuration.maxVideoSize {
      let maxSizeMB = Double(configuration.maxVideoSize) / (1024 * 1024)
      let currentSizeMB = Double(fileSize) / (1024 * 1024)
      throw AssetError.validationFailed(
        "视频大小 \(String(format: "%.2f", currentSizeMB))MB 超过最大限制 \(String(format: "%.2f", maxSizeMB))MB"
      )
    }
  }

  /// 验证MIME类型
  /// - Parameter mimeType: MIME类型
  /// - Throws: AssetError.validationFailed 如果MIME类型不被支持
  private func validateMimeType(_ mimeType: String) throws {
    guard configuration.supportedMimeTypes.contains(mimeType) else {
      throw AssetError.validationFailed("不支持的文件类型: \(mimeType)")
    }
  }

  /// 验证文件内容
  /// - Parameters:
  ///   - data: 文件数据
  ///   - mimeType: MIME类型
  /// - Throws: AssetError.validationFailed 如果文件内容不符合要求
  private func validateFileContent(data: Data, mimeType: String) throws {
    // 验证文件不为空
    guard !data.isEmpty else {
      throw AssetError.validationFailed("文件内容不能为空")
    }

    // 根据MIME类型进行特定验证
    if mimeType.hasPrefix("image/") {
      try validateImageContent(data: data)
    } else if mimeType.hasPrefix("video/") {
      try validateVideoContent(data: data)
    }
  }

  /// 验证图片内容
  /// - Parameter data: 图片数据
  /// - Throws: AssetError.validationFailed 如果图片内容不符合要求
  private func validateImageContent(data: Data) throws {
    // 检查图片文件头
    let imageHeaders: [String: [UInt8]] = [
      "image/jpeg": [0xFF, 0xD8, 0xFF],
      "image/png": [0x89, 0x50, 0x4E, 0x47],
      "image/gif": [0x47, 0x49, 0x46],
      "image/webp": [0x52, 0x49, 0x46, 0x46],
    ]

    var isValidImage = false
    for (_, header) in imageHeaders {
      if data.count >= header.count {
        let fileHeader = Array(data.prefix(header.count))
        if fileHeader == header {
          isValidImage = true
          break
        }
      }
    }

    guard isValidImage else {
      throw AssetError.validationFailed("无效的图片文件格式")
    }
  }

  /// 验证视频内容
  /// - Parameter data: 视频数据
  /// - Throws: AssetError.validationFailed 如果视频内容不符合要求
  private func validateVideoContent(data: Data) throws {
    // 检查视频文件头
    let videoHeaders: [String: [UInt8]] = [
      "video/mp4": [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70],
      "video/quicktime": [0x00, 0x00, 0x00, 0x14, 0x66, 0x74, 0x79, 0x70],
      "video/webm": [0x1A, 0x45, 0xDF, 0xA3],
    ]

    var isValidVideo = false
    for (_, header) in videoHeaders {
      if data.count >= header.count {
        let fileHeader = Array(data.prefix(header.count))
        if fileHeader == header {
          isValidVideo = true
          break
        }
      }
    }

    guard isValidVideo else {
      throw AssetError.validationFailed("无效的视频文件格式")
    }
  }
}
