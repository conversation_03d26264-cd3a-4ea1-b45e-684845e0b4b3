//
//  AssetApplicationService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/7.
//

import Combine
import Foundation

#if canImport(UIKit)
  import UIKit
#endif

// MARK: - Asset Application Service Protocol

/// 资产应用服务协议
/// 提供统一的资产管理和上传功能接口
public protocol AssetApplicationService {

  // MARK: - 资产管理功能

  /// 获取用户资产列表
  /// - Parameter query: 查询参数
  /// - Returns: 用户资产查询结果
  func getUserAssets(query: AssetListQuery) async throws -> UserAssetsResult

  /// 删除资产
  /// - Parameter id: 资产ID
  /// - Returns: 删除响应
  func deleteAsset(id: String) async throws -> AssetDeleteResponse

  /// 搜索资产
  /// - Parameters:
  ///   - searchTerm: 搜索关键词
  ///   - type: MIME类型过滤，可选
  ///   - limit: 结果数量限制
  /// - Returns: 匹配的资产列表
  func searchAssets(searchTerm: String, type: String?, limit: Int) async throws -> [UserAsset]

  /// 按标签获取资产
  /// - Parameters:
  ///   - tags: 标签列表
  ///   - limit: 结果数量限制
  /// - Returns: 匹配的资产列表
  func getAssetsByTags(tags: [String], limit: Int) async throws -> [UserAsset]

  /// 按类型获取资产
  /// - Parameters:
  ///   - type: MIME类型
  ///   - limit: 结果数量限制
  /// - Returns: 匹配的资产列表
  func getAssetsByType(type: String, limit: Int) async throws -> [UserAsset]

  // MARK: - 实时观察功能

  /// 观察用户资产变化
  /// - Parameter query: 查询参数
  /// - Returns: 资产列表变化的发布者
  func observeUserAssets(query: AssetListQuery) -> AnyPublisher<UserAssetsResult, Error>

  // MARK: - 资产上传功能

  /// 上传文件并创建资产
  /// - Parameters:
  ///   - data: 文件数据
  ///   - mimeType: MIME类型
  ///   - fileName: 文件名
  ///   - prefix: 文件路径前缀，可选
  ///   - tags: 标签，可选
  ///   - description: 描述，可选
  ///   - metadata: 元数据，可选
  ///   - isPublic: 是否公开，可选
  /// - Returns: 用户资产
  func uploadFileWithAsset(
    data: Data,
    mimeType: String,
    fileName: String,
    prefix: String?,
    tags: [String]?,
    description: String?,
    metadata: [String: AnyCodable]?,
    isPublic: Bool?
  ) async throws -> UserAsset

  /// 上传图片并创建资产
  /// - Parameters:
  ///   - image: UIImage对象
  ///   - fileName: 文件名
  ///   - quality: 压缩质量 (0.0-1.0)
  ///   - prefix: 文件路径前缀，可选
  ///   - tags: 标签，可选
  ///   - description: 描述，可选
  ///   - metadata: 元数据，可选
  ///   - isPublic: 是否公开，可选
  /// - Returns: 用户资产
  func uploadImageWithAsset(
    image: UIImage,
    fileName: String,
    quality: CGFloat,
    prefix: String?,
    tags: [String]?,
    description: String?,
    metadata: [String: AnyCodable]?,
    isPublic: Bool?
  ) async throws -> UserAsset

  /// 验证文件
  /// - Parameters:
  ///   - data: 文件数据
  ///   - mimeType: MIME类型
  /// - Throws: AssetError 如果文件不符合要求
  func validateFile(data: Data, mimeType: String) throws
}

// MARK: - Asset Error

/// 资产错误类型
public enum AssetError: Error, LocalizedError {
  case userNotAuthenticated
  case assetNotFound
  case accessDenied
  case uploadFailed(Error)
  case validationFailed(String)
  case networkError(Error)
  case unknown(Error)

  public var errorDescription: String? {
    switch self {
    case .userNotAuthenticated:
      return "用户未认证"
    case .assetNotFound:
      return "资产不存在"
    case .accessDenied:
      return "访问被拒绝"
    case .uploadFailed(let error):
      return "上传失败: \(error.localizedDescription)"
    case .validationFailed(let message):
      return "验证失败: \(message)"
    case .networkError(let error):
      return "网络错误: \(error.localizedDescription)"
    case .unknown(let error):
      return "未知错误: \(error.localizedDescription)"
    }
  }
}

// MARK: - Convenience Extensions

extension AssetApplicationService {

  /// 获取用户资产列表（使用默认参数）
  public func getUserAssets() async throws -> UserAssetsResult {
    let query = AssetListQuery()
    return try await getUserAssets(query: query)
  }

  /// 获取用户的图片资产
  public func getUserImages(limit: Int = 50) async throws -> [UserAsset] {
    return try await getAssetsByType(type: "image/*", limit: limit)
  }

  /// 获取用户的视频资产
  public func getUserVideos(limit: Int = 50) async throws -> [UserAsset] {
    return try await getAssetsByType(type: "video/*", limit: limit)
  }

  /// 搜索用户的图片资产
  public func searchImages(searchTerm: String, limit: Int = 50) async throws -> [UserAsset] {
    return try await searchAssets(searchTerm: searchTerm, type: "image/*", limit: limit)
  }

  /// 观察用户资产列表（使用默认参数）
  public func observeUserAssets() -> AnyPublisher<UserAssetsResult, Error> {
    let query = AssetListQuery()
    return observeUserAssets(query: query)
  }
}
