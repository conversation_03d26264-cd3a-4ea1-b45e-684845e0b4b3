//
//  AssetApplicationServiceImpl.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/7.
//

import Combine
import Foundation
import UIKit

// MARK: - Asset Application Service Implementation

/// 资产应用服务实现
final class AssetApplicationServiceImpl: AssetApplicationService {
  
  // MARK: - 用例依赖
  
  private let getUserAssetsUseCase: GetUserAssetsUseCase
  private let deleteAssetUseCase: DeleteAssetUseCase
  private let searchAssetsUseCase: SearchAssetsUseCase
  private let observeUserAssetsUseCase: ObserveUserAssetsUseCase
  private let uploadFileWithAssetUseCase: UploadFileWithAssetUseCase
  private let uploadImageWithAssetUseCase: UploadImageWithAssetUseCase
  
  // MARK: - 初始化
  
  /// 初始化资产应用服务实现
  /// - Parameters:
  ///   - getUserAssetsUseCase: 获取用户资产用例
  ///   - deleteAssetUseCase: 删除资产用例
  ///   - searchAssetsUseCase: 搜索资产用例
  ///   - observeUserAssetsUseCase: 观察用户资产用例
  ///   - uploadFileWithAssetUseCase: 上传文件并创建资产用例
  ///   - uploadImageWithAssetUseCase: 上传图片并创建资产用例
  init(
    getUserAssetsUseCase: GetUserAssetsUseCase,
    deleteAssetUseCase: DeleteAssetUseCase,
    searchAssetsUseCase: SearchAssetsUseCase,
    observeUserAssetsUseCase: ObserveUserAssetsUseCase,
    uploadFileWithAssetUseCase: UploadFileWithAssetUseCase,
    uploadImageWithAssetUseCase: UploadImageWithAssetUseCase
  ) {
    self.getUserAssetsUseCase = getUserAssetsUseCase
    self.deleteAssetUseCase = deleteAssetUseCase
    self.searchAssetsUseCase = searchAssetsUseCase
    self.observeUserAssetsUseCase = observeUserAssetsUseCase
    self.uploadFileWithAssetUseCase = uploadFileWithAssetUseCase
    self.uploadImageWithAssetUseCase = uploadImageWithAssetUseCase
  }
  
  // MARK: - 资产管理功能
  
  func getUserAssets(query: AssetListQuery) async throws -> UserAssetsResult {
    return try await getUserAssetsUseCase.execute(query: query)
  }
  
  func deleteAsset(id: String) async throws -> AssetDeleteResponse {
    return try await deleteAssetUseCase.execute(id: id)
  }
  
  func searchAssets(searchTerm: String, type: String?, limit: Int) async throws -> [UserAsset] {
    return try await searchAssetsUseCase.execute(searchTerm: searchTerm, type: type, limit: limit)
  }
  
  func getAssetsByTags(tags: [String], limit: Int) async throws -> [UserAsset] {
    return try await searchAssetsUseCase.executeByTags(tags: tags, limit: limit)
  }
  
  func getAssetsByType(type: String, limit: Int) async throws -> [UserAsset] {
    return try await searchAssetsUseCase.executeByType(type: type, limit: limit)
  }
  
  // MARK: - 实时观察功能
  
  func observeUserAssets(query: AssetListQuery) -> AnyPublisher<UserAssetsResult, Error> {
    return observeUserAssetsUseCase.execute(query: query)
  }
  
  // MARK: - 资产上传功能
  
  func uploadFileWithAsset(
    data: Data,
    mimeType: String,
    fileName: String,
    prefix: String?,
    tags: [String]?,
    description: String?,
    metadata: [String: AnyCodable]?,
    isPublic: Bool?
  ) async throws -> UserAsset {
    return try await uploadFileWithAssetUseCase.execute(
      data: data,
      mimeType: mimeType,
      fileName: fileName,
      prefix: prefix,
      tags: tags,
      description: description,
      metadata: metadata,
      isPublic: isPublic
    )
  }
  
  func uploadImageWithAsset(
    image: UIImage,
    fileName: String,
    quality: CGFloat,
    prefix: String?,
    tags: [String]?,
    description: String?,
    metadata: [String: AnyCodable]?,
    isPublic: Bool?
  ) async throws -> UserAsset {
    return try await uploadImageWithAssetUseCase.execute(
      image: image,
      fileName: fileName,
      quality: quality,
      prefix: prefix,
      tags: tags,
      description: description,
      metadata: metadata,
      isPublic: isPublic
    )
  }
  
  func validateFile(data: Data, mimeType: String) throws {
    // 注意：这里需要访问验证服务，但当前架构中验证服务在用例内部
    // 为了保持接口一致性，我们可以创建一个临时的验证服务实例
    // 或者在未来的重构中将验证服务提升到模块级别
    let defaultConfig = FileUploadConfiguration.default
    let validationService = AssetValidationService(configuration: defaultConfig)
    try validationService.validateFile(data: data, mimeType: mimeType)
  }
}
