//
//  AssetModule.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/7.
//

import Foundation

// MARK: - Asset Module

/// 资产模块
/// 统一管理资产相关功能，包括资产管理和上传
public final class AssetModule {
  
  // MARK: - 公开服务
  
  /// 资产应用服务
  public let assetService: AssetApplicationService
  
  // MARK: - 内部依赖
  
  private let dependencies: AssetModuleDependencies
  
  // MARK: - 初始化
  
  /// 初始化资产模块
  /// - Parameter dependencies: 模块依赖
  public init(dependencies: AssetModuleDependencies) {
    self.dependencies = dependencies
    
    // 创建用例
    let getUserAssetsUseCase = DefaultGetUserAssetsUseCase(
      assetRepository: dependencies.assetRepository,
      userStateManager: dependencies.userStateManager
    )
    
    let deleteAssetUseCase = DefaultDeleteAssetUseCase(
      assetRepository: dependencies.assetRepository,
      userStateManager: dependencies.userStateManager
    )
    
    let searchAssetsUseCase = DefaultSearchAssetsUseCase(
      assetRepository: dependencies.assetRepository,
      userStateManager: dependencies.userStateManager
    )
    
    let observeUserAssetsUseCase = DefaultObserveUserAssetsUseCase(
      assetRepository: dependencies.assetRepository,
      userStateManager: dependencies.userStateManager
    )
    
    let uploadFileWithAssetUseCase = DefaultUploadFileWithAssetUseCase(
      apiService: dependencies.apiService,
      validationService: AssetValidationService(configuration: dependencies.uploadConfiguration)
    )
    
    let uploadImageWithAssetUseCase = DefaultUploadImageWithAssetUseCase(
      apiService: dependencies.apiService,
      validationService: AssetValidationService(configuration: dependencies.uploadConfiguration)
    )
    
    // 创建应用服务实现
    let assetServiceImpl = AssetApplicationServiceImpl(
      getUserAssetsUseCase: getUserAssetsUseCase,
      deleteAssetUseCase: deleteAssetUseCase,
      searchAssetsUseCase: searchAssetsUseCase,
      observeUserAssetsUseCase: observeUserAssetsUseCase,
      uploadFileWithAssetUseCase: uploadFileWithAssetUseCase,
      uploadImageWithAssetUseCase: uploadImageWithAssetUseCase
    )
    
    self.assetService = assetServiceImpl
  }
}
