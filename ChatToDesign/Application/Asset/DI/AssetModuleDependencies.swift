//
//  AssetModuleDependencies.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/7.
//

import Foundation

// MARK: - Asset Module Dependencies

/// 资产模块依赖
public struct AssetModuleDependencies {
  
  // MARK: - 仓储
  
  /// 资产仓储
  public let assetRepository: AssetRepository
  
  // MARK: - 服务
  
  /// API服务
  public let apiService: APIService
  
  /// 用户状态管理器
  public let userStateManager: UserStateManager
  
  // MARK: - 配置
  
  /// 上传配置
  public let uploadConfiguration: FileUploadConfiguration
  
  // MARK: - 基础设施
  
  /// 日志服务
  public let loggerService: LoggerService
  
  /// 分析服务
  public let analyticsService: AnalyticsService
  
  // MARK: - 初始化
  
  /// 初始化资产模块依赖
  /// - Parameters:
  ///   - assetRepository: 资产仓储
  ///   - apiService: API服务
  ///   - userStateManager: 用户状态管理器
  ///   - uploadConfiguration: 上传配置
  ///   - loggerService: 日志服务
  ///   - analyticsService: 分析服务
  public init(
    assetRepository: AssetRepository,
    apiService: APIService,
    userStateManager: UserStateManager,
    uploadConfiguration: FileUploadConfiguration,
    loggerService: LoggerService,
    analyticsService: AnalyticsService
  ) {
    self.assetRepository = assetRepository
    self.apiService = apiService
    self.userStateManager = userStateManager
    self.uploadConfiguration = uploadConfiguration
    self.loggerService = loggerService
    self.analyticsService = analyticsService
  }
}
