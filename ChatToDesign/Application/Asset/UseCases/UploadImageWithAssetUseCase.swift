//
//  UploadImageWithAssetUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/7.
//

import Foundation
import UIKit

// MARK: - Upload Image With Asset Use Case Protocol

/// 上传图片并创建资产用例协议
public protocol UploadImageWithAssetUseCase {
  /// 执行上传图片并创建资产
  /// - Parameters:
  ///   - image: UIImage对象
  ///   - fileName: 文件名
  ///   - quality: 压缩质量 (0.0-1.0)
  ///   - prefix: 文件路径前缀，可选
  ///   - tags: 标签，可选
  ///   - description: 描述，可选
  ///   - metadata: 元数据，可选
  ///   - isPublic: 是否公开，可选
  /// - Returns: 用户资产
  func execute(
    image: UIImage,
    fileName: String,
    quality: CGFloat,
    prefix: String?,
    tags: [String]?,
    description: String?,
    metadata: [String: AnyCodable]?,
    isPublic: Bool?
  ) async throws -> UserAsset
}

// MARK: - Default Implementation

/// 上传图片并创建资产用例默认实现
final class DefaultUploadImageWithAssetUseCase: UploadImageWithAssetUseCase {

  // MARK: - 依赖

  private let apiService: APIService
  private let validationService: AssetValidationService

  // MARK: - 初始化

  /// 初始化上传图片并创建资产用例
  /// - Parameters:
  ///   - apiService: API服务
  ///   - validationService: 验证服务
  init(
    apiService: APIService,
    validationService: AssetValidationService
  ) {
    self.apiService = apiService
    self.validationService = validationService
  }

  // MARK: - Use Case Implementation

  func execute(
    image: UIImage,
    fileName: String,
    quality: CGFloat = 0.8,
    prefix: String?,
    tags: [String]?,
    description: String?,
    metadata: [String: AnyCodable]?,
    isPublic: Bool?
  ) async throws -> UserAsset {

    do {
      // 1. 转换图片为JPEG数据
      guard let imageData = image.jpegData(compressionQuality: quality) else {
        Logger.error("Failed to convert image to JPEG data for \(fileName)")
        throw AssetError.validationFailed("无法转换图片为JPEG格式")
      }

      Logger.debug(
        "Successfully converted image to JPEG data for \(fileName), size: \(imageData.count) bytes")

      // 2. 验证文件
      try validationService.validateFile(data: imageData, mimeType: "image/jpeg")
      Logger.debug("Image validation passed for \(fileName)")

      // 3. 准备上传请求
      let uploadPrefix = prefix ?? validationService.configuration.defaultPrefix
      let request = UploadWithAssetRequest.from(
        data: imageData,
        mimeType: "image/jpeg",
        prefix: uploadPrefix,
        fileName: fileName,
        sourceType: .userUpload,
        sourceTaskId: nil,
        generationPrompt: nil,
        tags: tags,
        description: description,
        metadata: metadata,
        status: nil,
        isPublic: isPublic
      )

      // 4. 执行上传
      Logger.info("Starting image upload for \(fileName)")
      let response = try await apiService.uploadFileWithAsset(request: request)

      // 5. 记录成功日志
      Logger.info("Successfully uploaded image \(fileName) with asset ID \(response.id)")

      return response

    } catch let error as FileUploadError {
      // 6. 转换文件上传错误为应用错误
      Logger.error("File upload error for image \(fileName): \(error.localizedDescription)")
      throw AssetError.uploadFailed(error)

    } catch let error as APIServiceError {
      // 7. 转换API错误为应用错误
      Logger.error("API error when uploading image \(fileName): \(error.localizedDescription)")
      throw AssetError.networkError(error)

    } catch let error as AssetError {
      // 8. 重新抛出资产错误
      throw error

    } catch {
      // 9. 处理其他未知错误
      Logger.error("Unknown error when uploading image \(fileName): \(error.localizedDescription)")
      throw AssetError.unknown(error)
    }
  }
}
