//
//  SearchAssetsUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/7.
//

import Foundation

// MARK: - Search Assets Use Case Protocol

/// 搜索资产用例协议
public protocol SearchAssetsUseCase {
  /// 执行搜索资产
  /// - Parameters:
  ///   - searchTerm: 搜索关键词
  ///   - type: MIME类型过滤，可选
  ///   - limit: 结果数量限制
  /// - Returns: 匹配的资产列表
  func execute(searchTerm: String, type: String?, limit: Int) async throws -> [UserAsset]

  /// 按标签获取资产
  /// - Parameters:
  ///   - tags: 标签列表
  ///   - limit: 结果数量限制
  /// - Returns: 匹配的资产列表
  func executeByTags(tags: [String], limit: Int) async throws -> [UserAsset]

  /// 按类型获取资产
  /// - Parameters:
  ///   - type: MIME类型
  ///   - limit: 结果数量限制
  /// - Returns: 匹配的资产列表
  func executeByType(type: String, limit: Int) async throws -> [UserAsset]
}

// MARK: - Default Implementation

/// 搜索资产用例默认实现
final class DefaultSearchAssetsUseCase: SearchAssetsUseCase {

  // MARK: - 依赖

  private let assetRepository: AssetRepository
  private let userStateManager: UserStateManager

  // MARK: - 初始化

  /// 初始化搜索资产用例
  /// - Parameters:
  ///   - assetRepository: 资产仓储
  ///   - userStateManager: 用户状态管理器
  init(
    assetRepository: AssetRepository,
    userStateManager: UserStateManager
  ) {
    self.assetRepository = assetRepository
    self.userStateManager = userStateManager
  }

  // MARK: - Use Case Implementation

  func execute(searchTerm: String, type: String?, limit: Int) async throws -> [UserAsset] {
    // 1. 验证用户认证状态
    guard let currentUser = userStateManager.currentUser else {
      Logger.error("User not authenticated when trying to search assets")
      throw AssetError.userNotAuthenticated
    }

    do {
      // 2. 执行搜索
      let assets = try await assetRepository.searchAssets(
        userId: currentUser.id,
        searchTerm: searchTerm,
        type: type,
        limit: limit
      )

      // 3. 记录成功日志
      Logger.debug(
        "Successfully searched assets for user \(currentUser.id), found \(assets.count) results")

      return assets

    } catch let error as AssetRepositoryError {
      // 4. 转换仓储错误为应用错误
      Logger.error("Asset repository error when searching assets: \(error.localizedDescription)")
      throw mapRepositoryError(error)

    } catch {
      // 5. 处理其他未知错误
      Logger.error("Unknown error when searching assets: \(error.localizedDescription)")
      throw AssetError.unknown(error)
    }
  }

  func executeByTags(tags: [String], limit: Int) async throws -> [UserAsset] {
    // 1. 验证用户认证状态
    guard let currentUser = userStateManager.currentUser else {
      Logger.error("User not authenticated when trying to get assets by tags")
      throw AssetError.userNotAuthenticated
    }

    do {
      // 2. 按标签获取资产
      let assets = try await assetRepository.getAssetsByTags(
        userId: currentUser.id,
        tags: tags,
        limit: limit
      )

      // 3. 记录成功日志
      Logger.debug(
        "Successfully got assets by tags for user \(currentUser.id), found \(assets.count) results")

      return assets

    } catch let error as AssetRepositoryError {
      // 4. 转换仓储错误为应用错误
      Logger.error(
        "Asset repository error when getting assets by tags: \(error.localizedDescription)")
      throw mapRepositoryError(error)

    } catch {
      // 5. 处理其他未知错误
      Logger.error("Unknown error when getting assets by tags: \(error.localizedDescription)")
      throw AssetError.unknown(error)
    }
  }

  func executeByType(type: String, limit: Int) async throws -> [UserAsset] {
    // 1. 验证用户认证状态
    guard let currentUser = userStateManager.currentUser else {
      Logger.error("User not authenticated when trying to get assets by type")
      throw AssetError.userNotAuthenticated
    }

    do {
      // 2. 按类型获取资产
      let assets = try await assetRepository.getAssetsByType(
        userId: currentUser.id,
        type: type,
        limit: limit
      )

      // 3. 记录成功日志
      Logger.debug(
        "Successfully got assets by type for user \(currentUser.id), found \(assets.count) results")

      return assets

    } catch let error as AssetRepositoryError {
      // 4. 转换仓储错误为应用错误
      Logger.error(
        "Asset repository error when getting assets by type: \(error.localizedDescription)")
      throw mapRepositoryError(error)

    } catch {
      // 5. 处理其他未知错误
      Logger.error("Unknown error when getting assets by type: \(error.localizedDescription)")
      throw AssetError.unknown(error)
    }
  }

  // MARK: - Private Methods

  /// 映射仓储错误为应用错误
  /// - Parameter error: 仓储错误
  /// - Returns: 应用错误
  private func mapRepositoryError(_ error: AssetRepositoryError) -> AssetError {
    switch error {
    case .userNotAuthenticated:
      return .userNotAuthenticated
    case .assetNotFound:
      return .assetNotFound
    case .accessDenied:
      return .accessDenied
    case .databaseError(let dbError):
      return .networkError(dbError)
    case .unknown(let unknownError):
      return .unknown(unknownError)
    }
  }
}
