//
//  DeleteAssetUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/7.
//

import Foundation

// MARK: - Delete Asset Use Case Protocol

/// 删除资产用例协议
public protocol DeleteAssetUseCase {
  /// 执行删除资产
  /// - Parameter id: 资产ID
  /// - Returns: 删除响应
  func execute(id: String) async throws -> AssetDeleteResponse
}

// MARK: - Default Implementation

/// 删除资产用例默认实现
final class DefaultDeleteAssetUseCase: DeleteAssetUseCase {
  
  // MARK: - 依赖
  
  private let assetRepository: AssetRepository
  private let userStateManager: UserStateManager
  
  // MARK: - 初始化
  
  /// 初始化删除资产用例
  /// - Parameters:
  ///   - assetRepository: 资产仓储
  ///   - userStateManager: 用户状态管理器
  init(
    assetRepository: AssetRepository,
    userStateManager: UserStateManager
  ) {
    self.assetRepository = assetRepository
    self.userStateManager = userStateManager
  }
  
  // MARK: - Use Case Implementation
  
  func execute(id: String) async throws -> AssetDeleteResponse {
    // 1. 验证用户认证状态
    guard let currentUser = userStateManager.currentUser else {
      Logger.error("User not authenticated when trying to delete asset \(id)")
      throw AssetError.userNotAuthenticated
    }
    
    do {
      // 2. 执行删除
      let response = try await assetRepository.deleteAsset(id: id, userId: currentUser.id)
      
      // 3. 记录成功日志
      Logger.info("Successfully deleted asset \(id) for user \(currentUser.id)")
      
      return response
      
    } catch let error as AssetRepositoryError {
      // 4. 转换仓储错误为应用错误
      Logger.error("Asset repository error when deleting asset \(id): \(error.localizedDescription)")
      throw mapRepositoryError(error)
      
    } catch {
      // 5. 处理其他未知错误
      Logger.error("Unknown error when deleting asset \(id): \(error.localizedDescription)")
      throw AssetError.unknown(error)
    }
  }
  
  // MARK: - Private Methods
  
  /// 映射仓储错误为应用错误
  /// - Parameter error: 仓储错误
  /// - Returns: 应用错误
  private func mapRepositoryError(_ error: AssetRepositoryError) -> AssetError {
    switch error {
    case .userNotAuthenticated:
      return .userNotAuthenticated
    case .assetNotFound:
      return .assetNotFound
    case .accessDenied:
      return .accessDenied
    case .databaseError(let dbError):
      return .networkError(dbError)
    case .unknown(let unknownError):
      return .unknown(unknownError)
    }
  }
}
