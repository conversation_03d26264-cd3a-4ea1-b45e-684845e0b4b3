//
//  UploadFileWithAssetUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/7.
//

import Foundation

// MARK: - Upload File With Asset Use Case Protocol

/// 上传文件并创建资产用例协议
public protocol UploadFileWithAssetUseCase {
  /// 执行上传文件并创建资产
  /// - Parameters:
  ///   - data: 文件数据
  ///   - mimeType: MIME类型
  ///   - fileName: 文件名
  ///   - prefix: 文件路径前缀，可选
  ///   - tags: 标签，可选
  ///   - description: 描述，可选
  ///   - metadata: 元数据，可选
  ///   - isPublic: 是否公开，可选
  /// - Returns: 用户资产
  func execute(
    data: Data,
    mimeType: String,
    fileName: String,
    prefix: String?,
    tags: [String]?,
    description: String?,
    metadata: [String: AnyCodable]?,
    isPublic: Bool?
  ) async throws -> UserAsset
}

// MARK: - Default Implementation

/// 上传文件并创建资产用例默认实现
final class DefaultUploadFileWithAssetUseCase: UploadFileWithAssetUseCase {

  // MARK: - 依赖

  private let apiService: APIService
  private let validationService: AssetValidationService

  // MARK: - 初始化

  /// 初始化上传文件并创建资产用例
  /// - Parameters:
  ///   - apiService: API服务
  ///   - validationService: 验证服务
  init(
    apiService: APIService,
    validationService: AssetValidationService
  ) {
    self.apiService = apiService
    self.validationService = validationService
  }

  // MARK: - Use Case Implementation

  func execute(
    data: Data,
    mimeType: String,
    fileName: String,
    prefix: String?,
    tags: [String]?,
    description: String?,
    metadata: [String: AnyCodable]?,
    isPublic: Bool?
  ) async throws -> UserAsset {

    do {
      // 1. 验证文件
      try validationService.validateFile(data: data, mimeType: mimeType)
      Logger.debug("File validation passed for \(fileName)")

      // 2. 准备上传请求
      let uploadPrefix = prefix ?? validationService.configuration.defaultPrefix
      let request = UploadWithAssetRequest.from(
        data: data,
        mimeType: mimeType,
        prefix: uploadPrefix,
        fileName: fileName,
        sourceType: .userUpload,
        sourceTaskId: nil,
        generationPrompt: nil,
        tags: tags,
        description: description,
        metadata: metadata,
        status: nil,
        isPublic: isPublic
      )

      // 3. 执行上传
      Logger.info("Starting file upload for \(fileName)")
      let response = try await apiService.uploadFileWithAsset(request: request)

      // 4. 记录成功日志
      Logger.info("Successfully uploaded file \(fileName) with asset ID \(response.id)")

      return response

    } catch let error as FileUploadError {
      // 5. 转换文件上传错误为应用错误
      Logger.error("File upload error for \(fileName): \(error.localizedDescription)")
      throw AssetError.uploadFailed(error)

    } catch let error as APIServiceError {
      // 6. 转换API错误为应用错误
      Logger.error("API error when uploading \(fileName): \(error.localizedDescription)")
      throw AssetError.networkError(error)

    } catch {
      // 7. 处理其他未知错误
      Logger.error("Unknown error when uploading \(fileName): \(error.localizedDescription)")
      throw AssetError.unknown(error)
    }
  }
}
