//
//  ObserveUserAssetsUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/7.
//

import Combine
import Foundation

// MARK: - Observe User Assets Use Case Protocol

/// 观察用户资产用例协议
public protocol ObserveUserAssetsUseCase {
  /// 执行观察用户资产变化
  /// - Parameter query: 查询参数
  /// - Returns: 资产列表变化的发布者
  func execute(query: AssetListQuery) -> AnyPublisher<UserAssetsResult, Error>
}

// MARK: - Default Implementation

/// 观察用户资产用例默认实现
final class DefaultObserveUserAssetsUseCase: ObserveUserAssetsUseCase {

  // MARK: - 依赖

  private let assetRepository: AssetRepository
  private let userStateManager: UserStateManager

  // MARK: - 初始化

  /// 初始化观察用户资产用例
  /// - Parameters:
  ///   - assetRepository: 资产仓储
  ///   - userStateManager: 用户状态管理器
  init(
    assetRepository: AssetRepository,
    userStateManager: UserStateManager
  ) {
    self.assetRepository = assetRepository
    self.userStateManager = userStateManager
  }

  // MARK: - Use Case Implementation

  func execute(query: AssetListQuery) -> AnyPublisher<UserAssetsResult, Error> {
    // 1. 验证用户认证状态
    guard let currentUser = userStateManager.currentUser else {
      Logger.error("User not authenticated when trying to observe assets")
      return Fail(error: AssetError.userNotAuthenticated)
        .eraseToAnyPublisher()
    }

    // 2. 创建观察发布者
    return assetRepository.observeUserAssets(userId: currentUser.id, query: query)
      .handleEvents(
        receiveSubscription: { _ in
          Logger.debug("Started observing assets for user \(currentUser.id)")
        },
        receiveOutput: { response in
          Logger.debug(
            "Received asset update for user \(currentUser.id): \(response.assets.count) assets")
        },
        receiveCompletion: { completion in
          switch completion {
          case .finished:
            Logger.debug("Asset observation completed for user \(currentUser.id)")
          case .failure(let error):
            Logger.error(
              "Asset observation failed for user \(currentUser.id): \(error.localizedDescription)")
          }
        },
        receiveCancel: {
          Logger.debug("Asset observation cancelled for user \(currentUser.id)")
        }
      )
      .mapError { error in
        // 3. 转换仓储错误为应用错误
        if let repositoryError = error as? AssetRepositoryError {
          return self.mapRepositoryError(repositoryError)
        } else {
          return AssetError.unknown(error)
        }
      }
      .eraseToAnyPublisher()
  }

  // MARK: - Private Methods

  /// 映射仓储错误为应用错误
  /// - Parameter error: 仓储错误
  /// - Returns: 应用错误
  private func mapRepositoryError(_ error: AssetRepositoryError) -> AssetError {
    switch error {
    case .userNotAuthenticated:
      return .userNotAuthenticated
    case .assetNotFound:
      return .assetNotFound
    case .accessDenied:
      return .accessDenied
    case .databaseError(let dbError):
      return .networkError(dbError)
    case .unknown(let unknownError):
      return .unknown(unknownError)
    }
  }
}
