//
//  GetUserAssetsUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/7.
//

import Foundation

// MARK: - Get User Assets Use Case Protocol

/// 获取用户资产用例协议
public protocol GetUserAssetsUseCase {
  /// 执行获取用户资产
  /// - Parameter query: 查询参数
  /// - Returns: 用户资产查询结果
  func execute(query: AssetListQuery) async throws -> UserAssetsResult
}

// MARK: - Default Implementation

/// 获取用户资产用例默认实现
final class DefaultGetUserAssetsUseCase: GetUserAssetsUseCase {

  // MARK: - 依赖

  private let assetRepository: AssetRepository
  private let userStateManager: UserStateManager

  // MARK: - 初始化

  /// 初始化获取用户资产用例
  /// - Parameters:
  ///   - assetRepository: 资产仓储
  ///   - userStateManager: 用户状态管理器
  init(
    assetRepository: AssetRepository,
    userStateManager: UserStateManager
  ) {
    self.assetRepository = assetRepository
    self.userStateManager = userStateManager
  }

  // MARK: - Use Case Implementation

  func execute(query: AssetListQuery) async throws -> UserAssetsResult {
    // 1. 验证用户认证状态
    guard let currentUser = userStateManager.currentUser else {
      Logger.error("User not authenticated when trying to get assets")
      throw AssetError.userNotAuthenticated
    }

    do {
      // 2. 执行查询
      let response = try await assetRepository.getUserAssets(userId: currentUser.id, query: query)

      // 3. 记录成功日志
      Logger.debug(
        "Successfully retrieved \(response.assets.count) assets for user \(currentUser.id), page \(query.page)"
      )

      return response

    } catch let error as AssetRepositoryError {
      // 4. 转换仓储错误为应用错误
      Logger.error("Asset repository error: \(error.localizedDescription)")
      throw mapRepositoryError(error)

    } catch {
      // 5. 处理其他未知错误
      Logger.error("Unknown error when getting user assets: \(error.localizedDescription)")
      throw AssetError.unknown(error)
    }
  }

  // MARK: - Private Methods

  /// 映射仓储错误为应用错误
  /// - Parameter error: 仓储错误
  /// - Returns: 应用错误
  private func mapRepositoryError(_ error: AssetRepositoryError) -> AssetError {
    switch error {
    case .userNotAuthenticated:
      return .userNotAuthenticated
    case .assetNotFound:
      return .assetNotFound
    case .accessDenied:
      return .accessDenied
    case .databaseError(let dbError):
      return .networkError(dbError)
    case .unknown(let unknownError):
      return .unknown(unknownError)
    }
  }
}
