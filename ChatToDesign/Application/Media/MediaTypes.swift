//
//  MediaTypes.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import Foundation

/// Media type
public enum MediaType: String, Codable {
 /// Image
 case image
 /// Audio
 case audio
 /// Video
 case video
 /// Other
 case other
}

/// 媒体错误类型
public enum MediaError: LocalizedError {
    case invalidURL
    case downloadFailed(String)
    case photoLibraryPermissionDenied
    case photoLibrarySaveFailed(String)
    case unknown(Error)
    
    public var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid media URL"
        case .downloadFailed(let message):
            return "Download failed: \(message)"
        case .photoLibraryPermissionDenied:
            return "Photo library access denied"
        case .photoLibrarySaveFailed(let message):
            return "Failed to save: \(message)"
        case .unknown(let error):
            return error.localizedDescription
        }
    }
}