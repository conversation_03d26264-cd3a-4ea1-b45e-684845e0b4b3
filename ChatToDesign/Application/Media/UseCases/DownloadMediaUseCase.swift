//
//  DownloadMediaUseCase.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import Foundation

/// 下载媒体的业务逻辑
final class DownloadMediaUseCase {
    
    /// 执行下载媒体
    func execute(from url: URL) async -> Result<Data, MediaError> {
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            return .success(data)
        } catch {
            return .failure(.downloadFailed(error.localizedDescription))
        }
    }
}