//
//  SaveMediaToPhotosUseCase.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import Foundation
import Photos
import Kingfisher

/// 保存媒体到相册的业务逻辑
final class SaveMediaToPhotosUseCase {
    
    // MARK: - Dependencies
    
    private let photoLibraryHelper: PhotoLibraryHelper
    private let downloadMediaUseCase: DownloadMediaUseCase
    
    // MARK: - Initialization
    
    init(
        photoLibraryHelper: PhotoLibraryHelper = PhotoLibraryHelper(),
        downloadMediaUseCase: DownloadMediaUseCase = DownloadMediaUseCase()
    ) {
        self.photoLibraryHelper = photoLibraryHelper
        self.downloadMediaUseCase = downloadMediaUseCase
    }
    
    // MARK: - Public Methods
    
    /// 执行保存媒体到相册（从 URL）
    func execute(from url: URL, mediaType: MediaType) async -> Result<Void, MediaError> {
        do {
            // 1. 检查相册权限
            let hasPermission = await photoLibraryHelper.requestPermission()
            guard hasPermission else {
                return .failure(.photoLibraryPermissionDenied)
            }
            
            // 2. 获取媒体数据
            let mediaData = try await getMediaData(from: url, mediaType: mediaType)
            
            // 3. 保存到相册
            try await photoLibraryHelper.saveToPhotos(data: mediaData, mediaType: mediaType)
            
            // 4. 记录分析事件
            Logger.info("Media saved to photos from URL: \(url)")
            
            return .success(())
            
        } catch let error as MediaError {
            return .failure(error)
        } catch {
            return .failure(.unknown(error))
        }
    }
    
    /// 执行保存媒体到相册（从数据）
    func execute(data: Data, mediaType: MediaType) async -> Result<Void, MediaError> {
        do {
            // 1. 检查相册权限
            let hasPermission = await photoLibraryHelper.requestPermission()
            guard hasPermission else {
                return .failure(.photoLibraryPermissionDenied)
            }
            
            // 2. 保存到相册
            try await photoLibraryHelper.saveToPhotos(data: data, mediaType: mediaType)
            
            // 3. 记录分析事件
            Logger.info("Media saved to photos from data")
            
            return .success(())
            
        } catch let error as MediaError {
            return .failure(error)
        } catch {
            return .failure(.unknown(error))
        }
    }
    
    // MARK: - Private Methods
    
    private func getMediaData(from url: URL, mediaType: MediaType) async throws -> Data {
        // 优先使用缓存
        if mediaType == .video {
            if let cachedURL = await VideoCacheManager.shared.getCachedVideoURL(for: url) {
                Logger.info("Using cached video data for: \(url)")
                return try Data(contentsOf: cachedURL)
            }
        }
        
        if mediaType == .image {
            // Check Kingfisher cache
            if let cachedImage = ImageCache.default.retrieveImageInMemoryCache(forKey: url.absoluteString) {
                Logger.info("Using cached image data for: \(url)")
                if let data = cachedImage.pngData() {
                    return data
                }
            }
            
            // Check disk cache
            if let cachedImage = try? await ImageCache.default.retrieveImageInDiskCache(forKey: url.absoluteString) {
                Logger.info("Using cached image from disk for: \(url)")
                if let data = cachedImage.pngData() {   
                    return data
                }
            }
        }
        
        // 无缓存：下载
        Logger.info("Downloading media from: \(url)")
        let result = await downloadMediaUseCase.execute(from: url)
        
        switch result {
        case .success(let data):
            return data
        case .failure(let error):
            throw error
        }
    }
}