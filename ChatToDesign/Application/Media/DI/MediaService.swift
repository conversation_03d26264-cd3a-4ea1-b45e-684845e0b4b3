//
//  MediaService.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import Foundation
import Kingfisher

/// 媒体处理服务协议 - 通用的媒体处理接口
public protocol MediaService {
    /// 保存媒体到相册（从 URL）
    func saveToPhotos(from url: URL, mediaType: MediaType) async -> Result<Void, MediaError>
    
    /// 保存媒体到相册（从数据）
    func saveToPhotos(data: Data, mediaType: MediaType) async -> Result<Void, MediaError>
    
    /// 下载媒体
    func downloadMedia(from url: URL) async -> Result<Data, MediaError>
    
    /// 检查媒体是否已缓存
    func isCached(url: URL, mediaType: MediaType) -> Bool
}

/// 媒体服务实现
final class MediaServiceImpl: MediaService {
    
    private let saveMediaToPhotosUseCase: SaveMediaToPhotosUseCase
    private let downloadMediaUseCase: DownloadMediaUseCase
    
    init(
        saveMediaToPhotosUseCase: SaveMediaToPhotosUseCase,
        downloadMediaUseCase: DownloadMediaUseCase
    ) {
        self.saveMediaToPhotosUseCase = saveMediaToPhotosUseCase
        self.downloadMediaUseCase = downloadMediaUseCase
    }
    
    func saveToPhotos(from url: URL, mediaType: MediaType) async -> Result<Void, MediaError> {
        return await saveMediaToPhotosUseCase.execute(from: url, mediaType: mediaType)
    }
    
    func saveToPhotos(data: Data, mediaType: MediaType) async -> Result<Void, MediaError> {
        return await saveMediaToPhotosUseCase.execute(data: data, mediaType: mediaType)
    }
    
    func downloadMedia(from url: URL) async -> Result<Data, MediaError> {
        return await downloadMediaUseCase.execute(from: url)
    }
    
    func isCached(url: URL, mediaType: MediaType) -> Bool {
        switch mediaType {
        case .video:
            // Check if video is cached (we need to use async method, but this is sync)
            // For now, return false - this could be improved with a synchronous cache check
            return false
        case .image:
            return ImageCache.default.isCached(forKey: url.absoluteString)
        default:
            return false
        }
    }
}