//
//  MediaModule.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import Foundation

/// 媒体模块依赖容器
public final class MediaModule {
    
    private let dependencies: ModuleDependencies
    
    public init(dependencies: ModuleDependencies) {
        self.dependencies = dependencies
    }
    
    /// 媒体服务 - ViewModel 使用的接口
    public var mediaService: MediaService {
        MediaServiceImpl(
            saveMediaToPhotosUseCase: saveMediaToPhotosUseCase,
            downloadMediaUseCase: downloadMediaUseCase
        )
    }
    
    // MARK: - Private Use Cases
    
    private var saveMediaToPhotosUseCase: SaveMediaToPhotosUseCase {
        SaveMediaToPhotosUseCase(
            photoLibraryHelper: PhotoLibraryHelper(),
            downloadMediaUseCase: downloadMediaUseCase
        )
    }
    
    private var downloadMediaUseCase: DownloadMediaUseCase {
        DownloadMediaUseCase()
    }
}