// UpdateUserProfileUseCase.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Foundation

/// 更新用户资料用例
/// 职责：处理用户资料更新逻辑，确保数据一致性
public final class UpdateUserProfileUseCase {
  // MARK: - 依赖
  
  private let userRepository: UserRepository
  private let userStateManager: UserStateManager

  // MARK: - 初始化
  
  /// 初始化更新用户资料用例
  /// - Parameters:
  ///   - userRepository: 用户仓储
  ///   - userStateManager: 用户状态管理器
  public init(
    userRepository: UserRepository,
    userStateManager: UserStateManager = UserStateManager.shared
  ) {
    self.userRepository = userRepository
    self.userStateManager = userStateManager
  }

  // MARK: - 公开方法
  
  /// 更新用户资料
  /// - Parameters:
  ///   - displayName: 新的显示名称（可选）
  ///   - photoURL: 新的头像URL（可选）
  /// - Returns: 更新后的用户
  /// - Throws: 如果更新失败，抛出 UserRepositoryError
  public func execute(displayName: String? = nil, photoURL: URL? = nil) async throws -> User {
    // 1. 获取当前用户
    guard let currentUser = userStateManager.currentUser else {
      throw UserRepositoryError.userNotFound
    }
    
    // 直接使用当前用户进行更新
    return try await executeWithUser(
      currentUser.updated { user in
        // 更新显示名称
        if let displayName = displayName {
          user.displayName = displayName
        }
        
        // 更新头像URL
        if let photoURL = photoURL {
          user.photoURL = photoURL
        }
      }
    )
  }
  
  /// 使用指定用户ID更新用户资料
  /// - Parameters:
  ///   - userId: 用户ID
  ///   - displayName: 新的显示名称（可选）
  ///   - photoURL: 新的头像URL（可选）
  /// - Returns: 更新后的用户
  /// - Throws: 如果更新失败，抛出 UserRepositoryError
//    private func execute(userId: String, displayName: String? = nil, photoURL: URL? = nil) async throws -> User {
//    do {
//      // 1. 获取用户
//      let user = try await userRepository.getUser(id: userId)
//      
//      // 2. 创建更新后的用户
//      return try await executeWithUser(
//        user.updated { updatedUser in
//          // 更新显示名称
//          if let displayName = displayName {
//            updatedUser.displayName = displayName
//          }
//          
//          // 更新头像URL
//          if let photoURL = photoURL {
//            updatedUser.photoURL = photoURL
//          }
//        }
//      )
//    } catch {
//      // 转换错误类型
//      if let repoError = error as? UserRepositoryError {
//        throw repoError
//      } else {
//        throw UserRepositoryError.unknown(error)
//      }
//    }
//  }
  
  /// 使用指定用户对象更新用户资料
  /// - Parameter user: 要更新的用户对象
  /// - Returns: 更新后的用户
  /// - Throws: 如果更新失败，抛出 UserRepositoryError
  public func executeWithUser(_ user: User) async throws -> User {
    do {
      // 1. 更新用户
      try await userRepository.updateUser(user)
      
      // 2. 如果是当前用户，更新状态管理器
      if userStateManager.currentUser?.id == user.id {
        userStateManager.setCurrentUser(user)
      }
      
      return user
    } catch {
      // 转换错误类型
      if let repoError = error as? UserRepositoryError {
        throw repoError
      } else {
        throw UserRepositoryError.unknown(error)
      }
    }
  }
}
