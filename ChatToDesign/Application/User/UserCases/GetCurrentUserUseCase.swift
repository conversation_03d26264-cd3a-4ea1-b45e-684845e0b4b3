// GetCurrentUserUseCase.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Combine
import Foundation

/// 获取当前用户用例
/// 职责：协调不同数据源获取当前用户信息，处理数据同步问题
public final class GetCurrentUserUseCase {
  // MARK: - 依赖
  
  private let authService: AuthService
  private let userRepository: UserRepository
  private let userStateManager: UserStateManager

  // 存储取消令牌
  private var cancellables = Set<AnyCancellable>()

  // MARK: - 初始化
  
  /// 初始化获取当前用户用例
  /// - Parameters:
  ///   - authService: 认证服务
  ///   - userRepository: 用户仓储
  ///   - userStateManager: 用户状态管理器
  public init(
    authService: AuthService,
    userRepository: UserRepository,
    userStateManager: UserStateManager = UserStateManager.shared
  ) {
    self.authService = authService
    self.userRepository = userRepository
    self.userStateManager = userStateManager

    // 监听认证状态变化，自动更新用户状态
    setupAuthStateObserver()
  }

  // MARK: - 私有方法
  
  /// 设置认证状态观察者
  private func setupAuthStateObserver() {
    authService.authStatePublisher
      .sink { [weak self] authState in
        guard let self = self else { return }

        switch authState {
        case .authenticated(let authUser):
          // 认证状态变为已登录，异步获取用户数据
          Task {
            _ = try? await self.fetchAndUpdateUser(userId: authUser.uid)
          }
        case .unauthenticated, .error:
          // 认证状态变为未登录或错误，清除当前用户
          self.userStateManager.clearCurrentUser()
        case .authenticating:
          // 正在认证中，不做处理
          break
        }
      }
      .store(in: &cancellables)
  }
  
  /// 获取并更新用户数据
  /// - Parameter userId: 用户ID
  /// - Returns: 获取到的用户
  /// - Throws: 如果用户不存在，抛出 UserRepositoryError.userNotFound
  private func fetchAndUpdateUser(userId: String) async throws -> User {
    do {
      // 获取用户数据
      let user = try await userRepository.getUser(id: userId)
      
      // 更新用户状态
      userStateManager.setCurrentUser(user)
      return user
    } catch {
      // 将错误转换为领域错误
      if let repoError = error as? UserRepositoryError {
        throw repoError
      } else {
        throw UserRepositoryError.unknown(error)
      }
    }
  }

  // MARK: - 公开方法
  
  /// 获取当前用户信息
  /// - Returns: 当前用户，如果未登录则返回nil
  /// - Throws: 如果获取失败，抛出 UserRepositoryError
  public func execute() async throws -> User? {
    // 如果用户状态管理器中已有当前用户，直接返回
    if let currentUser = userStateManager.currentUser {
      return currentUser
    }

    // 检查认证状态
    guard case let .authenticated(authUser) = authService.authState else {
      return nil
    }

    // 获取并更新用户数据
    return try await fetchAndUpdateUser(userId: authUser.uid)
  }

  /// 提供用户状态发布者
  /// - Returns: 用户状态发布者
  public func userPublisher() -> AnyPublisher<User?, Never> {
    return userStateManager.userPublisher
  }
}
