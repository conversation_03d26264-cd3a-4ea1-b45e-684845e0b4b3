//
//  UserService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 用户资料服务接口
/// 对外暴露的用户资料功能接口
public protocol UserService {
    /// 当前用户发布者
    var currentUserPublisher: AnyPublisher<User?, Never> { get }
    
    /// 获取当前用户
    var currentUser: User? { get }
    
    /// 更新用户资料
    /// - Parameters:
    ///   - displayName: 显示名称
    ///   - photoURL: 头像URL
    func updateProfile(displayName: String?, photoURL: URL?) async throws
    
    /// 更新用户偏好设置
    /// - Parameter preferences: 偏好设置
    func updatePreferences(preferences: UserPreferences) async throws
}