// UserModule.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Foundation
import Combine



/// 用户模块
/// 提供所有用户相关功能
public final class UserModule {
    // MARK: - 公开服务
    
    /// 用户应用服务
    public let userService: UserService
    
    // MARK: - 内部依赖
    
    private let dependencies: ModuleDependencies
    
    // MARK: - 初始化
    
    /// 初始化用户模块
    /// - Parameter dependencies: 模块依赖
    public init(dependencies: ModuleDependencies) {
        self.dependencies = dependencies
        
        // 创建用例
        let updateUserProfileUseCase = UpdateUserProfileUseCase(
            userRepository: dependencies.userRepository,
            userStateManager: dependencies.userStateManager
        )

        let getCurrentUserUseCase = GetCurrentUserUseCase(
            authService: dependencies.authService,
            userRepository: dependencies.userRepository,
            userStateManager: dependencies.userStateManager
        )
        
        // 创建应用服务实现
        let userServiceImpl = UserServiceImpl(
            getCurrentUserUseCase: getCurrentUserUseCase,
            updateUserProfileUseCase: updateUserProfileUseCase,
            userStateManager: dependencies.userStateManager
        )
        
        // 模块持有协议类型引用
        self.userService = userServiceImpl
    }
}

/// 用户应用服务实现
fileprivate final class UserServiceImpl: UserService {
    // MARK: - 属性
    
    private let updateUserProfileUseCase: UpdateUserProfileUseCase
    private let getCurrentUserUseCase: GetCurrentUserUseCase
    private let userStateManager: UserStateManager
    
    /// 当前用户发布者
    public var currentUserPublisher: AnyPublisher<User?, Never> {
        return userStateManager.userPublisher
    }
    
    // MARK: - 初始化
    
    /// 初始化用户应用服务
    /// - Parameters:
    ///   - updateUserProfileUseCase: 更新用户资料用例
    ///   - userStateManager: 用户状态管理器
    init(
        getCurrentUserUseCase: GetCurrentUserUseCase,
        updateUserProfileUseCase: UpdateUserProfileUseCase,
        userStateManager: UserStateManager
    ) {
        self.getCurrentUserUseCase = getCurrentUserUseCase
        self.updateUserProfileUseCase = updateUserProfileUseCase
        self.userStateManager = userStateManager
    }
    
    // MARK: - 公开方法
    
    /// 获取当前用户
    public var currentUser: User? {
        return UserStateManager.shared.currentUser
    }
    
    /// 更新用户资料
    /// - Parameters:
    ///   - displayName: 显示名称
    ///   - photoURL: 头像URL
    public func updateProfile(displayName: String? = nil, photoURL: URL? = nil) async throws {
       let _ = try await updateUserProfileUseCase.execute(displayName: displayName, photoURL: photoURL)
    }
    
    /// 更新用户偏好设置
    /// - Parameter preferences: 偏好设置
    public func updatePreferences(preferences: UserPreferences) async throws {
        guard let user = userStateManager.currentUser else {
            throw NSError(domain: "UserService", code: 1, userInfo: [NSLocalizedDescriptionKey: "没有当前用户"])
        }
        
        var updatedUser = user
        updatedUser.preferences = preferences

        let _ = try await updateUserProfileUseCase.executeWithUser(updatedUser)
    }
}
