//
//  GenerateDesignUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/7.
//

import Foundation

/// 生成设计用例协议
public protocol GenerateDesignUseCase {
  /// 生成设计图像
  /// - Parameters:
  ///   - request: 设计生成请求
  /// - Returns: 设计生成响应
  func execute(request: DesignGenerationRequest) async throws -> DesignGenerationResponse
}

/// 默认生成设计用例实现
public final class DefaultGenerateDesignUseCase: GenerateDesignUseCase {
  private let apiService: APIService

  /// 初始化
  /// - Parameter apiService: API服务
  public init(apiService: APIService) {
    self.apiService = apiService
  }

  /// 生成设计图像
  /// - Parameters:
  ///   - request: 设计生成请求
  /// - Returns: 设计生成响应
  public func execute(request: DesignGenerationRequest) async throws
    -> DesignGenerationResponse
  {
    return try await apiService.generateDesign(request: request)
  }
}
