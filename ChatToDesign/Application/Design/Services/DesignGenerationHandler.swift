import Combine
import SwiftUI
import UIKit

@MainActor
class DesignGenerationHandler: ObservableObject {
  // MARK: - Published State

  @Published private(set) var isLoading: Bool = false
  @Published private(set) var errorMessage: String? = nil
  @Published private(set) var generatedImageUrls: [String]? = nil  // To hold the final result
  @Published private(set) var taskCreatedSuccessfully: Bool = false  // New state for task creation success

  // MARK: - Dependencies

  private let assetService: AssetApplicationService
  private let designGenerationService: DesignGenerationService
  private let userService: UserService
  private let analyticsService: AnalyticsService

  // MARK: - Task Management

  private var currentInitialTask: Task<Void, Never>? = nil
  private var currentTaskId: String? = nil
  private var taskSubscription: AnyCancellable? = nil
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Initializer

  init(
    assetService: AssetApplicationService = AppDependencyContainer.shared.assetModule.assetService,
    designGenerationService: DesignGenerationService = AppDependencyContainer.shared.designModule
      .designGenerationService,
    userService: UserService = AppDependencyContainer.shared.userModule.userService,
    analyticsService: AnalyticsService = AppDependencyContainer.shared.analyticsService
  ) {
    self.assetService = assetService
    self.designGenerationService = designGenerationService
    self.userService = userService
    self.analyticsService = analyticsService
    Logger.debug("initialized")
  }

  deinit {
    Logger.debug("Cancelling tasks and subscriptions.")
    //    cancelGeneration()  // Ensure cleanup on deinit
  }

  // MARK: - Public API

  func startGeneration(
    prompt: String, referenceImages: [UIImage], designId: String?, chatId: String?
  ) {
    // Convert UIImages to upload them and get URLs, then call the URL-based method
    guard currentInitialTask == nil && taskSubscription == nil else {
      Logger.warning("Generation or listening already in progress.")
      return
    }

    // Reset state
    self.isLoading = true
    self.errorMessage = nil
    self.generatedImageUrls = nil
    self.taskCreatedSuccessfully = false
    self.currentTaskId = nil
    taskSubscription?.cancel()  // Cancel any previous observation

    let trimmedPrompt = prompt.trimmingCharacters(in: .whitespacesAndNewlines)
    guard !referenceImages.isEmpty || !trimmedPrompt.isEmpty else {
      handleError(DesignGenerationError.noInputProvided)
      return
    }

    // Start the initial task to upload images and get the taskId
    currentInitialTask = Task {
      do {
        Logger.info("DesignGenerationHandler: Starting generation initial task...")
        // 1. Upload Reference Images if any (respecting cancellation)
        let uploadedImageUrls = try await uploadReferenceImagesIfNeeded(
          images: referenceImages, chatId: chatId)
        try Task.checkCancellation()

        // 2. Call the URL-based generation method
        try await self.startGenerationWithImageUrls(
          prompt: trimmedPrompt,
          imageUrls: uploadedImageUrls,
          designId: designId,
          chatId: chatId
        )

      } catch let error as FileUploadError {
        Logger.error(" Upload failed: \(error)")
        handleError(DesignGenerationError.uploadFailed(error.localizedDescription))
      } catch let error as DesignGenerationError {  // Catch internal generation errors
        Logger.error("Generation setup failed: \(error)")
        handleError(error)
      } catch is CancellationError {
        Logger.info("Initial task cancelled.")  // Log cancellation as info
        handleCancellation(isInitialTask: true)
      } catch {
        Logger.error(
          "An unexpected error occurred during initial task: \(error)")
        handleError(DesignGenerationError.unexpectedError(error))
      }
    }
  }

  func startGenerationWithImageUrls(
    prompt: String, imageUrls: [String], designId: String?, chatId: String?
  ) async throws {
    let trimmedPrompt = prompt.trimmingCharacters(in: .whitespacesAndNewlines)
    guard !imageUrls.isEmpty || !trimmedPrompt.isEmpty else {
      throw DesignGenerationError.noInputProvided
    }

    // Call Design Generation Service to get Task ID
    Logger.info("DesignGenerationHandler: Calling design generation service to get taskId...")
    guard let userId = userService.currentUser?.id else {
      // Handle missing user ID scenario appropriately
      Logger.error("DesignGenerationHandler: Error - User ID not found.")
      throw DesignGenerationError.unexpectedError(
        NSError(
          domain: "AuthError", code: -1,
          userInfo: [NSLocalizedDescriptionKey: "User not logged in"]))
    }

    let taskId: String = try await designGenerationService.generateDesign(
      request: DesignGenerationRequest(
        prompt: trimmedPrompt,
        imageUrls: imageUrls,
        userId: userId  // Use fetched user ID
      )
    )

    Logger.info("DesignGenerationHandler: Received taskId: \(taskId)")

    // 埋点：图片生成开始
    analyticsService.track(
      .imageGenerationStarted(
        taskId: taskId,
        userId: userId,
        prompt: trimmedPrompt.isEmpty ? nil : trimmedPrompt
      ))

    try Task.checkCancellation()

    // Mark task creation as successful
    await MainActor.run {
      self.taskCreatedSuccessfully = true
      // Start Observing the Task via Combine
      self.currentTaskId = taskId
      self.startObservingTask(taskId: taskId)
      self.currentInitialTask = nil  // Initial task completed, observation started
    }
  }

  func cancelGeneration() {
    Logger.info("DesignGenerationHandler: Attempting to cancel generation...")  // Log cancellation attempt as info
    currentInitialTask?.cancel()
    taskSubscription?.cancel()
    if isLoading {  // Only update state if it was actually running
      handleCancellation(isInitialTask: currentInitialTask != nil)
    } else {
      // Ensure cleanup even if not actively loading
      taskSubscription = nil
      currentTaskId = nil
      currentInitialTask = nil
    }
  }

  // MARK: - Private Implementation

  private func uploadReferenceImagesIfNeeded(images: [UIImage], chatId: String?) async throws
    -> [String]
  {
    guard !images.isEmpty else {
      return []
    }
    Logger.info("DesignGenerationHandler: Uploading \(images.count) reference images...")
    let uploadedImageUrls = try await uploadReferenceImages(
      images, chatId: chatId ?? "defaultChatId")  // Provide a default if nil
    Logger.info(
      "DesignGenerationHandler: Finished uploading reference images: \(uploadedImageUrls)")
    return uploadedImageUrls
  }

  private func startObservingTask(taskId: String) {
    Logger.info("DesignGenerationHandler: Starting Combine observer for task: \(taskId)")
    taskSubscription?.cancel()  // Ensure no previous subscription is active

    taskSubscription = designGenerationService.observeDesignTask(taskId: taskId)
      .receive(on: DispatchQueue.main)  // Ensure updates are on the main thread
      .sink(
        receiveCompletion: { [weak self] completion in
          guard let self = self else { return }
          Logger.info("DesignGenerationHandler: Task observer received completion: \(completion)")  // Log completion info
          // We only mark loading as false when the observation completes (success, failure, or cancelled externally)
          // Errors during observation are handled here.
          if case .failure(let error) = completion {
            // Avoid overriding specific error messages set during receiveValue
            if self.errorMessage == nil {
              self.handleError(DesignGenerationError.unexpectedError(error))
            }
          } else {
            // If completed successfully without a final state update setting isLoading=false, do it now.
            if self.isLoading {
              Logger.info(
                "DesignGenerationHandler: Observation completed successfully, setting isLoading=false."
              )
              self.isLoading = false
            }
          }
          // Clear subscription info regardless of completion type
          self.taskSubscription = nil
          self.currentTaskId = nil
        },
        receiveValue: { [weak self] task in
          guard let self = self else { return }
          Logger.info("DesignGenerationHandler: Received task update: Status - \(task.status)")  // Log status update
          guard task.taskId == self.currentTaskId else {
            Logger.warning(
              "DesignGenerationHandler: Ignoring update for non-current task (\(task.taskId)). Current: \(self.currentTaskId ?? "nil")"
            )
            return
          }
          self.handleTaskStatusUpdate(task)
        })
    // Don't store in general cancellables, manage taskSubscription lifecycle directly
  }

  private func handleTaskStatusUpdate(_ task: ImageGenerationTask) {
    // This runs on MainActor due to .receive(on: DispatchQueue.main)
    let userId = userService.currentUser?.id ?? "unknown"

    switch task.status {
    case .pending, .processing:
      self.isLoading = true  // Ensure loading is true while processing
      self.errorMessage = nil  // Clear previous errors
    case .succeeded:
      Logger.info(
        "DesignGenerationHandler: Task succeeded. Result URLs: \(task.resultImageUrls ?? [])")  // Log success
      self.generatedImageUrls = task.resultImageUrls
      self.isLoading = false
      self.errorMessage = nil

      // 埋点：图片生成成功
      let duration = calculateTaskDuration(task: task)
      analyticsService.track(
        .imageGenerationCompleted(
          taskId: task.taskId,
          userId: userId,
          success: true,
          duration: duration
        ))

    case .failed, .no_result:
      let message =
        task.errorMessage
        ?? (task.status == .no_result
          ? "Generation completed with no results." : "Generation failed: Unknown error.")
      Logger.error(
        "DesignGenerationHandler: Task ended with status \(task.status). Message: \(message)")  // Log failure/no result

      // 埋点：图片生成失败
      let duration = calculateTaskDuration(task: task)
      analyticsService.track(
        .imageGenerationCompleted(
          taskId: task.taskId,
          userId: userId,
          success: false,
          duration: duration
        ))

      // Use the specific error enum
      handleError(DesignGenerationError.serviceError(message))
    case .deleted:
      Logger.info("DesignGenerationHandler: Task was deleted")
      self.isLoading = false
      self.errorMessage = "Task was deleted"
      self.generatedImageUrls = nil

      // 停止观察
      taskSubscription?.cancel()
      taskSubscription = nil
      currentTaskId = nil

    case .unknown:
      Logger.warning("DesignGenerationHandler: Warning: Received unknown task status.")  // Log unknown status as warning
      handleError(DesignGenerationError.serviceError("Received unexpected task status."))
    }
    // If task is terminal, cancel the subscription explicitly *if* the publisher doesn't auto-complete
    // Note: Depending on the `observeDesignTask` implementation, this might be redundant if it sends a completion event.
    // if task.status == .succeeded || task.status == .failed || task.status == .no_result {
    //      taskSubscription?.cancel()
    //      taskSubscription = nil
    //      currentTaskId = nil
    // }
  }

  // Helper for uploading images concurrently
  private func uploadReferenceImages(_ images: [UIImage], chatId: String) async throws -> [String] {
    try await withThrowingTaskGroup(of: String.self) { group in
      var urls: [String] = []

      for (index, image) in images.enumerated() {
        try Task.checkCancellation()  // Check for cancellation before starting upload

        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
          Logger.warning(
            "DesignGenerationHandler: Warning: Could not convert image \(index) to JPEG data.")  // Log image conversion warning
          continue  // Skip this image
        }

        let fileName = "\(UUID().uuidString).jpg"
        let mimeType = "image/jpeg"

        Logger.info(
          "DesignGenerationHandler: Adding upload task for image \(index) with fileName: \(fileName)"
        )  // Log adding upload task
        group.addTask { [assetService] in  // Capture assetUploadService
          // Check cancellation *inside* the task as well
          try Task.checkCancellation()
          Logger.info("DesignGenerationHandler: Executing upload for \(fileName)...")  // Log executing upload

          // 设置上传前缀，使用chatId作为路径的一部分
          let prefix = "uploads/user_assets"

          // 设置标签和元数据
          let tags = ["image", "reference", "user_asset"]
          let metadata: [String: AnyCodable] = [
            "purpose": AnyCodable("reference_image"),
            "originalFileName": AnyCodable(fileName),
          ]

          // Use the captured assetService
          let assetResponse = try await assetService.uploadFileWithAsset(
            data: imageData,
            mimeType: mimeType,
            fileName: fileName,
            prefix: prefix,
            tags: tags,
            description: "Reference image for design generation",
            metadata: metadata,
            isPublic: false
          )

          Logger.info(
            "DesignGenerationHandler: Upload finished for \(fileName). URL: \(assetResponse.url)"
          )  // Log upload finished
          return assetResponse.url
        }
      }

      // Collect results
      for try await urlString in group {
        try Task.checkCancellation()  // Check for cancellation while collecting results
        urls.append(urlString)
      }
      Logger.info("DesignGenerationHandler: Collected all uploaded URLs: \(urls)")  // Log collection finished
      return urls
    }
  }

  // MARK: - Error and Cancellation Handling (Run on MainActor)

  private func handleError(_ error: DesignGenerationError) {
    // Ensure this runs on the main thread as it updates @Published properties
    Logger.error("DesignGenerationHandler: Handling error: \(error.localizedDescription)")  // Log handled error
    self.errorMessage = error.localizedDescription
    self.isLoading = false
    self.currentInitialTask?.cancel()  // Cancel initial task if it's the source of error
    self.taskSubscription?.cancel()  // Cancel observation if running
    self.currentInitialTask = nil
    self.taskSubscription = nil
    self.currentTaskId = nil
  }

  private func handleCancellation(isInitialTask: Bool) {
    // Ensure this runs on the main thread
    Logger.info("DesignGenerationHandler: Handling cancellation (initial task: \(isInitialTask)).")  // Log handled cancellation
    // Use the specific error enum for cancellation
    // Only set error message if we weren't already in a failed state
    if self.errorMessage == nil {
      self.errorMessage = DesignGenerationError.cancelled.localizedDescription
    }
    self.isLoading = false
    self.currentInitialTask = nil  // Already cancelled or completed
    self.taskSubscription = nil  // Already cancelled
    self.currentTaskId = nil
  }

  /// 计算任务持续时间
  /// - Parameter task: 图片生成任务
  /// - Returns: 任务持续时间（秒），如果无法计算则返回nil
  private func calculateTaskDuration(task: ImageGenerationTask) -> TimeInterval? {
    guard let createdAt = task.createdAt?.dateValue(),
      let updatedAt = task.updatedAt?.dateValue()
    else {
      return nil
    }
    return updatedAt.timeIntervalSince(createdAt)
  }
}
