//
//  DesignModule.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/8.
//

import Combine
import Foundation

/// 设计模块
/// 提供所有设计生成相关功能
public final class DesignModule {
  // MARK: - 公开服务

  /// 设计生成服务
  public let designGenerationService: DesignGenerationService

  // MARK: - 内部依赖

  private let dependencies: ModuleDependencies

  // MARK: - 初始化

  /// 初始化设计模块
  /// - Parameter dependencies: 模块依赖
  public init(dependencies: ModuleDependencies) {
    self.dependencies = dependencies

    // 创建用例
    let generateDesignUseCase = DefaultGenerateDesignUseCase(
      apiService: dependencies.apiService
    )

    // 创建服务
    let designGenerationServiceImpl = DefaultDesignGenerationService(
      generateDesignUseCase: generateDesignUseCase,
      imageTaskRepository: dependencies.imageTaskRepository
    )

    // 模块持有协议类型引用
    self.designGenerationService = designGenerationServiceImpl
  }
}

/// Default implementation of the `DesignGenerationService`.
/// Orchestrates the design generation process using the provided use cases and repositories.
private final class DefaultDesignGenerationService: DesignGenerationService {

  private let generateDesignUseCase: GenerateDesignUseCase
  private let imageTaskRepository: ImageTaskRepository
  // Add other dependencies if needed, e.g., a network client for downloading images

  /// Initializes the service with necessary dependencies.
  /// - Parameters:
  ///   - generateDesignUseCase: The use case responsible for initiating the design generation task.
  ///   - imageTaskRepository: The repository for observing image task updates.
  public init(
    generateDesignUseCase: GenerateDesignUseCase, imageTaskRepository: ImageTaskRepository
  ) {
    self.generateDesignUseCase = generateDesignUseCase
    self.imageTaskRepository = imageTaskRepository
  }

  /// Initiates a design generation request.
  public func generateDesign(request: DesignGenerationRequest) async throws -> String {
    // Delegate the generation logic to the use case
    let response = try await generateDesignUseCase.execute(request: request)
    return response.taskId
  }

  /// Observes the status and results of a specific design generation task.
  public func observeDesignTask(taskId: String) -> AnyPublisher<ImageGenerationTask, Error> {
    // Delegate observation to the image task repository
    return imageTaskRepository.observeImageTask(taskId: taskId)
      .mapError { error -> Error in
        // Optionally map repository-specific errors to service-level errors if needed
        // For now, just pass the error through
        return error
      }
      .eraseToAnyPublisher()
  }

  /// Downloads the design image from the given URL.
  public func downloadDesignImage(from url: String) async throws -> Data {
    guard let imageUrl = URL(string: url) else {
      throw DesignGenerationError.serviceError("Invalid image URL: \(url)")  // Or a more specific URL error
    }

    do {
      // Use URLSession or a dedicated network client for the download
      let (data, response) = try await URLSession.shared.data(from: imageUrl)

      guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
        throw DesignGenerationError.serviceError(
          "Failed to download image, status code: \((response as? HTTPURLResponse)?.statusCode ?? -1)"
        )
      }
      return data
    } catch {
      // Wrap network errors into a service error or define a specific download error
      throw DesignGenerationError.serviceError(
        "Image download failed: \(error.localizedDescription)")
    }
  }

  // Implementation for the new getTaskList method
  public func getTaskList(userId: String, limit: Int, startAfterTaskId: String?) async throws
    -> [ImageGenerationTask]
  {
    do {
      return try await imageTaskRepository.getTaskList(
        userId: userId, limit: limit, startAfterTaskId: startAfterTaskId)
    } catch let repoError as ImageTaskRepositoryError {
      // Optionally map repository errors to service-level errors
      // For example, if ImageTaskRepositoryError.taskNotFound should be DesignGenerationError.serviceError("Tasks not found")
      // For now, rethrowing a generic service error or a new specific error type might be appropriate.
      // Or, if ImageTaskRepositoryError is already suitable for the ViewModel to handle, rethrow it directly.
      // Let's create a new case in DesignGenerationError or rethrow as a generic service error.
      Logger.error(
        "ImageTaskRepository error while fetching task list: \(repoError.localizedDescription)")
      // Depending on desired error handling strategy, either:
      // 1. Rethrow a new DesignGenerationError case:
      //    throw DesignGenerationError.taskListRetrievalFailed(underlyingError: repoError)
      // 2. Rethrow a generic service error:
      //    throw DesignGenerationError.serviceError("Failed to retrieve task list: \(repoError.localizedDescription)")
      // 3. If ImageTaskRepositoryError conforms to Error and is fine to expose, just rethrow:
      throw repoError  // This requires ImageTaskRepositoryError to be public or understood by the caller.
      // For now, let's assume it is okay to rethrow, but this might need refinement.
    } catch {
      // Catch any other unexpected errors
      Logger.error("Unexpected error while fetching task list: \(error.localizedDescription)")
      throw DesignGenerationError.unexpectedError(error)
    }
  }

  /// Observes the task list for a specific user.
  public func observeTaskList(userId: String, limit: Int) -> AnyPublisher<
    [ImageGenerationTask], Error
  > {
    return imageTaskRepository.observeTaskList(userId: userId, limit: limit)
      .mapError { error -> Error in
        // Optionally map repository-specific errors to service-level errors if needed
        Logger.error("Error observing task list for user \(userId): \(error.localizedDescription)")
        // For now, just pass the error through or wrap it in a generic service error
        if let repoError = error as? ImageTaskRepositoryError {
          // You could map specific ImageTaskRepositoryError cases to DesignGenerationError cases
          // For example: return DesignGenerationError.taskListObservationFailed(underlyingError: repoError)
          return repoError  // Or rethrow the original for now
        } else {
          return DesignGenerationError.serviceError(
            "Failed to observe task list: \(error.localizedDescription)")
        }
      }
      .eraseToAnyPublisher()
  }

  /// Updates the status of a specific image generation task.
  public func updateTaskStatus(taskId: String, status: TaskStatus) async throws {
    do {
      try await imageTaskRepository.updateTaskStatus(taskId: taskId, status: status)
      Logger.info("DefaultDesignGenerationService: Task \(taskId) status updated to \(status)")
    } catch {
      Logger.error(
        "DefaultDesignGenerationService: Failed to update task \(taskId) status: \(error.localizedDescription)"
      )
      throw DesignGenerationError.serviceError(
        "Failed to update task status: \(error.localizedDescription)")
    }
  }
}
