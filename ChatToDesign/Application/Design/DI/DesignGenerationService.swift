//
//  DesignGenerationService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/7.
//

import Combine
import Foundation

public struct DesignGenerationRequest: Codable {
  public let prompt: String
  public let imageUrls: [String]
  public let userId: String
}

public struct DesignGenerationResponse: Codable {
  public let taskId: String
}

public protocol DesignGenerationService {
  /// Initiates a design generation request asynchronously.
  ///
  /// This method sends the request to the backend and immediately returns the task ID
  /// that can be used to track the generation progress.
  ///
  /// - Parameter request: Contains the prompt, reference image URLs, and user ID.
  /// - Returns: A `String` representing the unique ID (taskId) of the generation task.
  /// - Throws: `DesignGenerationError` or other network/service errors if the initial request fails.
  func generateDesign(request: DesignGenerationRequest) async throws -> String  // Return String taskId

  /// Observes the status and results of a specific design generation task.
  ///
  /// Subscribing to the returned publisher provides real-time updates on the task's state,
  /// including status changes (pending, processing, succeeded, failed) and final results (generated image URLs or error message).
  ///
  /// - Parameter taskId: The ID of the task document to observe.
  /// - Returns: A Combine `AnyPublisher` that emits `ImageGenerationTask` updates or terminates with an `Error`.
  func observeDesignTask(taskId: String) -> AnyPublisher<ImageGenerationTask, Error>  // Add observation method

  /// 根据设计URL下载设计图像
  /// - Parameter url: 设计图像URL
  /// - Returns: 图像数据
  func downloadDesignImage(from url: String) async throws -> Data

  /// Fetches a list of image generation tasks for a specific user, with optional pagination.
  /// - Parameters:
  ///   - userId: The ID of the user whose tasks are to be fetched.
  ///   - limit: The maximum number of tasks to return.
  ///   - startAfterTaskId: Optionally, the ID of the task after which to start fetching (for pagination).
  /// - Returns: An array of `ImageGenerationTask` objects.
  /// - Throws: Errors if the task list cannot be fetched.
  func getTaskList(userId: String, limit: Int, startAfterTaskId: String?) async throws
    -> [ImageGenerationTask]

  /// Observes the task list for a specific user, ordered by creation date (newest first).
  ///
  /// Subscribing to the returned publisher provides real-time updates on the user's task list.
  ///
  /// - Parameters:
  ///   - userId: The ID of the user whose tasks to observe.
  ///   - limit: The maximum number of tasks to include in each emitted list.
  /// - Returns: A Combine `AnyPublisher` that emits an array of `ImageGenerationTask` updates or terminates with an `Error`.
  func observeTaskList(userId: String, limit: Int) -> AnyPublisher<[ImageGenerationTask], Error>

  /// Updates the status of a specific image generation task.
  ///
  /// - Parameters:
  ///   - taskId: The ID of the task to update.
  ///   - status: The new status to set for the task.
  /// - Throws: Errors if the task status cannot be updated.
  func updateTaskStatus(taskId: String, status: TaskStatus) async throws
}

enum DesignGenerationError: Error, LocalizedError {
  case serviceError(String)
  case uploadFailed(String)  // Added specific upload error case
  case noInputProvided  // User didn't provide prompt or images
  case unexpectedError(Error)
  case cancelled

  var errorDescription: String? {
    switch self {
    case .serviceError(let message):
      return "Generation service error: \(message)"
    case .uploadFailed(let message):
      return "Upload failed: \(message)"
    case .noInputProvided:
      return "Please provide a prompt or reference images."
    case .unexpectedError(let error):
      return "An unexpected error occurred: \(error.localizedDescription)"
    case .cancelled:
      return "Generation Cancelled"
    }
  }
}
