//
//  SupportModule.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/20.
//

import Foundation

/// Support module for dependency injection
/// Provides all support-related services and dependencies
public final class SupportModule {
    
    // MARK: - Dependencies
    
    private let dependencies: ModuleDependencies
    
    // MARK: - Initialization
    
    /// Initialize support module with dependencies
    /// - Parameter dependencies: Module dependencies
    public init(dependencies: ModuleDependencies) {
        self.dependencies = dependencies
    }
    
    // MARK: - Services
    
    /// Feedback service for submitting user feedback
    public lazy var feedbackService: FeedbackService = {
        return DefaultFeedbackService(apiService: dependencies.apiService)
    }()
    
    /// File upload service for feedback attachments
    public lazy var uploadService: FileUploadService = {
        return DefaultFileUploadService(
            apiService: dependencies.apiService,
            configuration: FileUploadConfiguration.feedback
        )
    }()
}

// MARK: - File Upload Configuration Extension

extension FileUploadConfiguration {
    /// Configuration for feedback attachments
    static let feedback = FileUploadConfiguration(
        maxFileSize: 5 * 1024 * 1024,  // 5MB
        supportedMimeTypes: [
            "image/jpeg",
            "image/png",
            "image/heic",
            "image/heif"
        ],
        defaultPrefix: "feedback"
    )
}
