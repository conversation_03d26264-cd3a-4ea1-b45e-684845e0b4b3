// UserStateManager.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Combine
import Foundation

/// 用户状态管理器 - 作为应用内的单一数据源
/// 职责：管理内存中的用户状态，提供响应式状态发布
public final class UserStateManager {
  // MARK: - 单例
  
  /// 共享实例
  public static let shared = UserStateManager()

  // MARK: - 状态
  
  /// 当前用户状态
  @Published private(set) public var currentUser: User?

  /// 用户状态发布者
  public var userPublisher: AnyPublisher<User?, Never> {
    $currentUser.eraseToAnyPublisher()
  }

  // MARK: - 初始化
  
  private init() {}

  // MARK: - 公开方法
  
  /// 设置当前用户
  /// - Parameter user: 用户对象
  public func setCurrentUser(_ user: User?) {
    currentUser = user
  }

  /// 更新当前用户
  /// - Parameters:
  ///   - displayName: 显示名称
  ///   - photoURL: 头像URL
  ///   - bio: 个人简介
  ///   - location: 位置
  ///   - preferences: 偏好设置
  public func updateCurrentUser(
    displayName: String? = nil,
    photoURL: URL? = nil,
    bio: String? = nil,
    location: String? = nil,
    preferences: UserPreferences? = nil
  ) {
    guard let user = currentUser else { return }

    // 使用 updated 方法创建新的用户对象
    currentUser = user.updated { updatedUser in
      // 更新显示名称
      if let displayName = displayName {
        updatedUser.displayName = displayName
      }
      
      // 更新头像URL
      if let photoURL = photoURL {
        updatedUser.photoURL = photoURL
      }
      
      // 更新个人简介
      if let bio = bio {
        updatedUser.bio = bio
      }
      
      // 更新位置
      if let location = location {
        updatedUser.location = location
      }
      
      // 更新偏好设置
      if let preferences = preferences {
        updatedUser.preferences = preferences
      }
    }
  }

  /// 清除当前用户
  public func clearCurrentUser() {
    currentUser = nil
  }
}
