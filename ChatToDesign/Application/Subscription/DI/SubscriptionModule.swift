//
//  SubscriptionModule.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/15.
//

import Combine
import Foundation

/// 订阅模块依赖
public struct SubscriptionModuleDependencies {
  /// 配置服务
  public let configService: ConfigService

  /// 用户服务
  public let userService: UserService

  /// 分析服务
  public let analyticsService: AnalyticsService

  /// 初始化依赖
  public init(
    configService: ConfigService,
    userService: UserService,
    analyticsService: AnalyticsService
  ) {
    self.configService = configService
    self.userService = userService
    self.analyticsService = analyticsService
  }
}

/// 订阅模块
/// 提供所有订阅相关功能的依赖注入容器
public final class SubscriptionModule {

  // MARK: - Public Services

  /// 订阅服务
  public let subscriptionService: SubscriptionService

  /// 订阅状态管理器
  public let subscriptionStateManager: SubscriptionStateManager

  /// 检查订阅状态用例
  public let checkSubscriptionStatusUseCase: CheckSubscriptionStatusUseCase

  /// 购买订阅用例
  public let purchaseSubscriptionUseCase: PurchaseSubscriptionUseCase

  /// 恢复购买用例
  public let restorePurchasesUseCase: RestorePurchasesUseCase

  /// Paywall 服务
  public let paywallService: PaywallService

  // MARK: - Private Properties

  private let dependencies: SubscriptionModuleDependencies
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Initialization

  /// 初始化订阅模块
  /// - Parameter dependencies: 模块依赖
  public init(dependencies: SubscriptionModuleDependencies) {
    self.dependencies = dependencies

    // 创建订阅服务
    self.subscriptionService = RevenueCatAdapter()

    // 创建订阅状态管理器
    self.subscriptionStateManager = SubscriptionStateManager(
      subscriptionService: subscriptionService,
      analyticsService: dependencies.analyticsService
    )

    // 创建用例
    self.checkSubscriptionStatusUseCase = DefaultCheckSubscriptionStatusUseCase(
      subscriptionService: subscriptionService

    )

    self.purchaseSubscriptionUseCase = DefaultPurchaseSubscriptionUseCase(
      subscriptionService: subscriptionService
    )

    self.restorePurchasesUseCase = DefaultRestorePurchasesUseCase(
      subscriptionService: subscriptionService
    )

    // 创建 Paywall 服务
    self.paywallService = RevenueCatPaywallAdapter(
      subscriptionService: subscriptionService,
      subscriptionStateManager: subscriptionStateManager
    )

    // 设置用户状态监听
    setupUserStateObserver()
  }

  // MARK: - Configuration

  /// 配置订阅模块
  /// - Parameter userId: 当前用户ID（可选）
  public func configure(userId: String? = nil) async {
    // 从配置服务获取RevenueCat API密钥
    let apiKey = getRevenueCatAPIKey()

    // 配置订阅状态管理器
    await subscriptionStateManager.configure(apiKey: apiKey, userId: userId)

    Logger.info("SubscriptionModule: 模块配置完成")
  }

  // MARK: - Private Methods

  /// 获取RevenueCat API密钥
  private func getRevenueCatAPIKey() -> String {
    // 这里应该从配置服务或环境变量中获取API密钥
    // 暂时返回占位符，实际使用时需要替换为真实的API密钥
    #if DEBUG
      return "appl_RrLtimhMOIExejPsGQUlTLPRgCD"
    #else
      return "appl_RrLtimhMOIExejPsGQUlTLPRgCD"
    #endif
  }

  /// 设置用户状态观察者
  private func setupUserStateObserver() {
    // 监听用户状态变化，当用户登录/登出时同步RevenueCat
    dependencies.userService.currentUserPublisher
      .sink { [weak self] user in
        guard let self = self else { return }
        Logger.info("SubscriptionModule: 用户状态变化 - \(user?.id ?? "nil")")

        if let user = user {
          // 用户登录
          self.userDidLogin(user)
        } else {
          // 用户登出
          self.userDidLogout()
        }
      }
      .store(in: &cancellables)
  }
}

// MARK: - 便利方法
extension SubscriptionModule {
  /// 检查用户是否为高级用户
  public var isPremiumUser: Bool {
    return subscriptionStateManager.isPremiumUser
  }

  /// 获取当前订阅层级
  public var currentTier: SubscriptionTier {
    return subscriptionStateManager.currentTier
  }

  /// 检查是否为专业版用户
  public var isProUser: Bool {
    return subscriptionStateManager.isProUser
  }

  /// 检查是否可以使用特定功能
  /// - Parameter feature: 功能类型
  /// - Returns: 是否可以使用
  public func canUseFeature(_ feature: AppFeature) -> Bool {
    return subscriptionStateManager.canUseFeature(feature)
  }
}

// MARK: - 用户状态观察者协议
public protocol UserStateObserver {
  func userDidLogin(_ user: User)
  func userDidLogout()
}

// MARK: - 订阅模块用户状态观察者实现
extension SubscriptionModule: UserStateObserver {
  public func userDidLogin(_ user: User) {
    Task {
      await subscriptionStateManager.loginUser(user.id)
      Logger.info("SubscriptionModule: 用户登录，同步订阅状态 - \(user.id)")
    }
  }

  public func userDidLogout() {
    Task {
      await subscriptionStateManager.logoutUser()
      Logger.info("SubscriptionModule: 用户登出，清理订阅状态")
    }
  }
}
