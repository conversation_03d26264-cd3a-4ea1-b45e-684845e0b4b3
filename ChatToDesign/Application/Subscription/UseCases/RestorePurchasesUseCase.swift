//
//  RestorePurchasesUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/15.
//

import Foundation

/// 恢复购买结果
public struct RestoreResult {
    /// 是否成功
    public let isSuccessful: Bool
    
    /// 恢复的订阅信息
    public let subscription: Subscription?
    
    /// 错误信息
    public let error: SubscriptionServiceError?
    
    /// 是否找到了可恢复的购买
    public let foundPurchases: Bool
    
    /// 初始化恢复结果
    public init(
        isSuccessful: Bool,
        subscription: Subscription? = nil,
        error: SubscriptionServiceError? = nil,
        foundPurchases: Bool = false
    ) {
        self.isSuccessful = isSuccessful
        self.subscription = subscription
        self.error = error
        self.foundPurchases = foundPurchases
    }
}

/// 恢复购买用例协议
public protocol RestorePurchasesUseCase {
    /// 恢复购买
    /// - Returns: 恢复结果
    func execute() async -> RestoreResult
    
    /// 检查是否有可恢复的购买
    /// - Returns: 是否有可恢复的购买
    func hasRestorablePurchases() async throws -> Bool
}

/// 恢复购买用例实现
public final class DefaultRestorePurchasesUseCase: RestorePurchasesUseCase {
    
    // MARK: - Private Properties
    
    private let subscriptionService: SubscriptionService

    
    // MARK: - Initialization
    
    /// 初始化恢复购买用例
    /// - Parameters:
    ///   - subscriptionService: 订阅服务
    ///   - Logger: 日志服务
    public init(
        subscriptionService: SubscriptionService

    ) {
        self.subscriptionService = subscriptionService
    }
    
    // MARK: - RestorePurchasesUseCase Implementation
    
    public func execute() async -> RestoreResult {
        Logger.info("RestorePurchasesUseCase: 开始恢复购买")
        
        do {
            let subscription = try await subscriptionService.restorePurchases()
            
            if let subscription = subscription {
                Logger.info("RestorePurchasesUseCase: 恢复购买成功 - 订阅层级: \(subscription.tier.displayName)")
                
                return RestoreResult(
                    isSuccessful: true,
                    subscription: subscription,
                    foundPurchases: true
                )
            } else {
                Logger.info("RestorePurchasesUseCase: 没有找到可恢复的购买")
                
                return RestoreResult(
                    isSuccessful: true,
                    foundPurchases: false
                )
            }
            
        } catch let error as SubscriptionServiceError {
            Logger.error("RestorePurchasesUseCase: 恢复购买失败 - \(error)")
            
            return RestoreResult(
                isSuccessful: false,
                error: error
            )
            
        } catch {
            let subscriptionError = SubscriptionServiceError.unknownError(error)
            Logger.error("RestorePurchasesUseCase: 恢复购买失败 - \(error)")
            
            return RestoreResult(
                isSuccessful: false,
                error: subscriptionError
            )
        }
    }
    
    public func hasRestorablePurchases() async throws -> Bool {
        Logger.debug("RestorePurchasesUseCase: 检查是否有可恢复的购买")
        
        do {
            // 通过获取当前订阅状态来判断是否有可恢复的购买
            let subscription = try await subscriptionService.getCurrentSubscription()
            let hasRestorable = subscription != nil
            
            Logger.debug("RestorePurchasesUseCase: 可恢复购买检查结果 - \(hasRestorable)")
            return hasRestorable
            
        } catch {
            Logger.error("RestorePurchasesUseCase: 检查可恢复购买失败 - \(error)")
            throw error
        }
    }
}

// MARK: - 便利方法
extension DefaultRestorePurchasesUseCase {
    /// 静默恢复购买（不显示用户界面）
    /// - Returns: 恢复结果
    public func silentRestore() async -> RestoreResult {
        Logger.debug("RestorePurchasesUseCase: 执行静默恢复")
        
        // 静默恢复通常在应用启动时执行，不需要用户交互
        return await execute()
    }
    
    /// 用户主动恢复购买
    /// - Returns: 恢复结果
    public func userInitiatedRestore() async -> RestoreResult {
        Logger.info("RestorePurchasesUseCase: 用户主动恢复购买")
        
        // 用户主动恢复，可能需要显示加载界面
        return await execute()
    }
}

// MARK: - 恢复购买验证
extension DefaultRestorePurchasesUseCase {
    /// 验证恢复的订阅是否有效
    /// - Parameter subscription: 恢复的订阅
    /// - Returns: 是否有效
    public func validateRestoredSubscription(_ subscription: Subscription) -> Bool {
        // 检查订阅是否过期
        if let expirationDate = subscription.expirationDate {
            let isExpired = expirationDate < Date()
            if isExpired {
                Logger.warning("RestorePurchasesUseCase: 恢复的订阅已过期")
                return false
            }
        }
        
        // 检查订阅状态
        if !subscription.isActive {
            Logger.warning("RestorePurchasesUseCase: 恢复的订阅不是活跃状态")
            return false
        }
        
        Logger.info("RestorePurchasesUseCase: 恢复的订阅验证通过")
        return true
    }
    
    /// 处理恢复购买后的业务逻辑
    /// - Parameter subscription: 恢复的订阅
    public func handleRestoredSubscription(_ subscription: Subscription) async {
        Logger.info("RestorePurchasesUseCase: 处理恢复的订阅 - \(subscription.tier.displayName)")
        
        // 这里可以添加恢复购买后的业务逻辑
        // 例如：同步用户数据、更新UI状态、发送分析事件等
        
        // 验证订阅
        let isValid = validateRestoredSubscription(subscription)
        if !isValid {
            Logger.warning("RestorePurchasesUseCase: 恢复的订阅验证失败")
            return
        }
        
        // 记录恢复购买事件
        Logger.info("RestorePurchasesUseCase: 订阅恢复成功，层级: \(subscription.tier.displayName)")
    }
}

// MARK: - 恢复购买流程状态
public enum RestoreFlowState {
    case idle
    case checking
    case restoring
    case completed(Subscription?)
    case failed(SubscriptionServiceError)
    case noRestorablePurchases
}

// MARK: - 恢复购买流程管理
extension DefaultRestorePurchasesUseCase {
    /// 执行完整的恢复购买流程
    /// - Returns: 恢复流程状态的异步序列
    public func executeRestoreFlow() -> AsyncStream<RestoreFlowState> {
        return AsyncStream { continuation in
            Task {
                // 1. 开始检查
                continuation.yield(.checking)
                
                do {
                    // 2. 检查是否有可恢复的购买
                    let hasRestorable = try await self.hasRestorablePurchases()
                    
                    if !hasRestorable {
                        continuation.yield(.noRestorablePurchases)
                        continuation.finish()
                        return
                    }
                    
                    // 3. 开始恢复
                    continuation.yield(.restoring)
                    
                    // 4. 执行恢复
                    let result = await self.execute()
                    
                    // 5. 处理结果
                    if result.isSuccessful {
                        if let subscription = result.subscription {
                            await self.handleRestoredSubscription(subscription)
                        }
                        continuation.yield(.completed(result.subscription))
                    } else if let error = result.error {
                        continuation.yield(.failed(error))
                    } else {
                        continuation.yield(.failed(.unknownError(NSError(domain: "RestoreFlow", code: -1))))
                    }
                    
                } catch let error as SubscriptionServiceError {
                    continuation.yield(.failed(error))
                } catch {
                    continuation.yield(.failed(.unknownError(error)))
                }
                
                continuation.finish()
            }
        }
    }
}
