//
//  PurchaseSubscriptionUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/15.
//

import Foundation

/// 购买订阅用例协议
public protocol PurchaseSubscriptionUseCase {
  /// 购买订阅
  /// - Parameter productId: 产品ID
  /// - Returns: 购买结果
  func execute(productId: String) async -> PurchaseResult

  /// 获取可用产品
  /// - Returns: 产品列表
  func getAvailableProducts() async throws -> [ProductInfo]
}

/// 购买订阅用例实现
public final class DefaultPurchaseSubscriptionUseCase: PurchaseSubscriptionUseCase {

  // MARK: - Private Properties

  private let subscriptionService: SubscriptionService

  // MARK: - Initialization

  /// 初始化购买订阅用例
  /// - Parameter subscriptionService: 订阅服务
  public init(subscriptionService: SubscriptionService) {
    self.subscriptionService = subscriptionService
  }

  // MARK: - PurchaseSubscriptionUseCase Implementation

  public func execute(productId: String) async -> PurchaseResult {
    Logger.info("PurchaseSubscriptionUseCase: 开始购买订阅 - \(productId)")

    do {
      let subscription = try await subscriptionService.purchaseSubscription(productId: productId)

      Logger.info(
        "PurchaseSubscriptionUseCase: 购买成功 - \(productId), 订阅层级: \(subscription.tier.displayName)")

      return PurchaseResult(
        isSuccessful: true,
        subscription: subscription
      )

    } catch let error as SubscriptionServiceError {
      Logger.error("PurchaseSubscriptionUseCase: 购买失败 - \(productId): \(error)")

      return PurchaseResult(
        isSuccessful: false,
        error: error,
        wasCancelled: error == .purchaseCancelled
      )

    } catch {
      let subscriptionError = SubscriptionServiceError.unknownError(error)
      Logger.error("PurchaseSubscriptionUseCase: 购买失败 - \(productId): \(error)")

      return PurchaseResult(
        isSuccessful: false,
        error: subscriptionError
      )
    }
  }

  public func getAvailableProducts() async throws -> [ProductInfo] {
      Logger.debug("PurchaseSubscriptionUseCase: 获取可用产品")

    do {
      let products = try await subscriptionService.getAvailableProducts()

        Logger.info("PurchaseSubscriptionUseCase: 获取到 \(products.count) 个可用产品")

      return products

    } catch {
        Logger.error("PurchaseSubscriptionUseCase: 获取产品失败 - \(error)")
      throw error
    }
  }
}

// MARK: - 便利方法
extension DefaultPurchaseSubscriptionUseCase {
  /// 购买高级版订阅
  /// - Returns: 购买结果
  public func purchasePremium() async -> PurchaseResult {
    // 这里应该使用实际的高级版产品ID
    return await execute(productId: "premium_monthly")
  }

  /// 购买专业版订阅
  /// - Returns: 购买结果
  public func purchasePro() async -> PurchaseResult {
    // 这里应该使用实际的专业版产品ID
    return await execute(productId: "pro_monthly")
  }

  /// 获取推荐产品
  /// - Returns: 推荐的产品信息
  public func getRecommendedProduct() async throws -> ProductInfo? {
    let products = try await getAvailableProducts()

    // 返回第一个高级产品作为推荐
    return products.first { product in
      product.identifier.lowercased().contains("premium")
    }
  }

  /// 获取按价格排序的产品
  /// - Returns: 按价格排序的产品列表
  public func getProductsSortedByPrice() async throws -> [ProductInfo] {
    let products = try await getAvailableProducts()

    // 简单的价格排序（实际实现中需要解析价格字符串）
    return products.sorted { product1, product2 in
      // 这里需要实现价格比较逻辑
      // 暂时按产品ID排序
      return product1.identifier < product2.identifier
    }
  }
}

// MARK: - 购买验证
extension DefaultPurchaseSubscriptionUseCase {
  /// 验证购买前的条件
  /// - Parameter productId: 产品ID
  /// - Returns: 是否可以购买
  public func canPurchase(productId: String) async -> Bool {
    do {
      // 检查产品是否存在
      let products = try await getAvailableProducts()
      let productExists = products.contains { $0.identifier == productId }

      if !productExists {
          Logger.warning("PurchaseSubscriptionUseCase: 产品不存在 - \(productId)")
        return false
      }

      // 检查是否已经有相同或更高级的订阅
      let currentSubscription = try await subscriptionService.getCurrentSubscription()
      if let subscription = currentSubscription, subscription.isActive {
        let currentTier = subscription.tier
        let targetTier = determineProductTier(productId: productId)

        if currentTier.rawValue >= targetTier.rawValue {
            Logger.info("PurchaseSubscriptionUseCase: 用户已有相同或更高级订阅")
          return false
        }
      }

      return true

    } catch {
        Logger.error("PurchaseSubscriptionUseCase: 购买条件检查失败 - \(error)")
      return false
    }
  }

  /// 根据产品ID确定订阅层级
  /// - Parameter productId: 产品ID
  /// - Returns: 订阅层级
  private func determineProductTier(productId: String) -> SubscriptionTier {
    let lowercased = productId.lowercased()

    if lowercased.contains("pro") {
      return .pro
    } else if lowercased.contains("premium") {
      return .premium
    } else {
      return .free
    }
  }
}

// MARK: - 购买流程状态
public enum PurchaseFlowState {
  case idle
  case loadingProducts
  case readyToPurchase([ProductInfo])
  case purchasing(String)
  case completed(Subscription)
  case failed(SubscriptionServiceError)
  case cancelled
}

// MARK: - 购买流程管理
extension DefaultPurchaseSubscriptionUseCase {
  /// 执行完整的购买流程
  /// - Parameter productId: 产品ID
  /// - Returns: 购买流程状态的异步序列
  public func executePurchaseFlow(productId: String) -> AsyncStream<PurchaseFlowState> {
    return AsyncStream { continuation in
      Task {
        // 1. 开始购买流程
        continuation.yield(.purchasing(productId))

        // 2. 验证购买条件
        let canPurchase = await self.canPurchase(productId: productId)
        if !canPurchase {
          continuation.yield(.failed(.purchaseFailed("不满足购买条件")))
          continuation.finish()
          return
        }

        // 3. 执行购买
        let result = await self.execute(productId: productId)

        // 4. 处理结果
        if result.isSuccessful, let subscription = result.subscription {
          continuation.yield(.completed(subscription))
        } else if result.wasCancelled {
          continuation.yield(.cancelled)
        } else if let error = result.error {
          continuation.yield(.failed(error))
        } else {
          continuation.yield(.failed(.unknownError(NSError(domain: "PurchaseFlow", code: -1))))
        }

        continuation.finish()
      }
    }
  }
}
