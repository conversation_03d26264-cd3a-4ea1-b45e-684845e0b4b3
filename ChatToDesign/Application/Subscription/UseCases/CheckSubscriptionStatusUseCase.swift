//
//  CheckSubscriptionStatusUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/15.
//

import Foundation

/// 检查订阅状态用例协议
public protocol CheckSubscriptionStatusUseCase {
  /// 检查当前订阅状态
  /// - Returns: 订阅信息，如果没有订阅则返回nil
  func execute() async throws -> Subscription?

  /// 检查是否为高级用户
  /// - Returns: 是否为高级用户
  func isPremiumUser() async throws -> Bool

  /// 检查特定权限
  /// - Parameter entitlementType: 权限类型
  /// - Returns: 是否拥有该权限
  func hasEntitlement(_ entitlementType: EntitlementType) async throws -> Bool

  /// 获取用户权限集合
  /// - Returns: 权限集合
  func getEntitlements() async throws -> EntitlementSet
}

/// 检查订阅状态用例实现
public final class DefaultCheckSubscriptionStatusUseCase: CheckSubscriptionStatusUseCase {

  // MARK: - Private Properties

  private let subscriptionService: SubscriptionService

  // MARK: - Initialization

  /// 初始化检查订阅状态用例
  /// - Parameters:
  ///   - subscriptionService: 订阅服务
  ///   - Logger: 日志服务
  public init(
    subscriptionService: SubscriptionService
  ) {
    self.subscriptionService = subscriptionService
  }

  // MARK: - CheckSubscriptionStatusUseCase Implementation

  public func execute() async throws -> Subscription? {
    Logger.debug("CheckSubscriptionStatusUseCase: 开始检查订阅状态")

    do {
      let subscription = try await subscriptionService.getCurrentSubscription()

      if let subscription = subscription {
        Logger.info(
          "CheckSubscriptionStatusUseCase: 找到订阅 - \(subscription.tier.displayName), 状态: \(subscription.status)"
        )
      } else {
        Logger.info("CheckSubscriptionStatusUseCase: 用户没有活跃订阅")
      }

      return subscription

    } catch {
      Logger.error("CheckSubscriptionStatusUseCase: 检查订阅状态失败 - \(error)")
      throw error
    }
  }

  public func isPremiumUser() async throws -> Bool {
    Logger.debug("CheckSubscriptionStatusUseCase: 检查是否为高级用户")

    do {
      let subscription = try await execute()
      let isPremium = subscription?.isActive == true && subscription?.tier != .free

      Logger.debug("CheckSubscriptionStatusUseCase: 高级用户状态 - \(isPremium)")
      return isPremium

    } catch {
      Logger.error("CheckSubscriptionStatusUseCase: 检查高级用户状态失败 - \(error)")
      throw error
    }
  }

  public func hasEntitlement(_ entitlementType: EntitlementType) async throws -> Bool {
    Logger.debug("CheckSubscriptionStatusUseCase: 检查权限 - \(entitlementType.displayName)")

    do {
      let hasEntitlement = try await subscriptionService.hasEntitlement(entitlementType)

      Logger.debug(
        "CheckSubscriptionStatusUseCase: 权限检查结果 - \(entitlementType.displayName): \(hasEntitlement)"
      )
      return hasEntitlement

    } catch {
      Logger.error(
        "CheckSubscriptionStatusUseCase: 检查权限失败 - \(entitlementType.displayName): \(error)")
      throw error
    }
  }

  public func getEntitlements() async throws -> EntitlementSet {
    Logger.debug("CheckSubscriptionStatusUseCase: 获取用户权限集合")

    do {
      let entitlements = try await subscriptionService.getEntitlements()

      Logger.info(
        "CheckSubscriptionStatusUseCase: 获取到 \(entitlements.entitlements.count) 个权限，其中 \(entitlements.activeEntitlements.count) 个激活"
      )

      return entitlements

    } catch {
      Logger.error("CheckSubscriptionStatusUseCase: 获取权限集合失败 - \(error)")
      throw error
    }
  }
}

// MARK: - 便利方法
extension DefaultCheckSubscriptionStatusUseCase {
  /// 检查是否为专业版用户
  public func isProUser() async throws -> Bool {
    return try await hasEntitlement(.proTier)
  }

  /// 获取当前用户层级
  public func getCurrentTier() async throws -> UserTier {
    let entitlements = try await getEntitlements()
    return PermissionManager.currentTier(from: entitlements)
  }

  /// 获取当前权限配置
  public func getCurrentPermissions() async throws -> TierPermissions {
    let tier = try await getCurrentTier()
    return PermissionManager.permissions(for: tier)
  }

  /// 检查是否可以使用特定功能
  /// - Parameter feature: 功能类型
  /// - Returns: 是否可以使用
  public func canUseFeature(_ feature: AppFeature) async throws -> Bool {
    let permissions = try await getCurrentPermissions()
    return PermissionManager.canUseFeature(feature, with: permissions)
  }

  /// 检查是否需要升级到指定层级
  /// - Parameter targetTier: 目标层级
  /// - Returns: 是否需要升级
  public func needsUpgradeTo(_ targetTier: UserTier) async throws -> Bool {
    let currentTier = try await getCurrentTier()
    return PermissionManager.needsUpgrade(from: currentTier, to: targetTier)
  }

  /// 检查是否需要显示付费墙
  /// - Parameter feature: 功能类型
  /// - Returns: 是否需要显示付费墙
  public func shouldShowPaywall(for feature: AppFeature) async throws -> Bool {
    return !(try await canUseFeature(feature))
  }
}

// MARK: - 订阅状态检查结果
public struct SubscriptionStatusResult {
  /// 是否有活跃订阅
  public let hasActiveSubscription: Bool

  /// 订阅信息
  public let subscription: Subscription?

  /// 权限集合
  public let entitlements: EntitlementSet

  /// 是否为高级用户
  public let isPremiumUser: Bool

  /// 初始化结果
  public init(
    hasActiveSubscription: Bool,
    subscription: Subscription?,
    entitlements: EntitlementSet,
    isPremiumUser: Bool
  ) {
    self.hasActiveSubscription = hasActiveSubscription
    self.subscription = subscription
    self.entitlements = entitlements
    self.isPremiumUser = isPremiumUser
  }
}

// MARK: - 扩展：获取完整状态
extension DefaultCheckSubscriptionStatusUseCase {
  /// 获取完整的订阅状态信息
  /// - Returns: 订阅状态结果
  public func getCompleteStatus() async throws -> SubscriptionStatusResult {
    Logger.debug("CheckSubscriptionStatusUseCase: 获取完整订阅状态")

    do {
      // 并行获取订阅信息和权限
      async let subscriptionTask = execute()
      async let entitlementsTask = getEntitlements()

      let (subscription, entitlements) = try await (subscriptionTask, entitlementsTask)

      let hasActiveSubscription = subscription?.isActive == true
      let isPremiumUser = hasActiveSubscription && subscription?.tier != .free

      let result = SubscriptionStatusResult(
        hasActiveSubscription: hasActiveSubscription,
        subscription: subscription,
        entitlements: entitlements,
        isPremiumUser: isPremiumUser
      )

      Logger.info("CheckSubscriptionStatusUseCase: 完整状态获取成功")
      return result

    } catch {
      Logger.error("CheckSubscriptionStatusUseCase: 获取完整状态失败 - \(error)")
      throw error
    }
  }
}
