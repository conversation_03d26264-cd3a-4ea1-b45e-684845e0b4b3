//
//  SubscriptionStateManager.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/15.
//

import Combine
import Foundation

/// 订阅状态管理器
/// 负责管理应用中的订阅状态，提供响应式的状态更新
//@MainActor
public final class SubscriptionStateManager: ObservableObject {

  // MARK: - Published Properties

  /// 当前订阅信息
  @Published public private(set) var currentSubscription: Subscription?

  /// 用户权限集合
  @Published public private(set) var entitlements: EntitlementSet = .empty

  /// 是否正在加载
  @Published public private(set) var isLoading: Bool = false

  /// 错误信息
  @Published public private(set) var error: SubscriptionServiceError?

  /// 是否已配置
  @Published public private(set) var isConfigured: Bool = false

  // MARK: - Computed Properties

  /// 是否为高级用户
  public var isPremiumUser: Bool {
    return currentSubscription?.isActive == true
  }

  /// 当前订阅层级
  public var currentTier: SubscriptionTier {
    return currentSubscription?.tier ?? .free
  }

  /// 是否为专业版用户
  public var isProUser: Bool {
    return entitlements.hasEntitlement(.proTier)
  }

  /// 当前用户层级（基于权限）
  public var userTier: UserTier {
    return PermissionManager.currentTier(from: entitlements)
  }

  /// 当前权限配置
  public var currentPermissions: TierPermissions {
    return PermissionManager.permissions(for: userTier)
  }

  // MARK: - Private Properties

  private let subscriptionService: SubscriptionService
  private let analyticsService: AnalyticsService
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Initialization

  /// 初始化订阅状态管理器
  /// - Parameters:
  ///   - subscriptionService: 订阅服务
  ///   - analyticsService: 分析服务
  public init(
    subscriptionService: SubscriptionService,
    analyticsService: AnalyticsService = AppDependencyContainer.shared.analyticsService
  ) {
    self.subscriptionService = subscriptionService
    self.analyticsService = analyticsService

    setupSubscriptions()
  }

  // MARK: - Public Methods

  /// 配置订阅服务
  /// - Parameters:
  ///   - apiKey: RevenueCat API密钥
  ///   - userId: 用户ID
  public func configure(apiKey: String, userId: String?) async {
    isLoading = true
    error = nil

    do {
      try await subscriptionService.configure(apiKey: apiKey, userId: userId)
      isConfigured = true
      Logger.info("SubscriptionStateManager: 配置成功")

      // 配置成功后立即刷新状态
      await refreshSubscriptionStatus()
    } catch {
      self.error = error as? SubscriptionServiceError ?? .unknownError(error)
      Logger.error("SubscriptionStateManager: 配置失败 - \(error)")
    }

    isLoading = false
  }

  /// 刷新订阅状态
  public func refreshSubscriptionStatus() async {
    guard isConfigured else {
      error = .notConfigured
      return
    }

    isLoading = true
    error = nil

    do {
      // 并行获取订阅信息和权限
      async let subscriptionTask = subscriptionService.getCurrentSubscription()
      async let entitlementsTask = subscriptionService.getEntitlements()

      let (subscription, entitlementSet) = try await (subscriptionTask, entitlementsTask)

      self.currentSubscription = subscription
      self.entitlements = entitlementSet

      Logger.info("SubscriptionStateManager: 状态刷新成功")
      Logger.debug(
        "SubscriptionStateManager: 当前订阅 - \(subscription?.tier.displayName ?? "无")")
      Logger.debug(
        "SubscriptionStateManager: 激活权限数量 - \(entitlementSet.activeEntitlements.count)")

    } catch {
      self.error = error as? SubscriptionServiceError ?? .unknownError(error)
      Logger.error("SubscriptionStateManager: 状态刷新失败 - \(error)")
    }

    isLoading = false
  }

  /// 购买订阅
  /// - Parameter productId: 产品ID
  public func purchaseSubscription(productId: String) async -> PurchaseResult {
    guard isConfigured else {
      return PurchaseResult(
        isSuccessful: false,
        error: .notConfigured
      )
    }

    // 获取用户ID用于埋点
    let userId = getCurrentUserId()

    // 埋点：开始购买订阅
    analyticsService.track(.subscriptionPurchaseStarted(productId: productId, userId: userId))

    isLoading = true
    error = nil

    do {
      let subscription = try await subscriptionService.purchaseSubscription(productId: productId)

      // 购买成功后更新状态
      self.currentSubscription = subscription
      await refreshSubscriptionStatus()

      Logger.info("SubscriptionStateManager: 购买成功 - \(productId)")

      // 埋点：购买成功
      analyticsService.track(
        .subscriptionPurchaseCompleted(
          productId: productId,
          userId: userId,
          success: true,
          revenue: extractRevenueFromSubscription(subscription)
        ))

      return PurchaseResult(
        isSuccessful: true,
        subscription: subscription
      )

    } catch let error as SubscriptionServiceError {
      self.error = error
      Logger.error("SubscriptionStateManager: 购买失败 - \(error)")

      // 埋点：购买失败
      analyticsService.track(
        .subscriptionPurchaseCompleted(
          productId: productId,
          userId: userId,
          success: false,
          revenue: nil
        ))

      return PurchaseResult(
        isSuccessful: false,
        error: error,
        wasCancelled: error == .purchaseCancelled
      )

    } catch {
      let subscriptionError = SubscriptionServiceError.unknownError(error)
      self.error = subscriptionError
      Logger.error("SubscriptionStateManager: 购买失败 - \(error)")

      // 埋点：购买失败（未知错误）
      analyticsService.track(
        .subscriptionPurchaseCompleted(
          productId: productId,
          userId: userId,
          success: false,
          revenue: nil
        ))

      return PurchaseResult(
        isSuccessful: false,
        error: subscriptionError
      )
    }
  }

  /// 恢复购买
  public func restorePurchases() async -> Bool {
    guard isConfigured else {
      error = .notConfigured
      return false
    }

    isLoading = true
    error = nil

    do {
      let subscription = try await subscriptionService.restorePurchases()

      if let subscription = subscription {
        self.currentSubscription = subscription
        Logger.info("SubscriptionStateManager: 恢复购买成功")
      } else {
        Logger.info("SubscriptionStateManager: 没有找到可恢复的购买")
      }

      // 刷新状态
      await refreshSubscriptionStatus()

      return subscription != nil

    } catch {
      self.error = error as? SubscriptionServiceError ?? .unknownError(error)
      Logger.error("SubscriptionStateManager: 恢复购买失败 - \(error)")
      return false
    }
  }

  /// 用户登录
  /// - Parameter userId: 用户ID
  public func loginUser(_ userId: String) async {
    do {
      try await subscriptionService.loginUser(userId)
      await refreshSubscriptionStatus()
      Logger.info("SubscriptionStateManager: 用户登录成功 - \(userId)")
    } catch {
      self.error = error as? SubscriptionServiceError ?? .unknownError(error)
      Logger.error("SubscriptionStateManager: 用户登录失败 - \(error)")
    }
  }

  /// 用户登出
  public func logoutUser() async {
    do {
      try await subscriptionService.logoutUser()

      // 清理状态
      currentSubscription = nil
      entitlements = .empty
      error = nil

      Logger.info("SubscriptionStateManager: 用户登出成功")
    } catch {
      self.error = error as? SubscriptionServiceError ?? .unknownError(error)
      Logger.error("SubscriptionStateManager: 用户登出失败 - \(error)")
    }
  }

  /// 检查特定权限
  /// - Parameter entitlementType: 权限类型
  /// - Returns: 是否拥有该权限
  public func hasEntitlement(_ entitlementType: EntitlementType) -> Bool {
    return entitlements.hasEntitlement(entitlementType)
  }

  /// 检查是否可以使用特定功能
  /// - Parameter feature: 功能类型
  /// - Returns: 是否可以使用
  public func canUseFeature(_ feature: AppFeature) -> Bool {
    return PermissionManager.canUseFeature(feature, with: currentPermissions)
  }

  /// 检查是否需要升级到指定层级
  /// - Parameter targetTier: 目标层级
  /// - Returns: 是否需要升级
  public func needsUpgrade(to targetTier: UserTier) -> Bool {
    return PermissionManager.needsUpgrade(from: userTier, to: targetTier)
  }

  /// 检查是否需要显示付费墙
  /// - Parameter feature: 功能类型
  /// - Returns: 是否需要显示付费墙
  public func shouldShowPaywall(for feature: AppFeature) -> Bool {
    return !canUseFeature(feature)
  }

  /// 清理错误状态
  public func clearError() {
    error = nil
  }

  // MARK: - Private Methods

  /// 设置订阅监听
  private func setupSubscriptions() {
    // 监听订阅服务的状态变化
    subscriptionService.subscriptionStatusPublisher
      .receive(on: DispatchQueue.main)
      .sink { [weak self] subscription in
        self?.currentSubscription = subscription
      }
      .store(in: &cancellables)

    subscriptionService.entitlementsPublisher
      .receive(on: DispatchQueue.main)
      .sink { [weak self] entitlements in
        self?.entitlements = entitlements
      }
      .store(in: &cancellables)
  }

  /// 获取当前用户ID
  /// - Returns: 用户ID，如果无法获取则返回"unknown"
  private func getCurrentUserId() -> String {
    // 这里应该从用户状态管理器或其他地方获取当前用户ID
    // 暂时返回占位符，实际使用时需要根据项目的用户管理方式来实现
    return UserStateManager.shared.currentUser?.id ?? "unknown"
  }

  /// 从订阅信息中提取收入金额
  /// - Parameter subscription: 订阅信息
  /// - Returns: 收入金额，如果无法提取则返回nil
  private func extractRevenueFromSubscription(_ subscription: Subscription) -> Double? {
    // 这里应该根据订阅信息提取实际的收入金额
    // 暂时返回nil，实际使用时需要根据订阅层级或价格信息来计算
    return nil
  }
}
