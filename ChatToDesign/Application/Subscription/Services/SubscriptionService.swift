//
//  SubscriptionService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/15.
//

import Combine
import Foundation

/// 订阅服务错误
public enum SubscriptionServiceError: Error, LocalizedError, Equatable {
  case notConfigured
  case userNotAuthenticated
  case purchaseInProgress
  case purchaseCancelled
  case purchaseFailed(String)
  case networkError
  case unknownError(Error)

  public var errorDescription: String? {
    switch self {
    case .notConfigured:
      return "订阅服务未配置"
    case .userNotAuthenticated:
      return "用户未认证"
    case .purchaseInProgress:
      return "购买正在进行中"
    case .purchaseCancelled:
      return "购买已取消"
    case .purchaseFailed(let message):
      return "购买失败: \(message)"
    case .networkError:
      return "网络连接错误"
    case .unknownError(let error):
      return "未知错误: \(error.localizedDescription)"
    }
  }

  // MARK: - Equatable
  public static func == (lhs: SubscriptionServiceError, rhs: SubscriptionServiceError) -> Bool {
    switch (lhs, rhs) {
    case (.notConfigured, .notConfigured),
      (.userNotAuthenticated, .userNotAuthenticated),
      (.purchaseInProgress, .purchaseInProgress),
      (.purchaseCancelled, .purchaseCancelled),
      (.networkError, .networkError):
      return true
    case (.purchaseFailed(let lhsMessage), .purchaseFailed(let rhsMessage)):
      return lhsMessage == rhsMessage
    case (.unknownError(let lhsError), .unknownError(let rhsError)):
      return lhsError.localizedDescription == rhsError.localizedDescription
    default:
      return false
    }
  }
}

/// 订阅周期单位
public enum PeriodUnit: String, Codable, CaseIterable {
  case day = "day"
  case week = "week"
  case month = "month"
  case year = "year"

  /// Display name for the unit
  public var displayName: String {
    switch self {
    case .day:
      return "Daily"
    case .week:
      return "Weekly"
    case .month:
      return "Monthly"
    case .year:
      return "Annually"
    }
  }
}

/// 订阅周期信息
public struct SubscriptionPeriodInfo: Codable, Equatable {
  /// Period value (e.g., 1 for "1 month", 3 for "3 months")
  public let value: Int

  /// Period unit (day, week, month, year)
  public let unit: PeriodUnit

  /// Initialize subscription period info
  public init(value: Int, unit: PeriodUnit) {
    self.value = value
    self.unit = unit
  }

  /// Whether this is an annual subscription
  public var isAnnual: Bool {
    return unit == .year
  }

  /// Whether this is a monthly subscription
  public var isMonthly: Bool {
    return unit == .month && value == 1
  }

  /// Whether this is a weekly subscription
  public var isWeekly: Bool {
    return unit == .week && value == 1
  }
}

/// 产品信息
public struct ProductInfo: Identifiable, Codable, Equatable {
  public let id: String
  public let identifier: String
  public let displayName: String
  public let description: String
  public let price: String
  public let priceLocale: String
  public let introductoryPrice: String?

  // Structured fields
  /// Structured subscription period information
  public let periodInfo: SubscriptionPeriodInfo?

  /// Structured trial period information
  public let trialPeriodInfo: SubscriptionPeriodInfo?

  public init(
    id: String,
    identifier: String,
    displayName: String,
    description: String,
    price: String,
    priceLocale: String,
    introductoryPrice: String? = nil,
    periodInfo: SubscriptionPeriodInfo? = nil,
    trialPeriodInfo: SubscriptionPeriodInfo? = nil
  ) {
    self.id = id
    self.identifier = identifier
    self.displayName = displayName
    self.description = description
    self.price = price
    self.priceLocale = priceLocale
    self.introductoryPrice = introductoryPrice
    self.periodInfo = periodInfo
    self.trialPeriodInfo = trialPeriodInfo
  }
}

// MARK: - ProductInfo Extensions

extension ProductInfo {
  /// Whether this is an annual subscription
  public var isAnnual: Bool {
    return periodInfo?.isAnnual ?? false
  }

  /// Whether this is a monthly subscription
  public var isMonthly: Bool {
    return periodInfo?.isMonthly ?? false
  }

  /// Whether this is a weekly subscription
  public var isWeekly: Bool {
    return periodInfo?.isWeekly ?? false
  }

  /// Get subscription period type
  public var periodType: SubscriptionPeriodType {
    guard let periodInfo = periodInfo else {
      return .unknown
    }

    switch periodInfo.unit {
    case .year:
      return .annual
    case .month:
      return periodInfo.value == 1 ? .monthly : .custom
    case .week:
      return periodInfo.value == 1 ? .weekly : .custom
    case .day:
      return .custom
    }
  }

  /// Whether has free trial
  public var hasFreeTrial: Bool {
    return trialPeriodInfo != nil
  }
}

/// Subscription period type enumeration
public enum SubscriptionPeriodType: String, Codable, CaseIterable {
  case annual = "annual"
  case monthly = "monthly"
  case weekly = "weekly"
  case custom = "custom"
  case unknown = "unknown"

  /// Display name for the period type
  public var displayName: String {
    switch self {
    case .annual:
      return "Annual"
    case .monthly:
      return "Monthly"
    case .weekly:
      return "Weekly"
    case .custom:
      return "Custom"
    case .unknown:
      return "Unknown"
    }
  }
}

/// 订阅服务协议
public protocol SubscriptionService {
  /// 配置服务
  /// - Parameters:
  ///   - apiKey: RevenueCat API密钥
  ///   - userId: 用户ID（可选）
  func configure(apiKey: String, userId: String?) async throws

  /// 获取当前订阅状态
  /// - Returns: 当前订阅信息
  func getCurrentSubscription() async throws -> Subscription?

  /// 获取用户权限
  /// - Returns: 权限集合
  func getEntitlements() async throws -> EntitlementSet

  /// 获取可用产品
  /// - Returns: 产品列表
  func getAvailableProducts() async throws -> [ProductInfo]

  /// 购买订阅
  /// - Parameter productId: 产品ID
  /// - Returns: 购买后的订阅信息
  func purchaseSubscription(productId: String) async throws -> Subscription

  /// 恢复购买
  /// - Returns: 恢复的订阅信息
  func restorePurchases() async throws -> Subscription?

  /// 检查特定权限
  /// - Parameter entitlementType: 权限类型
  /// - Returns: 是否拥有该权限
  func hasEntitlement(_ entitlementType: EntitlementType) async throws -> Bool

  /// 用户登录
  /// - Parameter userId: 用户ID
  func loginUser(_ userId: String) async throws

  /// 用户登出
  func logoutUser() async throws

  /// 订阅状态变化发布者
  var subscriptionStatusPublisher: AnyPublisher<Subscription?, Never> { get }

  /// 权限变化发布者
  var entitlementsPublisher: AnyPublisher<EntitlementSet, Never> { get }
}

/// 订阅服务配置
public struct SubscriptionServiceConfiguration {
  /// RevenueCat API密钥
  public let apiKey: String

  /// 是否启用调试日志
  public let enableDebugLogs: Bool

  /// 用户ID（可选）
  public let userId: String?

  /// 初始化配置
  public init(
    apiKey: String,
    enableDebugLogs: Bool = false,
    userId: String? = nil
  ) {
    self.apiKey = apiKey
    self.enableDebugLogs = enableDebugLogs
    self.userId = userId
  }
}

/// 购买结果
public struct PurchaseResult {
  /// 是否成功
  public let isSuccessful: Bool

  /// 订阅信息
  public let subscription: Subscription?

  /// 错误信息
  public let error: SubscriptionServiceError?

  /// 是否被用户取消
  public let wasCancelled: Bool

  /// 初始化购买结果
  public init(
    isSuccessful: Bool,
    subscription: Subscription? = nil,
    error: SubscriptionServiceError? = nil,
    wasCancelled: Bool = false
  ) {
    self.isSuccessful = isSuccessful
    self.subscription = subscription
    self.error = error
    self.wasCancelled = wasCancelled
  }
}
