//
//  PaywallService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/17.
//

import Combine
import Foundation

/// Paywall 展示模式
public enum PaywallPresentationMode {
  /// Sheet 模式（底部弹出）
  case sheet
  /// 全屏模式
  case fullScreen
  /// 条件展示（根据权限自动判断）
  case conditional(requiredEntitlement: EntitlementType)
  /// 条件展示（根据功能自动判断）
  case conditionalFeature(requiredFeature: AppFeature)
}

/// Paywall 展示选项
public struct PaywallPresentationOptions {
  /// 展示模式
  public let mode: PaywallPresentationMode
  /// 是否显示关闭按钮
  public let showCloseButton: Bool
  /// 特定的 Offering ID（可选）
  public let offeringId: String?
  /// 自定义标题（可选）
  public let customTitle: String?
  /// 自定义描述（可选）
  public let customDescription: String?

  public init(
    mode: PaywallPresentationMode = .sheet,
    showCloseButton: Bool = true,
    offeringId: String? = nil,
    customTitle: String? = nil,
    customDescription: String? = nil
  ) {
    self.mode = mode
    self.showCloseButton = showCloseButton
    self.offeringId = offeringId
    self.customTitle = customTitle
    self.customDescription = customDescription
  }
}

/// Paywall 事件类型
public enum PaywallEvent {
  /// Paywall 已展示
  case presented
  /// Paywall 已关闭
  case dismissed
  /// 开始购买
  case purchaseStarted(productId: String)
  /// 购买完成
  case purchaseCompleted(productId: String, subscription: Subscription)
  /// 购买失败
  case purchaseFailed(productId: String, error: SubscriptionServiceError)
  /// 购买取消
  case purchaseCancelled(productId: String)
  /// 开始恢复购买
  case restoreStarted
  /// 恢复购买完成
  case restoreCompleted(subscription: Subscription?)
  /// 恢复购买失败
  case restoreFailed(error: SubscriptionServiceError)
}

/// Paywall 状态
public enum PaywallState {
  /// 空闲状态
  case idle
  /// 正在加载产品
  case loadingProducts
  /// 产品加载完成
  case productsLoaded([ProductInfo])
  /// 正在购买
  case purchasing(productId: String)
  /// 正在恢复购买
  case restoring
  /// 错误状态
  case error(SubscriptionServiceError)
}

/// Paywall 服务协议
public protocol PaywallService {
  /// 当前 Paywall 状态
  var currentState: PaywallState { get }

  /// Paywall 状态发布者
  var statePublisher: AnyPublisher<PaywallState, Never> { get }

  /// Paywall 事件发布者
  var eventPublisher: AnyPublisher<PaywallEvent, Never> { get }

  /// 检查是否需要展示 Paywall
  /// - Parameter entitlement: 需要的权限类型
  /// - Returns: 是否需要展示 Paywall
  func shouldPresentPaywall(for entitlement: EntitlementType) async -> Bool

  /// 获取可用产品信息
  /// - Parameter offeringId: 特定的 Offering ID（可选）
  /// - Returns: 产品信息列表
  func getAvailableProducts(offeringId: String?) async throws -> [ProductInfo]

  /// 购买产品
  /// - Parameter productId: 产品ID
  /// - Returns: 购买结果
  func purchaseProduct(_ productId: String) async -> PurchaseResult

  /// 恢复购买
  /// - Returns: 恢复结果
  func restorePurchases() async -> RestoreResult

  /// 发送 Paywall 事件
  /// - Parameter event: 事件类型
  func sendEvent(_ event: PaywallEvent)
}

// MARK: - 便利扩展

extension PaywallPresentationOptions {
  /// 创建条件展示的选项（基于权限）
  /// - Parameter entitlement: 需要的权限
  /// - Returns: 配置好的选项
  public static func conditional(
    requiredEntitlement entitlement: EntitlementType,
    showCloseButton: Bool = true
  ) -> PaywallPresentationOptions {
    return PaywallPresentationOptions(
      mode: .conditional(requiredEntitlement: entitlement),
      showCloseButton: showCloseButton
    )
  }

  /// 创建条件展示的选项（基于功能）
  /// - Parameter feature: 需要的功能
  /// - Returns: 配置好的选项
  public static func conditional(
    requiredFeature feature: AppFeature,
    showCloseButton: Bool = true
  ) -> PaywallPresentationOptions {
    return PaywallPresentationOptions(
      mode: .conditionalFeature(requiredFeature: feature),
      showCloseButton: showCloseButton
    )
  }

  /// 创建 Sheet 展示的选项
  /// - Parameters:
  ///   - showCloseButton: 是否显示关闭按钮
  ///   - offeringId: 特定的 Offering ID
  /// - Returns: 配置好的选项
  public static func sheet(
    showCloseButton: Bool = true,
    offeringId: String? = nil
  ) -> PaywallPresentationOptions {
    return PaywallPresentationOptions(
      mode: .sheet,
      showCloseButton: showCloseButton,
      offeringId: offeringId
    )
  }

  /// 创建全屏展示的选项
  /// - Parameters:
  ///   - showCloseButton: 是否显示关闭按钮
  ///   - offeringId: 特定的 Offering ID
  /// - Returns: 配置好的选项
  public static func fullScreen(
    showCloseButton: Bool = true,
    offeringId: String? = nil
  ) -> PaywallPresentationOptions {
    return PaywallPresentationOptions(
      mode: .fullScreen,
      showCloseButton: showCloseButton,
      offeringId: offeringId
    )
  }
}
