//
//  PermissionManager.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/18.
//

import Foundation

/// 统一的权限管理器
/// 负责用户层级计算和权限配置管理
public class PermissionManager {

  // MARK: - 单例

  public static let shared = PermissionManager()

  private init() {}

  // MARK: - 核心方法

  /// 从 EntitlementSet 计算当前用户层级
  /// - Parameter entitlements: 权限集合
  /// - Returns: 用户层级
  public static func currentTier(from entitlements: EntitlementSet) -> UserTier {
    // 检查是否有 pro 权限
    if entitlements.hasEntitlement(.proTier) {
      return .pro
    }

    // 默认为免费用户
    return .free
  }

  /// 获取指定层级的权限配置
  /// - Parameter tier: 用户层级
  /// - Returns: 权限配置
  public static func permissions(for tier: UserTier) -> TierPermissions {
    return TierPermissions.permissions(for: tier)
  }

  /// 从 EntitlementSet 直接获取权限配置
  /// - Parameter entitlements: 权限集合
  /// - Returns: 权限配置
  public static func permissions(from entitlements: EntitlementSet) -> TierPermissions {
    let tier = currentTier(from: entitlements)
    return permissions(for: tier)
  }

  // MARK: - 便利方法

  /// 检查是否需要升级到指定层级
  /// - Parameters:
  ///   - currentTier: 当前层级
  ///   - requiredTier: 需要的层级
  /// - Returns: 是否需要升级
  public static func needsUpgrade(from currentTier: UserTier, to requiredTier: UserTier) -> Bool {
    return currentTier.tierLevel < requiredTier.tierLevel
  }

  /// 检查是否可以使用指定功能
  /// - Parameters:
  ///   - feature: 功能类型
  ///   - entitlements: 权限集合
  /// - Returns: 是否可以使用
  public static func canUseFeature(_ feature: AppFeature, with entitlements: EntitlementSet) -> Bool
  {
    let permissions = permissions(from: entitlements)
    return canUseFeature(feature, with: permissions)
  }

  /// 检查是否可以使用指定功能
  /// - Parameters:
  ///   - feature: 功能类型
  ///   - permissions: 权限配置
  /// - Returns: 是否可以使用
  public static func canUseFeature(_ feature: AppFeature, with permissions: TierPermissions) -> Bool
  {
    switch feature {
    case .removeWatermark:
      return permissions.canRemoveWatermark
    case .hdImageGeneration:
      return permissions.canUseHDImageGeneration
    case .hdVideoGeneration:
      return permissions.canUseHDVideoGeneration
    case .priorityQueue:
      return permissions.hasPriorityQueue
    case .advancedModels:
      return permissions.canUseAdvancedModels
    }
  }

  /// 获取功能所需的最低层级
  /// - Parameter feature: 功能类型
  /// - Returns: 所需的最低层级
  public static func requiredTier(for feature: AppFeature) -> UserTier {
    // 在当前的二层架构中，所有高级功能都需要 Pro 层级
    switch feature {
    case .removeWatermark, .hdImageGeneration, .hdVideoGeneration, .priorityQueue, .advancedModels:
      return .pro
    }
  }
}

// MARK: - 应用功能枚举

/// 应用功能类型
public enum AppFeature: String, CaseIterable {
  /// 去除水印
  case removeWatermark = "remove_watermark"
  /// 高清图片生成
  case hdImageGeneration = "hd_image_generation"
  /// 高清视频生成
  case hdVideoGeneration = "hd_video_generation"
  /// 优先队列
  case priorityQueue = "priority_queue"
  /// 高级模型
  case advancedModels = "advanced_models"

  /// 功能显示名称
  public var displayName: String {
    switch self {
    case .removeWatermark:
      return "去除水印"
    case .hdImageGeneration:
      return "高清图片生成"
    case .hdVideoGeneration:
      return "高清视频生成"
    case .priorityQueue:
      return "优先队列"
    case .advancedModels:
      return "高级AI模型"
    }
  }

  /// 功能描述
  public var description: String {
    switch self {
    case .removeWatermark:
      return "生成的内容不带应用水印"
    case .hdImageGeneration:
      return "生成高分辨率图片"
    case .hdVideoGeneration:
      return "生成高清视频内容"
    case .priorityQueue:
      return "享受优先处理队列，更快生成"
    case .advancedModels:
      return "使用最新最强大的AI模型"
    }
  }
}
