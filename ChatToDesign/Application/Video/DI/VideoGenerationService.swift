//
//  VideoGenerationService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/15.
//

import Combine
import Foundation

public struct VideoGenerationRequest: Codable {
  public let prompt: String
  public let imageUrls: [String]
  public let template: String
}

public struct VideoGenerationResponse: Codable {
  public let taskId: String
}

public protocol VideoGenerationService {
  /// Initiates a video generation request asynchronously.
  ///
  /// This method sends the request to the backend and immediately returns the task ID
  /// that can be used to track the generation progress.
  ///
  /// - Parameter request: Contains the prompt, reference image URLs, and user ID.
  /// - Returns: A `String` representing the unique ID (taskId) of the generation task.
  /// - Throws: `VideoGenerationError` or other network/service errors if the initial request fails.
  func generateVideo(request: VideoGenerationRequest) async throws -> String  // Return String taskId

  /// Observes the status and results of a specific video generation task.
  ///
  /// Subscribing to the returned publisher provides real-time updates on the task's state,
  /// including status changes (pending, processing, succeeded, failed) and final results (generated video URLs or error message).
  ///
  /// - Parameter taskId: The ID of the task document to observe.
  /// - Returns: A Combine `AnyPublisher` that emits `VideoGenerationTask` updates or terminates with an `Error`.
  func observeVideoTask(taskId: String) -> AnyPublisher<VideoGenerationTask, Error>  // Add observation method

  /// Fetches a list of video generation tasks for a specific user, with optional pagination.
  /// - Parameters:
  ///   - userId: The ID of the user whose tasks are to be fetched.
  ///   - limit: The maximum number of tasks to return.
  ///   - startAfterTaskId: Optionally, the ID of the task after which to start fetching (for pagination).
  /// - Returns: An array of `VideoGenerationTask` objects.
  /// - Throws: Errors if the task list cannot be fetched.
  func getTaskList(userId: String, limit: Int, startAfterTaskId: String?) async throws
    -> [VideoGenerationTask]

  /// Observes the task list for a specific user, ordered by creation date (newest first).
  ///
  /// Subscribing to the returned publisher provides real-time updates on the user's task list.
  ///
  /// - Parameters:
  ///   - userId: The ID of the user whose tasks to observe.
  ///   - limit: The maximum number of tasks to include in each emitted list.
  /// - Returns: A Combine `AnyPublisher` that emits an array of `VideoGenerationTask` updates or terminates with an `Error`.
  func observeTaskList(userId: String, limit: Int) -> AnyPublisher<[VideoGenerationTask], Error>

  /// Updates the status of a specific video generation task.
  ///
  /// - Parameters:
  ///   - taskId: The ID of the task to update.
  ///   - status: The new status to set for the task.
  /// - Throws: Errors if the task status cannot be updated.
  func updateTaskStatus(taskId: String, status: TaskStatus) async throws
}

/// 视频生成任务状态（使用与ImageGenerationTask相同的TaskStatus）
public typealias VideoGenerationTaskStatus = TaskStatus

/// 视频生成错误类型
public enum VideoGenerationError: Error, LocalizedError {
  case noInputProvided
  case networkError(Error)
  case invalidResponse
  case taskNotFound
  case unexpectedError(Error)

  public var errorDescription: String? {
    switch self {
    case .noInputProvided:
      return "No prompt or reference images provided"
    case .networkError(let error):
      return "Network error: \(error.localizedDescription)"
    case .invalidResponse:
      return "Invalid response from server"
    case .taskNotFound:
      return "Video generation task not found"
    case .unexpectedError(let error):
      return "Unexpected error: \(error.localizedDescription)"
    }
  }
}
