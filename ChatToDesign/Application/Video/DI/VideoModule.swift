//
//  VideoModule.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import Foundation

/// 视频模块
/// 提供所有视频生成相关功能
public final class VideoModule {
  // MARK: - 公开服务
  
  /// 视频生成服务
  public let videoGenerationService: VideoGenerationService
  
  /// 视频任务仓储
  public let videoTaskRepository: VideoTaskRepository
  
  // MARK: - 内部依赖
  
  private let dependencies: ModuleDependencies
  
  // MARK: - 初始化
  
  /// 初始化视频模块
  /// - Parameter dependencies: 模块依赖
  public init(dependencies: ModuleDependencies) {
    self.dependencies = dependencies
    
    // 创建视频任务仓储
    self.videoTaskRepository = FirestoreVideoTaskRepository()
    
    // 创建视频生成服务
    self.videoGenerationService = DefaultVideoGenerationService(
      apiService: dependencies.apiService,
      videoTaskRepository: videoTaskRepository
    )
  }
}
