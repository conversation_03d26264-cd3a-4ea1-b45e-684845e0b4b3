//
//  DefaultVideoGenerationService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import Combine
import Foundation

/// 默认视频生成服务实现
public final class DefaultVideoGenerationService: VideoGenerationService {

  // MARK: - Properties

  private let apiService: APIService
  private let videoTaskRepository: VideoTaskRepository

  // MARK: - Initialization

  /// 初始化视频生成服务
  /// - Parameters:
  ///   - apiService: API服务
  ///   - videoTaskRepository: 视频任务仓储
  public init(
    apiService: APIService,
    videoTaskRepository: VideoTaskRepository
  ) {
    self.apiService = apiService
    self.videoTaskRepository = videoTaskRepository
  }

  // MARK: - VideoGenerationService Implementation

  /// 生成视频
  /// - Parameter request: 视频生成请求
  /// - Returns: 任务ID
  public func generateVideo(request: VideoGenerationRequest) async throws -> String {
    Logger.info("DefaultVideoGenerationService: Starting video generation with request: \(request)")

    do {
      let response = try await apiService.generateVideo(request: request)
      Logger.info(
        "DefaultVideoGenerationService: Video generation started with taskId: \(response.taskId)")
      return response.taskId
    } catch {
      Logger.error(
        "DefaultVideoGenerationService: Video generation failed: \(error.localizedDescription)")
      throw VideoGenerationError.networkError(error)
    }
  }

  /// 观察视频任务
  /// - Parameter taskId: 任务ID
  /// - Returns: 视频生成任务发布者
  public func observeVideoTask(taskId: String) -> AnyPublisher<VideoGenerationTask, Error> {
    Logger.info("DefaultVideoGenerationService: Starting to observe video task: \(taskId)")
    return videoTaskRepository.observeVideoTask(taskId: taskId)
  }

  /// 获取任务列表
  /// - Parameters:
  ///   - userId: 用户ID
  ///   - limit: 限制数量
  ///   - startAfterTaskId: 开始位置任务ID
  /// - Returns: 视频生成任务列表
  public func getTaskList(userId: String, limit: Int, startAfterTaskId: String?) async throws
    -> [VideoGenerationTask]
  {
    Logger.info("DefaultVideoGenerationService: Getting task list for user: \(userId)")
    return try await videoTaskRepository.getTaskList(
      userId: userId, limit: limit, startAfterTaskId: startAfterTaskId)
  }

  /// 观察任务列表
  /// - Parameters:
  ///   - userId: 用户ID
  ///   - limit: 限制数量
  /// - Returns: 视频生成任务列表发布者
  public func observeTaskList(userId: String, limit: Int) -> AnyPublisher<
    [VideoGenerationTask], Error
  > {
    Logger.info("DefaultVideoGenerationService: Starting to observe task list for user: \(userId)")
    return videoTaskRepository.observeTaskList(userId: userId, limit: limit)
  }

  /// 更新视频生成任务状态
  /// - Parameters:
  ///   - taskId: 任务ID
  ///   - status: 新状态
  public func updateTaskStatus(taskId: String, status: TaskStatus) async throws {
    do {
      try await videoTaskRepository.updateTaskStatus(taskId: taskId, status: status)
      Logger.info("DefaultVideoGenerationService: Task \(taskId) status updated to \(status)")
    } catch {
      Logger.error(
        "DefaultVideoGenerationService: Failed to update task \(taskId) status: \(error.localizedDescription)"
      )
      throw VideoGenerationError.unexpectedError(error)
    }
  }
}
