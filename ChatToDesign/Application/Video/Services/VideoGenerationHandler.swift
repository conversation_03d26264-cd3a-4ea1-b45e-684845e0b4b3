//
//  VideoGenerationHandler.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/15.
//

import Combine
import Foundation
import UIKit

/// 视频生成处理器
/// 负责管理视频生成的整个流程，包括图片上传、任务创建和状态观察
@MainActor
public final class VideoGenerationHandler: ObservableObject {

  // MARK: - Published Properties

  /// 是否正在加载
  @Published public var isLoading = false

  /// 错误信息
  @Published public var errorMessage: String?

  /// 生成的视频URLs
  @Published public var generatedVideoUrls: [String]?

  /// 任务是否创建成功
  @Published public var taskCreatedSuccessfully = false

  /// 当前任务ID
  @Published public var currentTaskId: String?

  // MARK: - Private Properties

  private let videoGenerationService: VideoGenerationService
  private let assetService: AssetApplicationService

  private var currentInitialTask: Task<Void, Never>?
  private var taskSubscription: AnyCancellable?

  // MARK: - Initialization

  /// 初始化视频生成处理器
  /// - Parameters:
  ///   - videoGenerationService: 视频生成服务
  ///   - assetUploadService: 资产上传服务
  public init(
    videoGenerationService: VideoGenerationService,
    assetService: AssetApplicationService
  ) {
    self.videoGenerationService = videoGenerationService
    self.assetService = assetService
  }

  // MARK: - Public Methods

  /// 开始视频生成（使用UIImage）
  /// - Parameters:
  ///   - prompt: 提示词
  ///   - referenceImages: 参考图片
  ///   - template: 模板ID
  ///   - chatId: 聊天ID（可选）
  func startGeneration(
    prompt: String, referenceImages: [UIImage], template: String, chatId: String?
  ) {
    // Convert UIImages to upload them and get URLs, then call the URL-based method
    guard currentInitialTask == nil && taskSubscription == nil else {
      Logger.warning("Video generation or listening already in progress.")
      return
    }

    // Reset state
    self.isLoading = true
    self.errorMessage = nil
    self.generatedVideoUrls = nil
    self.taskCreatedSuccessfully = false
    self.currentTaskId = nil
    taskSubscription?.cancel()  // Cancel any previous observation

    let trimmedPrompt = prompt.trimmingCharacters(in: .whitespacesAndNewlines)
    guard !referenceImages.isEmpty || !trimmedPrompt.isEmpty else {
      handleError(VideoGenerationError.noInputProvided)
      return
    }

    // Start the initial task to upload images and get the taskId
    currentInitialTask = Task {
      do {
        Logger.info("VideoGenerationHandler: Starting generation initial task...")
        // 1. Upload Reference Images if any (respecting cancellation)
        let uploadedImageUrls = try await uploadReferenceImagesIfNeeded(
          images: referenceImages, chatId: chatId)
        try Task.checkCancellation()

        // 2. Call the URL-based generation method
        await MainActor.run {
          self.startGenerationWithImageUrls(
            prompt: trimmedPrompt,
            imageUrls: uploadedImageUrls,
            template: template,
            chatId: chatId
          )
        }
      } catch is CancellationError {
        Logger.info("VideoGenerationHandler: Initial task was cancelled.")
        await MainActor.run {
          self.isLoading = false
          self.currentInitialTask = nil
        }
      } catch {
        Logger.error("VideoGenerationHandler: Initial task failed: \(error.localizedDescription)")
        await MainActor.run {
          self.handleError(error)
          self.currentInitialTask = nil
        }
      }
    }
  }

  /// 开始视频生成（使用图片URLs）- 同步版本，用于 UI 调用
  /// - Parameters:
  ///   - prompt: 提示词
  ///   - imageUrls: 图片URLs
  ///   - template: 模板ID
  ///   - chatId: 聊天ID（可选）
  func startGenerationWithImageUrls(
    prompt: String, imageUrls: [String], template: String, chatId: String?
  ) {
    // 立即设置初始状态
    self.isLoading = true
    self.taskCreatedSuccessfully = false
    self.currentTaskId = nil
    self.errorMessage = nil
    self.generatedVideoUrls = nil

    // 启动异步任务
    Task {
      do {
        try await performGenerationWithImageUrls(
          prompt: prompt,
          imageUrls: imageUrls,
          template: template,
          chatId: chatId
        )
      } catch {
        await MainActor.run {
          self.handleError(error)
        }
      }
    }
  }

  /// 执行视频生成（使用图片URLs）- 异步版本
  /// - Parameters:
  ///   - prompt: 提示词
  ///   - imageUrls: 图片URLs
  ///   - template: 模板ID
  ///   - chatId: 聊天ID（可选）
  private func performGenerationWithImageUrls(
    prompt: String, imageUrls: [String], template: String, chatId: String?
  ) async throws {
    let trimmedPrompt = prompt.trimmingCharacters(in: .whitespacesAndNewlines)
    guard !imageUrls.isEmpty || !trimmedPrompt.isEmpty else {
      throw VideoGenerationError.noInputProvided
    }

    // 确保状态正确设置（创建任务阶段）
    await MainActor.run {
      self.isLoading = true
      self.taskCreatedSuccessfully = false
      self.currentTaskId = nil
      self.errorMessage = nil
      self.generatedVideoUrls = nil
    }

    // Call Video Generation Service to get Task ID
    Logger.info("VideoGenerationHandler: Calling video generation service to get taskId...")

    let taskId: String = try await videoGenerationService.generateVideo(
      request: VideoGenerationRequest(
        prompt: trimmedPrompt,
        imageUrls: imageUrls,
        template: template
      )
    )

    Logger.info("VideoGenerationHandler: Received taskId: \(taskId)")
    try Task.checkCancellation()

    // Mark task creation as successful and start observing
    await MainActor.run {
      self.taskCreatedSuccessfully = true
      self.currentTaskId = taskId
      // 注意：isLoading 保持为 true，因为现在开始生成阶段
      self.startObservingTask(taskId: taskId)
      self.currentInitialTask = nil  // Initial task completed, observation started
    }
  }

  /// 取消当前生成任务
  func cancelGeneration() {
    Logger.info("VideoGenerationHandler: Cancelling video generation...")

    // Cancel the initial task if it's running
    currentInitialTask?.cancel()
    currentInitialTask = nil

    // Cancel task observation
    taskSubscription?.cancel()
    taskSubscription = nil

    // Reset state
    self.isLoading = false
    self.errorMessage = nil
    self.currentTaskId = nil
    self.taskCreatedSuccessfully = false

    Logger.info("VideoGenerationHandler: Video generation cancelled.")
  }

  /// 重新生成视频
  /// - Parameters:
  ///   - prompt: 新的提示词
  ///   - imageUrls: 图片URLs
  ///   - template: 模板ID
  ///   - chatId: 聊天ID（可选）
  func regenerateVideo(prompt: String, imageUrls: [String], template: String, chatId: String?) {
    Logger.info("VideoGenerationHandler: Starting video regeneration...")

    // Cancel any existing generation
    cancelGeneration()

    // Start new generation
    startGenerationWithImageUrls(
      prompt: prompt,
      imageUrls: imageUrls,
      template: template,
      chatId: chatId
    )
  }

  // MARK: - Private Methods

  private func uploadReferenceImagesIfNeeded(images: [UIImage], chatId: String?) async throws
    -> [String]
  {
    guard !images.isEmpty else {
      return []
    }
    Logger.info("VideoGenerationHandler: Uploading \(images.count) reference images...")
    let uploadedImageUrls = try await uploadReferenceImages(
      images, chatId: chatId ?? "defaultChatId")  // Provide a default if nil
    Logger.info(
      "VideoGenerationHandler: Finished uploading reference images: \(uploadedImageUrls)")
    return uploadedImageUrls
  }

  // Helper for uploading images concurrently
  private func uploadReferenceImages(_ images: [UIImage], chatId: String) async throws -> [String] {
    try await withThrowingTaskGroup(of: String.self) { group in
      var urls: [String] = []

      for (index, image) in images.enumerated() {
        try Task.checkCancellation()  // Check for cancellation before starting upload

        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
          Logger.warning(
            "VideoGenerationHandler: Warning: Could not convert image \(index) to JPEG data.")  // Log image conversion warning
          continue  // Skip this image
        }

        let fileName = "\(UUID().uuidString).jpg"
        let mimeType = "image/jpeg"

        Logger.info(
          "VideoGenerationHandler: Adding upload task for image \(index) with fileName: \(fileName)"
        )  // Log adding upload task
        group.addTask { [assetService] in  // Capture assetUploadService
          // Check cancellation *inside* the task as well
          try Task.checkCancellation()
          Logger.info("VideoGenerationHandler: Executing upload for \(fileName)...")  // Log executing upload

          // 设置上传前缀，使用chatId作为路径的一部分
          let prefix = "uploads/user_assets"

          // 设置标签和元数据
          let tags = ["video", "reference", "user_asset"]
          let metadata: [String: AnyCodable] = [
            "purpose": AnyCodable("reference_image"),
            "originalFileName": AnyCodable(fileName),
          ]

          // Use the captured assetService
          let assetResponse = try await assetService.uploadFileWithAsset(
            data: imageData,
            mimeType: mimeType,
            fileName: fileName,
            prefix: prefix,
            tags: tags,
            description: "Reference image for video generation",
            metadata: metadata,
            isPublic: false
          )

          Logger.info(
            "VideoGenerationHandler: Upload finished for \(fileName). URL: \(assetResponse.url)"
          )  // Log upload finished
          return assetResponse.url
        }
      }

      // Collect results
      for try await urlString in group {
        try Task.checkCancellation()  // Check for cancellation while collecting results
        urls.append(urlString)
      }
      Logger.info("VideoGenerationHandler: Collected all uploaded URLs: \(urls)")  // Log collection finished
      return urls
    }
  }

  /// 开始观察任务状态
  /// - Parameter taskId: 任务ID
  private func startObservingTask(taskId: String) {
    Logger.info("VideoGenerationHandler: Starting to observe task: \(taskId)")

    taskSubscription = videoGenerationService.observeVideoTask(taskId: taskId)
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          switch completion {
          case .finished:
            Logger.info("VideoGenerationHandler: Task observation completed successfully")
          case .failure(let error):
            Logger.error(
              "VideoGenerationHandler: Task observation failed: \(error.localizedDescription)")
            self?.handleError(error)
          }
        },
        receiveValue: { [weak self] task in
          self?.handleTaskUpdate(task)
        }
      )
  }

  /// 处理任务更新
  /// - Parameter task: 视频生成任务
  private func handleTaskUpdate(_ task: VideoGenerationTask) {
    Logger.debug(
      "VideoGenerationHandler: Task update - Status: \(task.status), Progress: \(task.progress ?? 0)"
    )

    switch task.status {
    case .pending, .processing:
      // 任务进行中，保持加载状态
      self.isLoading = true
      self.errorMessage = nil

    case .succeeded:
      // 任务成功完成
      Logger.info("VideoGenerationHandler: Video generation completed successfully")
      self.isLoading = false
      self.errorMessage = nil
      self.generatedVideoUrls = task.resultVideoUrls

      // 停止观察
      taskSubscription?.cancel()
      taskSubscription = nil

    case .failed, .no_result:
      // 任务失败
      Logger.error(
        "VideoGenerationHandler: Video generation failed: \(task.errorMessage ?? "Unknown error")")
      let error = VideoGenerationError.unexpectedError(
        NSError(
          domain: "VideoGenerationError",
          code: -1,
          userInfo: [NSLocalizedDescriptionKey: task.errorMessage ?? "Video generation failed"]
        )
      )
      handleError(error)

      // 停止观察
      taskSubscription?.cancel()
      taskSubscription = nil

    case .deleted:
      // 任务已删除
      Logger.info("VideoGenerationHandler: Video generation task was deleted")
      self.isLoading = false
      self.errorMessage = "Video generation task was deleted"

      // 停止观察
      taskSubscription?.cancel()
      taskSubscription = nil

    case .unknown:
      // 未知状态
      Logger.warning("VideoGenerationHandler: Video generation has unknown status")
      self.isLoading = false
      self.errorMessage = "Video generation status unknown"

      // 停止观察
      taskSubscription?.cancel()
      taskSubscription = nil
    }
  }

  /// 处理错误
  /// - Parameter error: 错误
  private func handleError(_ error: Error) {
    Logger.error("VideoGenerationHandler: Error occurred: \(error.localizedDescription)")

    self.isLoading = false
    self.taskCreatedSuccessfully = false

    if let videoError = error as? VideoGenerationError {
      self.errorMessage = videoError.localizedDescription
    } else {
      self.errorMessage = error.localizedDescription
    }

    // 清理任务
    currentInitialTask?.cancel()
    currentInitialTask = nil
    taskSubscription?.cancel()
    taskSubscription = nil
  }
}
