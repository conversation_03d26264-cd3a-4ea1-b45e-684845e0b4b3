//
//  ForkChatUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 分叉聊天用例
public protocol ForkChatUseCase {
  /// 执行分叉聊天
  /// - Parameters:
  ///   - chatId: 源聊天ID
  ///   - userId: 用户ID
  ///   - title: 新标题
  /// - Returns: 分叉聊天ID
  func execute(chatId: String, userId: String, title: String?) async throws -> String
}

// MARK: - 分叉聊天用例实现
public final class DefaultForkChatUseCase: ForkChatUseCase {
    private let chatRepository: ChatRepository
    
    public init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    public func execute(chatId: String, userId: String, title: String?) async throws -> String {
        // 获取原始聊天
        let sourceChat = try await chatRepository.getChat(id: chatId)
        
        // 检查用户是否有访问权限
        guard sourceChat.canAccess(by: userId) else {
            throw ChatError.accessDenied
        }
        
        // 创建分叉聊天
        let forkedChat = Chat.createFork(
            fromChat: sourceChat,
            userId: userId,
            title: title
        )
        
        // 保存分叉聊天
        let forkedChatId = try await chatRepository.saveChat(forkedChat)
        
        // 增加原始聊天的分叉计数
        try await chatRepository.incrementForkCount(chatId: chatId)
        
        return forkedChatId
    }
}
