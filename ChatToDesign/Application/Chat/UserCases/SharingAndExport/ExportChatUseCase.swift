//
//  ExportChatUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 导出聊天用例
public protocol ExportChatUseCase {
  /// 执行导出聊天
  /// - Parameters:
  ///   - chatId: 聊天ID
  ///   - format: 导出格式  
  /// 
  func execute(chatId: String, format: ExportFormat) async throws -> Data
}

/// 导出格式
public enum ExportFormat {
  case markdown
  case html
}

// MARK: - 导出聊天用例实现
public final class DefaultExportChatUseCase: ExportChatUseCase {
    private let chatRepository: ChatRepository
    private let messageRepository: MessageRepository
    
    public init(chatRepository: ChatRepository, messageRepository: MessageRepository) {
        self.chatRepository = chatRepository
        self.messageRepository = messageRepository
    }
    
    public func execute(chatId: String, format: ExportFormat) async throws -> Data {
        // 获取聊天
        let chat = try await chatRepository.getChat(id: chatId)
        
        // 获取所有消息
        let messages = try await messageRepository.getMessages(
            forChatId: chatId,
            limit: 1000, // 设置一个合理的上限
            startAfter: nil
        )
        
        // 根据导出格式生成数据
        switch format {
        case .markdown:
            return try generateMarkdown(chat: chat, messages: messages)
        case .html:
            return try generateHTML(chat: chat, messages: messages)
        }
    }
    
    private func generateMarkdown(chat: Chat, messages: [Message]) throws -> Data {
        var markdown = "# \(chat.title)\n\n"
        
        if let description = chat.description {
            markdown += "\(description)\n\n"
        }
        
        markdown += "---\n\n"
        
        for message in messages {
            let sender = message.type == .user ? "用户" : "AI"
            markdown += "**\(sender)**:\n\n"
            
            if message.content.styleType == .markdown {
                markdown += message.content.text + "\n\n"
            } else {
                markdown += "```\n\(message.content.text)\n```\n\n"
            }
        }
        
        guard let data = markdown.data(using: .utf8) else {
            throw ChatError.invalidOperation
        }
        
        return data
    }
    
    private func generateHTML(chat: Chat, messages: [Message]) throws -> Data {
        var html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>\(chat.title)</title>
            <meta charset="utf-8">
            <style>
                body { font-family: system-ui, -apple-system, sans-serif; line-height: 1.5; max-width: 800px; margin: 0 auto; padding: 20px; }
                .message { margin-bottom: 20px; }
                .user { background-color: #f1f1f1; border-radius: 10px; padding: 10px; }
                .ai { background-color: #e3f2fd; border-radius: 10px; padding: 10px; }
                h1 { color: #333; }
                pre { background-color: #f8f8f8; padding: 10px; border-radius: 5px; overflow-x: auto; }
            </style>
        </head>
        <body>
            <h1>\(chat.title)</h1>
        """
        
        if let description = chat.description {
            html += "<p>\(description)</p>"
        }
        
        html += "<hr>"
        
        for message in messages {
            let className = message.type == .user ? "user" : "ai"
            let sender = message.type == .user ? "用户" : "AI"
            
            html += "<div class='message \(className)'>"
            html += "<strong>\(sender):</strong><br>"
            
            if message.content.styleType == .markdown {
                // 注意：实际应用中应使用Markdown转HTML的库
                // 这里简化处理
                html += "<pre>\(message.content.text)</pre>"
            } else {
                html += "<p>\(message.content.text.replacingOccurrences(of: "\n", with: "<br>"))</p>"
            }
            
            html += "</div>"
        }
        
        html += """
        </body>
        </html>
        """
        
        guard let data = html.data(using: .utf8) else {
            throw ChatError.invalidOperation
        }
        
        return data
    }
}
