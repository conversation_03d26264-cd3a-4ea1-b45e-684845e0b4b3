//
//  ShareChatUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 分享聊天用例
public protocol ShareChatUseCase {
  /// 执行分享聊天
  /// - Parameters:
  ///   - chatId: 聊天ID
  ///   - visibility: 可见性
  ///   - sharedWithUserIds: 共享用户ID列表
  func execute(chatId: String, visibility: ChatVisibility, sharedWithUserIds: [String]?) async throws
}

// MARK: - 分享聊天用例实现
public final class DefaultShareChatUseCase: ShareChatUseCase {
    private let chatRepository: ChatRepository
    
    public init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    public func execute(chatId: String, visibility: ChatVisibility, sharedWithUserIds: [String]?) async throws {
        // 验证如果是共享可见性，必须提供用户ID列表
        if visibility == .shared && (sharedWithUserIds == nil || sharedWithUserIds!.isEmpty) {
            throw ChatError.invalidOperation
        }
        
        try await chatRepository.updateChatVisibility(
            chatId: chatId,
            visibility: visibility,
            sharedWithUserIds: sharedWithUserIds
        )
    }
}