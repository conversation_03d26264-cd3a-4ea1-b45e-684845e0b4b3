//
//  SearchChatsUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 搜索聊天用例
public protocol SearchChatsUseCase {
  /// 执行搜索聊天
  /// - Parameters:
  ///   - query: 搜索关键词
  ///   - userId: 用户ID
  /// - Returns: 匹配的聊天列表
  func execute(query: String, userId: String) async throws -> [Chat]
}

// MARK: - 搜索聊天用例实现
public final class DefaultSearchChatsUseCase: SearchChatsUseCase {
    private let chatRepository: ChatRepository
    
    public init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    public func execute(query: String, userId: String) async throws -> [Chat] {
        // 获取用户的所有聊天
        let chats = try await chatRepository.getUserChats(
            userId: userId,
            limit: 100, // 设置一个合理的上限
            startAfter: nil,
            status: nil
        )
        
        // 客户端过滤
        // 注意：实际应用中应该在仓储层实现搜索功能以提高效率
        if query.isEmpty {
            return chats
        }
        
        let lowercaseQuery = query.lowercased()
        return chats.filter { chat in
            chat.title.lowercased().contains(lowercaseQuery) ||
            chat.description?.lowercased().contains(lowercaseQuery) == true
        }
    }
}