//
//  CreateChatUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 创建聊天用例
public protocol CreateChatUseCase {
  /// 执行创建聊天
  /// - Parameter chat: 要创建的聊天
  /// - Returns: 创建的聊天ID
  func execute(chat: Chat) async throws -> String
}

// MARK: - 创建聊天用例实现
public final class DefaultCreateChatUseCase: CreateChatUseCase {
    private let chatRepository: ChatRepository
    
    public init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    public func execute(chat: Chat) async throws -> String {
        return try await chatRepository.saveChat(chat)
    }
}