//
//  DeleteChatUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 删除聊天用例
public protocol DeleteChatUseCase {
  /// 执行删除聊天
  /// - Parameter id: 聊天ID
  func execute(id: String) async throws
}

// MARK: - 删除聊天用例实现
public final class DefaultDeleteChatUseCase: DeleteChatUseCase {
    private let chatRepository: ChatRepository
    
    public init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    public func execute(id: String) async throws {
        try await chatRepository.deleteChat(id: id)
    }
}