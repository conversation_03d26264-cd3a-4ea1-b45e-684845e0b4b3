//
//  GetChatListUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 获取聊天列表用例
public protocol GetChatListUseCase {
  /// 执行获取聊天列表
  /// - Parameters:
  ///   - userId: 用户ID
  ///   - limit: 限制数量
  ///   - startAfter: 开始位置
  ///   - status: 聊天状态过滤
  /// - Returns: 聊天列表
  func execute(
    userId: String,
    limit: Int,
    startAfter: String?,
    status: ChatStatus?
  ) async throws -> [Chat]
}

// MARK: - 获取聊天列表用例实现
public final class DefaultGetChatListUseCase: GetChatListUseCase {
    private let chatRepository: ChatRepository
    
    public init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    public func execute(
        userId: String,
        limit: Int,
        startAfter: String?,
        status: ChatStatus?
    ) async throws -> [Chat] {
        return try await chatRepository.getUserChats(
            userId: userId,
            limit: limit,
            startAfter: startAfter,
            status: status
        )
    }
}