//
//  GetChatUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 获取聊天用例
public protocol GetChatUseCase {
  /// 执行获取聊天
  /// - Parameter id: 聊天ID
  /// - Returns: 聊天对象
  func execute(id: String) async throws -> Chat
}

// MARK: - 获取聊天用例实现
public final class DefaultGetChatUseCase: GetChatUseCase {
    private let chatRepository: ChatRepository
    
    public init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    public func execute(id: String) async throws -> Chat {
        return try await chatRepository.getChat(id: id)
    }
}