//
//  UpdateChatUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 更新聊天用例
public protocol UpdateChatUseCase {
  /// 执行更新聊天
  /// - Parameter chat: 要更新的聊天
  func execute(chat: Chat) async throws
  
  /// 更新聊天标题
  /// - Parameters:
  ///   - chatId: 聊天ID
  ///   - title: 新标题
  func updateTitle(chatId: String, title: String) async throws
  
  /// 更新聊天描述
  /// - Parameters:
  ///   - chatId: 聊天ID
  ///   - description: 新描述
  func updateDescription(chatId: String, description: String?) async throws
}

// MARK: - 更新聊天用例实现
public final class DefaultUpdateChatUseCase: UpdateChatUseCase {
    private let chatRepository: ChatRepository
    
    public init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    public func execute(chat: Chat) async throws {
        try await chatRepository.updateChat(chat)
    }
    
    public func updateTitle(chatId: String, title: String) async throws {
        try await chatRepository.updateChatTitle(chatId: chatId, title: title)
    }
    
    public func updateDescription(chatId: String, description: String?) async throws {
        try await chatRepository.updateChatDescription(chatId: chatId, description: description)
    }
}