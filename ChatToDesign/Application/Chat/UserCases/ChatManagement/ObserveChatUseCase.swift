//
//  ObserveChatUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 观察聊天用例
public protocol ObserveChatUseCase {
  /// 执行观察聊天
  /// - Parameter id: 聊天ID
  /// - Returns: 聊天对象发布者
  func execute(id: String) -> AnyPublisher<Chat, Error>
}

// MARK: - 观察聊天用例实现
public final class DefaultObserveChatUseCase: ObserveChatUseCase {
    private let chatRepository: ChatRepository
    
    public init(chatRepository: ChatRepository) {
        self.chatRepository = chatRepository
    }
    
    public func execute(id: String) -> AnyPublisher<Chat, Error> {
        // 假设 ChatRepository 有一个 observeChat 方法
        // 实际实现可能需要根据 ChatRepository 的可用方法来调整
        let subject = PassthroughSubject<Chat, Error>()
        
        Task {
            do {
                let chat = try await chatRepository.getChat(id: id)
                subject.send(chat)
            } catch {
                subject.send(completion: .failure(error))
            }
        }
        
        return subject.eraseToAnyPublisher()
    }
}