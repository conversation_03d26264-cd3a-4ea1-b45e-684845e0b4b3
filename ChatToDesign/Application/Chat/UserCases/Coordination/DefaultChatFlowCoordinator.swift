//
//  DefaultChatFlowCoordinator.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 默认聊天流程协调器实现
public final class DefaultChatFlowCoordinator: ChatFlowCoordinator {
  private let createChatUseCase: CreateChatUseCase
  private let getChatUseCase: GetChatUseCase
  private let updateChatUseCase: UpdateChatUseCase
  private let forkChatUseCase: ForkChatUseCase
  private let shareChatUseCase: ShareChatUseCase
  
  public init(
    createChatUseCase: CreateChatUseCase,
    getChatUseCase: GetChatUseCase,
    updateChatUseCase: UpdateChatUseCase,
    forkChatUseCase: ForkChatUseCase,
    shareChatUseCase: ShareChatUseCase
  ) {
    self.createChatUseCase = createChatUseCase
    self.getChatUseCase = getChatUseCase
    self.updateChatUseCase = updateChatUseCase
    self.forkChatUseCase = forkChatUseCase
    self.shareChatUseCase = shareChatUseCase
  }
  
  public func forkChat(chatId: String, userId: String, title: String?) async throws -> String {
    return try await forkChatUseCase.execute(chatId: chatId, userId: userId, title: title)
  }
  
  public func shareChat(chatId: String, visibility: ChatVisibility, sharedWithUserIds: [String]?) async throws {
    try await shareChatUseCase.execute(chatId: chatId, visibility: visibility, sharedWithUserIds: sharedWithUserIds)
  }
  
  public func archiveChat(chatId: String) async throws {
    let chat = try await getChatUseCase.execute(id: chatId)
    
    guard chat.canBeArchived() else {
      throw ChatError.invalidOperation
    }
    
    var updatedChat = chat
    updatedChat.status = .archived
    
    try await updateChatUseCase.execute(chat: updatedChat)
  }
}