//
//  ChatFlowCoordinator.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 聊天流程协调器
/// 负责协调复杂的聊天流程
public protocol ChatFlowCoordinator {
  /// 分叉聊天
  /// - Parameters:
  ///   - chatId: 源聊天ID
  ///   - userId: 用户ID
  ///   - title: 新标题
  /// - Returns: 分叉聊天ID
  func forkChat(chatId: String, userId: String, title: String?) async throws -> String
  
  /// 分享聊天
  /// - Parameters:
  ///   - chatId: 聊天ID
  ///   - visibility: 可见性
  ///   - sharedWithUserIds: 共享用户ID列表
  func shareChat(chatId: String, visibility: ChatVisibility, sharedWithUserIds: [String]?) async throws
  
  /// 归档聊天
  /// - Parameter chatId: 聊天ID
  func archiveChat(chatId: String) async throws
}