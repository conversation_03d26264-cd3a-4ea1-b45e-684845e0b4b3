//
//  ObserveMessagesUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 观察消息列表用例
public protocol ObserveMessagesUseCase {
  /// 执行观察消息列表
  /// - Parameter chatId: 聊天ID
  /// - Returns: 消息列表发布者
  func execute(chatId: String) -> AnyPublisher<[Message], Error>
}

// MARK: - 观察消息列表用例实现
public final class DefaultObserveMessagesUseCase: ObserveMessagesUseCase {
    private let messageRepository: MessageRepository
    
    public init(messageRepository: MessageRepository) {
        self.messageRepository = messageRepository
    }
    
    public func execute(chatId: String) -> AnyPublisher<[Message], Error> {
        return messageRepository.observeMessages(chatId: chatId)
    }
}