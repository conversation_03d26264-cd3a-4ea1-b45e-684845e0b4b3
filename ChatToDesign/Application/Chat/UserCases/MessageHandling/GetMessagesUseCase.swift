//
//  GetMessagesUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 获取消息列表用例
public protocol GetMessagesUseCase {
  /// 执行获取消息列表
  /// - Parameters:
  ///   - chatId: 聊天ID
  ///   - limit: 限制数量
  ///   - startAfter: 开始位置
  /// - Returns: 消息列表
  func execute(chatId: String, limit: Int, startAfter: String?) async throws -> [Message]
}

// MARK: - 获取消息列表用例实现
public final class DefaultGetMessagesUseCase: GetMessagesUseCase {
    private let messageRepository: MessageRepository
    
    public init(messageRepository: MessageRepository) {
        self.messageRepository = messageRepository
    }
    
    public func execute(chatId: String, limit: Int, startAfter: String?) async throws -> [Message] {
        return try await messageRepository.getMessages(
            forChatId: chatId,
            limit: limit,
            startAfter: startAfter
        )
    }
}