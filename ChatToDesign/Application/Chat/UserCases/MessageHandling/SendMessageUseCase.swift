//
//  SendMessageUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 发送消息用例
public protocol SendMessageUseCase {
  /// 执行发送消息
  /// - Parameters:
  ///   - message: 要发送的消息
  /// - Returns: 发送的消息
  func execute(message: Message) async throws -> Message
}

// MARK: - 发送消息用例实现
public final class DefaultSendMessageUseCase: SendMessageUseCase {
    private let messageRepository: MessageRepository
    private let chatRepository: ChatRepository
    
    public init(messageRepository: MessageRepository, chatRepository: ChatRepository) {
        self.messageRepository = messageRepository
        self.chatRepository = chatRepository
    }
    
    public func execute(message: Message) async throws -> Message {
        // 确认聊天存在且可编辑
        let chat = try await chatRepository.getChat(id: message.chatId)
        
        guard chat.status == .active else {
            throw ChatError.chatArchived
        }
        
        guard chat.canEdit(by: message.sender.id) else {
            throw ChatError.accessDenied
        }
        
        // 保存消息
        let messageId = try await messageRepository.saveMessage(message)
        
        // 更新消息状态
        try await messageRepository.updateMessageStatus(
            id: messageId,
            chatId: message.chatId,
            status: .sent
        )
        
        // 增加聊天消息计数
        try await chatRepository.incrementMessageCount(chatId: message.chatId)
        
        // 获取已更新的消息
        let updatedMessage = try await messageRepository.getMessage(id: messageId, chatId: message.chatId)
        
        return updatedMessage
    }
}