//
//  GetAIReplyUseCase.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

/// 获取AI回复用例
public protocol GetAIReplyUseCase {
  /// 执行获取AI回复
  /// - Parameters:
  ///   - forMessage: 用户消息
  ///   - chatId: 聊天ID
  ///   - systemPrompt: 系统提示，可选
  /// - Returns: AI回复消息
  func execute(forMessage: Message, chatId: String, systemPrompt: String?) async throws -> Message
  
  /// 停止生成
  /// - Parameter chatId: 聊天ID
  func stopGeneration(chatId: String) async throws
}

// MARK: - 获取AI回复用例实现
public final class DefaultGetAIReplyUseCase: GetAIReplyUseCase {
    private let messageRepository: MessageRepository
    private let chatRepository: ChatRepository
    private let apiService: APIService
    
    public init(
        messageRepository: MessageRepository, 
        chatRepository: ChatRepository,
        apiService: APIService
    ) {
        self.messageRepository = messageRepository
        self.chatRepository = chatRepository
        self.apiService = apiService
    }
    
    public func execute(forMessage: Message, chatId: String, systemPrompt: String?) async throws -> Message {
        // 获取聊天
        _ = try await chatRepository.getChat(id: chatId)
        
        // 创建AI回复占位消息
        let aiReply = Message.createAIMessage(
            chatId: chatId,
            text: "..." // 占位文本
        )
        
        // 保存AI回复占位消息
        let aiReplyId = try await messageRepository.saveMessage(aiReply)
        
        // 更新消息状态为接收中
        try await messageRepository.updateMessageStatus(
            id: aiReplyId,
            chatId: chatId,
            status: .receiving
        )
        
        do {
            // 调用API服务处理消息
            try await apiService.processMessage(chatId: chatId, messageId: forMessage.id)
            
            // 获取更新后的AI回复消息
            // 注意：由于后端会直接写入Firestore，我们只需要获取最新的消息
            let updatedMessage = try await messageRepository.getMessage(id: aiReplyId, chatId: chatId)
            return updatedMessage
        } catch {
            // 处理错误情况
            Logger.error("处理AI回复失败: \(error)")
            
            // 更新消息状态为错误
            try await messageRepository.updateMessageStatus(
                id: aiReplyId,
                chatId: chatId,
                status: .error
            )
            
            // 获取已更新为错误状态的消息
            let errorMessage = try await messageRepository.getMessage(id: aiReplyId, chatId: chatId)
            throw error
        }
    }

    public func stopGeneration(chatId: String) async throws {
        // TODO: 实现停止生成的功能
        // 这需要后端支持，目前可能需要调用一个取消处理的API
    }
}
