// ChatModule.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Foundation
import Combine

// MARK: - 聊天服务接口


/// 聊天模块
/// 提供所有聊天相关功能
public final class ChatModule {
    // MARK: - 公开服务
    
    /// 聊天服务
    public let chatService: ChatService
    
    /// 聊天流程协调器
    public let chatFlowCoordinator: ChatFlowCoordinator
    
    // MARK: - 内部依赖
    
    private let dependencies: ModuleDependencies
    
    // MARK: - 初始化
    
    /// 初始化聊天模块
    /// - Parameter dependencies: 模块依赖
    public init(dependencies: ModuleDependencies) {
        self.dependencies = dependencies
        
        // 创建用例
        let createChatUseCase = DefaultCreateChatUseCase(
            chatRepository: dependencies.chatRepository
        )
        
        let getChatUseCase = DefaultGetChatUseCase(
            chatRepository: dependencies.chatRepository
        )
        
        let getChatListUseCase = DefaultGetChatListUseCase(
            chatRepository: dependencies.chatRepository
        )
        
        let observeChatUseCase = DefaultObserveChatUseCase(
            chatRepository: dependencies.chatRepository
        )
        
        let observeMessagesUseCase = DefaultObserveMessagesUseCase(
            messageRepository: dependencies.messageRepository
        )
        
        let sendMessageUseCase = DefaultSendMessageUseCase(
            messageRepository: dependencies.messageRepository,
            chatRepository: dependencies.chatRepository
        )
        
        let getMessagesUseCase = DefaultGetMessagesUseCase(
            messageRepository: dependencies.messageRepository
        )
        
        let updateChatUseCase = DefaultUpdateChatUseCase(
            chatRepository: dependencies.chatRepository
        )
        
        let deleteChatUseCase = DefaultDeleteChatUseCase(
            chatRepository: dependencies.chatRepository
        )
        
        let forkChatUseCase = DefaultForkChatUseCase(
            chatRepository: dependencies.chatRepository
        )
        
        let shareChatUseCase = DefaultShareChatUseCase(
            chatRepository: dependencies.chatRepository
        )
        
        let searchChatsUseCase = DefaultSearchChatsUseCase(
            chatRepository: dependencies.chatRepository
        )
        
        let exportChatUseCase = DefaultExportChatUseCase(
            chatRepository: dependencies.chatRepository,
            messageRepository: dependencies.messageRepository
        )

        let getAIReplyUseCase = DefaultGetAIReplyUseCase(
            messageRepository: dependencies.messageRepository,
            chatRepository: dependencies.chatRepository,
            apiService: dependencies.apiService
        )
        
        // 创建协调器
        let chatFlowCoordinatorImpl = DefaultChatFlowCoordinator(
            createChatUseCase: createChatUseCase,
            getChatUseCase: getChatUseCase,
            updateChatUseCase: updateChatUseCase,
            forkChatUseCase: forkChatUseCase,
            shareChatUseCase: shareChatUseCase
        )
        self.chatFlowCoordinator = chatFlowCoordinatorImpl
        
        // 创建统一的聊天服务实现
        let chatServiceImpl = DefaultChatService(
            createChatUseCase: createChatUseCase,
            getChatUseCase: getChatUseCase,
            getChatListUseCase: getChatListUseCase,
            observeChatUseCase: observeChatUseCase,
            observeMessagesUseCase: observeMessagesUseCase,
            sendMessageUseCase: sendMessageUseCase,
            getMessagesUseCase: getMessagesUseCase,
            updateChatUseCase: updateChatUseCase,
            deleteChatUseCase: deleteChatUseCase,
            searchChatsUseCase: searchChatsUseCase,
            exportChatUseCase: exportChatUseCase,
            chatFlowCoordinator: chatFlowCoordinator,
            getAIReplyUseCase: getAIReplyUseCase
        )
        
        // 模块持有协议类型引用
        self.chatService = chatServiceImpl
    }
}

// MARK: - 默认服务实现
fileprivate final class DefaultChatService: ChatService {
    // MARK: - 依赖注入
    
    private let createChatUseCase: CreateChatUseCase
    private let getChatUseCase: GetChatUseCase
    private let getChatListUseCase: GetChatListUseCase
    private let observeChatUseCase: ObserveChatUseCase
    private let observeMessagesUseCase: ObserveMessagesUseCase
    private let sendMessageUseCase: SendMessageUseCase
    private let getMessagesUseCase: GetMessagesUseCase
    private let updateChatUseCase: UpdateChatUseCase
    private let deleteChatUseCase: DeleteChatUseCase
    private let searchChatsUseCase: SearchChatsUseCase
    private let exportChatUseCase: ExportChatUseCase
    private let chatFlowCoordinator: ChatFlowCoordinator
    private let getAIReplyUseCase: GetAIReplyUseCase

    
    // MARK: - 初始化
    
    init(
        createChatUseCase: CreateChatUseCase,
        getChatUseCase: GetChatUseCase,
        getChatListUseCase: GetChatListUseCase,
        observeChatUseCase: ObserveChatUseCase,
        observeMessagesUseCase: ObserveMessagesUseCase,
        sendMessageUseCase: SendMessageUseCase,
        getMessagesUseCase: GetMessagesUseCase,
        updateChatUseCase: UpdateChatUseCase,
        deleteChatUseCase: DeleteChatUseCase,
        searchChatsUseCase: SearchChatsUseCase,
        exportChatUseCase: ExportChatUseCase,
        chatFlowCoordinator: ChatFlowCoordinator,
        getAIReplyUseCase: GetAIReplyUseCase

    ) {
        self.createChatUseCase = createChatUseCase
        self.getChatUseCase = getChatUseCase
        self.getChatListUseCase = getChatListUseCase
        self.observeChatUseCase = observeChatUseCase
        self.observeMessagesUseCase = observeMessagesUseCase
        self.sendMessageUseCase = sendMessageUseCase
        self.getMessagesUseCase = getMessagesUseCase
        self.updateChatUseCase = updateChatUseCase
        self.deleteChatUseCase = deleteChatUseCase
        self.searchChatsUseCase = searchChatsUseCase
        self.exportChatUseCase = exportChatUseCase
        self.chatFlowCoordinator = chatFlowCoordinator
        self.getAIReplyUseCase = getAIReplyUseCase
    }
    
    // MARK: - 会话相关功能实现
    
    func getChat(id: String) async throws -> Chat {
        return try await getChatUseCase.execute(id: id)
    }
    
    func observeChat(id: String) -> AnyPublisher<Chat, Error> {
        return observeChatUseCase.execute(id: id)
    }
    
    // MARK: - 消息相关功能实现
    
    func sendMessage(message: Message) async throws -> Message {
        return try await sendMessageUseCase.execute(message: message)
    }
    
    func getMessages(chatId: String, limit: Int, startAfter: String?) async throws -> [Message] {
        return try await getMessagesUseCase.execute(chatId: chatId, limit: limit, startAfter: startAfter)
    }
    
    func observeMessages(chatId: String) -> AnyPublisher<[Message], Error> {
        return observeMessagesUseCase.execute(chatId: chatId)
    }

    func getAIReply(forMessage message: Message, chatId: String, systemPrompt: String?) async throws -> Message {
        return try await getAIReplyUseCase.execute(forMessage: message, chatId: chatId, systemPrompt: systemPrompt)
    }

    func stopGeneration(chatId: String) async throws {
        return try await getAIReplyUseCase.stopGeneration(chatId: chatId)
    }   
    
    // MARK: - 管理相关功能实现
    
    func createChat(
        userId: String,
        title: String,
        description: String? = nil,
        parameters: ChatParameters? = nil
    ) async throws -> String {
        let chat = Chat.create(
            userId: userId,
            title: title,
            description: description,
            parameters: parameters
        )
        return try await createChatUseCase.execute(chat: chat)
    }
    
    func getChatList(
        forUser userId: String,
        limit: Int = 20,
        startAfter: String? = nil,
        status: ChatStatus? = nil
    ) async throws -> [Chat] {
        return try await getChatListUseCase.execute(
            userId: userId,
            limit: limit,
            startAfter: startAfter,
            status: status
        )
    }
    
    func updateChat(_ chat: Chat) async throws {
        try await updateChatUseCase.execute(chat: chat)
    }
    
    func updateChatTitle(chatId: String, title: String) async throws {
        try await updateChatUseCase.updateTitle(chatId: chatId, title: title)
    }
    
    func updateChatDescription(chatId: String, description: String?) async throws {
        try await updateChatUseCase.updateDescription(chatId: chatId, description: description)
    }
    
    func deleteChat(id: String) async throws {
        try await deleteChatUseCase.execute(id: id)
    }
    
    func searchChats(query: String, userId: String) async throws -> [Chat] {
        return try await searchChatsUseCase.execute(query: query, userId: userId)
    }
    
    func forkChat(chatId: String, userId: String, title: String? = nil) async throws -> String {
        return try await chatFlowCoordinator.forkChat(chatId: chatId, userId: userId, title: title)
    }
    
    func shareChat(chatId: String, visibility: ChatVisibility, sharedWithUserIds: [String]? = nil) async throws {
        try await chatFlowCoordinator.shareChat(
            chatId: chatId,
            visibility: visibility,
            sharedWithUserIds: sharedWithUserIds
        )
    }
    
    func exportChat(chatId: String, format: ExportFormat) async throws -> Data {
        return try await exportChatUseCase.execute(chatId: chatId, format: format)
    }
} 

