//
//  ChatService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//


import Foundation
import Combine

public protocol ChatService {
    // MARK: - 会话相关功能
    
    /// 获取聊天
    /// - Parameter id: 聊天ID
    /// - Returns: 聊天对象
    func getChat(id: String) async throws -> Chat
    
    /// 观察聊天变化
    /// - Parameter id: 聊天ID
    /// - Returns: 聊天对象发布者
    func observeChat(id: String) -> AnyPublisher<Chat, Error>
    
    // MARK: - 消息相关功能
    
    /// 发送消息
    /// - Parameter message: 要发送的消息
    func sendMessage(message: Message) async throws -> Message
    
    /// 获取聊天历史消息
    /// - Parameters:
    ///   - chatId: 聊天ID
    ///   - limit: 限制数量
    ///   - startAfter: 开始位置
    /// - Returns: 消息列表
    func getMessages(chatId: String, limit: Int, startAfter: String?) async throws -> [Message]
    
    /// 观察消息列表
    /// - Parameter chatId: 聊天ID
    /// - Returns: 消息列表发布者
    func observeMessages(chatId: String) -> AnyPublisher<[Message], Error>

    /// 获取AI回复
    /// - Parameters:
    ///   - message: 消息
    ///   - chatId: 聊天ID
    ///   - systemPrompt: 系统提示词
    /// - Returns: AI回复
    func getAIReply(forMessage message: Message, chatId: String, systemPrompt: String?) async throws -> Message


    /// 停止生成
    /// - Parameter chatId: 聊天ID
    func stopGeneration(chatId: String) async throws
    
    // MARK: - 管理相关功能
    
    /// 创建新的聊天
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - title: 聊天标题
    ///   - description: 聊天描述，可选
    ///   - parameters: 聊天参数，可选
    /// - Returns: 创建的聊天ID
    func createChat(
        userId: String,
        title: String,
        description: String?,
        parameters: ChatParameters?
    ) async throws -> String
    
    /// 获取用户的聊天列表
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - limit: 数量限制
    ///   - startAfter: 分页标记
    ///   - status: 状态过滤
    /// - Returns: 聊天列表
    func getChatList(
        forUser userId: String,
        limit: Int,
        startAfter: String?,
        status: ChatStatus?
    ) async throws -> [Chat]
    
    /// 更新聊天
    /// - Parameter chat: 要更新的聊天
    func updateChat(_ chat: Chat) async throws
    
    /// 更新聊天标题
    /// - Parameters:
    ///   - chatId: 聊天ID
    ///   - title: 新标题
    func updateChatTitle(chatId: String, title: String) async throws
    
    /// 更新聊天描述
    /// - Parameters:
    ///   - chatId: 聊天ID
    ///   - description: 新描述
    func updateChatDescription(chatId: String, description: String?) async throws
    
    /// 删除聊天
    /// - Parameter id: 聊天ID
    func deleteChat(id: String) async throws
    
    /// 搜索聊天
    /// - Parameters:
    ///   - query: 搜索关键词
    ///   - userId: 用户ID
    /// - Returns: 匹配的聊天列表
    func searchChats(query: String, userId: String) async throws -> [Chat]
    
    /// 创建聊天分支
    /// - Parameters:
    ///   - chatId: 源聊天ID
    ///   - userId: 用户ID
    ///   - title: 新标题，可选
    /// - Returns: 分支聊天ID
    func forkChat(chatId: String, userId: String, title: String?) async throws -> String
    
    /// 分享聊天
    /// - Parameters:
    ///   - chatId: 聊天ID
    ///   - visibility: 可见性
    ///   - sharedWithUserIds: 共享用户ID列表
    func shareChat(chatId: String, visibility: ChatVisibility, sharedWithUserIds: [String]?) async throws
    
    /// 导出聊天
    /// - Parameters:
    ///   - chatId: 聊天ID
    ///   - format: 导出格式
    /// - Returns: 导出结果
    func exportChat(chatId: String, format: ExportFormat) async throws -> Data
}