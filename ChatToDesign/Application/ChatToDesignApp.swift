//
//  ChatToDesignApp.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/14.
//

import SwiftUI

extension Notification.Name {
  static let showSubscriptionPage = Notification.Name("showSubscriptionPage")
  static let navigateToProfile = Notification.Name("navigateToProfile")
}

@main
struct ChatToDesignApp: App {
  @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

  var body: some Scene {
    WindowGroup {
      ZStack {
        RootView()
      }
      .preferredColorScheme(.dark)
    }
  }
}
