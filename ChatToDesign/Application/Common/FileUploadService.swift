//
//  FileUploadService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/20.
//

import Foundation
import UIKit

// MARK: - File Upload Service Protocol

/// 文件上传服务协议
public protocol FileUploadService {
    /// 上传文件数据
    /// - Parameters:
    ///   - data: 文件数据
    ///   - mimeType: MIME类型
    ///   - fileName: 文件名
    ///   - prefix: 文件路径前缀，可选，默认使用配置中的默认前缀
    /// - Returns: 上传后的文件URL
    func uploadFile(
        data: Data,
        mimeType: String,
        fileName: String,
        prefix: String?
    ) async throws -> URL
    
    /// 上传图片
    /// - Parameters:
    ///   - image: UIImage对象
    ///   - fileName: 文件名
    ///   - quality: 压缩质量 (0.0-1.0)
    ///   - prefix: 文件路径前缀，可选
    /// - Returns: 上传后的文件URL
    func uploadImage(
        image: UIImage,
        fileName: String,
        quality: CGFloat,
        prefix: String?
    ) async throws -> URL
    
    /// 验证文件是否符合上传要求
    /// - Parameters:
    ///   - data: 文件数据
    ///   - mimeType: MIME类型
    /// - Throws: FileUploadError 如果文件不符合要求
    func validateFile(data: Data, mimeType: String) throws
}

// MARK: - Default Implementation

/// 默认文件上传服务实现
public final class DefaultFileUploadService: FileUploadService {
    
    // MARK: - Properties
    
    private let apiService: APIService
    private let configuration: FileUploadConfiguration
    
    // MARK: - Initialization
    
    /// 初始化文件上传服务
    /// - Parameters:
    ///   - apiService: API服务
    ///   - configuration: 上传配置，默认使用默认配置
    public init(apiService: APIService, configuration: FileUploadConfiguration = .default) {
        self.apiService = apiService
        self.configuration = configuration
    }
    
    // MARK: - FileUploadService Implementation
    
    public func uploadFile(
        data: Data,
        mimeType: String,
        fileName: String,
        prefix: String? = nil
    ) async throws -> URL {
        // 1. 验证文件
        try validateFile(data: data, mimeType: mimeType)
        
        // 2. 准备上传请求
        let uploadPrefix = prefix ?? configuration.defaultPrefix
        let request = FileUploadRequest.from(
            data: data,
            mimeType: mimeType,
            prefix: uploadPrefix,
            fileName: fileName
        )
        
        Logger.debug("Uploading file: \(fileName), size: \(data.count) bytes, type: \(mimeType)")
        
        do {
            // 3. 执行上传
            let response = try await apiService.uploadFile(request: request)
            
            // 4. 验证响应URL
            guard let url = response.urlObject else {
                Logger.error("Invalid URL in upload response: \(response.url)")
                throw FileUploadError.parsingError(
                    NSError(domain: "FileUploadService", code: -1, userInfo: [
                        NSLocalizedDescriptionKey: "Invalid URL in response"
                    ])
                )
            }
            
            Logger.debug("File uploaded successfully: \(url.absoluteString)")
            return url
            
        } catch let error as APIServiceError {
            // 转换API错误为文件上传错误
            switch error {
            case .networkError(let networkError):
                throw FileUploadError.networkError(networkError)
            case .responseError(let statusCode, let message):
                throw FileUploadError.apiError(statusCode: statusCode, message: message)
            case .parsingError(let parseError):
                throw FileUploadError.parsingError(parseError)
            case .unknown(let unknownError):
                throw FileUploadError.unknown(unknownError)
            }
        } catch {
            throw FileUploadError.unknown(error)
        }
    }
    
    public func uploadImage(
        image: UIImage,
        fileName: String,
        quality: CGFloat = 0.8,
        prefix: String? = nil
    ) async throws -> URL {
        // 1. 转换图片为JPEG数据
        guard let imageData = image.jpegData(compressionQuality: quality) else {
            Logger.error("Failed to convert image to JPEG data")
            throw FileUploadError.invalidFileData
        }
        
        // 2. 上传文件
        return try await uploadFile(
            data: imageData,
            mimeType: "image/jpeg",
            fileName: fileName,
            prefix: prefix
        )
    }
    
    public func validateFile(data: Data, mimeType: String) throws {
        try configuration.validate(data: data, mimeType: mimeType)
    }
}

// MARK: - Convenience Extensions

extension FileUploadService {
    /// 上传文件数据（使用默认前缀）
    public func uploadFile(
        data: Data,
        mimeType: String,
        fileName: String
    ) async throws -> URL {
        return try await uploadFile(data: data, mimeType: mimeType, fileName: fileName, prefix: nil)
    }
    
    /// 上传图片（使用默认前缀和质量）
    public func uploadImage(
        image: UIImage,
        fileName: String
    ) async throws -> URL {
        return try await uploadImage(image: image, fileName: fileName, quality: 0.8, prefix: nil)
    }
}
