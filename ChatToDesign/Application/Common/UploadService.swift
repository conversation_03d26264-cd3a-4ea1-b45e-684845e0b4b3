// UploadService.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/20.
//

import Foundation
import UIKit

/// 上传错误类型
public enum UploadError: Error, LocalizedError {
    case invalidData
    case invalidMimeType
    case thumbnailGenerationFailed
    case uploadCancelled
    case storageError(Error)
    case unknown(Error?)
    
    public var errorDescription: String? {
        switch self {
        case .invalidData:
            return "无效的数据"
        case .invalidMimeType:
            return "无效的MIME类型"
        case .thumbnailGenerationFailed:
            return "缩略图生成失败"
        case .uploadCancelled:
            return "上传已取消"
        case .storageError(let error):
            return "存储错误: \(error.localizedDescription)"
        case .unknown(let error):
            return "未知错误: \(error?.localizedDescription ?? "未知原因")"
        }
    }
}

/// 上传服务协议
public protocol UploadService {
    /// 上传媒体文件到存储
    /// - Parameters:
    ///   - data: 文件数据
    ///   - fileName: 文件名
    ///   - mimeType: MIME类型
    ///   - chatId: 聊天ID
    /// - Returns: 上传结果，包含远程URL、缩略图URL和文件大小
    func uploadMedia(
        data: Data,
        fileName: String,
        mimeType: String,
        chatId: String
    ) async throws -> (remoteURL: URL, thumbnailURL: URL?, fileSize: Int)
    
    /// 生成缩略图
    /// - Parameters:
    ///   - imageData: 图片数据
    ///   - maxSize: 最大尺寸
    /// - Returns: 缩略图数据
    func generateThumbnail(
        from imageData: Data,
        maxSize: CGSize
    ) async throws -> Data
    
    /// 取消上传
    /// - Parameter identifier: 上传标识符
    func cancelUpload(identifier: String)
}

/// 基于StorageService的上传服务实现
public final class DefaultUploadService: UploadService {
    
    // MARK: - 属性
    
    private let storageService: StorageService
    private var uploadTasks: [String: Any] = [:]
    private var cancelledTasks: Set<String> = []
    
    // MARK: - 初始化
    
    public init(storageService: StorageService) {
        self.storageService = storageService
    }
    
    // MARK: - UploadService 实现
    
    public func uploadMedia(
        data: Data,
        fileName: String,
        mimeType: String,
        chatId: String
    ) async throws -> (remoteURL: URL, thumbnailURL: URL?, fileSize: Int) {
        // 1. 生成唯一标识符用于跟踪上传任务
        let uploadId = UUID().uuidString
        
        // 2. 确定存储类型
        guard let storageType = determineStorageType(from: mimeType) else {
            Logger.error("无效的MIME类型: \(mimeType)")
            throw UploadError.invalidMimeType
        }
        
        // 3. 确定存储路径
        let path = "chats/\(chatId)/media/\(fileName)"
        Logger.debug("附件上传路径: \(path)")
        
        // 检查任务是否被取消
        if cancelledTasks.contains(uploadId) {
            Logger.warning("上传任务已被取消: \(uploadId)")
            throw UploadError.uploadCancelled
        }
        
        do {
            // 4. 上传原始文件
            let uploadResult = try await storageService.uploadData(
                data,
                to: path,
                type: storageType,
                progress: { [weak self] progress in
                    // 检查任务是否被取消
                    if self?.cancelledTasks.contains(uploadId) == true {
                        Logger.warning("上传进度监控时检测到取消: \(uploadId)")
                        return
                    }
                    Logger.debug("上传进度: \(progress * 100)%")
                    self?.updateProgress(for: uploadId, progress: progress)
                }
            )
            Logger.debug("uploadService上传结果: \(uploadResult)")
            
            // 检查任务是否被取消
            if cancelledTasks.contains(uploadId) {
                Logger.warning("上传完成后检测到取消，准备删除已上传文件: \(uploadId)")
                // 尝试删除已上传的文件（这里可以使用 Task 异步处理）
                Task {
                    do {
                        try await storageService.deleteFile(at: path)
                        Logger.debug("已删除取消上传的文件: \(path)")
                    } catch {
                        Logger.error("删除取消上传的文件失败: \(error.localizedDescription)")
                    }
                }
                throw UploadError.uploadCancelled
            }
            
            // 5. 处理缩略图
            var thumbnailURL: URL? = nil
            if storageType == .image || storageType == .video {
                do {
                    // 生成缩略图
                    let thumbnailData = try await generateThumbnail(
                        from: data,
                        maxSize: CGSize(width: 300, height: 300)
                    )
                    
                    // 检查任务是否被取消
                    if cancelledTasks.contains(uploadId) {
                        Logger.warning("生成缩略图后检测到取消: \(uploadId)")
                        throw UploadError.uploadCancelled
                    }
                    
                    // 上传缩略图
                    let thumbnailPath = "chats/\(chatId)/thumbnails/\(fileName)"
                    let thumbnailResult = try await storageService.uploadData(
                        thumbnailData,
                        to: thumbnailPath,
                        type: .image,
                        progress: nil
                    )
                    
                    thumbnailURL = thumbnailResult.url
                    Logger.debug("缩略图上传成功: \(thumbnailURL?.absoluteString ?? "nil")")
                } catch {
                    Logger.error("生成或上传缩略图失败: \(error.localizedDescription)")
                    // 这里我们不抛出错误，因为主文件已上传成功，缩略图失败不应该导致整个上传失败
                }
            }
            
            // 6. 清理追踪信息
            uploadTasks.removeValue(forKey: uploadId)
            cancelledTasks.remove(uploadId)
            
            // 7. 返回结果
            return (
                remoteURL: uploadResult.url,
                thumbnailURL: thumbnailURL,
                fileSize: data.count
            )
        } catch {
            Logger.error("上传媒体文件失败: \(error.localizedDescription)")
            
            // 清理追踪信息
            uploadTasks.removeValue(forKey: uploadId)
            cancelledTasks.remove(uploadId)
            
            // 包装错误
            if let uploadError = error as? UploadError {
                throw uploadError
            } else if let storageError = error as? StorageError {
                throw UploadError.storageError(storageError)
            } else {
                throw UploadError.unknown(error)
            }
        }
    }
    
    public func generateThumbnail(
        from imageData: Data,
        maxSize: CGSize
    ) async throws -> Data {
        // 1. 加载图像
        guard let image = UIImage(data: imageData) else {
            Logger.error("无法从数据创建图像")
            throw UploadError.thumbnailGenerationFailed
        }
        
        do {
            // 2. 在后台线程中生成缩略图
            return try await withCheckedThrowingContinuation { continuation in
                DispatchQueue.global(qos: .userInitiated).async {
                    do {
                        // 计算缩略图尺寸，保持宽高比
                        let aspectRatio = image.size.width / image.size.height
                        var thumbnailSize = CGSize(width: maxSize.width, height: maxSize.height)
                        
                        if aspectRatio > 1 {
                            // 宽图
                            thumbnailSize.height = maxSize.width / aspectRatio
                        } else {
                            // 高图
                            thumbnailSize.width = maxSize.height * aspectRatio
                        }
                        
                        // 创建缩略图
                        let renderer = UIGraphicsImageRenderer(size: thumbnailSize)
                        let thumbnailImage = renderer.image { context in
                            image.draw(in: CGRect(origin: .zero, size: thumbnailSize))
                        }
                        
                        // 转换为数据
                        if let thumbnailData = thumbnailImage.jpegData(compressionQuality: 0.7) {
                            continuation.resume(returning: thumbnailData)
                        } else {
                            Logger.error("无法生成缩略图JPEG数据")
                            throw UploadError.thumbnailGenerationFailed
                        }
                    } catch {
                        Logger.error("生成缩略图失败: \(error.localizedDescription)")
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            Logger.error("缩略图生成过程出错: \(error.localizedDescription)")
            throw UploadError.thumbnailGenerationFailed
        }
    }
    
    public func cancelUpload(identifier: String) {
        Logger.debug("取消上传任务: \(identifier)")
        cancelledTasks.insert(identifier)
        uploadTasks.removeValue(forKey: identifier)
    }
    
    // MARK: - 私有辅助方法
    
    private func determineStorageType(from mimeType: String) -> StorageType? {
        if mimeType.starts(with: "image/") {
            return .image
        } else if mimeType.starts(with: "video/") {
            return .video
        } else if mimeType.starts(with: "audio/") {
            return .audio
        } else if !mimeType.isEmpty {
            return .file
        } else {
            return nil
        }
    }
    
    private func updateProgress(for identifier: String, progress: Double) {
        // 检查任务是否被取消
        if cancelledTasks.contains(identifier) {
            return
        }
        
        // 更新进度跟踪信息
        uploadTasks[identifier] = progress
        
        // 记录进度日志
        if progress == 1.0 {
            Logger.debug("上传任务完成 100%: \(identifier)")
        } else if progress.truncatingRemainder(dividingBy: 0.1) < 0.01 {  // 每 10% 记录一次
            Logger.debug("上传进度 \(Int(progress * 100))%: \(identifier)")
        }
    }
} 