//
//  Bundle+Extensions.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/20.
//

import Foundation

/// Bundle extensions for easy access to app information
extension Bundle {
    
    /// App version string (CFBundleShortVersionString)
    /// - Returns: App version like "1.0.0" or "Unknown" if not found
    public var appVersion: String {
        return infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
    }
    
    /// Build number string (CFBundleVersion)
    /// - Returns: Build number like "100" or "Unknown" if not found
    public var buildNumber: String {
        return infoDictionary?["CFBundleVersion"] as? String ?? "Unknown"
    }
    
    /// App name (CFBundleDisplayName or CFBundleName)
    /// - Returns: App display name or "Unknown" if not found
    public var appName: String {
        return infoDictionary?["CFBundleDisplayName"] as? String ??
               infoDictionary?["CFBundleName"] as? String ??
               "Unknown"
    }
    
    /// Bundle identifier
    /// - Returns: Bundle identifier like "com.a1d.chat-to-design" or "Unknown" if not found
    public var bundleId: String {
        return bundleIdentifier ?? "Unknown"
    }
    
    /// Full version string combining version and build
    /// - Returns: Version string like "1.0.0 (100)"
    public var fullVersionString: String {
        return "\(appVersion) (\(buildNumber))"
    }
}
