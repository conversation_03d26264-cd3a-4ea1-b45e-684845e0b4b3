// AuthModule.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Combine
import Foundation

/// 认证模块
/// 提供所有认证相关功能
public final class AuthModule {
  // MARK: - 公开服务

  /// 认证应用服务
  public let authService: AuthApplicationService

  // MARK: - 内部依赖

  private let dependencies: ModuleDependencies

  // MARK: - 初始化

  /// 初始化认证模块
  /// - Parameter dependencies: 模块依赖
  public init(dependencies: ModuleDependencies) {
    self.dependencies = dependencies

    // 创建用例
    let signInUseCase = SignInUseCase(
      authService: dependencies.authService,
      userRepository: dependencies.userRepository,
      userStateManager: dependencies.userStateManager,
      analyticsService: dependencies.analyticsService
    )

    let signOutUseCase = SignOutUseCase(
      authService: dependencies.authService,
      userStateManager: dependencies.userStateManager,
      analyticsService: dependencies.analyticsService
    )

    // 创建应用服务实现
    let authServiceImpl = AuthApplicationServiceImpl(
      signInUseCase: signInUseCase,
      signOutUseCase: signOutUseCase,
      authService: dependencies.authService
    )

    // 模块持有协议类型引用
    self.authService = authServiceImpl
  }
}

/// 认证应用服务实现
private final class AuthApplicationServiceImpl: AuthApplicationService {
  // MARK: - 属性

  private let signInUseCase: SignInUseCase
  private let signOutUseCase: SignOutUseCase
  private let authService: AuthService

  /// 认证状态发布者
  public var authStatePublisher: AnyPublisher<AuthState, Never> {
    return authService.authStatePublisher
  }

  // MARK: - 初始化

  /// 初始化认证应用服务
  /// - Parameters:
  ///   - signInUseCase: 登录用例
  ///   - signOutUseCase: 登出用例
  ///   - authService: 认证服务
  init(
    signInUseCase: SignInUseCase,
    signOutUseCase: SignOutUseCase,
    authService: AuthService
  ) {
    self.signInUseCase = signInUseCase
    self.signOutUseCase = signOutUseCase
    self.authService = authService
  }

  // MARK: - 公开方法

  /// 使用邮箱密码登录
  /// - Parameters:
  ///   - email: 邮箱
  ///   - password: 密码
  public func signIn(email: String, password: String) async throws {
    let _ = try await signInUseCase.execute(email: email, password: password)
  }

  /// 使用Google账号登录
  public func signInWithGoogle() async throws {
    let _ = try await signInUseCase.executeWithGoogle()
  }

  /// 登出
  public func signOut() async throws {
    try await signOutUseCase.execute()
  }

  /// 获取当前认证状态
  public var authState: AuthState {
    return authService.authState
  }
}
