// SignInUseCase.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/15.
//

import Foundation

/// 登录用例
/// 职责：处理用户登录逻辑，创建或获取用户，更新用户状态
final class SignInUseCase {
  private let authService: AuthService
  private let userRepository: UserRepository
  private let userStateManager: UserStateManager
  private let analyticsService: AnalyticsService

  init(
    authService: AuthService,
    userRepository: UserRepository,
    userStateManager: UserStateManager = UserStateManager.shared,
    analyticsService: AnalyticsService
  ) {
    self.authService = authService
    self.userRepository = userRepository
    self.userStateManager = userStateManager
    self.analyticsService = analyticsService
  }

  /// 使用邮箱密码登录
  /// - Parameters:
  ///   - email: 用户邮箱
  ///   - password: 用户密码
  /// - Returns: 登录成功的用户
  func execute(email: String, password: String) async throws -> User {
    do {
      let authUser = try await authService.signInWithEmail(email: email, password: password)
      let user = try await getOrCreateUser(authUser: authUser)

      // 埋点：用户登录成功
      analyticsService.trackUserSignIn(method: "email", userId: authUser.uid)

      return user
    } catch {
      // 埋点：登录失败
      analyticsService.trackError(
        code: "sign_in_failed",
        message: "Email sign in failed: \(error.localizedDescription)",
        context: ["method": "email", "email": email]
      )
      throw error
    }
  }

  /// 使用Google账号登录
  /// - Returns: 登录成功的用户
  func executeWithGoogle() async throws -> User {
    do {
      let authUser = try await authService.signInWithGoogle()
      let user = try await getOrCreateUser(authUser: authUser)

      // 埋点：用户Google登录成功
      analyticsService.trackUserSignIn(method: "google", userId: authUser.uid)

      return user
    } catch {
      // 埋点：Google登录失败
      analyticsService.trackError(
        code: "sign_in_failed",
        message: "Google sign in failed: \(error.localizedDescription)",
        context: ["method": "google"]
      )
      throw error
    }
  }

  /// 获取或创建用户
  /// - Parameter authUser: 认证用户
  /// - Returns: 完整的用户对象
  private func getOrCreateUser(authUser: AuthUser) async throws -> User {
    do {
      // 尝试获取现有用户
      let existingUser = try await userRepository.getUser(id: authUser.uid)
      // 用户存在，更新最后登录时间
      try await userRepository.updateLastLogin(id: authUser.uid)
      // 更新状态管理器
      userStateManager.setCurrentUser(existingUser)

      // 设置用户属性到分析服务
      setUserAnalyticsProperties(user: existingUser)

      return existingUser
    } catch UserRepositoryError.userNotFound {
      // 用户不存在，创建新用户
      let newUser = User.create(
        id: authUser.uid,
        email: authUser.email ?? "",
        displayName: authUser.displayName ?? "",
        photoURL: authUser.photoURL
      )

      // 保存到仓储
      let _ = try await userRepository.saveUser(newUser)

      // 更新状态管理器（即使数据库写入尚未完成，UI也能立即获取到用户数据）
      userStateManager.setCurrentUser(newUser)

      // 埋点：新用户注册
      analyticsService.track(.userSignUp(method: "auto", userId: newUser.id))

      // 设置用户属性到分析服务
      setUserAnalyticsProperties(user: newUser)

      return newUser
    }
  }

  /// 设置用户分析属性
  /// - Parameter user: 用户对象
  private func setUserAnalyticsProperties(user: User) {
    // 设置用户ID
    analyticsService.setUserId(user.id)

    // 设置用户属性
    let userProperties = PredefinedUserProperties(
      userId: user.id,
      email: user.email,
      displayName: user.displayName,
      registrationDate: user.createdAt,
      lastLoginDate: user.lastLogin
    )

    analyticsService.setUserProperties(userProperties)
  }
}
