// SignOutUseCase.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/15.
//

import Foundation

/// 登出用例
/// 职责：处理用户登出逻辑，清除用户状态
final class SignOutUseCase {
  private let authService: AuthService
  private let userStateManager: UserStateManager
  private let analyticsService: AnalyticsService

  init(
    authService: AuthService,
    userStateManager: UserStateManager = UserStateManager.shared,
    analyticsService: AnalyticsService
  ) {
    self.authService = authService
    self.userStateManager = userStateManager
    self.analyticsService = analyticsService
  }

  /// 执行登出操作
  /// - Throws: 登出过程中可能发生的错误
  func execute() async throws {
    // 获取当前用户ID用于埋点
    let currentUserId = userStateManager.currentUser?.id

    do {
      // 1. 先清除内存中的用户状态
      // 这确保即使认证服务登出失败，UI也不会显示已登出用户的数据
      userStateManager.clearCurrentUser()

      // 2. 调用认证服务登出
      // 这会触发 authStatePublisher 发布 .unauthenticated 状态
      try authService.signOut()

      // 埋点：用户登出成功
      if let userId = currentUserId {
        analyticsService.trackUserSignOut(userId: userId)
      }

      // 清除分析服务中的用户信息
      analyticsService.setUserId(nil)
      analyticsService.resetUserData()

    } catch {
      // 埋点：登出失败
      analyticsService.trackError(
        code: "sign_out_failed",
        message: "Sign out failed: \(error.localizedDescription)",
        context: ["user_id": currentUserId ?? "unknown"]
      )
      throw error
    }

    // 注意：即使上面的步骤抛出异常，用户状态也已经被清除，
    // 确保UI不会显示已登出用户的数据
  }
}
