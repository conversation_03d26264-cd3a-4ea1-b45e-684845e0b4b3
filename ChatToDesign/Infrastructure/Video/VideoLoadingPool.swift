//
//  VideoLoadingPool.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import Foundation

/// 视频加载任务池
actor VideoLoadingPool {
    
    static let shared = VideoLoadingPool()
    
    // MARK: - Properties
    
    private var loadingTasks: [URL: Task<URL?, Error>] = [:]
    private var activeLoads = 0
    private let maxConcurrentLoads = 3
    
    // 优先级队列
    private var highPriorityQueue: [LoadRequest] = []
    private var normalPriorityQueue: [LoadRequest] = []
    private var lowPriorityQueue: [LoadRequest] = []
    
    // MARK: - Types
    
    enum LoadPriority {
        case high    // 当前可见
        case normal  // 即将可见
        case low     // 预加载
    }
    
    private struct LoadRequest {
        let url: URL
        let priority: LoadPriority
        let completion: (Result<URL, Error>) -> Void
        let createdAt: Date = Date()
    }
    
    // MARK: - Initialization
    
    private init() {
        // 启动队列处理
        Task {
            await processQueue()
        }
    }
    
    // MARK: - Public Methods
    
    /// 请求加载视频
    func requestLoad(
        url: URL,
        priority: LoadPriority,
        completion: @escaping (Result<URL, Error>) -> Void
    ) async {
        // 检查是否已在加载
        if let existingTask = loadingTasks[url] {
            let result = await existingTask.result
            switch result {
            case .success(let localURL):
                completion(.success(localURL ?? url))
            case .failure(let error):
                completion(.failure(error))
            }
            return
        }
        
        // 检查是否已在缓存中
        if let cachedURL = await VideoCacheManager.shared.getCachedVideoURL(for: url) {
            completion(.success(cachedURL))
            return
        }
        
        // 添加到队列
        let request = LoadRequest(url: url, priority: priority, completion: completion)
        addToQueue(request)
        
        // 触发队列处理
        await processQueue()
    }
    
    /// 取消加载
    func cancelLoad(url: URL) {
        // 取消正在进行的任务
        if let task = loadingTasks[url] {
            task.cancel()
            loadingTasks.removeValue(forKey: url)
            activeLoads = max(0, activeLoads - 1)
        }
        
        // 从队列中移除
        removeFromQueues(url: url)
    }
    
    /// 获取加载状态
    func getLoadingStatus() -> (active: Int, queued: Int) {
        let queuedCount = highPriorityQueue.count + normalPriorityQueue.count + lowPriorityQueue.count
        return (active: activeLoads, queued: queuedCount)
    }
    
    /// 清理过期请求
    func cleanupExpiredRequests() {
        let now = Date()
        let maxAge: TimeInterval = 30 // 30秒过期
        
        highPriorityQueue.removeAll { now.timeIntervalSince($0.createdAt) > maxAge }
        normalPriorityQueue.removeAll { now.timeIntervalSince($0.createdAt) > maxAge }
        lowPriorityQueue.removeAll { now.timeIntervalSince($0.createdAt) > maxAge }
    }
    
    // MARK: - Private Methods
    
    private func addToQueue(_ request: LoadRequest) {
        // 避免重复添加
        removeFromQueues(url: request.url)
        
        switch request.priority {
        case .high:
            highPriorityQueue.append(request)
        case .normal:
            normalPriorityQueue.append(request)
        case .low:
            lowPriorityQueue.append(request)
        }
    }
    
    private func removeFromQueues(url: URL) {
        highPriorityQueue.removeAll { $0.url == url }
        normalPriorityQueue.removeAll { $0.url == url }
        lowPriorityQueue.removeAll { $0.url == url }
    }
    
    private func getNextRequest() -> LoadRequest? {
        if !highPriorityQueue.isEmpty {
            return highPriorityQueue.removeFirst()
        } else if !normalPriorityQueue.isEmpty {
            return normalPriorityQueue.removeFirst()
        } else if !lowPriorityQueue.isEmpty {
            return lowPriorityQueue.removeFirst()
        }
        return nil
    }
    
    private func processQueue() async {
        // 清理过期请求
        cleanupExpiredRequests()
        
        while activeLoads < maxConcurrentLoads {
            guard let request = getNextRequest() else {
                break
            }
            
            // 创建加载任务
            let task = Task<URL?, Error> {
                defer {
                    Task {
                        await self.onTaskCompleted(url: request.url)
                    }
                }
                
                do {
                    Logger.debug("VideoLoadingPool: Starting load for \(request.url.lastPathComponent)")
                    
                    // 记录开始时间
                    let startTime = Date()
                    
                    // 记录网络请求
                    VideoPerformanceMonitor.shared.recordNetworkRequest()
                    
                    // 实际下载
                    let (data, _) = try await URLSession.shared.data(from: request.url)
                    
                    // 缓存到本地
                    let localURL = await VideoCacheManager.shared.cacheVideo(url: request.url, data: data)
                    
                    // 记录加载时间
                    let loadTime = Date().timeIntervalSince(startTime)
                    VideoPerformanceMonitor.shared.recordVideoLoaded(loadTime: loadTime)
                    
                    Logger.debug("VideoLoadingPool: Completed load for \(request.url.lastPathComponent)")
                    return localURL
                    
                } catch {
                    VideoPerformanceMonitor.shared.recordError()
                    Logger.error("VideoLoadingPool: Failed to load \(request.url.lastPathComponent): \(error)")
                    throw error
                }
            }
            
            loadingTasks[request.url] = task
            activeLoads += 1
            
            // 异步处理结果
            Task {
                let result = await task.result
                switch result {
                case .success(let localURL):
                    request.completion(.success(localURL ?? request.url))
                case .failure(let error):
                    request.completion(.failure(error))
                }
            }
        }
    }
    
    private func onTaskCompleted(url: URL) {
        loadingTasks.removeValue(forKey: url)
        activeLoads = max(0, activeLoads - 1)
        
        // 继续处理队列
        Task {
            await processQueue()
        }
    }
}

// MARK: - Error Types

enum VideoLoadingError: Error {
    case networkError(Error)
    case cacheError
    case cancelled
}

extension VideoLoadingError: LocalizedError {
    var errorDescription: String? {
        switch self {
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .cacheError:
            return "Cache error"
        case .cancelled:
            return "Loading cancelled"
        }
    }
}