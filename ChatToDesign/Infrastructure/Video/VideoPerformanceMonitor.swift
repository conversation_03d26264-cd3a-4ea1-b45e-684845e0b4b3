//
//  VideoPerformanceMonitor.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import Foundation
import UIKit

/// 视频性能监控器
class VideoPerformanceMonitor {
    
    static let shared = VideoPerformanceMonitor()
    
    // MARK: - Properties
    
    private var metrics: PerformanceMetrics = PerformanceMetrics()
    private var isMonitoring = false
    private var monitoringTimer: Timer?
    
    // MARK: - Types
    
    struct PerformanceMetrics {
        var totalVideosLoaded: Int = 0
        var totalThumbnailsGenerated: Int = 0
        var averageLoadTime: TimeInterval = 0
        var memoryUsage: Int64 = 0
        var activePlayerCount: Int = 0
        var cacheHitRate: Double = 0
        var networkRequests: Int = 0
        var errors: Int = 0
        
        // 时间戳
        var lastUpdated: Date = Date()
    }
    
    // MARK: - Initialization
    
    private init() {
        setupMemoryWarningObserver()
    }
    
    deinit {
        stopMonitoring()
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Public Methods
    
    /// 开始性能监控
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        
        // 每5秒更新一次指标
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            self?.updateMetrics()
        }
        
        Logger.info("VideoPerformanceMonitor: Started monitoring")
    }
    
    /// 停止性能监控
    func stopMonitoring() {
        isMonitoring = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        Logger.info("VideoPerformanceMonitor: Stopped monitoring")
    }
    
    /// 记录视频加载
    func recordVideoLoaded(loadTime: TimeInterval) {
        metrics.totalVideosLoaded += 1
        
        // 计算平均加载时间
        let totalTime = metrics.averageLoadTime * Double(metrics.totalVideosLoaded - 1) + loadTime
        metrics.averageLoadTime = totalTime / Double(metrics.totalVideosLoaded)
        
        Logger.debug("VideoPerformanceMonitor: Video loaded in \(String(format: "%.2f", loadTime))s")
    }
    
    /// 记录缩略图生成
    func recordThumbnailGenerated() {
        metrics.totalThumbnailsGenerated += 1
    }
    
    /// 记录网络请求
    func recordNetworkRequest() {
        metrics.networkRequests += 1
    }
    
    /// 记录错误
    func recordError() {
        metrics.errors += 1
    }
    
    /// 获取当前性能指标
    func getCurrentMetrics() -> PerformanceMetrics {
        updateMetrics()
        return metrics
    }
    
    /// 重置指标
    func resetMetrics() {
        metrics = PerformanceMetrics()
        Logger.info("VideoPerformanceMonitor: Metrics reset")
    }
    
    /// 生成性能报告
    func generateReport() -> String {
        let metrics = getCurrentMetrics()
        
        return """
        📊 Video Performance Report
        ========================
        
        📱 General Stats:
        • Videos loaded: \(metrics.totalVideosLoaded)
        • Thumbnails generated: \(metrics.totalThumbnailsGenerated)
        • Network requests: \(metrics.networkRequests)
        • Errors: \(metrics.errors)
        
        ⚡ Performance:
        • Average load time: \(String(format: "%.2f", metrics.averageLoadTime))s
        • Cache hit rate: \(String(format: "%.1f", metrics.cacheHitRate * 100))%
        • Active players: \(metrics.activePlayerCount)
        
        💾 Memory:
        • Current usage: \(formatBytes(metrics.memoryUsage))
        
        🕐 Last updated: \(formatDate(metrics.lastUpdated))
        """
    }
    
    // MARK: - Private Methods
    
    private func updateMetrics() {
        // 更新内存使用情况
        metrics.memoryUsage = getMemoryUsage()
        
        // 更新播放器池状态
        let poolStatus = PlayerPool.shared.getPoolStatus()
        metrics.activePlayerCount = poolStatus.active
        
        // 更新缓存命中率
        updateCacheHitRate()
        
        // 更新时间戳
        metrics.lastUpdated = Date()
        
        // 如果指标异常，记录警告
        checkForPerformanceIssues()
    }
    
    private func updateCacheHitRate() {
        // 简化的缓存命中率计算
        if metrics.networkRequests > 0 {
            let cacheHits = max(0, metrics.totalVideosLoaded - metrics.networkRequests)
            metrics.cacheHitRate = Double(cacheHits) / Double(metrics.totalVideosLoaded)
        }
    }
    
    private func getMemoryUsage() -> Int64 {
        var info = task_vm_info_data_t()
        var count = mach_msg_type_number_t(MemoryLayout<task_vm_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(TASK_VM_INFO), $0, &count)
            }
        }
        
        return result == KERN_SUCCESS ? Int64(info.phys_footprint) : 0
    }
    
    private func checkForPerformanceIssues() {
        // 检查平均加载时间
        if metrics.averageLoadTime > 3.0 {
            Logger.warning("VideoPerformanceMonitor: High average load time: \(String(format: "%.2f", metrics.averageLoadTime))s")
        }
        
        // 检查错误率
        if metrics.totalVideosLoaded > 0 {
            let errorRate = Double(metrics.errors) / Double(metrics.totalVideosLoaded)
            if errorRate > 0.1 { // 10% 错误率
                Logger.warning("VideoPerformanceMonitor: High error rate: \(String(format: "%.1f", errorRate * 100))%")
            }
        }
        
        // 检查内存使用（更严格的阈值）
        let memoryMB = metrics.memoryUsage / (1024 * 1024)
        if memoryMB > 150 { // 降低到150MB
            Logger.warning("VideoPerformanceMonitor: High memory usage: \(memoryMB)MB")
            
            // 自动触发清理
            if memoryMB > 200 {
                Logger.error("VideoPerformanceMonitor: Critical memory usage, triggering cleanup")
                PlayerPool.shared.handleMemoryPressure()
            }
        }
        
        // 检查活跃播放器数量（更严格的阈值）
        if metrics.activePlayerCount > 8 {
            Logger.warning("VideoPerformanceMonitor: Too many active players: \(metrics.activePlayerCount)")
            
            // 超过建议值时自动清理
            if metrics.activePlayerCount > 12 {
                Logger.error("VideoPerformanceMonitor: Critical player count, triggering cleanup")
                PlayerPool.shared.handleMemoryPressure()
            }
        }
    }
    
    private func setupMemoryWarningObserver() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func handleMemoryWarning() {
        Logger.warning("VideoPerformanceMonitor: Memory warning received")
        recordError()
        
        // 主动触发清理
        PlayerPool.shared.handleMemoryPressure()
        
        // 生成内存警告报告
        let report = generateReport()
        Logger.info("VideoPerformanceMonitor: Memory warning report:\n\(report)")
    }
    
    // MARK: - Helper Methods
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useGB]
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: bytes)
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .medium
        return formatter.string(from: date)
    }
}

// MARK: - Debug Extension

#if DEBUG
extension VideoPerformanceMonitor {
    
    /// 调试模式下的实时监控视图
    func createDebugView() -> String {
        let metrics = getCurrentMetrics()
        
        return """
        🔍 Video Debug Info
        ==================
        
        📈 Real-time Stats:
        • Load time: \(String(format: "%.2f", metrics.averageLoadTime))s
        • Cache hits: \(String(format: "%.1f", metrics.cacheHitRate * 100))%
        • Memory: \(formatBytes(metrics.memoryUsage))
        • Players: \(metrics.activePlayerCount)
        • Errors: \(metrics.errors)
        
        ⏱️ Updated: \(formatDate(metrics.lastUpdated))
        """
    }
    
    /// 强制触发性能测试
    func triggerPerformanceTest() {
        Logger.info("VideoPerformanceMonitor: Starting performance test...")
        
        // 模拟一些性能指标
        for i in 1...10 {
            recordVideoLoaded(loadTime: Double.random(in: 0.5...3.0))
            recordThumbnailGenerated()
            recordNetworkRequest()
            
            if i % 5 == 0 {
                recordError()
            }
        }
        
        let report = generateReport()
        Logger.info("VideoPerformanceMonitor: Performance test complete:\n\(report)")
    }
}
#endif