//
//  PlayerView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import UIKit
import AVFoundation

/// 高性能视频播放器视图
class PlayerView: UIView {
    
    // MARK: - Properties
    
    private var player: AVPlayer?
    private var playerLayer: AVPlayerLayer?
    private var currentURL: URL?
    private var isConfigured = false
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupPlayer()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupPlayer()
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        playerLayer?.frame = bounds
    }
    
    // MARK: - Setup
    
    private func setupPlayer() {
        player = AVPlayer()
        playerLayer = AVPlayerLayer(player: player)
        playerLayer?.videoGravity = .resizeAspectFill
        playerLayer?.frame = bounds
        
        if let playerLayer = playerLayer {
            layer.addSublayer(playerLayer)
        }
        
        // 静音播放（适合预览）
        player?.isMuted = true
    }
    
    // MARK: - Public Methods
    
    func configure(with url: URL) {
        guard currentURL != url else { return }
        
        currentURL = url
        let playerItem = AVPlayerItem(url: url)
        player?.replaceCurrentItem(with: playerItem)
        
        // 设置循环播放
        setupLooping()
        isConfigured = true
    }
    
    func play() {
        guard isConfigured else { return }
        player?.play()
    }
    
    func pause() {
        player?.pause()
    }
    
    func reset() {
        pause()
        player?.replaceCurrentItem(with: nil)
        currentURL = nil
        isConfigured = false
        
        // 移除循环播放监听
        NotificationCenter.default.removeObserver(
            self,
            name: .AVPlayerItemDidPlayToEndTime,
            object: nil
        )
    }
    
    func cleanup() {
        reset()
        playerLayer?.removeFromSuperlayer()
        playerLayer = nil
        player = nil
    }
    
    // MARK: - Private Methods
    
    private func setupLooping() {
        guard let player = player, let currentItem = player.currentItem else { return }
        
        // 移除之前的监听
        NotificationCenter.default.removeObserver(
            self,
            name: .AVPlayerItemDidPlayToEndTime,
            object: nil
        )
        
        // 添加循环播放监听
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: currentItem,
            queue: .main
        ) { [weak self] _ in
            self?.player?.seek(to: .zero)
            self?.player?.play()
        }
    }
}