//
//  PlayerPool.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import UIKit
import Foundation

/// 视频播放器复用池
class PlayerPool {
    
    static let shared = PlayerPool()
    
    // MARK: - Properties
    
    private var availablePlayers: [PlayerView] = []
    private var activePlayers: Set<PlayerView> = []
    private var playerPriority: [PlayerView: Int] = [:] // 播放器优先级追踪
    private let maxPoolSize = 12  // 优化：减少最大池大小
    private let maxActivePlayerCount = 6  // 新增：最大同时播放数量限制
    private let minPoolSize = 4   // 优化：减少最小池大小
    
    private let queue = DispatchQueue(label: "com.chattodesign.playerpool", qos: .userInitiated)
    
    // MARK: - Initialization
    
    private init() {
        setupMemoryWarningObserver()
        preloadPlayers()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Public Methods
    
    /// 获取播放器
    func getPlayer(priority: PlayerPriority = .normal) -> PlayerView {
        // 检查是否在主线程，如果是，直接执行
        if Thread.isMainThread {
            return queue.sync {
                return getPlayerInternal(priority: priority)
            }
        } else {
            // 不在主线程，异步切换到主线程
            return DispatchQueue.main.sync {
                return self.getPlayer(priority: priority)
            }
        }
    }
    
    /// 内部获取播放器逻辑
    private func getPlayerInternal(priority: PlayerPriority) -> PlayerView {
        // 优先从可用播放器池获取
        if let player = availablePlayers.popLast() {
            activePlayers.insert(player)
            playerPriority[player] = priority.rawValue
            Logger.debug("PlayerPool: Reused available player, active: \(activePlayers.count)")
            return player
        }
        
        // 检查是否超过最大活跃播放器限制
        if activePlayers.count >= maxActivePlayerCount {
            // 只有高优先级请求才可以回收低优先级播放器
            if priority.rawValue >= PlayerPriority.high.rawValue {
                if let reclaimedPlayer = reclaimLowestPriorityPlayer() {
                    playerPriority[reclaimedPlayer] = priority.rawValue
                    Logger.info("PlayerPool: Reclaimed low priority player for high priority request, active: \(activePlayers.count)")
                    return reclaimedPlayer
                }
            } else {
                // 低优先级请求被拒绝
                Logger.warning("PlayerPool: Rejected low priority request, active players at limit: \(activePlayers.count)")
                return PlayerView() // 返回临时播放器
            }
        }
        
        // 检查是否可以创建新播放器（在池大小限制内）
        if totalPlayerCount < maxPoolSize {
            let player = PlayerView()
            activePlayers.insert(player)
            playerPriority[player] = priority.rawValue
            Logger.debug("PlayerPool: Created new player, total: \(totalPlayerCount), active: \(activePlayers.count)")
            return player
        }
        
        // 最后手段：创建临时播放器（不计入池管理）
        Logger.warning("PlayerPool: Creating temporary player as fallback")
        let tempPlayer = PlayerView()
        playerPriority[tempPlayer] = priority.rawValue
        return tempPlayer
    }
    
    /// 归还播放器
    func returnPlayer(_ player: PlayerView) {
        queue.async { [weak self] in
            guard let self = self else { return }
            
            Logger.debug("🔄 PlayerPool: Returning player - before: active: \(self.activePlayers.count), available: \(self.availablePlayers.count)")
            
            player.reset()
            self.activePlayers.remove(player)
            self.playerPriority.removeValue(forKey: player)
            
            // 优化：更严格的池大小控制
            let maxAvailable = max(2, self.maxPoolSize - self.maxActivePlayerCount)
            if self.availablePlayers.count < maxAvailable {
                self.availablePlayers.append(player)
                Logger.debug("✅ PlayerPool: Returned player - after: active: \(self.activePlayers.count), available: \(self.availablePlayers.count)")
            } else {
                player.cleanup()
                Logger.debug("🗑️ PlayerPool: Cleaned up excess player - after: active: \(self.activePlayers.count), available: \(self.availablePlayers.count)")
            }
        }
    }
    
    /// 清理所有播放器
    func cleanup() {
        queue.async { [weak self] in
            guard let self = self else { return }
            
            for player in self.availablePlayers {
                player.cleanup()
            }
            self.availablePlayers.removeAll()
            
            for player in self.activePlayers {
                player.cleanup()
            }
            self.activePlayers.removeAll()
            
            Logger.info("PlayerPool: All players cleaned up")
        }
    }
    
    /// 获取池状态
    func getPoolStatus() -> (active: Int, available: Int, total: Int, maxActive: Int) {
        return queue.sync {
            (
                active: activePlayers.count,
                available: availablePlayers.count,
                total: totalPlayerCount,
                maxActive: maxActivePlayerCount
            )
        }
    }
    
    /// 智能回收最低优先级播放器
    private func reclaimLowestPriorityPlayer() -> PlayerView? {
        // 找到优先级最低的播放器
        let sortedPlayers = activePlayers.sorted { player1, player2 in
            let priority1 = playerPriority[player1] ?? PlayerPriority.normal.rawValue
            let priority2 = playerPriority[player2] ?? PlayerPriority.normal.rawValue
            return priority1 < priority2
        }
        
        guard let lowestPriorityPlayer = sortedPlayers.first else { return nil }
        
        // 重置该播放器但保持在活跃集合中
        lowestPriorityPlayer.reset()
        Logger.debug("PlayerPool: Reclaimed player with priority \(playerPriority[lowestPriorityPlayer] ?? 0)")
        
        return lowestPriorityPlayer
    }
    
    /// 播放器优先级枚举
    enum PlayerPriority: Int {
        case low = 1
        case normal = 2
        case high = 3
        case critical = 4
    }
    
    // MARK: - Private Methods
    
    private var totalPlayerCount: Int {
        return activePlayers.count + availablePlayers.count
    }
    
    private func preloadPlayers() {
        // 直接在主线程创建播放器，避免死锁
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            var players: [PlayerView] = []
            for _ in 0..<self.minPoolSize {
                players.append(PlayerView())
            }
            
            self.queue.async {
                self.availablePlayers.append(contentsOf: players)
                Logger.info("PlayerPool: Preloaded \(self.minPoolSize) players")
            }
        }
    }
    
    private func setupMemoryWarningObserver() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func handleMemoryWarning() {
        handleMemoryPressure()
    }
    
    /// 处理内存压力（可被外部调用）
    func handleMemoryPressure() {
        queue.async { [weak self] in
            guard let self = self else { return }
            
            Logger.warning("PlayerPool: Handling memory pressure, cleaning up")
            
            // 保留最少数量的播放器，更积极的清理策略
            let keepCount = max(1, self.minPoolSize / 3)
            let removeCount = max(0, self.availablePlayers.count - keepCount)
            
            // 内存压力下强制清理活跃播放器：先清理低优先级，再清理普通优先级
            let sortedPlayers = self.activePlayers.sorted { player1, player2 in
                let priority1 = self.playerPriority[player1] ?? PlayerPriority.normal.rawValue
                let priority2 = self.playerPriority[player2] ?? PlayerPriority.normal.rawValue
                return priority1 < priority2
            }
            
            var cleanedActiveCount = 0
            let targetActiveCount = self.maxActivePlayerCount
            let playersToRemove = max(0, self.activePlayers.count - targetActiveCount)
            
            for player in sortedPlayers.prefix(playersToRemove + 2) { // 额外清理两个
                player.reset()
                self.activePlayers.remove(player)
                self.playerPriority.removeValue(forKey: player)
                player.cleanup()
                cleanedActiveCount += 1
                
                Logger.debug("PlayerPool: Cleaned up player with priority \(self.playerPriority[player] ?? 0)")
            }
            
            for _ in 0..<removeCount {
                if let player = self.availablePlayers.popLast() {
                    player.cleanup()
                }
            }
            
            Logger.info("PlayerPool: Memory pressure cleanup completed - removed \(removeCount) available players, \(cleanedActiveCount) active players, remaining active: \(self.activePlayers.count)")
        }
    }
}

//// MARK: - PlayerView Extension for Set Conformance
//
//extension PlayerView: Hashable {
//    func hash(into hasher: inout Hasher) {
//        hasher.combine(ObjectIdentifier(self))
//    }
//    
//    static func == (lhs: PlayerView, rhs: PlayerView) -> Bool {
//        return lhs === rhs
//    }
//}
