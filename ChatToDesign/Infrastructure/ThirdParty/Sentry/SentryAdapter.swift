import Foundation
import Sentry

final class SentryAdapter: ErrorReportingService {
  // MARK: - Properties

  private let dsn: String
  private var currentUser: ErrorUser?

  // MARK: - Initialization

  init(dsn: String) {
    self.dsn = dsn
  }

  // MARK: - ErrorReportingService Implementation

  func setup() {
    SentrySDK.start { options in
      options.dsn = self.dsn
      options.debug = false
      options.enableAutoSessionTracking = true

      #if DEBUG
        options.environment = "development"
        options.profilesSampleRate = 1
        options.tracesSampleRate = 1
      #else
        options.environment = "production"
        options.profilesSampleRate = 0.1
        options.tracesSampleRate = 0.1
      #endif
    }
  }

  func setUser(_ user: ErrorUser?) {
    currentUser = user

    if let user = user {
      let sentryUser = Sentry.User(userId: user.id)
      sentryUser.username = user.username
      sentryUser.email = user.email
      sentryUser.data = user.data
      SentrySDK.setUser(sentryUser)
    } else {
      SentrySDK.setUser(nil)
    }
  }

  func setTag(key: String, value: String) {
    SentrySDK.configureScope { scope in
      scope.setTag(value: value, key: key)
    }
  }

  func setExtra(key: String, value: Any) {
    SentrySDK.configureScope { scope in
      scope.setExtra(value: value, key: key)
    }
  }

  func capture(
    error: Error,
    severity: ErrorSeverity,
    context: ErrorContext?
  ) {
    let event = Event(error: error)
    configureEvent(event, severity: severity, context: context)
    SentrySDK.capture(event: event)
  }

  func capture(
    message: String,
    severity: ErrorSeverity,
    context: ErrorContext?
  ) {
    let event = Event(level: convertSeverity(severity))
    event.message = SentryMessage(formatted: message)
    configureEvent(event, severity: severity, context: context)
    SentrySDK.capture(event: event)
  }

  func startPerformanceMonitoring(
    name: String,
    operation: String
  ) -> String {
    let transaction = SentrySDK.startTransaction(
      name: name,
      operation: operation
    )
    return transaction.traceId.sentryIdString
  }

  func finishPerformanceMonitoring(id: String) {
    // Note: Sentry doesn't provide a direct way to retrieve transactions by trace ID
    // This method would need to be redesigned to store transaction references locally
    // For now, we'll leave this as a placeholder
  }

  func addBreadcrumb(
    message: String,
    category: String,
    level: ErrorSeverity
  ) {
    let breadcrumb = Breadcrumb(level: convertSeverity(level), category: category)
    breadcrumb.message = message
    SentrySDK.addBreadcrumb(breadcrumb)
  }

  // MARK: - Private Helpers

  private func configureEvent(
    _ event: Event,
    severity: ErrorSeverity,
    context: ErrorContext?
  ) {
    event.level = convertSeverity(severity)

    if let context = context {
      // 添加标签
      for (key, value) in context.tags {
        event.tags?[key] = value
      }

      // 添加额外信息
      for (key, value) in context.extra {
        event.extra?[key] = value
      }

      // 设置用户信息
      if let user = context.user {
        let sentryUser = Sentry.User(userId: user.id)
        sentryUser.username = user.username
        sentryUser.email = user.email
        sentryUser.data = user.data
        event.user = sentryUser
      }
    }
  }

  private func convertSeverity(_ severity: ErrorSeverity) -> SentryLevel {
    switch severity {
    case .fatal:
      return .fatal
    case .error:
      return .error
    case .warning:
      return .warning
    case .info:
      return .info
    case .debug:
      return .debug
    }
  }
}
