import Foundation

// MARK: - Models

/// 错误严重程度
public enum ErrorSeverity {
  case fatal  // 致命错误
  case error  // 一般错误
  case warning  // 警告
  case info  // 信息
  case debug  // 调试
}

/// 错误上下文
public struct ErrorContext {
  public let tags: [String: String]
  public let extra: [String: Any]
  public let user: ErrorUser?

  public init(
    tags: [String: String] = [:],
    extra: [String: Any] = [:],
    user: ErrorUser? = nil
  ) {
    self.tags = tags
    self.extra = extra
    self.user = user
  }
}

/// 错误用户信息
public struct ErrorUser {
  public let id: String
  public let username: String?
  public let email: String?
  public let data: [String: Any]?

  public init(
    id: String,
    username: String? = nil,
    email: String? = nil,
    data: [String: Any]? = nil
  ) {
    self.id = id
    self.username = username
    self.email = email
    self.data = data
  }
}

// MARK: - Service Interface

/// 错误报告服务接口
public protocol ErrorReportingService {
  /// 配置服务
  func setup()

  /// 设置用户信息
  func setUser(_ user: ErrorUser?)

  /// 添加标签
  func setTag(key: String, value: String)

  /// 添加额外信息
  func setExtra(key: String, value: Any)

  /// 捕获错误
  func capture(
    error: Error,
    severity: ErrorSeverity,
    context: ErrorContext?
  )

  /// 捕获消息
  func capture(
    message: String,
    severity: ErrorSeverity,
    context: ErrorContext?
  )

  /// 开始性能监控
  func startPerformanceMonitoring(
    name: String,
    operation: String
  ) -> String

  /// 结束性能监控
  func finishPerformanceMonitoring(id: String)

  /// 添加面包屑
  func addBreadcrumb(
    message: String,
    category: String,
    level: ErrorSeverity
  )
}
