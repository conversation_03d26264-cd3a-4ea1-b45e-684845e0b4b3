// AnalyticsUserProperties.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/6/16.
//

import Foundation

// MARK: - User Properties Protocol

/// 分析用户Properties协议
public protocol AnalyticsUserProperties {
  /// 用户Properties字典
  var properties: [String: Any] { get }
}

// MARK: - Predefined User Properties

/// 预定义的用户Properties
public struct PredefinedUserProperties: AnalyticsUserProperties {

  // MARK: - Basic Properties

  /// 用户ID
  public let userId: String?
  /// 用户邮箱
  public let email: String?
  /// 用户显示名称
  public let displayName: String?
  /// 用户状态
  public let userStatus: String?
  /// 注册时间
  public let registrationDate: Date?
  /// 最后登录时间
  public let lastLoginDate: Date?

  // MARK: - Subscription Properties

  /// 订阅状态
  public let subscriptionStatus: String?
  /// 订阅开始时间
  public let subscriptionStartDate: Date?
  /// 订阅到期时间
  public let subscriptionExpiryDate: Date?
  /// 是否试用用户
  public let isTrialUser: Bool?

  // MARK: - Behavioral Properties

  /// 总聊天数量
  public let totalChats: Int?
  /// 总消息数量
  public let totalMessages: Int?
  /// 总图片生成数量
  public let totalImageGenerations: Int?
  /// 最后活跃时间
  public let lastActiveDate: Date?
  /// 应用使用天数
  public let appUsageDays: Int?

  // MARK: - Preference Properties

  /// 偏好主题
  public let preferredTheme: String?
  /// 偏好语言
  public let preferredLanguage: String?
  /// 通知设置
  public let notificationsEnabled: Bool?
  /// 地理位置
  public let location: String?

  // MARK: - Technical Properties

  /// 应用版本
  public let appVersion: String?
  /// 设备类型
  public let deviceType: String?
  /// 操作系统版本
  public let osVersion: String?
  /// 首次安装时间
  public let firstInstallDate: Date?

  // MARK: - Initialization

  public init(
    userId: String? = nil,
    email: String? = nil,
    displayName: String? = nil,
    userStatus: String? = nil,
    registrationDate: Date? = nil,
    lastLoginDate: Date? = nil,
    subscriptionStatus: String? = nil,
    subscriptionStartDate: Date? = nil,
    subscriptionExpiryDate: Date? = nil,
    isTrialUser: Bool? = nil,
    totalChats: Int? = nil,
    totalMessages: Int? = nil,
    totalImageGenerations: Int? = nil,
    lastActiveDate: Date? = nil,
    appUsageDays: Int? = nil,
    preferredTheme: String? = nil,
    preferredLanguage: String? = nil,
    notificationsEnabled: Bool? = nil,
    location: String? = nil,
    appVersion: String? = nil,
    deviceType: String? = nil,
    osVersion: String? = nil,
    firstInstallDate: Date? = nil
  ) {
    self.userId = userId
    self.email = email
    self.displayName = displayName
    self.userStatus = userStatus
    self.registrationDate = registrationDate
    self.lastLoginDate = lastLoginDate
    self.subscriptionStatus = subscriptionStatus
    self.subscriptionStartDate = subscriptionStartDate
    self.subscriptionExpiryDate = subscriptionExpiryDate
    self.isTrialUser = isTrialUser
    self.totalChats = totalChats
    self.totalMessages = totalMessages
    self.totalImageGenerations = totalImageGenerations
    self.lastActiveDate = lastActiveDate
    self.appUsageDays = appUsageDays
    self.preferredTheme = preferredTheme
    self.preferredLanguage = preferredLanguage
    self.notificationsEnabled = notificationsEnabled
    self.location = location
    self.appVersion = appVersion
    self.deviceType = deviceType
    self.osVersion = osVersion
    self.firstInstallDate = firstInstallDate
  }

  // MARK: - AnalyticsUserProperties Implementation

  public var properties: [String: Any] {
    var props: [String: Any] = [:]

    // Basic Properties
    if let userId = userId { props["user_id"] = userId }
    if let email = email { props["email"] = email }
    if let displayName = displayName { props["display_name"] = displayName }
    if let userStatus = userStatus { props["user_status"] = userStatus }
    if let registrationDate = registrationDate {
      props["registration_date"] = registrationDate.timeIntervalSince1970
    }
    if let lastLoginDate = lastLoginDate {
      props["last_login_date"] = lastLoginDate.timeIntervalSince1970
    }

    // Subscription Properties
    if let subscriptionStatus = subscriptionStatus {
      props["subscription_status"] = subscriptionStatus
    }
    if let subscriptionStartDate = subscriptionStartDate {
      props["subscription_start_date"] = subscriptionStartDate.timeIntervalSince1970
    }
    if let subscriptionExpiryDate = subscriptionExpiryDate {
      props["subscription_expiry_date"] = subscriptionExpiryDate.timeIntervalSince1970
    }
    if let isTrialUser = isTrialUser { props["is_trial_user"] = isTrialUser }

    // Behavioral Properties
    if let totalChats = totalChats { props["total_chats"] = totalChats }
    if let totalMessages = totalMessages { props["total_messages"] = totalMessages }
    if let totalImageGenerations = totalImageGenerations {
      props["total_image_generations"] = totalImageGenerations
    }
    if let lastActiveDate = lastActiveDate {
      props["last_active_date"] = lastActiveDate.timeIntervalSince1970
    }
    if let appUsageDays = appUsageDays { props["app_usage_days"] = appUsageDays }

    // Preference Properties
    if let preferredTheme = preferredTheme { props["preferred_theme"] = preferredTheme }
    if let preferredLanguage = preferredLanguage { props["preferred_language"] = preferredLanguage }
    if let notificationsEnabled = notificationsEnabled {
      props["notifications_enabled"] = notificationsEnabled
    }
    if let location = location { props["location"] = location }

    // Technical Properties
    if let appVersion = appVersion { props["app_version"] = appVersion }
    if let deviceType = deviceType { props["device_type"] = deviceType }
    if let osVersion = osVersion { props["os_version"] = osVersion }
    if let firstInstallDate = firstInstallDate {
      props["first_install_date"] = firstInstallDate.timeIntervalSince1970
    }

    return props
  }
}

// MARK: - Custom User Properties

/// 自定义用户Properties
public struct CustomUserProperties: AnalyticsUserProperties {
  public let properties: [String: Any]

  public init(properties: [String: Any]) {
    self.properties = properties
  }
}

// MARK: - User Properties Builder

/// 用户Properties构建器
public class UserPropertiesBuilder {
  private var properties: [String: Any] = [:]

  public init() {}

  // MARK: - Basic Properties

  @discardableResult
  public func userId(_ value: String?) -> UserPropertiesBuilder {
    if let value = value { properties["user_id"] = value }
    return self
  }

  @discardableResult
  public func email(_ value: String?) -> UserPropertiesBuilder {
    if let value = value { properties["email"] = value }
    return self
  }

  @discardableResult
  public func displayName(_ value: String?) -> UserPropertiesBuilder {
    if let value = value { properties["display_name"] = value }
    return self
  }

  @discardableResult
  public func subscriptionStatus(_ value: String?) -> UserPropertiesBuilder {
    if let value = value { properties["subscription_status"] = value }
    return self
  }

  @discardableResult
  public func customProperty(key: String, value: Any) -> UserPropertiesBuilder {
    properties[key] = value
    return self
  }

  public func build() -> CustomUserProperties {
    return CustomUserProperties(properties: properties)
  }
}
