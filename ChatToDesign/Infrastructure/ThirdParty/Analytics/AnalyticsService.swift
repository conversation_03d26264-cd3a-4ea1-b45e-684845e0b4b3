// AnalyticsService.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/6/16.
//

import Foundation

// MARK: - Analytics Service Protocol

/// 分析服务协议
public protocol AnalyticsService {

  // MARK: - Configuration

  /// 配置分析服务
  /// - Parameter configuration: 配置信息
  func configure(with configuration: AnalyticsConfiguration) async

  /// 启用或禁用分析收集
  /// - Parameter enabled: 是否启用
  func setAnalyticsCollectionEnabled(_ enabled: Bool)

  // MARK: - Event Tracking

  /// 追踪事件
  /// - Parameter event: 分析事件
  func track(event: AnalyticsEvent)

  /// 追踪多个事件
  /// - Parameter events: 事件数组
  func track(events: [AnalyticsEvent])

  /// 追踪屏幕访问
  /// - Parameters:
  ///   - screenName: 屏幕名称
  ///   - screenClass: 屏幕类别
  func trackScreenView(screenName: String, screenClass: String)

  // MARK: - User Properties

  /// 设置用户Properties
  /// - Parameter properties: 用户Properties
  func setUserProperties(_ properties: AnalyticsUserProperties)

  /// 设置用户ID
  /// - Parameter userId: 用户ID
  func setUserId(_ userId: String?)

  /// 设置单个用户Properties
  /// - Parameters:
  ///   - key: Properties键
  ///   - value: Properties值
  func setUserProperty(key: String, value: Any?)

  // MARK: - Session Management

  /// 开始会话
  func startSession()

  /// 结束会话
  func endSession()

  // MARK: - Error Tracking

  /// 追踪错误
  /// - Parameters:
  ///   - error: 错误对象
  ///   - context: 错误上下文
  func trackError(_ error: Error, context: [String: Any]?)

  /// 追踪自定义错误
  /// - Parameters:
  ///   - errorCode: 错误代码
  ///   - errorMessage: 错误消息
  ///   - context: 错误上下文
  func trackError(code: String, message: String, context: [String: Any]?)

  // MARK: - Performance Tracking

  /// 开始性能追踪
  /// - Parameters:
  ///   - name: 追踪名称
  ///   - attributes: Properties
  /// - Returns: 追踪ID
  func startPerformanceTrace(name: String, attributes: [String: String]?) -> String?

  /// 结束性能追踪
  /// - Parameter traceId: 追踪ID
  func stopPerformanceTrace(traceId: String)

  // MARK: - Conversion Tracking

  /// 追踪转化事件
  /// - Parameters:
  ///   - eventName: 事件名称
  ///   - value: 转化价值
  ///   - currency: 货币类型
  ///   - parameters: 额外参数
  func trackConversion(
    eventName: String, value: Double?, currency: String?, parameters: [String: Any]?)

  // MARK: - Data Management

  /// 刷新待发送的事件
  func flush()

  /// 重置用户数据
  func resetUserData()

  /// 清除所有数据
  func clearAllData()
}

// MARK: - Analytics Configuration

/// 分析服务配置
public struct AnalyticsConfiguration {
  /// 是否启用调试模式
  public let debugEnabled: Bool
  /// 是否启用自动屏幕追踪
  public let autoScreenTrackingEnabled: Bool
  /// 是否启用自动会话追踪
  public let autoSessionTrackingEnabled: Bool
  /// 事件批量上传大小
  public let batchSize: Int
  /// 事件上传间隔（秒）
  public let uploadInterval: TimeInterval
  /// 是否启用崩溃追踪
  public let crashTrackingEnabled: Bool
  /// 是否启用性能监控
  public let performanceMonitoringEnabled: Bool
  /// 自定义配置
  public let customProperties: [String: Any]

  /// Default configuration
  public static let `default` = AnalyticsConfiguration(
    debugEnabled: false,
    autoScreenTrackingEnabled: true,
    autoSessionTrackingEnabled: true,
    batchSize: 20,
    uploadInterval: 30,
    crashTrackingEnabled: true,
    performanceMonitoringEnabled: true,
    customProperties: [:]
  )

  /// 调试配置
  public static let debug = AnalyticsConfiguration(
    debugEnabled: true,
    autoScreenTrackingEnabled: true,
    autoSessionTrackingEnabled: true,
    batchSize: 1,
    uploadInterval: 5,
    crashTrackingEnabled: true,
    performanceMonitoringEnabled: true,
    customProperties: [:]
  )

  public init(
    debugEnabled: Bool = false,
    autoScreenTrackingEnabled: Bool = true,
    autoSessionTrackingEnabled: Bool = true,
    batchSize: Int = 20,
    uploadInterval: TimeInterval = 30,
    crashTrackingEnabled: Bool = true,
    performanceMonitoringEnabled: Bool = true,
    customProperties: [String: Any] = [:]
  ) {
    self.debugEnabled = debugEnabled
    self.autoScreenTrackingEnabled = autoScreenTrackingEnabled
    self.autoSessionTrackingEnabled = autoSessionTrackingEnabled
    self.batchSize = batchSize
    self.uploadInterval = uploadInterval
    self.crashTrackingEnabled = crashTrackingEnabled
    self.performanceMonitoringEnabled = performanceMonitoringEnabled
    self.customProperties = customProperties
  }
}

// MARK: - Analytics Service Extensions

extension AnalyticsService {

  /// 追踪预定义事件的便捷方法
  /// - Parameter event: 预定义事件
  public func track(_ event: PredefinedAnalyticsEvent) {
    Logger.info("AnalyticsService: 追踪事件 - \(event)")
    track(event: event)
  }

  /// 追踪自定义事件的便捷方法
  /// - Parameters:
  ///   - name: 事件名称
  ///   - parameters: 事件参数
  ///   - category: 事件类别
  public func track(
    name: String, parameters: [String: Any] = [:], category: AnalyticsEventCategory = .userBehavior
  ) {
    let event = CustomAnalyticsEvent(name: name, parameters: parameters, category: category)
    track(event: event)
  }

  /// 追踪用户注册
  /// - Parameters:
  ///   - method: 注册方式
  ///   - userId: 用户ID
  public func trackUserSignUp(method: String, userId: String) {
    track(.userSignUp(method: method, userId: userId))
  }

  /// 追踪用户登录
  /// - Parameters:
  ///   - method: 登录方式
  ///   - userId: 用户ID
  public func trackUserSignIn(method: String, userId: String) {
    track(.userSignIn(method: method, userId: userId))
  }

  /// 追踪用户登出
  /// - Parameter userId: 用户ID
  public func trackUserSignOut(userId: String) {
    track(.userSignOut(userId: userId))
  }

  /// 追踪功能使用
  /// - Parameters:
  ///   - featureName: 功能名称
  ///   - userId: 用户ID
  public func trackFeatureUsed(featureName: String, userId: String?) {
    track(.featureUsed(featureName: featureName, userId: userId))
  }

  /// 追踪付费墙展示
  /// - Parameters:
  ///   - trigger: 触发原因
  ///   - userId: 用户ID
  public func trackPaywallShown(trigger: String, userId: String?) {
    track(.paywallShown(trigger: trigger, userId: userId))
  }

  /// 追踪订阅购买
  /// - Parameters:
  ///   - productId: 产品ID
  ///   - userId: 用户ID
  ///   - success: 是否成功
  ///   - revenue: 收入金额
  public func trackSubscriptionPurchase(
    productId: String, userId: String, success: Bool, revenue: Double?
  ) {
    track(
      .subscriptionPurchaseCompleted(
        productId: productId, userId: userId, success: success, revenue: revenue))
  }
}
