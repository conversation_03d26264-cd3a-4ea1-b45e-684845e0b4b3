// AnalyticsScreenTracker.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/6/16.
//

import SwiftUI

/// 屏幕访问追踪 ViewModifier
/// 自动追踪页面的访问和停留时长
public struct AnalyticsScreenTracker: ViewModifier {
  
  // MARK: - Properties
  
  /// 屏幕名称
  private let screenName: String
  
  /// 屏幕类别（可选）
  private let screenClass: String?
  
  /// 分析服务
  private let analyticsService: AnalyticsService
  
  /// 页面进入时间
  @State private var enterTime: Date?
  
  // MARK: - Initialization
  
  /// Initialization屏幕追踪器
  /// - Parameters:
  ///   - screenName: 屏幕名称
  ///   - screenClass: 屏幕类别（可选，默认为nil）
  ///   - analyticsService: 分析服务（默认使用全局服务）
  public init(
    screenName: String,
    screenClass: String? = nil,
    analyticsService: AnalyticsService = AppDependencyContainer.shared.analyticsService
  ) {
    self.screenName = screenName
    self.screenClass = screenClass
    self.analyticsService = analyticsService
  }
  
  // MARK: - ViewModifier Implementation
  
  public func body(content: Content) -> some View {
    content
      .onAppear {
        trackScreenView()
      }
      .onDisappear {
        trackScreenExit()
      }
  }
  
  // MARK: - Private Methods
  
  /// 追踪屏幕访问
  private func trackScreenView() {
    enterTime = Date()
    
    // 追踪屏幕访问事件
    analyticsService.trackScreenView(
      screenName: screenName,
      screenClass: screenClass ?? String(describing: type(of: self))
    )
    
    // 追踪自定义屏幕访问事件（包含更多信息）
    analyticsService.track(
      name: "screen_entered",
      parameters: [
        "screen_name": screenName,
        "screen_class": screenClass ?? "unknown",
        "timestamp": Date().timeIntervalSince1970
      ],
      category: .userBehavior
    )
  }
  
  /// 追踪屏幕退出
  private func trackScreenExit() {
    guard let enterTime = enterTime else { return }
    
    let exitTime = Date()
    let duration = exitTime.timeIntervalSince(enterTime)
    
    // 追踪屏幕退出事件（包含停留时长）
    analyticsService.track(
      name: "screen_exited",
      parameters: [
        "screen_name": screenName,
        "screen_class": screenClass ?? "unknown",
        "duration": duration,
        "timestamp": exitTime.timeIntervalSince1970
      ],
      category: .userBehavior
    )
    
    self.enterTime = nil
  }
}

// MARK: - View Extension

public extension View {
  
  /// 添加屏幕访问追踪
  /// - Parameters:
  ///   - screenName: 屏幕名称
  ///   - screenClass: 屏幕类别（可选）
  ///   - analyticsService: 分析服务（可选，默认使用全局服务）
  /// - Returns: 添加了追踪功能的视图
  func trackScreenView(
    _ screenName: String,
    screenClass: String? = nil,
    analyticsService: AnalyticsService = AppDependencyContainer.shared.analyticsService
  ) -> some View {
    self.modifier(
      AnalyticsScreenTracker(
        screenName: screenName,
        screenClass: screenClass,
        analyticsService: analyticsService
      )
    )
  }
}

// MARK: - Predefined Screen Names

/// 预定义的屏幕名称Constants
public enum ScreenNames {
  
  // MARK: - Authentication Screens
  public static let login = "login"
  public static let signup = "signup"
  
  // MARK: - Main Screens
  public static let home = "home"
  public static let profile = "profile"
  public static let settings = "settings"
  
  // MARK: - Generation Screens
  public static let imageGeneration = "image_generation"
  public static let videoGeneration = "video_generation"
  public static let generationHistory = "generation_history"
  
  // MARK: - Chat Screens
  public static let chatList = "chat_list"
  public static let chatDetail = "chat_detail"
  
  // MARK: - Subscription Screens
  public static let paywall = "paywall"
  public static let subscriptionManagement = "subscription_management"
  
  // MARK: - Content Screens
  public static let cmsContent = "cms_content"
  public static let contentDetail = "content_detail"
  
  // MARK: - Other Screens
  public static let onboarding = "onboarding"
  public static let tutorial = "tutorial"
  public static let about = "about"
}

// MARK: - Screen Classes

/// 预定义的屏幕类别Constants
public enum ScreenClasses {
  public static let authentication = "Authentication"
  public static let main = "Main"
  public static let generation = "Generation"
  public static let chat = "Chat"
  public static let subscription = "Subscription"
  public static let content = "Content"
  public static let settings = "Settings"
  public static let onboarding = "Onboarding"
}
