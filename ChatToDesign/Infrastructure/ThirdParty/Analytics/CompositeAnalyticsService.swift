// CompositeAnalyticsService.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/6/16.
//

import Foundation

/// 复合分析服务 - 支持同时使用多个分析服务
public final class CompositeAnalyticsService: AnalyticsService {
  
  // MARK: - Properties
  
  private var services: [AnalyticsService] = []
  private let logger = Logger.self
  private var isConfigured = false
  
  // MARK: - Initialization
  
  public init(services: [AnalyticsService] = []) {
    self.services = services
  }
  
  // MARK: - Service Management
  
  /// 添加分析服务
  /// - Parameter service: 分析服务
  public func addService(_ service: AnalyticsService) {
    services.append(service)
    logger.debug("CompositeAnalyticsService: 添加服务，当前服务数量: \(services.count)")
  }
  
  /// 移除分析服务
  /// - Parameter serviceType: 服务类型
  public func removeService<T: AnalyticsService>(ofType serviceType: T.Type) {
    services.removeAll { service in
      return type(of: service) == serviceType
    }
    logger.debug("CompositeAnalyticsService: 移除服务，当前服务数量: \(services.count)")
  }
  
  /// 获取指定类型的服务
  /// - Parameter serviceType: 服务类型
  /// - Returns: 服务实例
  public func getService<T: AnalyticsService>(ofType serviceType: T.Type) -> T? {
    return services.first { service in
      return type(of: service) == serviceType
    } as? T
  }
  
  // MARK: - AnalyticsService Implementation
  
  public func configure(with configuration: AnalyticsConfiguration) async {
    logger.info("CompositeAnalyticsService: 开始配置 \(services.count) 个服务")
    
    // 并发配置所有服务
    await withTaskGroup(of: Void.self) { group in
      for service in services {
        group.addTask {
          await service.configure(with: configuration)
        }
      }
    }
    
    isConfigured = true
    logger.info("CompositeAnalyticsService: 所有服务配置完成")
  }
  
  public func setAnalyticsCollectionEnabled(_ enabled: Bool) {
    executeOnAllServices { service in
      service.setAnalyticsCollectionEnabled(enabled)
    }
    logger.info("CompositeAnalyticsService: 数据收集 \(enabled ? "启用" : "禁用")")
  }
  
  // MARK: - Event Tracking
  
  public func track(event: AnalyticsEvent) {
    guard isConfigured else {
      logger.warning("CompositeAnalyticsService: 服务未配置，跳过事件追踪")
      return
    }
    
    executeOnAllServices { service in
      service.track(event: event)
    }
    
    logger.debug("CompositeAnalyticsService: 事件追踪 - \(event.name)")
  }
  
  public func track(events: [AnalyticsEvent]) {
    guard isConfigured else {
      logger.warning("CompositeAnalyticsService: 服务未配置，跳过批量事件追踪")
      return
    }
    
    executeOnAllServices { service in
      service.track(events: events)
    }
    
    logger.debug("CompositeAnalyticsService: 批量事件追踪 - \(events.count) 个事件")
  }
  
  public func trackScreenView(screenName: String, screenClass: String) {
    executeOnAllServices { service in
      service.trackScreenView(screenName: screenName, screenClass: screenClass)
    }
    
    logger.debug("CompositeAnalyticsService: 屏幕访问 - \(screenName)")
  }
  
  // MARK: - User Properties
  
  public func setUserProperties(_ properties: AnalyticsUserProperties) {
    guard isConfigured else {
      logger.warning("CompositeAnalyticsService: 服务未配置，跳过用户Properties设置")
      return
    }
    
    executeOnAllServices { service in
      service.setUserProperties(properties)
    }
    
    logger.debug("CompositeAnalyticsService: 用户Properties设置完成")
  }
  
  public func setUserId(_ userId: String?) {
    executeOnAllServices { service in
      service.setUserId(userId)
    }
    
    logger.debug("CompositeAnalyticsService: 用户ID设置 - \(userId ?? "nil")")
  }
  
  public func setUserProperty(key: String, value: Any?) {
    executeOnAllServices { service in
      service.setUserProperty(key: key, value: value)
    }
    
    logger.debug("CompositeAnalyticsService: 用户Properties设置 - \(key)")
  }
  
  // MARK: - Session Management
  
  public func startSession() {
    executeOnAllServices { service in
      service.startSession()
    }
    
    logger.debug("CompositeAnalyticsService: 会话开始")
  }
  
  public func endSession() {
    executeOnAllServices { service in
      service.endSession()
    }
    
    logger.debug("CompositeAnalyticsService: 会话结束")
  }
  
  // MARK: - Error Tracking
  
  public func trackError(_ error: Error, context: [String: Any]?) {
    executeOnAllServices { service in
      service.trackError(error, context: context)
    }
    
    logger.debug("CompositeAnalyticsService: 错误追踪 - \(error.localizedDescription)")
  }
  
  public func trackError(code: String, message: String, context: [String: Any]?) {
    executeOnAllServices { service in
      service.trackError(code: code, message: message, context: context)
    }
    
    logger.debug("CompositeAnalyticsService: 自定义错误追踪 - \(code)")
  }
  
  // MARK: - Performance Tracking
  
  public func startPerformanceTrace(name: String, attributes: [String: String]?) -> String? {
    let traceId = UUID().uuidString
    
    executeOnAllServices { service in
      _ = service.startPerformanceTrace(name: name, attributes: attributes)
    }
    
    logger.debug("CompositeAnalyticsService: 性能追踪开始 - \(name)")
    return traceId
  }
  
  public func stopPerformanceTrace(traceId: String) {
    executeOnAllServices { service in
      service.stopPerformanceTrace(traceId: traceId)
    }
    
    logger.debug("CompositeAnalyticsService: 性能追踪结束 - \(traceId)")
  }
  
  // MARK: - Conversion Tracking
  
  public func trackConversion(eventName: String, value: Double?, currency: String?, parameters: [String: Any]?) {
    executeOnAllServices { service in
      service.trackConversion(eventName: eventName, value: value, currency: currency, parameters: parameters)
    }
    
    logger.debug("CompositeAnalyticsService: 转化追踪 - \(eventName)")
  }
  
  // MARK: - Data Management
  
  public func flush() {
    executeOnAllServices { service in
      service.flush()
    }
    
    logger.debug("CompositeAnalyticsService: 数据刷新")
  }
  
  public func resetUserData() {
    executeOnAllServices { service in
      service.resetUserData()
    }
    
    logger.debug("CompositeAnalyticsService: 用户数据重置")
  }
  
  public func clearAllData() {
    executeOnAllServices { service in
      service.clearAllData()
    }
    
    logger.debug("CompositeAnalyticsService: 数据清除")
  }
}

// MARK: - Private Helper Methods

private extension CompositeAnalyticsService {
  
  /// 在所有服务上执行操作
  /// - Parameter operation: 操作闭包
  func executeOnAllServices(_ operation: (AnalyticsService) -> Void) {
    for service in services {
      do {
        operation(service)
      } catch {
        logger.error("CompositeAnalyticsService: 服务操作失败 - \(error.localizedDescription)")
      }
    }
  }
  
  /// 异步在所有服务上执行操作
  /// - Parameter operation: 异步操作闭包
  func executeOnAllServicesAsync(_ operation: @escaping (AnalyticsService) async -> Void) async {
    await withTaskGroup(of: Void.self) { group in
      for service in services {
        group.addTask {
          do {
            await operation(service)
          } catch {
            self.logger.error("CompositeAnalyticsService: 异步服务操作失败 - \(error.localizedDescription)")
          }
        }
      }
    }
  }
}

// MARK: - Convenience Initializers

public extension CompositeAnalyticsService {
  
  /// 创建包含 Firebase Analytics 的复合服务
  /// - Returns: 复合分析服务
  static func withFirebase() -> CompositeAnalyticsService {
    let firebaseService = FirebaseAnalyticsAdapter()
    return CompositeAnalyticsService(services: [firebaseService])
  }
  
  /// 创建包含多个服务的复合服务
  /// - Parameter serviceTypes: 服务类型数组
  /// - Returns: 复合分析服务
  static func with(serviceTypes: [AnalyticsService.Type]) -> CompositeAnalyticsService {
    let services = serviceTypes.compactMap { serviceType -> AnalyticsService? in
      if serviceType == FirebaseAnalyticsAdapter.self {
        return FirebaseAnalyticsAdapter()
      }
      // 可以在这里添加其他服务类型的创建逻辑
      return nil
    }
    
    return CompositeAnalyticsService(services: services)
  }
}
