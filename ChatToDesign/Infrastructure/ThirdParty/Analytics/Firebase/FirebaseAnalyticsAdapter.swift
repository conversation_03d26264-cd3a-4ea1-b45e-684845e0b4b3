// FirebaseAnalyticsAdapter.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/6/16.
//

import FirebaseAnalytics
import FirebaseCore
import Foundation

/// Firebase Analytics 适配器
public final class FirebaseAnalyticsAdapter: AnalyticsService {

  // MARK: - Properties

  private var configuration: AnalyticsConfiguration?
  private var isConfigured = false
  private let logger = Logger.self

  // MARK: - Initialization

  public init() {
    // Firebase Analytics 在 FirebaseApp.configure() 后即可使用
    // 无需等待自定义配置完成
    isConfigured = true
    logger.debug("Firebase Analytics: 预配置完成，服务可用")
  }

  // MARK: - AnalyticsService Implementation

  public func configure(with configuration: AnalyticsConfiguration) async {
    self.configuration = configuration

    // 设置调试模式
    if configuration.debugEnabled {
      Analytics.setAnalyticsCollectionEnabled(true)
      logger.debug("Firebase Analytics: 调试模式已启用")
    }

    // 设置自动屏幕追踪
    if !configuration.autoScreenTrackingEnabled {
      // Firebase Analytics 默认启用自动屏幕追踪，这里可以通过配置禁用
      logger.debug("Firebase Analytics: 自动屏幕追踪配置 - \(configuration.autoScreenTrackingEnabled)")
    }

    // 设置自定义配置
    for (key, value) in configuration.customProperties {
      logger.debug("Firebase Analytics: 设置自定义配置 \(key) = \(value)")
    }

    isConfigured = true
    logger.info("Firebase Analytics: 配置完成")
  }

  public func setAnalyticsCollectionEnabled(_ enabled: Bool) {
    Analytics.setAnalyticsCollectionEnabled(enabled)
    logger.info("Firebase Analytics: 数据收集 \(enabled ? "启用" : "禁用")")
  }

  // MARK: - Event Tracking

  public func track(event: AnalyticsEvent) {
    guard isConfigured else {
      logger.warning("Firebase Analytics: 服务未配置，跳过事件追踪")
      return
    }

    let eventName = sanitizeEventName(event.name)
    let parameters = sanitizeParameters(event.parameters)

    Analytics.logEvent(eventName, parameters: parameters)

    logger.debug("Firebase Analytics: 事件追踪 - \(eventName), 参数: \(parameters)")
  }

  public func track(events: [AnalyticsEvent]) {
    for event in events {
      track(event: event)
    }
  }

  public func trackScreenView(screenName: String, screenClass: String) {
    let parameters: [String: Any] = [
      AnalyticsParameterScreenName: screenName,
      AnalyticsParameterScreenClass: screenClass,
    ]

    Analytics.logEvent(AnalyticsEventScreenView, parameters: parameters)
    logger.debug("Firebase Analytics: 屏幕访问 - \(screenName)")
  }

  // MARK: - User Properties

  public func setUserProperties(_ properties: AnalyticsUserProperties) {
    guard isConfigured else {
      logger.warning("Firebase Analytics: 服务未配置，跳过用户Properties设置")
      return
    }

    let sanitizedProperties = sanitizeParameters(properties.properties)

    for (key, value) in sanitizedProperties {
      let sanitizedKey = sanitizePropertyName(key)
      Analytics.setUserProperty(String(describing: value), forName: sanitizedKey)
    }

    logger.debug("Firebase Analytics: 用户Properties设置完成，Properties数量: \(sanitizedProperties.count)")
  }

  public func setUserId(_ userId: String?) {
    Analytics.setUserID(userId)
    logger.debug("Firebase Analytics: 用户ID设置 - \(userId ?? "nil")")
  }

  public func setUserProperty(key: String, value: Any?) {
    let sanitizedKey = sanitizePropertyName(key)
    let stringValue = value != nil ? String(describing: value!) : nil
    Analytics.setUserProperty(stringValue, forName: sanitizedKey)
    logger.debug("Firebase Analytics: 用户Properties设置 - \(sanitizedKey) = \(stringValue ?? "nil")")
  }

  // MARK: - Session Management

  public func startSession() {
    // Firebase Analytics 自动管理会话
    logger.debug("Firebase Analytics: 会话开始（自动管理）")
  }

  public func endSession() {
    // Firebase Analytics 自动管理会话
    logger.debug("Firebase Analytics: 会话结束（自动管理）")
  }

  // MARK: - Error Tracking

  public func trackError(_ error: Error, context: [String: Any]?) {
    var parameters: [String: Any] = [
      "error_description": error.localizedDescription,
      "error_domain": (error as NSError).domain,
      "error_code": (error as NSError).code,
    ]

    if let context = context {
      for (key, value) in context {
        parameters["context_\(key)"] = value
      }
    }

    Analytics.logEvent("error_occurred", parameters: sanitizeParameters(parameters))
    logger.debug("Firebase Analytics: 错误追踪 - \(error.localizedDescription)")
  }

  public func trackError(code: String, message: String, context: [String: Any]?) {
    var parameters: [String: Any] = [
      "error_code": code,
      "error_message": message,
    ]

    if let context = context {
      for (key, value) in context {
        parameters["context_\(key)"] = value
      }
    }

    Analytics.logEvent("error_occurred", parameters: sanitizeParameters(parameters))
    logger.debug("Firebase Analytics: 自定义错误追踪 - \(code): \(message)")
  }

  // MARK: - Performance Tracking

  public func startPerformanceTrace(name: String, attributes: [String: String]?) -> String? {
    // Firebase Analytics 不直接支持性能追踪，这里记录开始事件
    var parameters: [String: Any] = ["trace_name": name]

    if let attributes = attributes {
      for (key, value) in attributes {
        parameters["attr_\(key)"] = value
      }
    }

    Analytics.logEvent("performance_trace_start", parameters: sanitizeParameters(parameters))

    let traceId = UUID().uuidString
    logger.debug("Firebase Analytics: 性能追踪开始 - \(name), ID: \(traceId)")
    return traceId
  }

  public func stopPerformanceTrace(traceId: String) {
    let parameters: [String: Any] = ["trace_id": traceId]
    Analytics.logEvent("performance_trace_stop", parameters: parameters)
    logger.debug("Firebase Analytics: 性能追踪结束 - ID: \(traceId)")
  }

  // MARK: - Conversion Tracking

  public func trackConversion(
    eventName: String, value: Double?, currency: String?, parameters: [String: Any]?
  ) {
    var conversionParameters: [String: Any] = [:]

    if let value = value {
      conversionParameters[AnalyticsParameterValue] = value
    }

    if let currency = currency {
      conversionParameters[AnalyticsParameterCurrency] = currency
    }

    if let parameters = parameters {
      conversionParameters.merge(parameters) { _, new in new }
    }

    let sanitizedEventName = sanitizeEventName(eventName)
    Analytics.logEvent(sanitizedEventName, parameters: sanitizeParameters(conversionParameters))

    logger.debug("Firebase Analytics: 转化追踪 - \(sanitizedEventName), 价值: \(value ?? 0)")
  }

  // MARK: - Data Management

  public func flush() {
    // Firebase Analytics 自动处理数据上传
    logger.debug("Firebase Analytics: 数据刷新（自动处理）")
  }

  public func resetUserData() {
    Analytics.setUserID(nil)
    logger.debug("Firebase Analytics: 用户数据重置")
  }

  public func clearAllData() {
    Analytics.setUserID(nil)
    // Firebase Analytics 不提供清除所有数据的直接方法
    logger.debug("Firebase Analytics: 数据清除（部分支持）")
  }
}

// MARK: - Private Helper Methods

extension FirebaseAnalyticsAdapter {

  /// 清理事件名称，确保符合 Firebase 规范
  fileprivate func sanitizeEventName(_ name: String) -> String {
    // Firebase 事件名称限制：最多40个字符，只能包含字母、数字和下划线
    let sanitized = name.replacingOccurrences(
      of: "[^a-zA-Z0-9_]", with: "_", options: .regularExpression)
    return String(sanitized.prefix(40))
  }

  /// 清理Properties名称，确保符合 Firebase 规范
  fileprivate func sanitizePropertyName(_ name: String) -> String {
    // Firebase 用户Properties名称限制：最多24个字符，只能包含字母、数字和下划线
    let sanitized = name.replacingOccurrences(
      of: "[^a-zA-Z0-9_]", with: "_", options: .regularExpression)
    return String(sanitized.prefix(24))
  }

  /// 清理参数，确保符合 Firebase 规范
  fileprivate func sanitizeParameters(_ parameters: [String: Any]) -> [String: Any] {
    var sanitized: [String: Any] = [:]

    for (key, value) in parameters {
      let sanitizedKey = sanitizePropertyName(key)

      // Firebase 参数值类型限制
      if let stringValue = value as? String {
        sanitized[sanitizedKey] = String(stringValue.prefix(100))  // 限制字符串长度
      } else if let numberValue = value as? NSNumber {
        sanitized[sanitizedKey] = numberValue
      } else if let boolValue = value as? Bool {
        sanitized[sanitizedKey] = boolValue
      } else {
        sanitized[sanitizedKey] = String(describing: value).prefix(100)
      }
    }

    return sanitized
  }
}
