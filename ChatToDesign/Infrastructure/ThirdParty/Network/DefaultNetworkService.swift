//
//  DefaultNetworkService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/7.
//

import Foundation

/// 默认网络服务Implementation
public final class DefaultNetworkService: NetworkService {
  private let session: URLSession

  /// Initialization
  /// - Parameter session: URL会话，默认为共享会话
  public init(session: URLSession = .shared) {
    self.session = session
  }

  /// 下载数据
  /// - Parameter url: 数据URL
  /// - Returns: 下载的数据
  public func downloadData(from url: URL) async throws -> Data {
    do {
      let (data, response) = try await session.data(from: url)

      guard let httpResponse = response as? HTTPURLResponse else {
        throw NetworkServiceError.invalidResponse
      }

      guard 200...299 ~= httpResponse.statusCode else {
        throw NetworkServiceError.httpError(statusCode: httpResponse.statusCode)
      }

      return data
    } catch {
      if let networkError = error as? NetworkServiceError {
        throw networkError
      } else {
        throw NetworkServiceError.requestFailed(error)
      }
    }
  }
}

/// 网络服务错误
public enum NetworkServiceError: Error {
  /// 无效的响应
  case invalidResponse
  /// HTTP错误
  case httpError(statusCode: Int)
  /// 请求失败
  case requestFailed(Error)
}
