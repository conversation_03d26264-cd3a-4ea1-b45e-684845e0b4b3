//
//  FirebaseTokenProvider.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//

import Foundation

/// Firebase Token提供者
/// 用于为Moya的AccessTokenPlugin提供Firebase ID Token
public final class FirebaseTokenProvider {
  
  // MARK: - Properties
  
  private let authService: AuthService
  
  // MARK: - Initialization
  
  /// InitializationFirebase Token提供者
  /// - Parameter authService: 认证服务
  public init(authService: AuthService) {
    self.authService = authService
  }
  
  // MARK: - Token提供方法
  
  /// 获取当前用户的Firebase ID Token
  /// - Returns: Firebase ID Token，如果用户未登录则返回空字符串
  public func getToken() -> String {
    // 检查用户是否已认证
    guard case .authenticated = authService.authState else {
      Logger.debug("用户未认证，跳过token获取")
      return ""
    }
    
    // 由于AccessTokenPlugin的tokenClosure是同步的，我们需要使用同步方式获取token
    // 这里我们使用一个信号量来等待异步操作完成
    let semaphore = DispatchSemaphore(value: 0)
    var token: String = ""
    
    Task {
      do {
        if let idToken = try await authService.getCurrentUserIDToken() {
          token = idToken
          Logger.debug("成功获取Firebase ID Token \(token)")
        } else {
          Logger.debug("Firebase ID Token为空")
        }
      } catch {
        Logger.error("获取Firebase ID Token失败: \(error.localizedDescription)")
      }
      semaphore.signal()
    }
    
    // 等待异步操作完成，最多等待5秒
    let result = semaphore.wait(timeout: .now() + 5.0)
    if result == .timedOut {
      Logger.error("获取Firebase ID Token超时")
    }
    
    return token
  }
}
