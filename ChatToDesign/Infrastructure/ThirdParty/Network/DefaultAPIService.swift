//
//  APIService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//

import Foundation
import Moya

/// API端点，定义所有应用程序使用的网络端点
public enum APIEndpoint {
  /// 处理消息端点
  case processMessage(chatId: String, messageId: String)
  /// 图像生成端点
  case generateDesign(request: DesignGenerationRequest)
  /// CMS数据端点
  case fetchImageUseCaseCMS
  /// 视频用例CMS数据端点
  case fetchVideoUseCaseCMS
  /// 文件上传端点
  case uploadFile(request: FileUploadRequest)
  /// 上传文件并创建资产端点
  case uploadFileWithAsset(request: UploadWithAssetRequest)
  /// 获取用户资产列表端点
  case getUserAssets(query: AssetListQuery)
  /// 删除资产端点
  case deleteAsset(id: String)
  /// 视频生成端点
  case generateVideo(request: VideoGenerationRequest)
  /// Explore数据端点
  case fetchExploreData(query: ExploreQuery)
  /// 获取公开资产详情端点
  case getPublicAssetDetail(id: String)
}

extension APIEndpoint: TargetType, AccessTokenAuthorizable {
  /// API基础URL
  public var baseURL: URL {
    switch self {
    case .processMessage:
      return URL(string: "http://192.168.110.17:3000/api")!  // 实际开发时替换为真实的API基地址
    case .generateDesign, .fetchImageUseCaseCMS, .fetchVideoUseCaseCMS, .uploadFile,
      .uploadFileWithAsset,
      .getUserAssets,
      .deleteAsset, .generateVideo, .fetchExploreData, .getPublicAssetDetail:
      return URL(string: "https://api-test.picadabra.ai")!  // 图像生成API的基地址
    }
  }

  /// API路径
  public var path: String {
    switch self {
    case .processMessage:
      return "/process-message"
    case .generateDesign:
      return "/api/v1/image-generation"
    case .fetchImageUseCaseCMS:
      return "/api/v1/ghibli-cms"
    case .fetchVideoUseCaseCMS:
      return "/api/v1/video-usecase-cms"
    case .uploadFile:
      return "/api/v1/file-upload"
    case .uploadFileWithAsset:
      return "/api/v1/file-upload/with-asset"
    case .getUserAssets:
      return "/api/v1/assets"
    case .deleteAsset(let id):
      return "/api/v1/assets/\(id)"
    case .generateVideo:
      return "/api/v1/video-generation/video/template"
    case .fetchExploreData:
      return "/api/v1/explore"
    case .getPublicAssetDetail(let id):
      return "/api/v1/assets/\(id)/public"
    }
  }

  /// HTTP方法
  public var method: Moya.Method {
    switch self {
    case .processMessage, .generateDesign, .uploadFile, .uploadFileWithAsset, .generateVideo:
      return .post
    case .fetchImageUseCaseCMS, .fetchVideoUseCaseCMS, .getUserAssets, .fetchExploreData,
      .getPublicAssetDetail:
      return .get
    case .deleteAsset:
      return .delete
    }
  }

  /// 样例数据，用于测试
  public var sampleData: Data {
    return Data()  // 在实际开发中提供模拟的响应数据
  }

  /// 请求任务
  public var task: Task {
    switch self {
    case .processMessage(let chatId, let messageId):
      return .requestParameters(
        parameters: ["chatId": chatId, "messageId": messageId],
        encoding: JSONEncoding.default
      )
    case .generateDesign(let request):
      return .requestParameters(
        parameters: [
          "prompt": request.prompt, "imageUrls": request.imageUrls, "userId": request.userId,
        ],
        encoding: JSONEncoding.default
      )
    case .uploadFile(let request):
      return .requestParameters(
        parameters: [
          "mimeType": request.mimeType,
          "base64Data": request.base64Data,
          "prefix": request.prefix,
          "fileName": request.fileName,
        ],
        encoding: JSONEncoding.default
      )
    case .uploadFileWithAsset(let request):
      var parameters: [String: Any] = [
        "mimeType": request.mimeType,
        "base64Data": request.base64Data,
        "prefix": request.prefix,
        "fileName": request.fileName,
      ]

      if let tags = request.tags {
        parameters["tags"] = tags
      }

      if let description = request.description {
        parameters["description"] = description
      }

      if let metadata = request.metadata {
        // Convert AnyCodable dictionary to regular dictionary
        let metadataDict = metadata.mapValues { $0.value }
        parameters["metadata"] = metadataDict
      }

      if let isPublic = request.isPublic {
        parameters["isPublic"] = isPublic
      }

      return .requestParameters(
        parameters: parameters,
        encoding: JSONEncoding.default
      )
    case .getUserAssets(let query):
      var parameters: [String: Any] = [
        "page": query.page,
        "limit": query.limit,
      ]

      if let status = query.status {
        parameters["status"] = status
      }

      if let tags = query.tags {
        parameters["tags"] = tags
      }

      if let search = query.search {
        parameters["search"] = search
      }

      return .requestParameters(
        parameters: parameters,
        encoding: URLEncoding.queryString
      )
    case .fetchImageUseCaseCMS:
      return .requestPlain
    case .fetchVideoUseCaseCMS:
      return .requestPlain
    case .deleteAsset:
      return .requestPlain
    case .generateVideo(let request):
      return .requestParameters(
        parameters: [
          "prompt": request.prompt,
          "imageUrls": request.imageUrls,
          "template": request.template,
        ],
        encoding: JSONEncoding.default
      )
    case .fetchExploreData(let query):
      return .requestParameters(
        parameters: [
          "page": query.page,
          "limit": query.limit,
          "sourceType": query.sourceType,
          "sortBy": query.sortBy,
        ],
        encoding: URLEncoding.queryString
      )
    case .getPublicAssetDetail:
      return .requestPlain
    }
  }

  /// HTTP头部
  public var headers: [String: String]? {
    return ["Content-type": "application/json", "accept": "application/json"]
  }

  // MARK: - AccessTokenAuthorizable

  /// 认证类型
  public var authorizationType: AuthorizationType? {
    switch self {
    case .fetchImageUseCaseCMS, .fetchVideoUseCaseCMS, .fetchExploreData, .getPublicAssetDetail:
      // CMS数据、Explore数据和公开资产详情不需要认证
      return nil
    case .processMessage, .generateDesign, .uploadFile, .uploadFileWithAsset, .getUserAssets,
      .deleteAsset, .generateVideo:
      // 这些端点需要Bearer token认证
      return .bearer
    }
  }
}

/// API服务错误类型
public enum APIServiceError: Error {
  /// 网络错误
  case networkError(Error)
  /// 响应错误
  case responseError(statusCode: Int, message: String)
  /// 解析错误
  case parsingError(Error)
  /// 未知错误
  case unknown(Error)
}

/// 默认API服务Implementation
public final class DefaultAPIService: APIService {
  private let provider: MoyaProvider<APIEndpoint>

  /// Initialization
  /// - Parameter provider: Moya提供者
  public init(provider: MoyaProvider<APIEndpoint> = MoyaProvider<APIEndpoint>()) {
    self.provider = provider
  }

  /// 处理消息
  /// - Parameters:
  ///   - chatId: 聊天ID
  ///   - messageId: 消息ID
  /// - Returns: 处理结果
  public func processMessage(chatId: String, messageId: String) async throws {
    return try await withCheckedThrowingContinuation { continuation in
      provider.request(.processMessage(chatId: chatId, messageId: messageId)) { result in
        switch result {
        case .success(let response):
          // 处理成功响应
          if response.statusCode >= 200 && response.statusCode < 300 {
            continuation.resume(returning: ())
          } else {
            // 处理错误Status code
            let errorMessage =
              (try? response.mapJSON() as? [String: Any])?["error"] as? String ?? "Unknown error"
            continuation.resume(
              throwing: APIServiceError.responseError(
                statusCode: response.statusCode, message: errorMessage))
          }
        case .failure(let error):
          continuation.resume(throwing: APIServiceError.networkError(error))
        }
      }
    }
  }

  /// 生成设计图像
  /// - Parameters:
  ///   - request: 设计生成请求
  /// - Returns: 设计生成响应

  public func generateDesign(request: DesignGenerationRequest) async throws
    -> DesignGenerationResponse
  {
    Logger.debug("Generating design with prompt: \(request.prompt)")
    return try await withCheckedThrowingContinuation { continuation in
      provider.request(.generateDesign(request: request)) { result in
        switch result {
        case .success(let response):
          // 处理成功响应
          if response.statusCode >= 200 && response.statusCode < 300 {
            do {
              let designResponse = try JSONDecoder().decode(
                DesignGenerationResponse.self, from: response.data)
              continuation.resume(returning: designResponse)
            } catch {
              continuation.resume(throwing: APIServiceError.parsingError(error))
            }
          } else {
            // 处理错误Status code
            let errorMessage =
              (try? response.mapJSON() as? [String: Any])?["error"] as? String ?? "Unknown error"
            continuation.resume(
              throwing: APIServiceError.responseError(
                statusCode: response.statusCode, message: errorMessage))
          }
        case .failure(let error):
          continuation.resume(throwing: APIServiceError.networkError(error))
        }
      }
    }
  }

  /// 上传文件
  /// - Parameter request: 文件上传请求
  /// - Returns: 文件上传响应
  public func uploadFile(request: FileUploadRequest) async throws -> FileUploadResponse {
    Logger.debug("Uploading file: \(request.fileName) with type: \(request.mimeType)")
    return try await withCheckedThrowingContinuation { continuation in
      provider.request(.uploadFile(request: request)) { result in
        switch result {
        case .success(let response):
          // 处理成功响应
          if response.statusCode >= 200 && response.statusCode < 300 {
            do {
              let uploadResponse = try JSONDecoder().decode(
                FileUploadResponse.self, from: response.data)
              Logger.debug("File uploaded successfully: \(uploadResponse.url)")
              continuation.resume(returning: uploadResponse)
            } catch {
              Logger.error("Failed to parse upload response: \(error.localizedDescription)")
              continuation.resume(throwing: APIServiceError.parsingError(error))
            }
          } else {
            // 处理错误Status code
            let errorMessage =
              (try? response.mapJSON() as? [String: Any])?["error"] as? String ?? "Unknown error"
            Logger.error("Upload failed with status \(response.statusCode): \(errorMessage)")
            continuation.resume(
              throwing: APIServiceError.responseError(
                statusCode: response.statusCode, message: errorMessage))
          }
        case .failure(let error):
          Logger.error("Upload network error: \(error.localizedDescription)")
          continuation.resume(throwing: APIServiceError.networkError(error))
        }
      }
    }
  }

  /// 上传文件并创建资产
  /// - Parameter request: 上传文件并创建资产请求
  /// - Returns: 资产响应
  public func uploadFileWithAsset(request: UploadWithAssetRequest) async throws -> UserAsset {
    Logger.debug("Uploading file with asset: \(request.fileName) with type: \(request.mimeType)")
    return try await withCheckedThrowingContinuation { continuation in
      provider.request(.uploadFileWithAsset(request: request)) { result in
        switch result {
        case .success(let response):
          // 处理成功响应
          if response.statusCode >= 200 && response.statusCode < 300 {
            do {
              // 先打印原始 JSON 数据用于调试
              if let jsonString = String(data: response.data, encoding: .utf8) {
                Logger.debug("Raw asset response JSON: \(jsonString)")
              }

              let assetResponse = try JSONDecoder().decode(
                UserAsset.self, from: response.data)
              Logger.debug("File uploaded with asset successfully: \(assetResponse.url)")
              continuation.resume(returning: assetResponse)
            } catch {
              Logger.error("Failed to parse asset response: \(error.localizedDescription)")

              // 添加详细的解码Error message
              if let decodingError = error as? DecodingError {
                switch decodingError {
                case .keyNotFound(let key, let context):
                  Logger.error(
                    "Missing key '\(key.stringValue)' at path: \(context.codingPath.map { $0.stringValue }.joined(separator: "."))"
                  )
                case .typeMismatch(let type, let context):
                  Logger.error(
                    "Type mismatch for type '\(type)' at path: \(context.codingPath.map { $0.stringValue }.joined(separator: "."))"
                  )
                case .valueNotFound(let type, let context):
                  Logger.error(
                    "Value not found for type '\(type)' at path: \(context.codingPath.map { $0.stringValue }.joined(separator: "."))"
                  )
                case .dataCorrupted(let context):
                  Logger.error(
                    "Data corrupted at path: \(context.codingPath.map { $0.stringValue }.joined(separator: "."))"
                  )
                @unknown default:
                  Logger.error("Unknown decoding error: \(decodingError)")
                }
              }

              continuation.resume(throwing: APIServiceError.parsingError(error))
            }
          } else {
            // 处理错误Status code
            let errorMessage =
              (try? response.mapJSON() as? [String: Any])?["error"] as? String ?? "Unknown error"
            Logger.error(
              "Upload with asset failed with status \(response.statusCode): \(errorMessage)")
            continuation.resume(
              throwing: APIServiceError.responseError(
                statusCode: response.statusCode, message: errorMessage))
          }
        case .failure(let error):
          Logger.error("Upload with asset network error: \(error.localizedDescription)")
          continuation.resume(throwing: APIServiceError.networkError(error))
        }
      }
    }
  }

  /// 获取用户资产列表
  /// - Parameter query: 查询参数
  /// - Returns: 用户资产列表响应
  public func getUserAssets(query: AssetListQuery) async throws -> UserAssetsResult {
    Logger.debug("Getting user assets with query: page=\(query.page), limit=\(query.limit)")
    return try await withCheckedThrowingContinuation { continuation in
      provider.request(.getUserAssets(query: query)) { result in
        switch result {
        case .success(let response):
          // 处理成功响应
          if response.statusCode >= 200 && response.statusCode < 300 {
            do {
              let assetsResponse = try JSONDecoder().decode(
                UserAssetsResult.self, from: response.data)
              Logger.debug("Successfully fetched \(assetsResponse.assets.count) assets")
              continuation.resume(returning: assetsResponse)
            } catch {
              Logger.error("Failed to parse user assets response: \(error.localizedDescription)")
              continuation.resume(throwing: APIServiceError.parsingError(error))
            }
          } else {
            // 处理错误Status code
            let errorMessage =
              (try? response.mapJSON() as? [String: Any])?["error"] as? String ?? "Unknown error"
            Logger.error(
              "Get user assets failed with status \(response.statusCode): \(errorMessage)")
            continuation.resume(
              throwing: APIServiceError.responseError(
                statusCode: response.statusCode, message: errorMessage))
          }
        case .failure(let error):
          Logger.error("Get user assets network error: \(error.localizedDescription)")
          continuation.resume(throwing: APIServiceError.networkError(error))
        }
      }
    }
  }

  /// 删除资产
  /// - Parameter id: 资产ID
  /// - Returns: 删除响应
  public func deleteAsset(id: String) async throws -> AssetDeleteResponse {
    Logger.debug("Deleting asset with id: \(id)")
    return try await withCheckedThrowingContinuation { continuation in
      provider.request(.deleteAsset(id: id)) { result in
        switch result {
        case .success(let response):
          // 处理成功响应
          if response.statusCode >= 200 && response.statusCode < 300 {
            do {
              let deleteResponse = try JSONDecoder().decode(
                AssetDeleteResponse.self, from: response.data)
              Logger.debug("Asset deleted successfully: \(deleteResponse.id)")
              continuation.resume(returning: deleteResponse)
            } catch {
              Logger.error("Failed to parse delete response: \(error.localizedDescription)")
              continuation.resume(throwing: APIServiceError.parsingError(error))
            }
          } else {
            // 处理错误Status code
            let errorMessage =
              (try? response.mapJSON() as? [String: Any])?["error"] as? String ?? "Unknown error"
            Logger.error("Delete asset failed with status \(response.statusCode): \(errorMessage)")
            continuation.resume(
              throwing: APIServiceError.responseError(
                statusCode: response.statusCode, message: errorMessage))
          }
        case .failure(let error):
          Logger.error("Delete asset network error: \(error.localizedDescription)")
          continuation.resume(throwing: APIServiceError.networkError(error))
        }
      }
    }
  }

  /// 获取CMS数据
  /// - Returns: CMS数据数组
  public func fetchImageUseCaseCMS() async throws -> [ImageTemplateItem] {
    Logger.debug("Fetching CMS data")
    return try await withCheckedThrowingContinuation { continuation in
      provider.request(.fetchImageUseCaseCMS) { result in
        switch result {
        case .success(let response):
          // 处理成功响应
          if response.statusCode >= 200 && response.statusCode < 300 {
            do {
              let ImageTemplateItems = try JSONDecoder().decode(
                [ImageTemplateItem].self, from: response.data)
              Logger.debug("Successfully fetched \(ImageTemplateItems.count) CMS items")
              continuation.resume(returning: ImageTemplateItems.filter { !$0.isDisabled })
            } catch {
              Logger.error("Failed to parse CMS data: \(error.localizedDescription)")
              continuation.resume(throwing: APIServiceError.parsingError(error))
            }
          } else {
            // 处理错误Status code
            let errorMessage =
              (try? response.mapJSON() as? [String: Any])?["error"] as? String ?? "Unknown error"
            Logger.error("CMS fetch failed with status \(response.statusCode): \(errorMessage)")
            continuation.resume(
              throwing: APIServiceError.responseError(
                statusCode: response.statusCode, message: errorMessage))
          }
        case .failure(let error):
          Logger.error("CMS fetch network error: \(error.localizedDescription)")
          continuation.resume(throwing: APIServiceError.networkError(error))
        }
      }
    }
  }

  /// 获取视频用例CMS数据
  /// - Returns: 视频用例响应数组
  public func fetchVideoUseCaseCMS() async throws -> [VideoUseCaseResponse] {
    Logger.debug("Fetching video use case CMS data")
    return try await withCheckedThrowingContinuation { continuation in
      provider.request(.fetchVideoUseCaseCMS) { result in
        switch result {
        case .success(let response):
          // 处理成功响应
          if response.statusCode >= 200 && response.statusCode < 300 {
            do {
              let videoUseCases = try JSONDecoder().decode(
                [VideoUseCaseResponse].self, from: response.data)
              Logger.debug("Successfully fetched \(videoUseCases.count) video use cases")
              continuation.resume(returning: videoUseCases)
            } catch {
              Logger.error("Failed to parse video use case data: \(error.localizedDescription)")
              continuation.resume(throwing: APIServiceError.parsingError(error))
            }
          } else {
            // 处理错误Status code
            let errorMessage =
              (try? response.mapJSON() as? [String: Any])?["error"] as? String ?? "Unknown error"
            Logger.error(
              "Video use case fetch failed with status \(response.statusCode): \(errorMessage)")
            continuation.resume(
              throwing: APIServiceError.responseError(
                statusCode: response.statusCode, message: errorMessage))
          }
        case .failure(let error):
          Logger.error("Video use case fetch network error: \(error.localizedDescription)")
          continuation.resume(throwing: APIServiceError.networkError(error))
        }
      }
    }
  }

  /// 生成视频
  /// - Parameter request: 视频生成请求
  /// - Returns: 视频生成响应
  public func generateVideo(request: VideoGenerationRequest) async throws -> VideoGenerationResponse
  {
    Logger.debug("Generating video with request: \(request)")
    return try await withCheckedThrowingContinuation { continuation in
      provider.request(.generateVideo(request: request)) { result in
        switch result {
        case .success(let response):
          // 处理成功响应
          if response.statusCode >= 200 && response.statusCode < 300 {
            do {
              let videoResponse = try JSONDecoder().decode(
                VideoGenerationResponse.self, from: response.data)
              Logger.debug(
                "Video generation started successfully with taskId: \(videoResponse.taskId)")
              continuation.resume(returning: videoResponse)
            } catch {
              Logger.error(
                "Failed to parse video generation response: \(error.localizedDescription)")
              continuation.resume(throwing: APIServiceError.parsingError(error))
            }
          } else {
            // 处理错误Status code
            let errorMessage =
              (try? response.mapJSON() as? [String: Any])?["error"] as? String ?? "Unknown error"
            Logger.error(
              "Video generation failed with status \(response.statusCode): \(errorMessage)")
            continuation.resume(
              throwing: APIServiceError.responseError(
                statusCode: response.statusCode, message: errorMessage))
          }
        case .failure(let error):
          Logger.error("Video generation network error: \(error.localizedDescription)")
          continuation.resume(throwing: APIServiceError.networkError(error))
        }
      }
    }
  }

  /// 获取 Explore 数据
  /// - Parameter query: 查询参数
  /// - Returns: Explore 响应数据
  public func fetchExploreData(query: ExploreQuery) async throws -> ExploreResponse {
    Logger.debug(
      "Fetching explore data with query: page=\(query.page), limit=\(query.limit), sourceType=\(query.sourceType), sortBy=\(query.sortBy)"
    )
    return try await withCheckedThrowingContinuation { continuation in
      provider.request(.fetchExploreData(query: query)) { result in
        switch result {
        case .success(let response):
          // 处理成功响应
          if response.statusCode >= 200 && response.statusCode < 300 {
            do {
              let exploreResponse = try JSONDecoder().decode(
                ExploreResponse.self, from: response.data)
              Logger.debug("Successfully fetched \(exploreResponse.items.count) explore items")
              continuation.resume(returning: exploreResponse)
            } catch {
              Logger.error("Failed to parse explore data: \(error.localizedDescription)")
              continuation.resume(throwing: APIServiceError.parsingError(error))
            }
          } else {
            // 处理错误Status code
            let errorMessage =
              (try? response.mapJSON() as? [String: Any])?["error"] as? String ?? "Unknown error"
            Logger.error("Explore fetch failed with status \(response.statusCode): \(errorMessage)")
            continuation.resume(
              throwing: APIServiceError.responseError(
                statusCode: response.statusCode, message: errorMessage))
          }
        case .failure(let error):
          Logger.error("Explore fetch network error: \(error.localizedDescription)")
          continuation.resume(throwing: APIServiceError.networkError(error))
        }
      }
    }
  }

  /// 获取公开资产详情
  /// - Parameter id: 资产ID
  /// - Returns: 用户资产详情
  public func getPublicAssetDetail(id: String) async throws -> UserAsset {
    Logger.debug("Getting public asset detail for id: \(id)")
    return try await withCheckedThrowingContinuation { continuation in
      provider.request(.getPublicAssetDetail(id: id)) { result in
        switch result {
        case .success(let response):
          if response.statusCode >= 200 && response.statusCode < 300 {
            do {
              let asset = try JSONDecoder().decode(UserAsset.self, from: response.data)
              Logger.debug("Successfully fetched public asset detail for id: \(id)")
              continuation.resume(returning: asset)
            } catch {
              Logger.error("Failed to parse public asset detail: \(error.localizedDescription)")
              continuation.resume(throwing: APIServiceError.parsingError(error))
            }
          } else {
            // 处理错误Status code
            let errorMessage =
              (try? response.mapJSON() as? [String: Any])?["error"] as? String ?? "Unknown error"
            Logger.error(
              "Public asset detail fetch failed with status \(response.statusCode): \(errorMessage)"
            )
            continuation.resume(
              throwing: APIServiceError.responseError(
                statusCode: response.statusCode, message: errorMessage))
          }
        case .failure(let error):
          Logger.error("Public asset detail fetch network error: \(error.localizedDescription)")
          continuation.resume(throwing: APIServiceError.networkError(error))
        }
      }
    }
  }

}
