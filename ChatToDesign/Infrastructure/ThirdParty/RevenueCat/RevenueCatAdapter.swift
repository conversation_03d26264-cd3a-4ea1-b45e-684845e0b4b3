//
//  RevenueCatAdapter.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/15.
//

import Combine
import Foundation
import RevenueCat

/// RevenueCat适配器
/// ImplementationSubscriptionService协议，封装RevenueCat SDK的功能
public final class RevenueCatAdapter: NSObject, SubscriptionService {

  // MARK: - Private Properties

  private var isConfigured: Bool = false

  // Combine Subjects
  private let subscriptionStatusSubject = CurrentValueSubject<Subscription?, Never>(nil)
  private let entitlementsSubject = CurrentValueSubject<EntitlementSet, Never>(.empty)

  // MARK: - SubscriptionService Protocol

  public var subscriptionStatusPublisher: AnyPublisher<Subscription?, Never> {
    subscriptionStatusSubject.eraseToAnyPublisher()
  }

  public var entitlementsPublisher: AnyPublisher<EntitlementSet, Never> {
    entitlementsSubject.eraseToAnyPublisher()
  }

  // MARK: - Initialization

  /// InitializationRevenueCat适配器
  public override init() {
    super.init()
  }

  // MARK: - SubscriptionService Implementation

  public func configure(apiKey: String, userId: String?) async throws {
    guard !isConfigured else {
      Logger.warning("RevenueCatAdapter: 已经配置过，跳过重复配置")
      return
    }

    Logger.info("RevenueCatAdapter: 开始配置 RevenueCat")

    // 配置RevenueCat
    #if DEBUG
      Purchases.logLevel = .debug
    #else
      Purchases.logLevel = .error
    #endif

    Logger.info(
      "RevenueCatAdapter: 配置 RevenueCat - API Key: \(apiKey.prefix(10))..., User ID: \(userId ?? "nil")"
    )
    Purchases.configure(withAPIKey: apiKey, appUserID: userId)
    Purchases.shared.delegate = self

    isConfigured = true
    Logger.info("RevenueCatAdapter: RevenueCat 配置完成")

    // Initialization后立即获取用户信息
    await refreshCustomerInfo()
  }

  public func getCurrentSubscription() async throws -> Subscription? {
    guard isConfigured else {
      throw SubscriptionServiceError.notConfigured
    }

    return try await withCheckedThrowingContinuation { continuation in
      Purchases.shared.getCustomerInfo { customerInfo, error in
        if let error = error {
          continuation.resume(throwing: self.mapRevenueCatError(error))
          return
        }

        guard let customerInfo = customerInfo else {
          continuation.resume(returning: nil)
          return
        }

        let subscription = self.mapCustomerInfoToSubscription(customerInfo)
        continuation.resume(returning: subscription)
      }
    }
  }

  public func getEntitlements() async throws -> EntitlementSet {
    guard isConfigured else {
      throw SubscriptionServiceError.notConfigured
    }

    return try await withCheckedThrowingContinuation { continuation in
      Purchases.shared.getCustomerInfo { customerInfo, error in
        if let error = error {
          continuation.resume(throwing: self.mapRevenueCatError(error))
          return
        }

        guard let customerInfo = customerInfo else {
          continuation.resume(returning: .empty)
          return
        }

        let entitlements = self.mapCustomerInfoToEntitlements(customerInfo)
        continuation.resume(returning: entitlements)
      }
    }
  }

  public func getAvailableProducts() async throws -> [ProductInfo] {
    guard isConfigured else {
      Logger.error("RevenueCatAdapter: 尝试获取产品但服务未配置")
      throw SubscriptionServiceError.notConfigured
    }

    Logger.info("RevenueCatAdapter: 开始获取可用产品")

    // 添加诊断信息
    Logger.info("RevenueCatAdapter: 当前 Bundle ID - \(Bundle.main.bundleIdentifier ?? "未知")")
    Logger.info("RevenueCatAdapter: RevenueCat App User ID - \(Purchases.shared.appUserID)")
    Logger.info("RevenueCatAdapter: RevenueCat 是否匿名 - \(Purchases.shared.isAnonymous)")

    return try await withCheckedThrowingContinuation { continuation in
      Purchases.shared.getOfferings { offerings, error in
        if let error = error {
          Logger.error("RevenueCatAdapter: 获取 Offerings 失败 - \(error)")
          Logger.error("RevenueCatAdapter: 错误详情 - \(error.localizedDescription)")
          if let nsError = error as NSError? {
            Logger.error("RevenueCatAdapter: 错误代码 - \(nsError.code), 域 - \(nsError.domain)")
            Logger.error("RevenueCatAdapter: 用户信息 - \(nsError.userInfo)")

            // 添加更详细的错误诊断
            if nsError.domain == "RevenueCat.OfferingsManager.Error" && nsError.code == 1 {
              Logger.error("RevenueCatAdapter: 这是配置问题 - 产品无法从 App Store Connect 获取")
              Logger.error("RevenueCatAdapter: 可能原因：")
              Logger.error("RevenueCatAdapter: 1. 开发者账户状态问题")
              Logger.error("RevenueCatAdapter: 2. Bundle ID 不匹配")
              Logger.error("RevenueCatAdapter: 3. App Store Connect 中没有配置产品")
              Logger.error(
                "RevenueCatAdapter: 4. RevenueCat Dashboard 中的产品 ID 与 App Store Connect 不匹配")
            }
          }
          continuation.resume(throwing: self.mapRevenueCatError(error))
          return
        }

        guard let offerings = offerings else {
          Logger.warning("RevenueCatAdapter: Offerings 为空")
          continuation.resume(returning: [])
          return
        }

        Logger.info("RevenueCatAdapter: 成功获取 Offerings - \(offerings.all.count) 个")
        Logger.debug(
          "RevenueCatAdapter: Current Offering - \(offerings.current?.identifier ?? "无")")

        let products = self.mapOfferingsToProducts(offerings)
        Logger.info("RevenueCatAdapter: 映射得到 \(products.count) 个产品")

        for product in products {
          Logger.debug(
            "RevenueCatAdapter: 产品 - ID: \(product.id), 名称: \(product.displayName), 价格: \(product.price)"
          )
        }

        continuation.resume(returning: products)
      }
    }
  }

  public func purchaseSubscription(productId: String) async throws -> Subscription {
    guard isConfigured else {
      throw SubscriptionServiceError.notConfigured
    }

    // 首先获取产品
    let offerings = try await getOfferings()
    guard let package = findPackage(productId: productId, in: offerings) else {
      throw SubscriptionServiceError.purchaseFailed("找不到产品: \(productId)")
    }

    return try await withCheckedThrowingContinuation { continuation in
      Purchases.shared.purchase(package: package) {
        transaction, customerInfo, error, userCancelled in
        if userCancelled {
          continuation.resume(throwing: SubscriptionServiceError.purchaseCancelled)
          return
        }

        if let error = error {
          continuation.resume(throwing: self.mapRevenueCatError(error))
          return
        }

        guard let customerInfo = customerInfo else {
          continuation.resume(throwing: SubscriptionServiceError.purchaseFailed("无法获取用户信息"))
          return
        }

        if let subscription = self.mapCustomerInfoToSubscription(customerInfo) {
          continuation.resume(returning: subscription)
        } else {
          continuation.resume(throwing: SubscriptionServiceError.purchaseFailed("购买成功但无法解析订阅信息"))
        }
      }
    }
  }

  public func restorePurchases() async throws -> Subscription? {
    guard isConfigured else {
      throw SubscriptionServiceError.notConfigured
    }

    return try await withCheckedThrowingContinuation { continuation in
      Purchases.shared.restorePurchases { customerInfo, error in
        if let error = error {
          continuation.resume(throwing: self.mapRevenueCatError(error))
          return
        }

        guard let customerInfo = customerInfo else {
          continuation.resume(returning: nil)
          return
        }

        let subscription = self.mapCustomerInfoToSubscription(customerInfo)
        continuation.resume(returning: subscription)
      }
    }
  }

  public func hasEntitlement(_ entitlementType: EntitlementType) async throws -> Bool {
    let entitlements = try await getEntitlements()
    return entitlements.hasEntitlement(entitlementType)
  }

  public func loginUser(_ userId: String) async throws {
    guard isConfigured else {
      throw SubscriptionServiceError.notConfigured
    }

    Logger.info("RevenueCatAdapter: 开始登录用户 - \(userId)")

    return try await withCheckedThrowingContinuation { continuation in
      Purchases.shared.logIn(userId) { customerInfo, created, error in
        if let error = error {
          Logger.error("RevenueCatAdapter: 用户登录失败 - \(userId): \(error)")
          continuation.resume(throwing: self.mapRevenueCatError(error))
          return
        }

        Logger.info(
          "RevenueCatAdapter: 用户登录成功 - \(userId), 新用户: \(created), AppUserID: \(customerInfo?.originalAppUserId ?? "unknown")"
        )
        continuation.resume()
      }
    }
  }

  public func logoutUser() async throws {
    guard isConfigured else {
      throw SubscriptionServiceError.notConfigured
    }

    return try await withCheckedThrowingContinuation { continuation in
      Purchases.shared.logOut { customerInfo, error in
        if let error = error {
          continuation.resume(throwing: self.mapRevenueCatError(error))
          return
        }

        Logger.info("RevenueCatAdapter: 用户登出成功")
        continuation.resume()
      }
    }
  }

  // MARK: - Private Helper Methods

  /// 刷新用户信息
  private func refreshCustomerInfo() async {
    do {
      let subscription = try await getCurrentSubscription()
      let entitlements = try await getEntitlements()

      subscriptionStatusSubject.send(subscription)
      entitlementsSubject.send(entitlements)
    } catch {
      Logger.error("RevenueCatAdapter: 刷新用户信息失败 - \(error)")
    }
  }

  /// 获取Offerings
  private func getOfferings() async throws -> Offerings {
    return try await withCheckedThrowingContinuation { continuation in
      Purchases.shared.getOfferings { offerings, error in
        if let error = error {
          continuation.resume(throwing: self.mapRevenueCatError(error))
          return
        }

        guard let offerings = offerings else {
          continuation.resume(throwing: SubscriptionServiceError.purchaseFailed("无法获取产品信息"))
          return
        }

        continuation.resume(returning: offerings)
      }
    }
  }

  /// 查找指定产品ID的Package
  private func findPackage(productId: String, in offerings: Offerings) -> Package? {
    // 在当前Offering中查找
    if let currentOffering = offerings.current {
      for package in currentOffering.availablePackages {
        if package.storeProduct.productIdentifier == productId {
          return package
        }
      }
    }

    // 在所有Offerings中查找
    for offering in offerings.all.values {
      for package in offering.availablePackages {
        if package.storeProduct.productIdentifier == productId {
          return package
        }
      }
    }

    return nil
  }

  /// 将RevenueCat错误映射为SubscriptionServiceError
  private func mapRevenueCatError(_ error: Error) -> SubscriptionServiceError {
    // 检查是否是配置相关的错误
    let errorDescription = error.localizedDescription.lowercased()
    if errorDescription.contains("configuration") || errorDescription.contains("offerings")
      || errorDescription.contains("products registered")
    {
      Logger.error("RevenueCatAdapter: 检测到配置错误 - \(error.localizedDescription)")
      return .purchaseFailed("RevenueCat 配置错误：请检查产品配置和 API 密钥")
    }

    if let errorCode = error as? ErrorCode {
      switch errorCode {
      case .paymentPendingError:
        return .purchaseInProgress
      case .purchaseCancelledError:
        return .purchaseCancelled
      case .networkError:
        return .networkError
      case .receiptAlreadyInUseError:
        return .purchaseFailed("收据已被使用")
      case .invalidReceiptError:
        return .purchaseFailed("收据无效")
      case .missingReceiptFileError:
        return .purchaseFailed("收据文件缺失")
      case .productNotAvailableForPurchaseError:
        return .purchaseFailed("产品不可购买")
      case .purchaseNotAllowedError:
        return .purchaseFailed("不允许购买")
      case .purchaseInvalidError:
        return .purchaseFailed("购买无效")
      case .productAlreadyPurchasedError:
        return .purchaseFailed("产品已购买")
      case .configurationError:
        return .purchaseFailed("RevenueCat 配置错误")
      case .unknownError:
        return .unknownError(error)
      default:
        return .purchaseFailed(error.localizedDescription)
      }
    }

    return .unknownError(error)
  }

  /// 将CustomerInfo映射为Subscription
  private func mapCustomerInfoToSubscription(_ customerInfo: CustomerInfo) -> Subscription? {
    // 查找活跃的权限 - 直接从entitlements中查找活跃的权限
    guard let activeEntitlement = customerInfo.entitlements.all.values.first(where: { $0.isActive })
    else {
      return nil
    }

    // 确定订阅层级
    let tier = determineSubscriptionTier(from: activeEntitlement.productIdentifier)

    // 确定订阅状态
    let status = mapEntitlementInfoToStatus(activeEntitlement)

    // 确定是否在试用期 - 检查periodType是否为trial
    let isInTrialPeriod = activeEntitlement.periodType == .trial

    return Subscription(
      id: activeEntitlement.productIdentifier,
      productId: activeEntitlement.productIdentifier,
      status: status,
      tier: tier,
      purchaseDate: activeEntitlement.latestPurchaseDate ?? Date(),
      expirationDate: activeEntitlement.expirationDate,
      willRenew: activeEntitlement.willRenew,
      isInTrialPeriod: isInTrialPeriod,
      trialEndDate: isInTrialPeriod ? activeEntitlement.expirationDate : nil
    )
  }

  /// 将CustomerInfo映射为EntitlementSet
  private func mapCustomerInfoToEntitlements(_ customerInfo: CustomerInfo) -> EntitlementSet {
    let entitlements = customerInfo.entitlements.all.compactMap {
      (key, entitlementInfo) -> Entitlement? in
      guard let entitlementType = mapIdentifierToEntitlementType(key) else {
        return nil
      }

      // 确定是否在试用期 - 检查periodType是否为trial
      let isInTrialPeriod = entitlementInfo.periodType == .trial

      return Entitlement(
        id: key,
        identifier: key,
        type: entitlementType,
        isActive: entitlementInfo.isActive,
        willRenew: entitlementInfo.willRenew,
        latestPurchaseDate: entitlementInfo.latestPurchaseDate,
        expirationDate: entitlementInfo.expirationDate,
        productIdentifier: entitlementInfo.productIdentifier,
        isInTrialPeriod: isInTrialPeriod
      )
    }

    return EntitlementSet(entitlements: entitlements)
  }

  /// 将Offerings映射为ProductInfo数组
  private func mapOfferingsToProducts(_ offerings: Offerings) -> [ProductInfo] {
    var products: [ProductInfo] = []

    Logger.debug("RevenueCatAdapter: 开始映射 Offerings 到产品")
    Logger.debug("RevenueCatAdapter: 总共有 \(offerings.all.count) 个 Offering")

    // 处理当前Offering
    if let currentOffering = offerings.current {
      Logger.info("RevenueCatAdapter: 处理当前 Offering - \(currentOffering.identifier)")
      Logger.debug("RevenueCatAdapter: 当前 Offering 有 \(currentOffering.availablePackages.count) 个包")

      for package in currentOffering.availablePackages {
        Logger.debug("RevenueCatAdapter: 处理包 - \(package.identifier)")
        let product = mapPackageToProductInfo(package)
        products.append(product)
        Logger.debug("RevenueCatAdapter: 成功映射产品 - \(product.displayName)")
      }
    } else {
      Logger.warning("RevenueCatAdapter: 没有当前 Offering")

      // 如果没有当前 Offering，尝试处理所有 Offering
      for (identifier, offering) in offerings.all {
        Logger.debug("RevenueCatAdapter: 处理 Offering - \(identifier)")
        for package in offering.availablePackages {
          Logger.debug("RevenueCatAdapter: 处理包 - \(package.identifier)")
          let product = mapPackageToProductInfo(package)
          products.append(product)
          Logger.debug("RevenueCatAdapter: 成功映射产品 - \(product.displayName)")
        }
      }
    }

    Logger.info("RevenueCatAdapter: 映射完成，总共 \(products.count) 个产品")
    return products
  }

  /// 将Package映射为ProductInfo
  private func mapPackageToProductInfo(_ package: Package) -> ProductInfo {
    let storeProduct = package.storeProduct

    // Create structured period info
    let periodInfo = createSubscriptionPeriodInfo(from: storeProduct.subscriptionPeriod)
    let trialPeriodInfo = createSubscriptionPeriodInfo(
      from: storeProduct.introductoryDiscount?.subscriptionPeriod)

    return ProductInfo(
      id: package.identifier,
      identifier: storeProduct.productIdentifier,
      displayName: storeProduct.localizedTitle,
      description: storeProduct.localizedDescription,
      price: storeProduct.localizedPriceString,
      priceLocale: storeProduct.currencyCode ?? "USD",
      introductoryPrice: storeProduct.introductoryDiscount?.localizedPriceString,
      periodInfo: periodInfo,
      trialPeriodInfo: trialPeriodInfo
    )
  }

  /// Create structured subscription period info from RevenueCat SubscriptionPeriod
  private func createSubscriptionPeriodInfo(from period: SubscriptionPeriod?)
    -> SubscriptionPeriodInfo?
  {
    guard let period = period else { return nil }

    let unit: PeriodUnit
    switch period.unit {
    case .day:
      unit = .day
    case .week:
      unit = .week
    case .month:
      unit = .month
    case .year:
      unit = .year
    @unknown default:
      unit = .month  // Default fallback
    }

    return SubscriptionPeriodInfo(
      value: period.value,
      unit: unit
    )
  }

  /// 根据产品标识符确定订阅层级
  private func determineSubscriptionTier(from productIdentifier: String) -> SubscriptionTier {
    let lowercased = productIdentifier.lowercased()

    if lowercased.contains("pro") {
      return .pro
    } else if lowercased.contains("premium") {
      return .premium
    } else {
      return .free
    }
  }

  /// 将EntitlementInfo状态映射为SubscriptionStatus
  private func mapEntitlementInfoToStatus(_ entitlementInfo: EntitlementInfo) -> SubscriptionStatus
  {
    if entitlementInfo.isActive {
      return .active
    } else if entitlementInfo.billingIssueDetectedAt != nil {
      return .inBillingRetryPeriod
    } else {
      return .expired
    }
  }

  /// 将标识符映射为EntitlementType
  private func mapIdentifierToEntitlementType(_ identifier: String) -> EntitlementType? {
    switch identifier.lowercased() {
    case "pro_tier", "pro", "premium", "premium_features":
      return .proTier
    default:
      return nil
    }
  }

  /// 格式化CustomerInfo用于日志输出
  private func formatCustomerInfo(_ customerInfo: CustomerInfo) -> String {
    var output = "CustomerInfo Summary:\n"

    // 基本信息
    output += "  📱 App Version: \(customerInfo.originalApplicationVersion ?? "Unknown")\n"
    output += "  👤 User ID: \(customerInfo.originalAppUserId)\n"
    output += "  📅 First Seen: \(formatDate(customerInfo.firstSeen))\n"
    output += "  🔄 Request Date: \(formatDate(customerInfo.requestDate))\n"

    // 活跃权限
    output += "  🎯 Active Entitlements:\n"
    if customerInfo.entitlements.all.isEmpty {
      output += "    - None\n"
    } else {
      for (key, entitlement) in customerInfo.entitlements.all {
        let status = entitlement.isActive ? "✅ Active" : "❌ Inactive"
        let expiry = entitlement.expirationDate.map { "expires \(formatDate($0))" } ?? "no expiry"
        output += "    - \(key): \(status), \(expiry)\n"
        if entitlement.isActive {
          output += "      Product: \(entitlement.productIdentifier)\n"
          output += "      Will Renew: \(entitlement.willRenew ? "Yes" : "No")\n"
          if let unsubscribeDate = entitlement.unsubscribeDetectedAt {
            output += "      Unsubscribed: \(formatDate(unsubscribeDate))\n"
          }
        }
      }
    }

    // 活跃订阅
    output += "  💳 Active Subscriptions:\n"
    if customerInfo.activeSubscriptions.isEmpty {
      output += "    - None\n"
    } else {
      for productId in customerInfo.activeSubscriptions {
        output += "    - \(productId)\n"
      }
    }

    return output
  }

  /// 格式化日期
  private func formatDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateStyle = .short
    formatter.timeStyle = .short
    return formatter.string(from: date)
  }

}

// MARK: - PurchasesDelegate

extension RevenueCatAdapter: PurchasesDelegate {
  public func purchases(_ purchases: Purchases, receivedUpdated customerInfo: CustomerInfo) {
    Logger.info("RevenueCatAdapter: 收到用户信息更新")
    Logger.info(formatCustomerInfo(customerInfo))

    Task {
      await refreshCustomerInfo()
    }
  }

  public func purchases(
    _ purchases: Purchases, readyForPromotedProduct product: StoreProduct,
    purchase makeDeferredPurchase: @escaping StartPurchaseBlock
  ) {
    Logger.info("RevenueCat: 收到推广产品购买请求 - \(product.productIdentifier)")

    // 这里可以根据应用状态决定是否立即执行购买
    // 暂时直接执行
    makeDeferredPurchase { (transaction, customerInfo, error, cancelled) in
      if let error = error {
        Logger.error("RevenueCat: 推广产品购买失败 - \(error)")
      } else if cancelled {
        Logger.info("RevenueCat: 推广产品购买被取消")
      } else {
        Logger.info("RevenueCat: 推广产品购买成功")
      }
    }
  }
}
