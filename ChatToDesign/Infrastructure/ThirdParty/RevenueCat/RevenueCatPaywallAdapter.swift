//
//  RevenueCatPaywallAdapter.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/17.
//

import Combine
import Foundation
import RevenueCat

/// RevenueCat Paywall 适配器
/// Implementation PaywallService 协议，封装 RevenueCatUI 的功能
public final class RevenueCatPaywallAdapter: PaywallService {

  // MARK: - Private Properties

  private let subscriptionService: SubscriptionService
  private let subscriptionStateManager: SubscriptionStateManager

  // Combine Subjects
  private let stateSubject = CurrentValueSubject<PaywallState, Never>(.idle)
  private let eventSubject = PassthroughSubject<PaywallEvent, Never>()

  // MARK: - PaywallService Implementation

  public var currentState: PaywallState {
    return stateSubject.value
  }

  public var statePublisher: AnyPublisher<PaywallState, Never> {
    return stateSubject.eraseToAnyPublisher()
  }

  public var eventPublisher: AnyPublisher<PaywallEvent, Never> {
    return eventSubject.eraseToAnyPublisher()
  }

  // MARK: - Initialization

  /// Initialization RevenueCat Paywall 适配器
  /// - Parameters:
  ///   - subscriptionService: 订阅服务
  ///   - subscriptionStateManager: 订阅状态管理器
  public init(
    subscriptionService: SubscriptionService,
    subscriptionStateManager: SubscriptionStateManager
  ) {
    self.subscriptionService = subscriptionService
    self.subscriptionStateManager = subscriptionStateManager
  }

  // MARK: - PaywallService Implementation

  public func shouldPresentPaywall(for entitlement: EntitlementType) async -> Bool {
    do {
      let hasEntitlement = try await subscriptionService.hasEntitlement(entitlement)
      return !hasEntitlement
    } catch {
      Logger.error("RevenueCatPaywallAdapter: Check permissions失败 - \(error)")
      // 出错时默认显示 Paywall
      return true
    }
  }

  public func getAvailableProducts(offeringId: String?) async throws -> [ProductInfo] {
    stateSubject.send(.loadingProducts)

    do {
      let products = try await subscriptionService.getAvailableProducts()
      stateSubject.send(.productsLoaded(products))
      return products
    } catch {
      let subscriptionError = error as? SubscriptionServiceError ?? .unknownError(error)
      stateSubject.send(.error(subscriptionError))
      throw subscriptionError
    }
  }

  public func purchaseProduct(_ productId: String) async -> PurchaseResult {
    stateSubject.send(.purchasing(productId: productId))
    sendEvent(.purchaseStarted(productId: productId))

    let result = await subscriptionStateManager.purchaseSubscription(productId: productId)

    if result.isSuccessful {
      if let subscription = result.subscription {
        sendEvent(.purchaseCompleted(productId: productId, subscription: subscription))
      }
    } else if result.wasCancelled {
      sendEvent(.purchaseCancelled(productId: productId))
    } else if let error = result.error {
      sendEvent(.purchaseFailed(productId: productId, error: error))
    }

    stateSubject.send(.idle)
    return result
  }

  public func restorePurchases() async -> RestoreResult {
    stateSubject.send(.restoring)
    sendEvent(.restoreStarted)

    do {
      let subscription = try await subscriptionService.restorePurchases()

      let result = RestoreResult(
        isSuccessful: true,
        subscription: subscription,
        foundPurchases: subscription != nil
      )

      sendEvent(.restoreCompleted(subscription: subscription))
      stateSubject.send(.idle)

      return result

    } catch {
      let subscriptionError = error as? SubscriptionServiceError ?? .unknownError(error)

      let result = RestoreResult(
        isSuccessful: false,
        error: subscriptionError
      )

      sendEvent(.restoreFailed(error: subscriptionError))
      stateSubject.send(.error(subscriptionError))

      return result
    }
  }

  public func sendEvent(_ event: PaywallEvent) {
    eventSubject.send(event)
    Logger.debug("RevenueCatPaywallAdapter: 发送事件 - \(event)")
  }
}