// DefaultLoggerAdapter.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Foundation
import os.log

/// 默认日志适配器，使用系统 OSLog Implementation
public final class DefaultLoggerAdapter: LoggerService {
  
  /// 服务配置
  public let configuration: LoggerConfiguration
  
  /// OSLog 实例
  private let osLog: OSLog
  
  /// Initialization日志适配器
  /// - Parameter configuration: 日志配置
  public init(configuration: LoggerConfiguration = .default) {
    self.configuration = configuration
    self.osLog = OSLog(
      subsystem: configuration.subsystem,
      category: configuration.category
    )
  }
  
  /// 记录日志
  /// - Parameters:
  ///   - message: 日志消息
  ///   - level: 日志级别
  ///   - context: 日志上下文
  public func log(_ message: String, level: LogLevel, context: LogContext) {
    let fileName = (context.file as NSString).lastPathComponent
    let logMessage = "[\(fileName):\(context.line)] \(context.function) - \(message)"
    
    switch level {
    case .debug:
      os_log(.debug, log: osLog, "%{public}@", logMessage)
    case .info:
      os_log(.info, log: osLog, "%{public}@", logMessage)
    case .warning:
      os_log(.error, log: osLog, "⚠️ %{public}@", logMessage)
    case .error:
      os_log(.fault, log: osLog, "🔴 %{public}@", logMessage)
    }
    
    #if DEBUG
      if configuration.printToConsoleInDebug {
        print("[\(level.rawValue)] \(logMessage)")
      }
    #endif
  }
  
  /// Log debug level message
  /// - Parameters:
  ///   - message: 日志消息
  ///   - context: 日志上下文
  public func debug(_ message: String, context: LogContext = LogContext()) {
    log(message, level: .debug, context: context)
  }
  
  /// Log info level message
  /// - Parameters:
  ///   - message: 日志消息
  ///   - context: 日志上下文
  public func info(_ message: String, context: LogContext = LogContext()) {
    log(message, level: .info, context: context)
  }
  
  /// Log warning level message
  /// - Parameters:
  ///   - message: 日志消息
  ///   - context: 日志上下文
  public func warning(_ message: String, context: LogContext = LogContext()) {
    log(message, level: .warning, context: context)
  }
  
  /// Log error level message
  /// - Parameters:
  ///   - message: 日志消息
  ///   - context: 日志上下文
  public func error(_ message: String, context: LogContext = LogContext()) {
    log(message, level: .error, context: context)
  }
} 