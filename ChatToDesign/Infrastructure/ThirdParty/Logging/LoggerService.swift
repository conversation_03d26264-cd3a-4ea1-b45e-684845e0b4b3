// LoggerService.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Foundation

/// 日志级别
public enum LogLevel: String {
  /// 调试信息
  case debug = "DEBUG"
  /// 普通信息
  case info = "INFO"
  /// 警告信息
  case warning = "WARNING"
  /// Error message
  case error = "ERROR"
}

/// 日志配置
public struct LoggerConfiguration {
  /// 子系统名称
  let subsystem: String
  /// 分类名称
  let category: String
  /// 是否在调试模式下打印到控制台
  let printToConsoleInDebug: Bool

  /// Default configuration
  public static let `default` = LoggerConfiguration(
    subsystem: Bundle.main.bundleIdentifier ?? "com.a1d.ChatToDesign",
    category: "App",
    printToConsoleInDebug: false
  )

  /// Initialization日志配置
  public init(
    subsystem: String,
    category: String,
    printToConsoleInDebug: Bool = false
  ) {
    self.subsystem = subsystem
    self.category = category
    self.printToConsoleInDebug = printToConsoleInDebug
  }
}

/// 日志上下文
public struct LogContext {
  /// 文件名
  let file: String
  /// 函数名
  let function: String
  /// 行号
  let line: Int

  /// 创建日志上下文
  public init(
    file: String = #file,
    function: String = #function,
    line: Int = #line
  ) {
    self.file = file
    self.function = function
    self.line = line
  }
}

/// 日志服务接口
public protocol LoggerService {
  /// 服务配置
  var configuration: LoggerConfiguration { get }

  /// 记录日志
  func log(_ message: String, level: LogLevel, context: LogContext)

  /// Log debug level message
  func debug(_ message: String, context: LogContext)

  /// Log info level message
  func info(_ message: String, context: LogContext)

  /// Log warning level message
  func warning(_ message: String, context: LogContext)

  /// Log error level message
  func error(_ message: String, context: LogContext)
}
