import Foundation

// MARK: - Models

/// 配置值类型
public enum ConfigValue {
  case string(String)
  case bool(Bool)
  case number(Double)
  case json([String: Any])

  // 便利访问器
  public var stringValue: String? {
    if case .string(let value) = self { return value }
    return nil
  }

  public var boolValue: Bool? {
    if case .bool(let value) = self { return value }
    return nil
  }

  public var numberValue: Double? {
    if case .number(let value) = self { return value }
    return nil
  }

  public var jsonValue: [String: Any]? {
    if case .json(let value) = self { return value }
    return nil
  }
}

// MARK: - Errors

public enum ConfigError: Error, LocalizedError {
  case fetchFailed(String)
  case invalidKey(String)
  case invalidValue(String)
  case activateFailed(String)

  public var errorDescription: String? {
    switch self {
    case .fetchFailed(let reason):
      return "获取配置失败: \(reason)"
    case .invalidKey(let key):
      return "无效的配置键: \(key)"
    case .invalidValue(let detail):
      return "无效的配置值: \(detail)"
    case .activateFailed(let reason):
      return "激活配置失败: \(reason)"
    }
  }
}

// MARK: - Service Interface

/// 配置服务接口
public protocol ConfigService {
  /// 获取配置值
  func getValue(for key: String) throws -> ConfigValue

  /// 获取字符串值
  func getString(for key: String) throws -> String

  /// 获取布尔值
  func getBool(for key: String) throws -> Bool

  /// 获取数字值
  func getNumber(for key: String) throws -> Double

  /// 获取 JSON 值
  func getJSON(for key: String) throws -> [String: Any]

  /// 获取所有配置
  func getAllValues() -> [String: ConfigValue]

  /// 从远程获取最新配置
  func fetch() async throws

  /// 激活获取的配置
  func activate() async throws

  /// 获取并激活配置
  func fetchAndActivate() async throws
}
