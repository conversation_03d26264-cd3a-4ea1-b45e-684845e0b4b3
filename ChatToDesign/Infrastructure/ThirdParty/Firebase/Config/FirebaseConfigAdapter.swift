import FirebaseRemoteConfig
import Foundation

final class FirebaseConfigAdapter: ConfigService {
  // MARK: - Properties

  private let config: RemoteConfig

  // MARK: - Initialization

  init(config: RemoteConfig = .remoteConfig()) {
    self.config = config
    setupDefaults()
  }

  private func setupDefaults() {
    config.setDefaults(fromPlist: "RemoteConfigDefaults")
  }

  // MARK: - ConfigService Implementation

  func getValue(for key: String) throws -> ConfigValue {
    let value = config.configValue(forKey: key)
    return try convertToConfigValue(value)
  }

  func getString(for key: String) throws -> String {
    let value = config.configValue(forKey: key)
    let stringValue = value.stringValue
    if stringValue.isEmpty {
      throw ConfigError.invalidValue("Value is not a string type")
    }
    return stringValue
  }

  func getBool(for key: String) throws -> Bool {
    let value = config.configValue(forKey: key)
    return value.boolValue
  }

  func getNumber(for key: String) throws -> Double {
    let value = config.configValue(forKey: key)
    let numberValue = value.numberValue
    if numberValue == nil {
      throw ConfigError.invalidValue("Value is not a number type")
    }
    return numberValue.doubleValue
  }

  func getJSON(for key: String) throws -> [String: Any] {
    let value = config.configValue(forKey: key)
    let jsonData = value.dataValue
    
    do {
      if let json = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {
        return json
      } else {
        throw ConfigError.invalidValue("Value is not a valid JSON structure")
      }
    } catch {
      throw ConfigError.invalidValue("Value is not valid JSON: \(error.localizedDescription)")
    }
  }

  func getAllValues() -> [String: ConfigValue] {
    var result: [String: ConfigValue] = [:]
    
    // Get all keys, Firebase's keys property may return optional values
    let allKeys = config.allKeys
    
//    for key in allKeys {
//      if let value = try? getValue(for: key) {
//        result[key] = value
//      }
//    }
    return result
  }

  func fetch() async throws {
    do {
      let status = try await config.fetch()
      guard status == .success else {
        throw ConfigError.fetchFailed("Status code: \(status)")
      }
    } catch {
      throw ConfigError.fetchFailed(error.localizedDescription)
    }
  }

  func activate() async throws {
    do {
      let success = try await config.activate()
      guard success else {
        throw ConfigError.activateFailed("Activation failed")
      }
    } catch {
      throw ConfigError.activateFailed(error.localizedDescription)
    }
  }

  func fetchAndActivate() async throws {
    do {
      let status = try await config.fetchAndActivate()
      guard status != .error else {
        throw ConfigError.fetchFailed("Fetch or activation failed")
      }
    } catch {
      throw ConfigError.fetchFailed(error.localizedDescription)
    }
  }

  // MARK: - Private Helpers

  private func convertToConfigValue(_ value: RemoteConfigValue) throws -> ConfigValue {
    // Check string value
    let stringValue = value.stringValue
    if !stringValue.isEmpty {
      return .string(stringValue)
    }

    // Check number value
//    if let numberValue = value.numberValue {
//      return .number(numberValue.doubleValue)
//    }

    // Check boolean value - use actual boolValue, not string processing
    if value.boolValue || value.source != .default { // Only consider boolean type for non-default values
      return .bool(value.boolValue)
    }

    // Check JSON value
    let dataValue = value.dataValue
    do {
      if let json = try JSONSerialization.jsonObject(with: dataValue) as? [String: Any] {
        return .json(json)
      }
    } catch {}

    // Unable to convert to any known type
    throw ConfigError.invalidValue("Unable to convert value type")
  }
}
