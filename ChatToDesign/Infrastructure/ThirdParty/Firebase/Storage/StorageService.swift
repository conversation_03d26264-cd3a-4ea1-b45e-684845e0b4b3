import Foundation
import UIKit

// MARK: - Models

/// 存储类型
public enum StorageType {
  case image
  case video
  case audio
  case file

  var contentType: String {
    switch self {
    case .image: return "image/jpeg"
    case .video: return "video/mp4"
    case .audio: return "audio/m4a"
    case .file: return "application/octet-stream"
    }
  }

  var path: String {
    switch self {
    case .image: return "images"
    case .video: return "videos"
    case .audio: return "audios"
    case .file: return "files"
    }
  }
}

/// 存储结果
public struct StorageResult {
  public let url: URL
  public let path: String
  public let type: StorageType
}

// MARK: - Errors

public enum StorageError: Error, LocalizedError {
  case invalidData
  case invalidPath
  case uploadFailed(String)
  case downloadFailed(String)
  case deleteFailed(String)

  public var errorDescription: String? {
    switch self {
    case .invalidData:
      return "无效的数据"
    case .invalidPath:
      return "无效的路径"
    case .uploadFailed(let reason):
      return "上传失败: \(reason)"
    case .downloadFailed(let reason):
      return "下载失败: \(reason)"
    case .deleteFailed(let reason):
      return "删除失败: \(reason)"
    }
  }
}

// MARK: - Service Interface

/// 存储服务接口
public protocol StorageService {
  // 基础存储操作
  func uploadData(
    _ data: Data,
    to path: String,
    type: StorageType,
    progress: ((Double) -> Void)?
  ) async throws -> StorageResult

  func uploadImage(
    _ image: UIImage,
    to path: String,
    quality: CGFloat,
    progress: ((Double) -> Void)?
  ) async throws -> StorageResult

  func downloadData(from path: String) async throws -> Data
  func getDownloadURL(for path: String) async throws -> URL
  func deleteFile(at path: String) async throws
  func fileExists(at path: String) async throws -> Bool

  // 媒体存储操作
  func uploadMedia(
    _ data: Data,
    type: StorageType,
    quality: CGFloat?,
    progress: ((Double) -> Void)?
  ) async throws -> StorageResult
}
