import FirebaseStorage
import Foundation
import UIKit

final class FirebaseStorageAdapter: StorageService {
  // MARK: - Properties

  private let storage: Storage

  // MARK: - Initialization

  init(storage: Storage = .storage()) {
    self.storage = storage
  }

  // MARK: - StorageService Implementation
  func uploadData(
    _ data: Data,
    to path: String,
    type: StorageType,
    progress: ((Double) -> Void)?
) async throws -> StorageResult {
    let metadata = StorageMetadata()
    metadata.contentType = type.contentType
    Logger.debug("上传路径: \(path)")
    let storageRef = storage.reference().child(path)
    Logger.debug("存储引用: \(storageRef)")

    do {
        if let progress = progress {
            let task = storageRef.putData(data, metadata: metadata)
            
            // 添加进度监听
            task.observe(.progress) { snapshot in
                let percentComplete = Double(snapshot.progress?.completedUnitCount ?? 0) / Double(snapshot.progress?.totalUnitCount ?? 1)
                progress(percentComplete)
            }
            
            // 使用 Continuation 等待任务完成
            try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
                var hasResumed = false
                
                task.observe(.success) { _ in
                    guard !hasResumed else { return }
                    hasResumed = true
                    continuation.resume()
                }
                
                task.observe(.failure) { snapshot in
                    guard !hasResumed else { return }
                    hasResumed = true
                    continuation.resume(throwing: snapshot.error ?? StorageError.uploadFailed("Unknown error"))
                }
            }
        } else {
            // 无进度回调时直接使用 putDataAsync
            _ = try await storageRef.putDataAsync(data, metadata: metadata)
        }
        
        // 获取下载 URL
        let url = try await storageRef.downloadURL()
        Logger.debug("上传成功，下载 URL: \(url)")
        return StorageResult(url: url, path: path, type: type)
        
    } catch {
        Logger.error("上传失败: \(error.localizedDescription)")
        throw StorageError.uploadFailed(error.localizedDescription)
    }
}


  // func uploadData(
  //   _ data: Data,
  //   to path: String,
  //   type: StorageType,
  //   progress: ((Double) -> Void)?
  // ) async throws -> StorageResult {
  //   let metadata = StorageMetadata()
  //   metadata.contentType = type.contentType
  //   Logger.debug("上传路径: \(path)")
  //   let storageRef = storage.reference().child(path)
  //   Logger.debug("上传路径: \(storageRef)")

  //   do {
  //     if let progress = progress {
  //       let task = storageRef.putData(data, metadata: metadata)
  //       Logger.debug("上传任务: \(task)")
  //       // 设置进度监听
  //       task.observe(.progress) { snapshot in
  //         let percentComplete =
  //           Double(snapshot.progress?.completedUnitCount ?? 0)
  //           / Double(snapshot.progress?.totalUnitCount ?? 1)
  //         progress(percentComplete)
  //       }

  //       // 添加状态和错误监听
  //       task.observe(.success) { snapshot in
  //         Logger.debug("上传成功: \(snapshot)")
  //       }
        
  //       task.observe(.failure) { snapshot in
  //         if let error = snapshot.error {
  //           Logger.error("上传失败: \(error.localizedDescription)")
  //         }
  //       }

  //       do {
  //         let snapshot = try await task.snapshot
  //         Logger.debug("上传任务快照: \(snapshot)")
  //       } catch {
  //         Logger.error("上传过程中发生错误: \(error.localizedDescription)")
  //         throw StorageError.uploadFailed(error.localizedDescription)
  //       }
  //     } else {
  //       do {
  //         let snapshot = try await storageRef.putDataAsync(data, metadata: metadata)
  //         Logger.debug("上传数据完成，快照: \(snapshot)")
  //       } catch {
  //         Logger.error("上传数据失败: \(error.localizedDescription)")
  //         throw StorageError.uploadFailed(error.localizedDescription)
  //       }
  //     }

  //     do {
  //       let url = try await storageRef.downloadURL()
  //       Logger.debug("上传结果: \(url)")
  //       return StorageResult(url: url, path: path, type: type)
  //     } catch {
  //       Logger.error("获取下载URL失败: \(error.localizedDescription)")
  //       throw StorageError.uploadFailed("获取下载URL失败: \(error.localizedDescription)")
  //     }
  //   } catch {
  //     // 捕获并包装所有其他错误
  //     if let storageError = error as? StorageError {
  //       throw storageError
  //     } else {
  //       Logger.error("存储操作失败: \(error.localizedDescription)")
  //       throw StorageError.uploadFailed(error.localizedDescription)
  //     }
  //   }
  // }

  func uploadImage(
    _ image: UIImage,
    to path: String,
    quality: CGFloat,
    progress: ((Double) -> Void)?
  ) async throws -> StorageResult {
    guard let imageData = image.jpegData(compressionQuality: quality) else {
      throw StorageError.invalidData
    }

    return try await uploadData(
      imageData,
      to: path,
      type: .image,
      progress: progress
    )
  }

  func uploadMedia(
    _ data: Data,
    type: StorageType,
    quality: CGFloat?,
    progress: ((Double) -> Void)?
  ) async throws -> StorageResult {
    let fileName = UUID().uuidString + type.fileExtension
    let path = "\(type.path)/\(fileName)"

    return try await uploadData(
      data,
      to: path,
      type: type,
      progress: progress
    )
  }

  func downloadData(from path: String) async throws -> Data {
    let storageRef = storage.reference().child(path)
    return try await storageRef.data(maxSize: 100 * 1024 * 1024)  // 100MB max
  }

  func getDownloadURL(for path: String) async throws -> URL {
    let storageRef = storage.reference().child(path)
    return try await storageRef.downloadURL()
  }

  func deleteFile(at path: String) async throws {
    let storageRef = storage.reference().child(path)
    try await storageRef.delete()
  }

  func fileExists(at path: String) async throws -> Bool {
    let storageRef = storage.reference().child(path)
    do {
      _ = try await storageRef.getMetadata()
      return true
    } catch {
      if let errorCode = (error as NSError).userInfo[StorageErrorDomain] as? Int,
        errorCode == StorageErrorCode.objectNotFound.rawValue
      {
        return false
      }
      throw error
    }
  }
}

// MARK: - Helpers

extension StorageType {
  fileprivate var fileExtension: String {
    switch self {
    case .image: return ".jpg"
    case .video: return ".mp4"
    case .audio: return ".m4a"
    case .file: return ""
    }
  }
}
