// FirestoreMessageRepository.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Combine
import FirebaseFirestore
import Foundation

/**
 Firebase Firestore 消息仓储Implementation
 
 Implementation MessageRepository 接口，使用 Firebase Firestore 作为数据源
 */
public final class FirestoreMessageRepository {
    // MARK: - Constants

    private enum Collection {
        static let chats = "chats"
        static let messages = "messages"
    }
    
    private enum Field {
        // 消息字段
        static let id = "id"
        static let chatId = "chatId"
        static let sender = "sender"
        static let senderId = "senderId"
        static let senderType = "senderType"
        static let type = "type"
        static let status = "status"
        static let content = "content"
        static let text = "text"
        static let styleType = "styleType"
        static let attachments = "attachments"
        static let attributes = "attributes"
        static let metadata = "metadata"
        static let timestamp = "timestamp"
        static let created = "created"
        static let updated = "updated"
        static let isRead = "isRead"
        static let replyMessageId = "replyMessageId"
        
        // 附件字段
        static let mediaId = "id"
        static let mediaType = "type"
        static let mediaUrl = "url"
        static let thumbnailUrl = "thumbnailUrl"
        static let filename = "filename"
        static let fileSize = "fileSize"
        static let mimeType = "mimeType"
        static let isUploaded = "isUploaded"
        static let uploadProgress = "uploadProgress"
    }

    // MARK: - Properties

    private let firestore: Firestore
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization

    public init(firestore: Firestore = .firestore()) {
        self.firestore = firestore
    }
    
    // MARK: - Private Helper Methods
    
    private func messageCollectionRef(chatId: String) -> CollectionReference {
        return firestore.collection(Collection.chats).document(chatId).collection(Collection.messages)
    }
    
    private func messageDocumentRef(id: String, chatId: String) -> DocumentReference {
        return messageCollectionRef(chatId: chatId).document(id)
    }
    
    private func chatDocumentRef(chatId: String) -> DocumentReference {
        return firestore.collection(Collection.chats).document(chatId)
    }
    
    /// 将消息对象转换为Firestore数据
    private func mapToFirestoreData(message: Message) -> [String: Any] {
        // 发送者
        let senderData: [String: Any] = [
            "id": message.sender.id,
            "type": message.sender.type.rawValue
        ]
        
        // 时间戳
        let timestampData: [String: Any] = [
            "created": Timestamp(date: message.timestamp.created),
            "updated": Timestamp(date: message.timestamp.updated)
        ]
        
        // 附件
        var attachmentsData: [[String: Any]]?
        if let attachments = message.content.attachments {
            attachmentsData = attachments.map { attachment in
                return [
                    Field.mediaId: attachment.id,
                    Field.mediaType: attachment.type.rawValue,
                    Field.mediaUrl: attachment.url.absoluteString,
                    Field.thumbnailUrl: attachment.thumbnailUrl?.absoluteString ?? "",
                    Field.filename: attachment.filename,
                    Field.fileSize: attachment.fileSize,
                    Field.mimeType: attachment.mimeType,
                    Field.isUploaded: attachment.isUploaded,
                    Field.uploadProgress: attachment.uploadProgress
                ]
            }
        }
        
        // 内容
        let contentData: [String: Any] = [
            Field.text: message.content.text,
            Field.styleType: message.content.styleType.rawValue,
            Field.attachments: attachmentsData as Any,
            Field.attributes: message.content.attributes as Any
        ]
        
        // 完整消息数据
        var messageData: [String: Any] = [
            Field.id: message.id,
            Field.chatId: message.chatId,
            Field.sender: senderData,
            Field.type: message.type.rawValue,
            Field.status: message.status.rawValue,
            Field.content: contentData,
            Field.timestamp: timestampData,
            Field.isRead: message.isRead
        ]
        
        // 可选字段
        if let metadata = message.metadata {
            messageData[Field.metadata] = metadata
        }

        if let replyMessageId = message.replyMessageId {
            messageData[Field.replyMessageId] = replyMessageId
        }
        
        return messageData
    }
    
    /// 将Firestore数据转换为消息对象
    private func mapToMessage(id: String, data: [String: Any]) throws -> Message {
        // 验证必要字段
        guard let chatId = data[Field.chatId] as? String else {
            throw MessageRepositoryError.databaseError(NSError(domain: "FirestoreMessageRepository", code: 400, userInfo: [NSLocalizedDescriptionKey: "缺少聊天ID字段"]))
        }
        
        // 发送者
        var senderId = ""
        var senderType = Message.Sender.SenderType.user
        
        if let senderData = data[Field.sender] as? [String: Any] {
            if let id = senderData["id"] as? String {
                senderId = id
            }
            
            if let typeString = senderData["type"] as? String, let type = Message.Sender.SenderType(rawValue: typeString) {
                senderType = type
            }
        }
        
        let sender = Message.Sender(id: senderId, type: senderType)
        
        // 消息类型
        let typeString = data[Field.type] as? String ?? MessageType.user.rawValue
        let type = MessageType(rawValue: typeString) ?? .user
        
        // 消息状态
        let statusString = data[Field.status] as? String ?? MessageStatus.sent.rawValue
        let status = MessageStatus(rawValue: statusString) ?? .sent
        
        // 内容
        var text = ""
        var styleType = MessageContent.StyleType.plainText
        var attachments: [MediaAttachment]?
        var attributes: [String: String]?
        
        if let contentData = data[Field.content] as? [String: Any] {
            text = contentData[Field.text] as? String ?? ""
            
            if let styleTypeString = contentData[Field.styleType] as? String, let style = MessageContent.StyleType(rawValue: styleTypeString) {
                styleType = style
            }
            
            if let attributesData = contentData[Field.attributes] as? [String: String] {
                attributes = attributesData
            }
            
            if let attachmentsData = contentData[Field.attachments] as? [[String: Any]] {
                attachments = attachmentsData.compactMap { attachmentData -> MediaAttachment? in
                    guard
                        let id = attachmentData[Field.mediaId] as? String,
                        let typeString = attachmentData[Field.mediaType] as? String,
                        let type = MediaType(rawValue: typeString),
                        let urlString = attachmentData[Field.mediaUrl] as? String,
                        let url = URL(string: urlString),
                        let filename = attachmentData[Field.filename] as? String,
                        let fileSize = attachmentData[Field.fileSize] as? Int,
                        let mimeType = attachmentData[Field.mimeType] as? String,
                        let isUploaded = attachmentData[Field.isUploaded] as? Bool,
                        let uploadProgress = attachmentData[Field.uploadProgress] as? Double
                    else {
                        return nil
                    }
                    
                    let thumbnailUrl: URL?
                    if let thumbnailString = attachmentData[Field.thumbnailUrl] as? String, !thumbnailString.isEmpty {
                        thumbnailUrl = URL(string: thumbnailString)
                    } else {
                        thumbnailUrl = nil
                    }
                    
                    return MediaAttachment(
                        id: id,
                        type: type,
                        url: url,
                        thumbnailUrl: thumbnailUrl,
                        filename: filename,
                        fileSize: fileSize,
                        mimeType: mimeType,
                        isUploaded: isUploaded,
                        uploadProgress: uploadProgress
                    )
                }
            }
        }
        
        let content = MessageContent(
            text: text,
            styleType: styleType,
            attachments: attachments,
            attributes: attributes
        )
        
        // 时间戳
        var created = Date()
        var updated = Date()
        
        if let timestampData = data[Field.timestamp] as? [String: Any] {
            if let createdTimestamp = timestampData[Field.created] as? Timestamp {
                created = createdTimestamp.dateValue()
            }
            
            if let updatedTimestamp = timestampData[Field.updated] as? Timestamp {
                updated = updatedTimestamp.dateValue()
            }
        }
        
        let timestamp = Message.Timestamp(created: created, updated: updated)
        
        // 已读状态
        let isRead = data[Field.isRead] as? Bool ?? false
        
        // 元数据
        let metadata = data[Field.metadata] as? [String: String]

        // 回复消息ID
        let replyMessageId = data[Field.replyMessageId] as? String
        
        return Message(
            id: id,
            chatId: chatId,
            sender: sender,
            type: type,
            status: status,
            content: content,
            replyMessageId: replyMessageId,
            metadata: metadata,
            timestamp: timestamp,
            isRead: isRead
        )
    }
}

// MARK: - MessageRepository Implementation

extension FirestoreMessageRepository: MessageRepository {
    public func getMessage(id: String, chatId: String) async throws -> Message {
        do {
            // Check if chat exists
            let chatDocument = try await chatDocumentRef(chatId: chatId).getDocument()
            guard chatDocument.exists else {
                throw MessageRepositoryError.chatNotFound
            }
            
            // 获取消息
            let messageDocument = try await messageDocumentRef(id: id, chatId: chatId).getDocument()
            guard messageDocument.exists else {
                throw MessageRepositoryError.messageNotFound
            }
            
            guard let data = messageDocument.data() else {
                throw MessageRepositoryError.databaseError(NSError(domain: "FirestoreMessageRepository", code: 500, userInfo: [NSLocalizedDescriptionKey: "无法解析消息数据"]))
            }
            
            return try mapToMessage(id: id, data: data)
        } catch let error as MessageRepositoryError {
            throw error
        } catch {
            throw MessageRepositoryError.databaseError(error)
        }
    }
    
    public func getMessages(forChatId: String, limit: Int = 50, startAfter: String? = nil) async throws -> [Message] {
        do {
            // Check if chat exists
            let chatDocument = try await chatDocumentRef(chatId: forChatId).getDocument()
            guard chatDocument.exists else {
                throw MessageRepositoryError.chatNotFound
            }
            
            // 构建查询
            var query = messageCollectionRef(chatId: forChatId)
                .order(by: "\(Field.timestamp).\(Field.created)", descending: true)
                .limit(to: limit)
            
            // Pagination处理
            if let startAfter = startAfter {
                let startDoc = try await messageDocumentRef(id: startAfter, chatId: forChatId).getDocument()
                if startDoc.exists {
                    query = query.start(afterDocument: startDoc)
                }
            }
            
            // 执行查询
            let snapshot = try await query.getDocuments()
            
            // 映射结果
            return try snapshot.documents.compactMap { document in
                try mapToMessage(id: document.documentID, data: document.data())
            }
        } catch let error as MessageRepositoryError {
            throw error
        } catch {
            throw MessageRepositoryError.databaseError(error)
        }
    }
    
    public func saveMessage(_ message: Message) async throws -> String {
        do {
            // Check if chat exists
            let chatDocument = try await chatDocumentRef(chatId: message.chatId).getDocument()
            guard chatDocument.exists else {
                throw MessageRepositoryError.chatNotFound
            }
            
            // 生成消息ID（如果没有）
            let messageId = message.id.isEmpty ? UUID().uuidString : message.id
            
            // 转换为Firestore数据
            var firestoreData = mapToFirestoreData(message: message)
            firestoreData[Field.id] = messageId
            
            // 保存消息
            try await messageDocumentRef(id: messageId, chatId: message.chatId).setData(firestoreData)
            
            return messageId
        } catch let error as MessageRepositoryError {
            throw error
        } catch {
            throw MessageRepositoryError.failedToCreateMessage
        }
    }
    
    public func updateMessage(_ message: Message) async throws {
        do {
            // 检查消息是否存在
            let messageDocument = try await messageDocumentRef(id: message.id, chatId: message.chatId).getDocument()
            guard messageDocument.exists else {
                throw MessageRepositoryError.messageNotFound
            }
            
            // 转换为Firestore数据
            let firestoreData = mapToFirestoreData(message: message)
            
            // 更新消息
            try await messageDocumentRef(id: message.id, chatId: message.chatId).updateData(firestoreData)
        } catch let error as MessageRepositoryError {
            throw error
        } catch {
            throw MessageRepositoryError.failedToUpdateMessage
        }
    }
    
    public func updateMessageStatus(id: String, chatId: String, status: MessageStatus) async throws {
        do {
            // 检查消息是否存在
            let messageDocument = try await messageDocumentRef(id: id, chatId: chatId).getDocument()
            guard messageDocument.exists else {
                throw MessageRepositoryError.messageNotFound
            }
            
            // 更新状态
            try await messageDocumentRef(id: id, chatId: chatId).updateData([
                Field.status: status.rawValue,
                "\(Field.timestamp).\(Field.updated)": Timestamp(date: Date())
            ])
        } catch let error as MessageRepositoryError {
            throw error
        } catch {
            throw MessageRepositoryError.failedToUpdateMessage
        }
    }
    
    public func markMessageAsRead(id: String, chatId: String) async throws {
        do {
            // 检查消息是否存在
            let messageDocument = try await messageDocumentRef(id: id, chatId: chatId).getDocument()
            guard messageDocument.exists else {
                throw MessageRepositoryError.messageNotFound
            }
            
            // 标记为已读
            try await messageDocumentRef(id: id, chatId: chatId).updateData([
                Field.isRead: true,
                "\(Field.timestamp).\(Field.updated)": Timestamp(date: Date())
            ])
        } catch let error as MessageRepositoryError {
            throw error
        } catch {
            throw MessageRepositoryError.failedToUpdateMessage
        }
    }
    
    public func deleteMessage(id: String, chatId: String) async throws {
        do {
            // 检查消息是否存在
            let messageDocument = try await messageDocumentRef(id: id, chatId: chatId).getDocument()
            guard messageDocument.exists else {
                throw MessageRepositoryError.messageNotFound
            }
            
            // 删除消息
            try await messageDocumentRef(id: id, chatId: chatId).delete()
        } catch let error as MessageRepositoryError {
            throw error
        } catch {
            throw MessageRepositoryError.failedToDeleteMessage
        }
    }
    
    public func deleteMessages(ids: [String], chatId: String) async throws {
        do {
            // Check if chat exists
            let chatDocument = try await chatDocumentRef(chatId: chatId).getDocument()
            guard chatDocument.exists else {
                throw MessageRepositoryError.chatNotFound
            }
            
            // 使用批量写入进行删除
            let batch = firestore.batch()
            
            for id in ids {
                let messageRef = messageDocumentRef(id: id, chatId: chatId)
                batch.deleteDocument(messageRef)
            }
            
            try await batch.commit()
        } catch let error as MessageRepositoryError {
            throw error
        } catch {
            throw MessageRepositoryError.failedToDeleteMessage
        }
    }
    
    public func getLatestMessages(forChatId: String, limit: Int = 1) async throws -> [Message] {
        return try await getMessages(forChatId: forChatId, limit: limit, startAfter: nil)
    }
    
    public func getMessageCount(forChatId: String) async throws -> Int {
        do {
            // Check if chat exists
            let chatDocument = try await chatDocumentRef(chatId: forChatId).getDocument()
            guard chatDocument.exists else {
                throw MessageRepositoryError.chatNotFound
            }
            
            // 获取消息数量
            let snapshot = try await messageCollectionRef(chatId: forChatId).count.getAggregation(source: .server)
            return Int(truncating: snapshot.count)
        } catch let error as MessageRepositoryError {
            throw error
        } catch {
            throw MessageRepositoryError.databaseError(error)
        }
    }
    
    public func observeMessages(chatId: String) -> AnyPublisher<[Message], Error> {
        let publisher = PassthroughSubject<[Message], Error>()
        
        // 监听消息变化
        let listener = messageCollectionRef(chatId: chatId)
            .order(by: "\(Field.timestamp).\(Field.created)", descending: true)
            .addSnapshotListener { snapshot, error in
                if let error = error {
                    publisher.send(completion: .failure(MessageRepositoryError.databaseError(error)))
                    return
                }
                
                guard let documents = snapshot?.documents else {
                    publisher.send([])
                    return
                }
                
                do {
                    let messages = try documents.compactMap { document in
                        try self.mapToMessage(id: document.documentID, data: document.data())
                    }
                    publisher.send(messages)
                } catch {
                    publisher.send(completion: .failure(MessageRepositoryError.databaseError(error)))
                }
            }
        
        // 保存监听器以避免被释放
        publisher
            .handleEvents(receiveCancel: {
                listener.remove()
            })
            .sink(
                receiveCompletion: { _ in },
                receiveValue: { _ in }
            )
            .store(in: &cancellables)
        
        return publisher.eraseToAnyPublisher()
    }
    
    public func observeMessage(id: String, chatId: String) -> AnyPublisher<Message, Error> {
        let publisher = PassthroughSubject<Message, Error>()
        
        // 监听消息变化
        let listener = messageDocumentRef(id: id, chatId: chatId)
            .addSnapshotListener { snapshot, error in
                if let error = error {
                    publisher.send(completion: .failure(MessageRepositoryError.databaseError(error)))
                    return
                }
                
                guard let document = snapshot, document.exists, let data = document.data() else {
                    publisher.send(completion: .failure(MessageRepositoryError.messageNotFound))
                    return
                }
                
                do {
                    let message = try self.mapToMessage(id: document.documentID, data: data)
                    publisher.send(message)
                } catch {
                    publisher.send(completion: .failure(MessageRepositoryError.databaseError(error)))
                }
            }
        
        // 保存监听器以避免被释放
        publisher
            .handleEvents(receiveCancel: {
                listener.remove()
            })
            .sink(
                receiveCompletion: { _ in },
                receiveValue: { _ in }
            )
            .store(in: &cancellables)
        
        return publisher.eraseToAnyPublisher()
    }
} 