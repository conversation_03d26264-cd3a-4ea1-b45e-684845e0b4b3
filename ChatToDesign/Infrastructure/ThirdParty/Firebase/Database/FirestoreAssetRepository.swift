//
//  FirestoreAssetRepository.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/20.
//

import Combine
import FirebaseFirestore
import Foundation

/// Firebase Firestore 资产仓储Implementation
///
/// Implementation AssetRepository 接口，使用 Firebase Firestore 作为数据源
public final class FirestoreAssetRepository {
  // MARK: - Constants

  private enum Collection {
    static let assets = "assets"
  }

  private enum Field {
    static let id = "id"
    static let userId = "userId"
    static let thumbnailUrl = "thumbnailUrl"
    static let path = "path"
    static let bucket = "bucket"
    static let name = "name"
    static let size = "size"
    static let type = "type"
    static let url = "url"
    static let sourceType = "sourceType"
    static let sourceTaskId = "sourceTaskId"
    static let generationPrompt = "generationPrompt"
    static let likeCount = "likeCount"
    static let favoriteCount = "favoriteCount"
    static let tags = "tags"
    static let description = "description"
    static let metadata = "metadata"
    static let status = "status"
    static let isPublic = "isPublic"
    static let createdAt = "createdAt"
    static let updatedAt = "updatedAt"
  }

  // MARK: - Properties

  private let firestore: Firestore

  // MARK: - Initialization

  public init(firestore: Firestore = .firestore()) {
    self.firestore = firestore
  }

  // MARK: - Private Helper Methods

  /// 资产集合引用
  private var assetsCollection: CollectionReference {
    return firestore.collection(Collection.assets)
  }

  /// 资产文档引用
  /// - Parameter id: 资产ID
  /// - Returns: 文档引用
  private func assetDocumentRef(id: String) -> DocumentReference {
    return assetsCollection.document(id)
  }

  /// 将 Firestore 数据转换为 UserAsset
  /// - Parameters:
  ///   - id: 资产ID
  ///   - data: Firestore 数据
  /// - Returns: UserAsset 对象
  /// - Throws: 如果数据格式不正确
  private func mapToUserAsset(id: String, data: [String: Any]) throws -> UserAsset {
    guard let userId = data[Field.userId] as? String,
      let path = data[Field.path] as? String,
      let bucket = data[Field.bucket] as? String,
      let name = data[Field.name] as? String,
      let size = data[Field.size] as? Int,
      let type = data[Field.type] as? String,
      let url = data[Field.url] as? String,
      let sourceTypeString = data[Field.sourceType] as? String,
      let sourceType = SourceType(rawValue: sourceTypeString),
      let likeCount = data[Field.likeCount] as? Int,
      let favoriteCount = data[Field.favoriteCount] as? Int,
      let createdAtTimestamp = data[Field.createdAt] as? Timestamp,
      let updatedAtTimestamp = data[Field.updatedAt] as? Timestamp
    else {
      throw AssetRepositoryError.databaseError(
        NSError(
          domain: "FirestoreAssetRepository",
          code: 500,
          userInfo: [NSLocalizedDescriptionKey: "无法解析资产数据"]
        )
      )
    }

    // 可选字段
    let thumbnailUrl = data[Field.thumbnailUrl] as? String
    let sourceTaskId = data[Field.sourceTaskId] as? String
    let generationPrompt = data[Field.generationPrompt] as? String
    let tags = data[Field.tags] as? [String]
    let description = data[Field.description] as? String
    let statusString = data[Field.status] as? String
    let status = statusString != nil ? AssetStatus(rawValue: statusString!) : nil
    let isPublic = data[Field.isPublic] as? Bool

    // 元数据处理
    var metadata: [String: AnyCodable]?
    if let metadataDict = data[Field.metadata] as? [String: Any] {
      metadata = metadataDict.mapValues { AnyCodable($0) }
    }

    return UserAsset(
      id: id,
      userId: userId,
      thumbnailUrl: thumbnailUrl,
      path: path,
      bucket: bucket,
      name: name,
      size: size,
      type: type,
      url: url,
      sourceType: sourceType,
      sourceTaskId: sourceTaskId,
      generationPrompt: generationPrompt,
      likeCount: likeCount,
      favoriteCount: favoriteCount,
      tags: tags,
      description: description,
      metadata: metadata,
      createdAt: createdAtTimestamp.dateValue(),
      updatedAt: updatedAtTimestamp.dateValue(),
      status: status,
      isPublic: isPublic
    )
  }

  /// 构建基础查询
  /// - Parameter userId: 用户ID
  /// - Returns: 基础查询
  private func buildBaseQuery(userId: String) -> Query {
    return
      assetsCollection
      .whereField(Field.userId, isEqualTo: userId)
      .whereField(Field.status, isNotEqualTo: AssetStatus.deleted.rawValue)
  }

  /// 应用查询过滤条件
  /// - Parameters:
  ///   - query: 基础查询
  ///   - assetQuery: 查询参数
  /// - Returns: 应用过滤条件后的查询
  private func applyFilters(to query: Query, with assetQuery: AssetListQuery) -> Query {
    var filteredQuery = query

    // 类型过滤
    if let type = assetQuery.type {
      if type.hasSuffix("/*") {
        // 处理通配符类型，如 "image/*"
        let prefix = String(type.dropLast(2))
        filteredQuery =
          filteredQuery
          .whereField(Field.type, isGreaterThanOrEqualTo: prefix)
          .whereField(Field.type, isLessThan: prefix + "~")
      } else {
        filteredQuery = filteredQuery.whereField(Field.type, isEqualTo: type)
      }
    }

    // 来源类型过滤
    if let sourceType = assetQuery.sourceType {
      filteredQuery = filteredQuery.whereField(Field.sourceType, isEqualTo: sourceType.rawValue)
    }

    // 状态过滤
    if let status = assetQuery.status {
      filteredQuery = filteredQuery.whereField(Field.status, isEqualTo: status.rawValue)
    }

    return filteredQuery
  }

  /// 处理 Firestore 错误
  /// - Parameter error: 原始错误
  /// - Returns: 转换后的领域错误
  private func handleFirestoreError(_ error: Error) -> AssetRepositoryError {
    if let nsError = error as NSError?, nsError.domain == FirestoreErrorDomain {
      switch nsError.code {
      case FirestoreErrorCode.permissionDenied.rawValue:
        return .accessDenied
      case FirestoreErrorCode.notFound.rawValue:
        return .assetNotFound
      default:
        return .databaseError(error)
      }
    } else {
      return .unknown(error)
    }
  }
}

// MARK: - AssetRepository Implementation

extension FirestoreAssetRepository: AssetRepository {
  public func getUserAssets(
    userId: String,
    query: AssetListQuery
  ) async throws -> UserAssetsResult {
    do {
      // 构建基础查询
      var firestoreQuery = buildBaseQuery(userId: userId)

      // 应用过滤条件
      firestoreQuery = applyFilters(to: firestoreQuery, with: query)

      // 排序
      firestoreQuery = firestoreQuery.order(by: Field.createdAt, descending: true)

      // Pagination处理
      let offset = (query.page - 1) * query.limit
      if offset > 0 {
        // 对于Pagination，我们需要先获取前面的文档来Implementation startAfter
        // 这里简化处理，实际生产环境可能需要更复杂的Pagination策略
        firestoreQuery = firestoreQuery.limit(to: offset + query.limit)
      } else {
        firestoreQuery = firestoreQuery.limit(to: query.limit)
      }

      // 执行查询
      let snapshot = try await firestoreQuery.getDocuments()

      // 转换数据
      var assets: [UserAsset] = []
      let documents = offset > 0 ? Array(snapshot.documents.dropFirst(offset)) : snapshot.documents

      for document in documents {
        let asset = try mapToUserAsset(id: document.documentID, data: document.data())
        assets.append(asset)
      }

      // 处理搜索过滤（在内存中进行，因为 Firestore 的全文搜索有限）
      if let searchTerm = query.search, !searchTerm.isEmpty {
        assets = assets.filter { asset in
          asset.name.localizedCaseInsensitiveContains(searchTerm)
            || asset.description?.localizedCaseInsensitiveContains(searchTerm) == true
        }
      }

      // 处理标签过滤
      if let tagsString = query.tags, !tagsString.isEmpty {
        let searchTags = tagsString.split(separator: ",").map {
          $0.trimmingCharacters(in: .whitespaces)
        }
        assets = assets.filter { asset in
          guard let assetTags = asset.tags else { return false }
          return searchTags.allSatisfy { searchTag in
            assetTags.contains { $0.localizedCaseInsensitiveContains(searchTag) }
          }
        }
      }

      // 创建Pagination信息（简化版本）
      let pagination = PaginationInfo(
        page: query.page,
        limit: query.limit,
        total: assets.count  // 这里简化了，实际应该是总数
          //        totalPages: max(1, (assets.count + query.limit - 1) / query.limit)
      )

      Logger.debug("Successfully retrieved \(assets.count) assets for user \(userId)")
      return UserAssetsResult(assets: assets, pagination: pagination)

    } catch let error as AssetRepositoryError {
      throw error
    } catch {
      Logger.error("Failed to get user assets: \(error.localizedDescription)")
      throw handleFirestoreError(error)
    }
  }

  public func deleteAsset(
    id: String,
    userId: String
  ) async throws -> AssetDeleteResponse {
    do {
      let assetRef = assetDocumentRef(id: id)

      // 首先验证资产存在且属于当前用户
      let snapshot = try await assetRef.getDocument()

      guard snapshot.exists else {
        throw AssetRepositoryError.assetNotFound
      }

      guard let data = snapshot.data(),
        let assetUserId = data[Field.userId] as? String
      else {
        throw AssetRepositoryError.databaseError(
          NSError(
            domain: "FirestoreAssetRepository",
            code: 500,
            userInfo: [NSLocalizedDescriptionKey: "无法解析资产数据"]
          )
        )
      }

      // 验证权限
      guard assetUserId == userId else {
        throw AssetRepositoryError.accessDenied
      }

      // 软删除：更新状态
      try await assetRef.updateData([
        Field.status: AssetStatus.deleted.rawValue,
        Field.updatedAt: Timestamp(date: Date()),
      ])

      Logger.debug("Successfully deleted asset: \(id)")
      return AssetDeleteResponse(message: "Asset deleted successfully", id: id)

    } catch let error as AssetRepositoryError {
      throw error
    } catch {
      Logger.error("Failed to delete asset: \(error.localizedDescription)")
      throw handleFirestoreError(error)
    }
  }

  public func searchAssets(
    userId: String,
    searchTerm: String,
    type: String?,
    limit: Int
  ) async throws -> [UserAsset] {
    let query = AssetListQuery(
      page: 1,
      limit: limit,
      type: type,
      sourceType: nil,
      status: nil,
      tags: nil,
      search: searchTerm
    )

    let response = try await getUserAssets(userId: userId, query: query)
    return response.assets
  }

  public func getAssetsByTags(
    userId: String,
    tags: [String],
    limit: Int
  ) async throws -> [UserAsset] {
    let tagsString = tags.joined(separator: ",")
    let query = AssetListQuery(
      page: 1,
      limit: limit,
      type: nil,
      sourceType: nil,
      status: nil,
      tags: tagsString,
      search: nil
    )

    let response = try await getUserAssets(userId: userId, query: query)
    return response.assets
  }

  public func getAssetsByType(
    userId: String,
    type: String,
    limit: Int
  ) async throws -> [UserAsset] {
    let query = AssetListQuery(
      page: 1,
      limit: limit,
      type: type,
      sourceType: nil,
      status: nil,
      tags: nil,
      search: nil
    )

    let response = try await getUserAssets(userId: userId, query: query)
    return response.assets
  }

  public func observeUserAssets(
    userId: String,
    query: AssetListQuery
  ) -> AnyPublisher<UserAssetsResult, Error> {
    let subject = PassthroughSubject<UserAssetsResult, Error>()

    // 构建基础查询
    var firestoreQuery = buildBaseQuery(userId: userId)

    // 应用过滤条件
    firestoreQuery = applyFilters(to: firestoreQuery, with: query)

    // 排序
    firestoreQuery = firestoreQuery.order(by: Field.createdAt, descending: true)

    // 限制数量
    firestoreQuery = firestoreQuery.limit(to: query.limit)

    // 添加监听器
    let listener = firestoreQuery.addSnapshotListener { [weak self] snapshot, error in
      guard let self = self else { return }

      if let error = error {
        subject.send(completion: .failure(self.handleFirestoreError(error)))
        return
      }

      guard let snapshot = snapshot else {
        subject.send(
          completion: .failure(
            AssetRepositoryError.unknown(
              NSError(
                domain: "FirestoreAssetRepository", code: 500,
                userInfo: [NSLocalizedDescriptionKey: "快照为空"])
            )))
        return
      }

      do {
        // 转换数据
        var assets: [UserAsset] = []
        for document in snapshot.documents {
          let asset = try self.mapToUserAsset(id: document.documentID, data: document.data())
          assets.append(asset)
        }

        // 处理搜索过滤（在内存中进行）
        if let searchTerm = query.search, !searchTerm.isEmpty {
          assets = assets.filter { asset in
            asset.name.localizedCaseInsensitiveContains(searchTerm)
              || asset.description?.localizedCaseInsensitiveContains(searchTerm) == true
          }
        }

        // 处理标签过滤
        if let tagsString = query.tags, !tagsString.isEmpty {
          let searchTags = tagsString.split(separator: ",").map {
            $0.trimmingCharacters(in: .whitespaces)
          }
          assets = assets.filter { asset in
            guard let assetTags = asset.tags else { return false }
            return searchTags.allSatisfy { searchTag in
              assetTags.contains { $0.localizedCaseInsensitiveContains(searchTag) }
            }
          }
        }

        // 创建Pagination信息
        let pagination = PaginationInfo(
          page: query.page,
          limit: query.limit,
          total: assets.count
        )

        let response = UserAssetsResult(assets: assets, pagination: pagination)
        subject.send(response)

      } catch {
        subject.send(completion: .failure(self.handleFirestoreError(error)))
      }
    }

    // 返回一个会自动清理监听器的发布者
    return
      subject
      .handleEvents(receiveCancel: {
        listener.remove()
      })
      .eraseToAnyPublisher()
  }
}
