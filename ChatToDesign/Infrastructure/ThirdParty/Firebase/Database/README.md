# Firebase 仓储实现

## 概述

本模块提供了领域仓储接口的 Firebase Firestore 实现，遵循了六边形架构原则，将基础设施实现与领域层进行了清晰分离。

## 文件结构

- `FirestoreUserRepository.swift` - 用户仓储的 Firebase 实现
- `FirebaseRepositoryFactory.swift` - 仓储工厂，用于创建各种仓储实现

## 使用方法

### 使用工厂创建仓储

推荐使用工厂模式创建仓储实例：

```swift
let userRepository = FirebaseRepositoryFactory.shared.makeUserRepository()
```

### 直接创建仓储实例

也可以直接创建仓储实例：

```swift
let userRepository = FirestoreUserRepository()
```

## 错误处理

所有仓储实现都使用领域层定义的 `UserRepositoryError` 和 `ChatRepositoryError` 类型，确保错误信息传递的一致性。核心错误处理原则：

1. 捕获所有基础设施异常（如 Firestore 错误），转换为领域错误
2. 为每个错误添加上下文信息，帮助调试
3. 保持错误层次结构的一致性

## 未来计划

1. 实现 `FirestoreChatRepository`
2. 添加缓存机制
3. 增加针对不同部署环境的配置支持
4. 添加详细的性能监控和日志
