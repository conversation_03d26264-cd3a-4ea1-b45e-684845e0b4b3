import Combine
import FirebaseFirestore
import Foundation

class FirestoreVideoTaskRepository: VideoTaskRepository {

  private let firestore: Firestore
  private let taskCollectionName = "video-generation-tasks"

  init(firestore: Firestore = Firestore.firestore()) {
    self.firestore = firestore
  }

  private func taskDocumentRef(taskId: String) -> DocumentReference {
    return firestore.collection(taskCollectionName).document(taskId)
  }

  func observeVideoTask(taskId: String) -> AnyPublisher<VideoGenerationTask, Error> {
    let documentRef = taskDocumentRef(taskId: taskId)
    let subject = PassthroughSubject<VideoGenerationTask, Error>()

    // Add the snapshot listener
    let listenerRegistration = documentRef.addSnapshotListener { documentSnapshot, error in
      // Handle Firestore errors
      if let error = error {
        subject.send(completion: .failure(VideoTaskRepositoryError.firestoreError(error)))
        return
      }

      // Check if document exists
      guard let document = documentSnapshot, document.exists else {
        // If the document doesn't exist upon first listen, send an error.
        subject.send(completion: .failure(VideoTaskRepositoryError.taskNotFound))
        return
      }

      // Decode the document data
      do {
        // Use Firestore's built-in Codable support
        // This automatically handles Timestamps and the @DocumentID property wrapper
        let task = try document.data(as: VideoGenerationTask.self)

        // Send the latest task data
        subject.send(task)

        // Optional: Uncomment to automatically complete the stream when task reaches a final state
        // if task.status == .succeeded || task.status == .failed {
        //    subject.send(completion: .finished)
        // }

      } catch let decodingError {
        Logger.error("Error decoding video task \(taskId): \(decodingError)")
        // Complete the stream with an error if decoding fails
        subject.send(completion: .failure(VideoTaskRepositoryError.decodingError(decodingError)))
      }
    }

    // Return a publisher that handles listener removal on cancellation
    return
      subject
      .handleEvents(receiveCancel: {
        Logger.info("Subscription cancelled for video task \(taskId). Removing Firestore listener.")
        listenerRegistration.remove()  // Remove listener
      })
      .eraseToAnyPublisher()
  }

  // MARK: - Observe Task List for User
  func observeTaskList(userId: String, limit: Int = 20) -> AnyPublisher<
    [VideoGenerationTask], Error
  > {
    let subject = PassthroughSubject<[VideoGenerationTask], Error>()
    let collectionRef = firestore.collection(taskCollectionName)

    let query =
      collectionRef
      .whereField("userId", isEqualTo: userId)
      .order(by: "createdAt", descending: true)
      .limit(to: limit)

    let listenerRegistration = query.addSnapshotListener { querySnapshot, error in
      if let error = error {
        Logger.error("Error listening to video task list for user \(userId): \(error)")
        subject.send(completion: .failure(VideoTaskRepositoryError.firestoreError(error)))
        return
      }

      guard let snapshot = querySnapshot else {
        // This case should ideally not happen if error is nil, but good to be safe.
        Logger.warning("Video task list snapshot was nil for user \(userId) without an error.")
        subject.send([])  // Send empty list or handle as an error as appropriate
        return
      }

      let tasks = snapshot.documents.compactMap { document -> VideoGenerationTask? in
        do {
          return try document.data(as: VideoGenerationTask.self)
        } catch let decodingError {
          Logger.error(
            "Error decoding video task \(document.documentID) in list for user \(userId): \(decodingError)"
          )
          // Optionally, you could send an error through the subject here if one failure is critical,
          // or just skip the problematic document.
          return nil
        }
      }
      subject.send(tasks)
    }

    return
      subject
      .handleEvents(receiveCancel: {
        Logger.info(
          "Subscription cancelled for video task list (userId: \(userId)). Removing Firestore listener."
        )
        listenerRegistration.remove()
      })
      .eraseToAnyPublisher()
  }

  // MARK: - Get Single Task
  func getTask(taskId: String) async throws -> VideoGenerationTask {
    let documentRef = taskDocumentRef(taskId: taskId)
    do {
      let document = try await documentRef.getDocument()
      guard document.exists else {
        throw VideoTaskRepositoryError.taskNotFound
      }
      // Use Firestore Codable support
      let task = try document.data(as: VideoGenerationTask.self)
      return task
    } catch let error where !(error is VideoTaskRepositoryError) {
      // Catch Firestore specific errors or other unexpected errors
      throw VideoTaskRepositoryError.firestoreError(error)
    } catch {
      // Re-throw known errors like taskNotFound or decodingError if data(as:) throws
      throw error
    }
  }

  // MARK: - Get Task List for User with Pagination
  func getTaskList(userId: String, limit: Int = 20, startAfterTaskId: String? = nil) async throws
    -> [VideoGenerationTask]
  {
    let collectionRef = firestore.collection(taskCollectionName)
    var query: Query =
      collectionRef
      .whereField("userId", isEqualTo: userId)
      .order(by: "createdAt", descending: true)  // Order by creation time, newest first
      .limit(to: limit)

    // Handle pagination: Fetch the document to start after
    if let startAfterId = startAfterTaskId {
      do {
        let startDocument = try await taskDocumentRef(taskId: startAfterId).getDocument()
        if startDocument.exists {
          query = query.start(afterDocument: startDocument)
        } else {
          // Handle case where the startAfter document doesn't exist (e.g., deleted)
          // Decide behavior: return empty list, log warning, or throw error?
          // For now, let's return an empty list or proceed without pagination
          Logger.warning(
            "Warning: startAfterTaskId \(startAfterId) not found, fetching from beginning.")
          // Or potentially: throw VideoTaskRepositoryError.invalidPaginationToken
        }
      } catch {
        // Error fetching the start document, treat as Firestore error
        throw VideoTaskRepositoryError.firestoreError(error)
      }
    }

    // Execute the query
    do {
      let snapshot = try await query.getDocuments()
      // Decode documents, skipping any that fail to decode
      let tasks = try snapshot.documents.compactMap { document -> VideoGenerationTask? in
        do {
          return try document.data(as: VideoGenerationTask.self)
        } catch {
          Logger.warning(
            "Warning: Failed to decode video task document \(document.documentID): \(error)")
          return nil  // Skip this document if decoding fails
        }
      }
      return tasks
    } catch {
      // Catch Firestore query errors
      throw VideoTaskRepositoryError.firestoreError(error)
    }
  }

  // MARK: - Update Task Status
  func updateTaskStatus(taskId: String, status: TaskStatus) async throws {
    let documentRef = taskDocumentRef(taskId: taskId)
    do {
      try await documentRef.updateData([
        "status": status.rawValue
      ])
      Logger.info("FirestoreVideoTaskRepository: Task \(taskId) status updated to \(status)")
    } catch {
      Logger.error(
        "FirestoreVideoTaskRepository: Failed to update task \(taskId) status: \(error.localizedDescription)"
      )
      throw VideoTaskRepositoryError.firestoreError(error)
    }
  }

}
