// FirestoreUserRepository.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import FirebaseFirestore
import Foundation

/// Firebase Firestore 用户仓储Implementation
///
/// Implementation UserRepository 接口，使用 Firebase Firestore 作为数据源
public final class FirestoreUserRepository {
  // MARK: - Constants

  private enum Collection {
    static let users = "users"
  }

  private enum Field {
    static let email = "email"
    static let displayName = "displayName"
    static let status = "status"
    static let createdAt = "createdAt"
    static let lastLogin = "lastLogin"
    static let photoURL = "photoURL"
    static let bio = "bio"
    static let location = "location"
    static let preferences = "preferences"
  }

  // MARK: - Properties

  private let firestore: Firestore

  // MARK: - Initialization

  public init(firestore: Firestore = .firestore()) {
    self.firestore = firestore
  }
}

// MARK: - UserRepository Implementation

extension FirestoreUserRepository: UserRepository {
  public func getUser(id: String) async throws -> User {
    do {
      let snapshot = try await firestore.collection(Collection.users).document(id).getDocument()

      guard snapshot.exists else {
        throw UserRepositoryError.userNotFound
      }

      guard let data = snapshot.data() else {
        throw UserRepositoryError.databaseError(
          NSError(
            domain: "FirestoreUserRepository", code: 500,
            userInfo: [NSLocalizedDescriptionKey: "无法解析用户数据"]))
      }

      return try mapToUser(id: id, data: data)
    } catch let error as UserRepositoryError {
      throw error
    } catch {
      throw UserRepositoryError.databaseError(error)
    }
  }

  public func findUserByEmail(email: String) async throws -> User? {
    do {
      let query = firestore.collection(Collection.users)
        .whereField(Field.email, isEqualTo: email)
        .limit(to: 1)

      let snapshot = try await query.getDocuments()

      guard let document = snapshot.documents.first else {
        return nil
      }

      return try mapToUser(id: document.documentID, data: document.data())
    } catch {
      throw UserRepositoryError.databaseError(error)
    }
  }

  public func saveUser(_ user: User) async throws -> String {
    do {
      let userId = user.id.isEmpty ? UUID().uuidString : user.id
      let userRef = firestore.collection(Collection.users).document(userId)

      let userData: [String: Any] = [
        Field.email: user.email,
        Field.displayName: user.displayName,
        Field.status: user.status.rawValue,
        Field.createdAt: user.createdAt,
        Field.lastLogin: user.lastLogin,
        Field.photoURL: user.photoURL?.absoluteString ?? "",
        Field.bio: user.bio ?? "",
        Field.location: user.location ?? "",
        Field.preferences: [
          "theme": user.preferences.theme,
          "language": user.preferences.language,
          "notificationsEnabled": user.preferences.notificationsEnabled,
        ],
      ]

      try await userRef.setData(userData)
      return userId
    } catch {
      throw UserRepositoryError.failedToCreateUser
    }
  }

  public func updateUser(_ user: User) async throws {
    do {
      guard !user.id.isEmpty else {
        throw UserRepositoryError.failedToUpdateUser
      }

      let userRef = firestore.collection(Collection.users).document(user.id)

      // 检查用户是否存在
      let document = try await userRef.getDocument()
      guard document.exists else {
        throw UserRepositoryError.userNotFound
      }

      let userData: [String: Any] = [
        Field.displayName: user.displayName,
        Field.status: user.status.rawValue,
        Field.photoURL: user.photoURL?.absoluteString ?? "",
        Field.bio: user.bio ?? "",
        Field.location: user.location ?? "",
        Field.preferences: [
          "theme": user.preferences.theme,
          "language": user.preferences.language,
          "notificationsEnabled": user.preferences.notificationsEnabled,
        ],
      ]

      try await userRef.updateData(userData)
    } catch let error as UserRepositoryError {
      throw error
    } catch {
      throw UserRepositoryError.failedToUpdateUser
    }
  }

  public func updateLastLogin(id: String) async throws {
    do {
      try await firestore.collection(Collection.users).document(id).updateData([
        Field.lastLogin: FieldValue.serverTimestamp()
      ])
    } catch {
      throw UserRepositoryError.failedToUpdateUser
    }
  }

  public func deleteUser(id: String) async throws {
    do {
      try await firestore.collection(Collection.users).document(id).delete()
    } catch {
      throw UserRepositoryError.failedToDeleteUser
    }
  }

  public func userExists(id: String) async throws -> Bool {
    do {
      let document = try await firestore.collection(Collection.users).document(id).getDocument()
      return document.exists
    } catch {
      throw UserRepositoryError.databaseError(error)
    }
  }
}

// MARK: - Private Helper Methods

extension FirestoreUserRepository {
  fileprivate func mapToUser(id: String, data: [String: Any]) throws -> User {
    guard let email = data[Field.email] as? String else {
      throw UserRepositoryError.databaseError(
        NSError(
          domain: "FirestoreUserRepository", code: 400,
          userInfo: [NSLocalizedDescriptionKey: "缺少电子邮件字段"]))
    }

    let displayName = data[Field.displayName] as? String ?? ""

    // 处理状态
    let statusRawValue = data[Field.status] as? String ?? UserStatus.active.rawValue
    let status = UserStatus(rawValue: statusRawValue) ?? .active

    // 处理时间戳
    let createdAt: Date
    if let timestamp = data[Field.createdAt] as? Timestamp {
      createdAt = timestamp.dateValue()
    } else {
      createdAt = Date()
    }

    let lastLogin: Date
    if let timestamp = data[Field.lastLogin] as? Timestamp {
      lastLogin = timestamp.dateValue()
    } else {
      lastLogin = Date()
    }

    // 处理头像URL
    let photoURL: URL?
    if let photoURLString = data[Field.photoURL] as? String, !photoURLString.isEmpty {
      photoURL = URL(string: photoURLString)
    } else {
      photoURL = nil
    }

    // 处理可选字段
    let bio = data[Field.bio] as? String
    let location = data[Field.location] as? String

    // 处理偏好设置
    var preferences = UserPreferences.default
    if let prefsData = data[Field.preferences] as? [String: Any] {
      if let theme = prefsData["theme"] as? String {
        preferences.theme = theme
      }
      if let language = prefsData["language"] as? String {
        preferences.language = language
      }
      if let notificationsEnabled = prefsData["notificationsEnabled"] as? Bool {
        preferences.notificationsEnabled = notificationsEnabled
      }
    }

    return User(
      id: id,
      email: email,
      displayName: displayName,
      photoURL: photoURL,
      status: status,
      createdAt: createdAt,
      lastLogin: lastLogin,
      bio: bio,
      location: location,
      preferences: preferences
    )
  }
}
