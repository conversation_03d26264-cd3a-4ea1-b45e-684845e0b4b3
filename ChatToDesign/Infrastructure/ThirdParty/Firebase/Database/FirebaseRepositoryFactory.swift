// FirebaseRepositoryFactory.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import FirebaseFirestore
import Foundation

/// Firebase 仓储工厂类
///
/// 用于创建各种 Firebase 仓储Implementation
public final class FirebaseRepositoryFactory {
  // MARK: - 单例

  public static let shared = FirebaseRepositoryFactory()

  // MARK: - Properties

  private let firestore: Firestore

  // MARK: - Initialization

  public init(firestore: Firestore = .firestore()) {
    self.firestore = firestore
  }

  // MARK: - 工厂方法

  /// 创建用户仓储
  /// - Returns: 用户仓储Implementation
  public func makeUserRepository() -> any UserRepository {
    return FirestoreUserRepository(firestore: firestore)
  }

  /// 创建消息仓储
  /// - Returns: 消息仓储Implementation
  public func makeMessageRepository() -> any MessageRepository {
    return FirestoreMessageRepository(firestore: firestore)
  }

  /// 创建聊天仓储
  /// - Returns: 聊天仓储Implementation
  public func makeChatRepository() -> any ChatRepository {
    return FirestoreChatRepository(firestore: firestore)
  }

  /// 创建资产仓储
  /// - Returns: 资产仓储Implementation
  public func makeAssetRepository() -> any AssetRepository {
    return FirestoreAssetRepository(firestore: firestore)
  }
}
