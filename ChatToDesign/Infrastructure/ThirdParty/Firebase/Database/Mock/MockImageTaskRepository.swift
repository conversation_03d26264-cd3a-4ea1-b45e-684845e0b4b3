//import Combine
//import Foundation
//
//class MockImageTaskRepository: ImageTaskRepository {
//  func observeImageTask(taskId: String) -> AnyPublisher<ImageGenerationTask, Error> {
//    let subject = PassthroughSubject<ImageGenerationTask, Error>()
//
//    // Simulate async updates via Combine
//    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
//      print("MockImageTaskRepository: Simulating 'processing' update for \(taskId)")
//      subject.send(
//        ImageGenerationTask(
//          taskId: taskId, userId: "mockUser", status: .processing, prompt: "mock prompt"))
//    }
//    DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
//      print("MockImageTaskRepository: Simulating 'succeeded' update for \(taskId)")
//      let finalTask = ImageGenerationTask(
//        taskId: taskId, userId: "mockUser", status: .succeeded, prompt: "mock prompt",
//        resultImageUrls: ["https://example.com/result1.jpg", "https://example.com/result2.jpg"])
//      subject.send(finalTask)
//      subject.send(completion: .finished)  // Indicate stream completion on final state
//    }
//    // If simulating failure:
//    // DispatchQueue.main.asyncAfter(deadline: .now() + 6.0) {
//    //     print("MockImageTaskRepository: Simulating 'failed' update for \(taskId)")
//    //     subject.send(completion: .failure(NSError(domain: "MockError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Simulated generation failure."])))
//    // }
//
//    // Return a type-erased publisher
//    // Add handleEvents to simulate listener removal on cancellation
//    return
//      subject
//      .handleEvents(receiveCancel: {
//        print(
//          "MockImageTaskRepository: Subscription cancelled for task \(taskId). (Simulating listener removal)"
//        )
//      })
//      .eraseToAnyPublisher()
//  }
//}
