// FirestoreChatRepository.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import FirebaseFirestore
import Foundation

/**
 Firebase Firestore chat repository implementation

 Implements ChatRepository interface, using Firebase Firestore as data source
 */
public final class FirestoreChatRepository {
    // MARK: - Constants

    private enum Collection {
        static let chats = "chats"
        static let messages = "messages"
        static let users = "users"
    }
    
    private enum Field {
        static let userId = "userId"
        static let title = "title"
        static let description = "description"
        static let createdAt = "createdAt"
        static let updatedAt = "updatedAt"
        static let status = "status"
        static let model = "model"
        static let modelType = "modelType"
        static let messageCount = "messageCount"
        static let parentChatId = "parentChatId"
        static let visibility = "visibility"
        static let sharedWithUserIds = "sharedWithUserIds"
        static let forkCount = "forkCount"
        static let tags = "tags"
        static let parameters = "parameters"
        static let systemPrompt = "systemPrompt"
        static let temperature = "temperature"
        static let maxResponseLength = "maxResponseLength"
    }

    // MARK: - Properties

    private let firestore: Firestore

    // MARK: - Initialization

    public init(firestore: Firestore = .firestore()) {
        self.firestore = firestore
    }

    // MARK: - Private Helper Methods

    /// Map Firestore data to Chat object
    /// - Parameters:
    ///   - id: Document ID
    ///   - data: Firestore data
    /// - Returns: Chat object
    private func mapToChat(id: String, data: [String: Any]) throws -> Chat {
        guard let userId = data[Field.userId] as? String,
              let title = data[Field.title] as? String else {
            throw ChatRepositoryError.databaseError(NSError(
                domain: "FirestoreChatRepository",
                code: 500,
                userInfo: [NSLocalizedDescriptionKey: "Required fields missing"]
            ))
        }
        
        let description = data[Field.description] as? String
        
        let createdAt: Date?
        if let timestamp = data[Field.createdAt] as? Timestamp {
            createdAt = timestamp.dateValue()
        } else {
            createdAt = nil
        }
        
        let updatedAt: Date?
        if let timestamp = data[Field.updatedAt] as? Timestamp {
            updatedAt = timestamp.dateValue()
        } else {
            updatedAt = nil
        }
        
        let statusRaw = data[Field.status] as? String ?? "active"
        let status = ChatStatus(rawValue: statusRaw) ?? .active
        
        let model: AIModel
        if let modelTypeRaw = data[Field.modelType] as? String {
            if modelTypeRaw.hasPrefix("custom_") {
                let customName = modelTypeRaw.replacingOccurrences(of: "custom_", with: "")
                model = .custom(customName)
            } else {
                model = .gemini
            }
        } else {
            model = .gemini
        }
        
        let messageCount = data[Field.messageCount] as? Int ?? 0
        let parentChatId = data[Field.parentChatId] as? String
        
        let visibilityRaw = data[Field.visibility] as? String ?? "private"
        let visibility = ChatVisibility(rawValue: visibilityRaw) ?? .private
        
        let sharedWithUserIds = data[Field.sharedWithUserIds] as? [String]
        let forkCount = data[Field.forkCount] as? Int ?? 0
        let tags = data[Field.tags] as? [String]
        
        var parameters: ChatParameters?
        if let paramsData = data[Field.parameters] as? [String: Any] {
            let systemPrompt = paramsData[Field.systemPrompt] as? String
            let temperature = paramsData[Field.temperature] as? Double
            let maxResponseLength = paramsData[Field.maxResponseLength] as? Int
            
            parameters = ChatParameters(
                systemPrompt: systemPrompt,
                temperature: temperature,
                modelParameters: nil,
                maxResponseLength: maxResponseLength
            )
        }
        
        let chat = Chat(
            id: id,
            userId: userId,
            title: title,
            description: description,
            createdAt: createdAt,
            updatedAt: updatedAt,
            status: status,
            model: model,
            messageCount: messageCount,
            parentChatId: parentChatId,
            visibility: visibility,
            sharedWithUserIds: sharedWithUserIds,
            forkCount: forkCount,
            tags: tags,
            parameters: parameters
        )
        
        return chat
    }
    
    /// Convert Chat object to Firestore data
    /// - Parameter chat: Chat object
    /// - Returns: Firestore data
    private func mapFromChat(_ chat: Chat) -> [String: Any] {
        var data: [String: Any] = [
            Field.userId: chat.userId,
            Field.title: chat.title,
            Field.messageCount: chat.messageCount,
            Field.status: chat.status.rawValue,
            Field.visibility: chat.visibility.rawValue,
            Field.forkCount: chat.forkCount
        ]
        
        // Convert model type
        switch chat.model {
        case .gemini:
            data[Field.modelType] = "gemini"
        case .custom(let name):
            data[Field.modelType] = "custom_\(name)"
        }

        // Add optional fields
        if let description = chat.description {
            data[Field.description] = description
        }
        
        if let createdAt = chat.createdAt {
            data[Field.createdAt] = Timestamp(date: createdAt)
        } else {
            data[Field.createdAt] = Timestamp(date: Date())
        }
        
        // Update time is always current time
        data[Field.updatedAt] = Timestamp(date: Date())
        
        if let parentChatId = chat.parentChatId {
            data[Field.parentChatId] = parentChatId
        }
        
        if let sharedWithUserIds = chat.sharedWithUserIds {
            data[Field.sharedWithUserIds] = sharedWithUserIds
        }
        
        if let tags = chat.tags {
            data[Field.tags] = tags
        }
        
        // Convert chat parameters
        if let parameters = chat.parameters {
            var paramsData: [String: Any] = [:]
            
            if let systemPrompt = parameters.systemPrompt {
                paramsData[Field.systemPrompt] = systemPrompt
            }
            
            if let temperature = parameters.temperature {
                paramsData[Field.temperature] = temperature
            }
            
            if let maxResponseLength = parameters.maxResponseLength {
                paramsData[Field.maxResponseLength] = maxResponseLength
            }
            
            if !paramsData.isEmpty {
                data[Field.parameters] = paramsData
            }
        }
        
        return data
    }
}

// MARK: - ChatRepository Implementation

extension FirestoreChatRepository: ChatRepository {
    public func getChat(id: String) async throws -> Chat {
        do {
            let snapshot = try await firestore.collection(Collection.chats).document(id).getDocument()
            
            guard snapshot.exists else {
                throw ChatRepositoryError.chatNotFound
            }
            
            guard let data = snapshot.data() else {
                throw ChatRepositoryError.databaseError(NSError(
                    domain: "FirestoreChatRepository",
                    code: 500,
                    userInfo: [NSLocalizedDescriptionKey: "Unable to parse chat data"]
                ))
            }
            
            return try mapToChat(id: id, data: data)
        } catch let error as ChatRepositoryError {
            throw error
        } catch {
            throw ChatRepositoryError.databaseError(error)
        }
    }
    
    public func getUserChats(
        userId: String,
        limit: Int = 50,
        startAfter: String? = nil,
        status: ChatStatus? = nil
    ) async throws -> [Chat] {
        do {
            var query = firestore.collection(Collection.chats)
                .whereField(Field.userId, isEqualTo: userId)
                .limit(to: limit)
            
            if let status = status {
                query = query.whereField(Field.status, isEqualTo: status.rawValue)
            } else {
                // By default, do not return deleted chats
                query = query.whereField(Field.status, isNotEqualTo: ChatStatus.deleted.rawValue)
            }
            
            // Sort by update time in descending order
            query = query.order(by: Field.updatedAt, descending: true)
            
            // Pagination
            if let startAfter = startAfter {
                let startDoc = try await firestore.collection(Collection.chats).document(startAfter).getDocument()
                if startDoc.exists {
                    query = query.start(afterDocument: startDoc)
                }
            }
            
            let snapshot = try await query.getDocuments()
            
            var chats: [Chat] = []
            for document in snapshot.documents {
                let chat = try mapToChat(id: document.documentID, data: document.data())
                chats.append(chat)
            }
            
            return chats
        } catch {
            throw ChatRepositoryError.databaseError(error)
        }
    }
    
    public func saveChat(_ chat: Chat) async throws -> String {
        do {
            let chatId = chat.id ?? UUID().uuidString
            Logger.info("saveChat: \(chatId)")
            let chatRef = firestore.collection(Collection.chats).document(chatId)
            
            var chatData = mapFromChat(chat)
            Logger.info("chatData: \(chatData)")
            // Ensure creation time exists
            if chat.createdAt == nil {
                chatData[Field.createdAt] = Timestamp(date: Date())
            }
            
            try await chatRef.setData(chatData)
            return chatId
        } catch {
            throw ChatRepositoryError.failedToCreateChat
        }
    }
    
    public func updateChat(_ chat: Chat) async throws {
        guard let chatId = chat.id else {
            throw ChatRepositoryError.failedToUpdateChat
        }
        
        do {
            // Check if chat exists and is not archived
            let existingChat = try await getChat(id: chatId)
            
            if existingChat.status == .archived {
                throw ChatRepositoryError.chatArchived
            }
            
            if existingChat.status == .deleted {
                throw ChatRepositoryError.chatNotFound
            }
            
            // Check permissions
            if existingChat.userId != chat.userId {
                throw ChatRepositoryError.unauthorized
            }
            
            let chatData = mapFromChat(chat)
            try await firestore.collection(Collection.chats).document(chatId).updateData(chatData)
        } catch let error as ChatRepositoryError {
            throw error
        } catch {
            throw ChatRepositoryError.failedToUpdateChat
        }
    }
    
    public func updateChatTitle(chatId: String, title: String) async throws {
        do {
            // Check if chat exists
            let existingChat = try await getChat(id: chatId)
            
            if existingChat.status == .archived {
                throw ChatRepositoryError.chatArchived
            }
            
            if existingChat.status == .deleted {
                throw ChatRepositoryError.chatNotFound
            }
            
            let updateData: [String: Any] = [
                Field.title: title,
                Field.updatedAt: Timestamp(date: Date())
            ]
            
            try await firestore.collection(Collection.chats).document(chatId).updateData(updateData)
        } catch let error as ChatRepositoryError {
            throw error
        } catch {
            throw ChatRepositoryError.failedToUpdateChat
        }
    }
    
    public func updateChatDescription(chatId: String, description: String?) async throws {
        do {
            // Check if chat exists
            let existingChat = try await getChat(id: chatId)
            
            if existingChat.status == .archived {
                throw ChatRepositoryError.chatArchived
            }
            
            if existingChat.status == .deleted {
                throw ChatRepositoryError.chatNotFound
            }
            
            var updateData: [String: Any] = [
                Field.updatedAt: Timestamp(date: Date())
            ]
            
            if let description = description {
                updateData[Field.description] = description
            } else {
                updateData[Field.description] = FieldValue.delete()
            }
            
            try await firestore.collection(Collection.chats).document(chatId).updateData(updateData)
        } catch let error as ChatRepositoryError {
            throw error
        } catch {
            throw ChatRepositoryError.failedToUpdateChat
        }
    }
    
    public func updateChatStatus(chatId: String, status: ChatStatus) async throws {
        do {
            // Check if chat exists
            let existingChat = try await getChat(id: chatId)
            
            if existingChat.status == .archived && status != .deleted {
                throw ChatRepositoryError.chatArchived
            }
            
            if existingChat.status == .deleted && status != .deleted {
                throw ChatRepositoryError.chatNotFound
            }
            
            let updateData: [String: Any] = [
                Field.status: status.rawValue,
                Field.updatedAt: Timestamp(date: Date())
            ]
            
            try await firestore.collection(Collection.chats).document(chatId).updateData(updateData)
        } catch let error as ChatRepositoryError {
            throw error
        } catch {
            throw ChatRepositoryError.failedToUpdateChat
        }
    }
    
    public func incrementMessageCount(chatId: String) async throws {
        do {
            // Check if chat exists
            let existingChat = try await getChat(id: chatId)
            
            if existingChat.status == .archived {
                throw ChatRepositoryError.chatArchived
            }
            
            if existingChat.status == .deleted {
                throw ChatRepositoryError.chatNotFound
            }
            
            let updateData: [String: Any] = [
                Field.messageCount: FieldValue.increment(Int64(1)),
                Field.updatedAt: Timestamp(date: Date())
            ]
            
            try await firestore.collection(Collection.chats).document(chatId).updateData(updateData)
        } catch let error as ChatRepositoryError {
            throw error
        } catch {
            throw ChatRepositoryError.failedToUpdateChat
        }
    }
    
    public func updateChatVisibility(
        chatId: String,
        visibility: ChatVisibility,
        sharedWithUserIds: [String]?
    ) async throws {
        do {
            // Check if chat exists
            let existingChat = try await getChat(id: chatId)
            
            if existingChat.status == .archived {
                throw ChatRepositoryError.chatArchived
            }
            
            if existingChat.status == .deleted {
                throw ChatRepositoryError.chatNotFound
            }
            
            var updateData: [String: Any] = [
                Field.visibility: visibility.rawValue,
                Field.updatedAt: Timestamp(date: Date())
            ]
            
            // If shared visibility, must provide shared user ID list
            if visibility == .shared {
                guard let sharedWithUserIds = sharedWithUserIds, !sharedWithUserIds.isEmpty else {
                    throw ChatRepositoryError.failedToUpdateChat
                }
                updateData[Field.sharedWithUserIds] = sharedWithUserIds
            } else {
                // For other visibility, clear shared user list
                updateData[Field.sharedWithUserIds] = FieldValue.delete()
            }
            
            try await firestore.collection(Collection.chats).document(chatId).updateData(updateData)
        } catch let error as ChatRepositoryError {
            throw error
        } catch {
            throw ChatRepositoryError.failedToUpdateChat
        }
    }
    
    public func deleteChat(id: String) async throws {
        do {
            // Mark as deleted instead of actually deleting
            let updateData: [String: Any] = [
                Field.status: ChatStatus.deleted.rawValue,
                Field.updatedAt: Timestamp(date: Date())
            ]
            
            try await firestore.collection(Collection.chats).document(id).updateData(updateData)
        } catch {
            throw ChatRepositoryError.failedToDeleteChat
        }
    }
    
    public func getChatMessageCount(chatId: String) async throws -> Int {
        do {
            let chat = try await getChat(id: chatId)
            return chat.messageCount
        } catch {
            throw error
        }
    }
    
    public func canUserAccessChat(chatId: String, userId: String) async throws -> Bool {
        do {
            let chat = try await getChat(id: chatId)
            return chat.canAccess(by: userId)
        } catch ChatRepositoryError.chatNotFound {
            return false
        } catch {
            throw error
        }
    }
    
    public func canUserEditChat(chatId: String, userId: String) async throws -> Bool {
        do {
            let chat = try await getChat(id: chatId)
            return chat.canEdit(by: userId)
        } catch ChatRepositoryError.chatNotFound {
            return false
        } catch {
            throw error
        }
    }
    
    public func getForkCount(chatId: String) async throws -> Int {
        do {
            let chat = try await getChat(id: chatId)
            return chat.forkCount
        } catch {
            throw error
        }
    }
    
    public func incrementForkCount(chatId: String) async throws {
        do {
            let updateData: [String: Any] = [
                Field.forkCount: FieldValue.increment(Int64(1))
            ]
            
            try await firestore.collection(Collection.chats).document(chatId).updateData(updateData)
        } catch {
            throw ChatRepositoryError.failedToUpdateChat
        }
    }
} 