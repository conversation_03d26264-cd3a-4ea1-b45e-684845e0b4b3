import FirebaseAuth
import Foundation

/// Firebase 用户数据传输对象
struct FirebaseUserDTO: AuthUser {
  private let user: FirebaseAuth.User

  init(user: FirebaseAuth.User) {
    self.user = user
  }

  // MARK: - AuthUser Protocol

  var uid: String { user.uid }
  var email: String? { user.email }
  var isEmailVerified: Bool { user.isEmailVerified }
  var displayName: String? { user.displayName }
  var photoURL: URL? { user.photoURL }

  // MARK: - Additional Firebase Specific Properties

  var phoneNumber: String? { user.phoneNumber }
  var providerID: String { user.providerID }

  // MARK: - Helpers

  var isAnonymous: Bool { user.isAnonymous }
  var metadata: UserMetadata { user.metadata }

  func getIDToken() async throws -> String {
    try await user.getIDToken()
  }
}
