import Combine
import FirebaseAuth
import FirebaseCore
import GoogleSignIn
import SwiftUI

final class FirebaseAuthAdapter: ObservableObject, AuthService {
  // MARK: - Properties

  private var authStateHandle: AuthStateDidChangeListenerHandle?
  private let auth: Auth
  private var cancellables = Set<AnyCancellable>()

  @Published private(set) var authState: AuthState = .unauthenticated

  var authStatePublisher: AnyPublisher<AuthState, Never> {
    $authState.eraseToAnyPublisher()
  }

  // MARK: - Initialization

  init(auth: Auth = .auth()) {
    self.auth = auth

    // 先检查当前用户状态，再设置监听器
    if let user = auth.currentUser {
      self.authState = .authenticated(FirebaseUserDTO(user: user))
    } else {
      self.authState = .unauthenticated
    }

    setupAuthListener()
  }

  deinit {
    if let handle = authStateHandle {
      auth.removeStateDidChangeListener(handle)
    }
  }

  // MARK: - Private Methods

  private func setupAuthListener() {
    authStateHandle = auth.addStateDidChangeListener { [weak self] _, firebaseUser in
      guard let self = self else { return }

      DispatchQueue.main.async {
        if let user = firebaseUser {
          self.authState = .authenticated(FirebaseUserDTO(user: user))
        } else {
          self.authState = .unauthenticated
        }
      }
    }
  }

  // MARK: - AuthService Implementation

  func signInWithGoogle() async throws -> AuthUser {
    guard let clientID = FirebaseApp.app()?.options.clientID else {
      throw AuthError.configurationError
    }

    let config = GIDConfiguration(clientID: clientID)
    GIDSignIn.sharedInstance.configuration = config

    guard let windowScene = await UIApplication.shared.connectedScenes.first as? UIWindowScene,
      let rootViewController = await windowScene.windows.first?.rootViewController
    else {
      throw AuthError.configurationError
    }

    do {
      let result = try await GIDSignIn.sharedInstance.signIn(withPresenting: rootViewController)

      guard let idToken = result.user.idToken?.tokenString else {
        throw AuthError.unknown(description: "无法获取 Google 身份令牌")
      }

      let accessToken = result.user.accessToken.tokenString
      let credential = GoogleAuthProvider.credential(
        withIDToken: idToken,
        accessToken: accessToken
      )

      let authResult = try await auth.signIn(with: credential)
      return FirebaseUserDTO(user: authResult.user)
    } catch let error as GIDSignInError where error.code == .canceled {
      throw AuthError.userCancelled
    } catch {
      throw AuthError.unknown(description: error.localizedDescription)
    }
  }

  func signInWithEmail(email: String, password: String) async throws -> AuthUser {
    do {
      let authResult = try await auth.signIn(withEmail: email, password: password)
      return FirebaseUserDTO(user: authResult.user)
    } catch let error as NSError {
      switch error.code {
      case AuthErrorCode.wrongPassword.rawValue:
        throw AuthError.invalidCredentials
      case AuthErrorCode.networkError.rawValue:
        throw AuthError.networkError
      default:
        throw AuthError.unknown(description: error.localizedDescription)
      }
    }
  }

  func signOut() throws {
    do {
      try auth.signOut()
    } catch {
      throw AuthError.unknown(description: error.localizedDescription)
    }
  }

  func getCurrentUserIDToken() async throws -> String? {
    guard let currentUser = auth.currentUser else {
      return nil
    }

    return try await withCheckedThrowingContinuation { continuation in
      currentUser.getIDTokenForcingRefresh(true) { idToken, error in
        if let error = error {
          continuation.resume(
            throwing: AuthError.unknown(description: "获取ID Token失败: \(error.localizedDescription)"))
          return
        }

        continuation.resume(returning: idToken)
      }
    }
  }
}
