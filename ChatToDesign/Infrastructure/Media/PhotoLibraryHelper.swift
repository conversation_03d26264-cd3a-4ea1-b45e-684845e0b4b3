//
//  PhotoLibraryHelper.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import Photos
import UIKit

/// 相册操作辅助类
final class PhotoLibraryHelper {
    
    /// 请求相册权限
    func requestPermission() async -> Bool {
        let status = PHPhotoLibrary.authorizationStatus(for: .addOnly)
        
        switch status {
        case .authorized, .limited:
            return true
        case .notDetermined:
            return await PHPhotoLibrary.requestAuthorization(for: .addOnly) == .authorized
        default:
            return false
        }
    }
    
    /// 保存媒体到相册
    func saveToPhotos(data: Data, mediaType: MediaType) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges({
                if mediaType == .video {
                    // 保存视频
                    let tempURL = self.createTempFile(data: data, fileExtension: "mp4")
                    PHAssetChangeRequest.creationRequestForAssetFromVideo(atFileURL: tempURL)
                } else {
                    // 保存图片
                    if let image = UIImage(data: data) {
                        PHAssetChangeRequest.creationRequestForAsset(from: image)
                    }
                }
            }) { success, error in
                if success {
                    continuation.resume()
                } else {
                    let message = error?.localizedDescription ?? "Unknown error"
                    continuation.resume(throwing: MediaError.photoLibrarySaveFailed(message))
                }
            }
        }
    }
    
    private func createTempFile(data: Data, fileExtension: String) -> URL {
        let tempDir = FileManager.default.temporaryDirectory
        let fileName = UUID().uuidString + "." + fileExtension
        let fileURL = tempDir.appendingPathComponent(fileName)
        try? data.write(to: fileURL)
        return fileURL
    }
}