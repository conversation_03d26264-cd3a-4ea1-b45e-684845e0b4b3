//
//  VideoPreloadService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import Foundation

/// 视频预加载服务
/// 负责智能预加载即将显示的视频，提升用户体验
public actor VideoPreloadService {
  
  // MARK: - Shared Instance
  
  public static let shared = VideoPreloadService()
  
  // MARK: - Configuration
  
  private struct Configuration {
    static let maxConcurrentDownloads = 3  // 最大并发下载数
    static let preloadDistance = 5         // 预加载距离（当前可见项目前后5个）
    static let preloadDelay: TimeInterval = 0.5  // 预加载延迟（避免快速滚动时频繁预加载）
  }
  
  // MARK: - Properties
  
  private var activeDownloads: Set<URL> = []
  private var preloadQueue: [URL] = []
  private var preloadTimer: Timer?
  
  // MARK: - Initialization
  
  private init() {}
  
  // MARK: - Public Methods
  
  /// 预加载视频列表中的部分视频
  /// - Parameters:
  ///   - videoEffects: 视频效果列表
  ///   - visibleRange: 当前可见的范围
  public func preloadVideos(from videoEffects: [VideoEffect], visibleRange: Range<Int>) {
    Task {
      await schedulePreload(from: videoEffects, visibleRange: visibleRange)
    }
  }
  
  /// 预加载单个视频
  /// - Parameter url: Video URL
  public func preloadVideo(url: URL) {
    Task {
      await addToPreloadQueue(url: url)
    }
  }
  
  /// 取消所有预加载任务
  public func cancelAllPreloads() {
    activeDownloads.removeAll()
    preloadQueue.removeAll()
    preloadTimer?.invalidate()
    preloadTimer = nil
  }
  
  /// 获取预加载状态
  public func getPreloadStatus() -> (activeDownloads: Int, queuedDownloads: Int) {
    return (activeDownloads: activeDownloads.count, queuedDownloads: preloadQueue.count)
  }
  
  // MARK: - Private Methods
  
  /// 安排预加载任务
  private func schedulePreload(from videoEffects: [VideoEffect], visibleRange: Range<Int>) {
    // 取消之前的定时器
    preloadTimer?.invalidate()
    
    // 延迟执行预加载，避免快速滚动时频繁触发
    preloadTimer = Timer.scheduledTimer(withTimeInterval: Configuration.preloadDelay, repeats: false) { [weak self] _ in
      Task {
        await self?.performPreload(from: videoEffects, visibleRange: visibleRange)
      }
    }
  }
  
  /// 执行预加载
  private func performPreload(from videoEffects: [VideoEffect], visibleRange: Range<Int>) async {
    let startIndex = max(0, visibleRange.lowerBound - Configuration.preloadDistance)
    let endIndex = min(videoEffects.count, visibleRange.upperBound + Configuration.preloadDistance)
    
    // 收集需要预加载的 URL
    var urlsToPreload: [URL] = []
    
    for index in startIndex..<endIndex {
      if let url = URL(string: videoEffects[index].videoUrl) {
        // 检查是否已经缓存
        if await VideoCacheManager.shared.getCachedVideoURL(for: url) == nil {
          urlsToPreload.append(url)
        }
      }
    }
    
    // 按优先级排序（距离可见区域越近优先级越高）
    urlsToPreload.sort { url1, url2 in
      let index1 = videoEffects.firstIndex { URL(string: $0.videoUrl) == url1 } ?? 0
      let index2 = videoEffects.firstIndex { URL(string: $0.videoUrl) == url2 } ?? 0
      
      let distance1 = min(abs(index1 - visibleRange.lowerBound), abs(index1 - visibleRange.upperBound))
      let distance2 = min(abs(index2 - visibleRange.lowerBound), abs(index2 - visibleRange.upperBound))
      
      return distance1 < distance2
    }
    
    // 添加到预加载队列
    for url in urlsToPreload {
      await addToPreloadQueue(url: url)
    }
    
    // 开始处理队列
    await processPreloadQueue()
  }
  
  /// 添加到预加载队列
  private func addToPreloadQueue(url: URL) async {
    // 避免重复添加
    if !preloadQueue.contains(url) && !activeDownloads.contains(url) {
      preloadQueue.append(url)
      Logger.debug("添加到预加载队列: \(url.lastPathComponent)")
    }
  }
  
  /// 处理预加载队列
  private func processPreloadQueue() async {
    while !preloadQueue.isEmpty && activeDownloads.count < Configuration.maxConcurrentDownloads {
      let url = preloadQueue.removeFirst()
      activeDownloads.insert(url)
      
      // 异步预加载
      Task.detached {
        await self.performSinglePreload(url: url)
      }
    }
  }
  
  /// 执行单个预加载
  private func performSinglePreload(url: URL) async {
    defer {
      Task {
        await self.removeFromActiveDownloads(url: url)
        await self.processPreloadQueue()  // 继续处理队列
      }
    }
    
    do {
      Logger.debug("开始预加载视频: \(url.lastPathComponent)")
      
      // 检查是否已经缓存（可能在预加载过程中被其他地方缓存了）
      if await VideoCacheManager.shared.getCachedVideoURL(for: url) != nil {
        Logger.debug("视频已缓存，跳过预加载: \(url.lastPathComponent)")
        return
      }
      
      // 下载并缓存
      let (data, _) = try await URLSession.shared.data(from: url)
      await VideoCacheManager.shared.cacheVideo(url: url, data: data)
      
      Logger.debug("视频预加载完成: \(url.lastPathComponent)")
      
    } catch {
      Logger.error("视频预加载失败: \(url.lastPathComponent) - \(error)")
    }
  }
  
  /// 从活跃下载列表中移除
  private func removeFromActiveDownloads(url: URL) async {
    activeDownloads.remove(url)
  }
}

// MARK: - VideoEffect Array Extension

extension Array where Element == VideoEffect {
  
  /// 获取可见范围内的Video URL
  func videoURLs(in range: Range<Int>) -> [URL] {
      let safeRange = Swift.max(0, range.lowerBound)..<Swift.min(count, range.upperBound)
    return safeRange.compactMap { index in
      URL(string: self[index].videoUrl)
    }
  }
}
