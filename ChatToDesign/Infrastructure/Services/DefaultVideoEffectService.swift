//
//  DefaultVideoEffectService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import Foundation

// MARK: - Video Effect Service Error

public enum VideoEffectServiceError: Error, LocalizedError {
  case apiError(Error)
  case invalidData
  case parsingError(Error)

  public var errorDescription: String? {
    switch self {
    case .apiError(let error):
      return "Failed to fetch video effects from API: \(error.localizedDescription)"
    case .invalidData:
      return "Invalid video effects data format"
    case .parsingError(let error):
      return "Failed to parse video effects data: \(error.localizedDescription)"
    }
  }
}

// MARK: - Default Video Effect Service

public class DefaultVideoEffectService: VideoEffectService {

  // MARK: - Properties

  private let apiService: APIService

  // MARK: - Initialization

  public init(apiService: APIService) {
    self.apiService = apiService
  }

  // MARK: - VideoEffectService Implementation

  public func fetchVideoEffects() async throws -> [VideoEffect] {
    Logger.debug("Fetching video effects from API")

    do {
      let videoUseCases = try await apiService.fetchVideoUseCaseCMS()
      Logger.debug("Successfully fetched \(videoUseCases.count) video use cases from API")

      // Convert API response to VideoEffect models
      let videoEffects = videoUseCases.map { useCaseResponse in
        convertToVideoEffect(from: useCaseResponse)
      }

      Logger.debug("Successfully converted \(videoEffects.count) video effects")
      return videoEffects

    } catch {
      Logger.error("Failed to fetch video effects from API: \(error)")
      throw VideoEffectServiceError.apiError(error)
    }
  }

  public func fetchVideoEffectCategories() async throws -> [VideoEffectCategory] {
    let videoEffects = try await fetchVideoEffects()

    // Group effects by category
    let groupedEffects = Dictionary(grouping: videoEffects) { $0.category }

    // Convert to VideoEffectCategory objects and sort by category name
    let categories = groupedEffects.map { (categoryName, effects) in
      VideoEffectCategory(name: categoryName, effects: effects)
    }.sorted { $0.name < $1.name }

    Logger.debug("Successfully grouped video effects into \(categories.count) categories")
    return categories
  }

  // MARK: - Private Methods

  private func convertToVideoEffect(from response: VideoUseCaseResponse) -> VideoEffect {
    // Convert provider string to enum
    let provider = VideoEffectProvider(rawValue: response.provider) ?? .vidu

    return VideoEffect(
      id: response.id,
      name: response.name,
      videoUrl: response.videoUrl,
      posterUrl: response.coverImgUrl,
      isHot: response.isHot,
      isNew: response.isNew,
      category: response.category,
      urlType: "video",
      provider: provider,
      imageCount: response.imageCount,
      detail: response.detail,
      inputInstruction: response.inputInstruction,
      prompt: response.prompt,
      template: response.template
    )
  }
}
