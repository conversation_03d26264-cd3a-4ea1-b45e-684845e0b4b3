//
//  DefaultFeedbackService.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/20.
//

import Foundation

/// Default implementation of FeedbackService
public final class DefaultFeedbackService: FeedbackService {
    
    // MARK: - Dependencies
    
    private let apiService: APIService
    
    // MARK: - Configuration
    
    private let maxContentLength = 5000
    private let maxAttachments = 3
    
    // MARK: - Initialization
    
    /// Initialize with dependencies
    /// - Parameter apiService: API service for network requests
    public init(apiService: APIService) {
        self.apiService = apiService
    }
    
    // MARK: - FeedbackService Implementation
    
    /// Submit user feedback
    /// - Parameter feedback: The feedback to submit
    /// - Throws: FeedbackServiceError if submission fails
    public func submitFeedback(_ feedback: Feedback) async throws {
        Logger.info("Submitting feedback: \(feedback.type.rawValue) - \(feedback.subject)")
        
        // Validate feedback before submission
        try validateFeedback(feedback)
        
        do {
            // Create API request
            let request = SubmitFeedbackRequest(
                type: feedback.type.rawValue,
                subject: feedback.subject,
                content: feedback.content,
                deviceInfo: feedback.deviceInfo,
                appVersion: feedback.appVersion,
                attachments: feedback.attachments
            )
            
            // Submit to API
            let _: SubmitFeedbackResponse = try await apiService.submitFeedback(request: request)
            
            Logger.info("Feedback submitted successfully: \(feedback.id)")
            
        } catch let error as APIServiceError {
            Logger.error("Failed to submit feedback: \(error)")
            throw mapAPIError(error)
        } catch {
            Logger.error("Unknown error submitting feedback: \(error)")
            throw FeedbackServiceError.unknown(error)
        }
    }
    
    // MARK: - Private Methods
    
    /// Validate feedback before submission
    /// - Parameter feedback: Feedback to validate
    /// - Throws: FeedbackServiceError if validation fails
    private func validateFeedback(_ feedback: Feedback) throws {
        // Check subject is not empty
        if feedback.subject.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw FeedbackServiceError.invalidFeedback("Subject cannot be empty")
        }
        
        // Check content is not empty
        if feedback.content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw FeedbackServiceError.invalidFeedback("Content cannot be empty")
        }
        
        // Check content length
        if feedback.content.count > maxContentLength {
            throw FeedbackServiceError.contentTooLong(maxLength: maxContentLength)
        }
        
        // Check attachment count
        if feedback.attachments.count > maxAttachments {
            throw FeedbackServiceError.tooManyAttachments(maxCount: maxAttachments)
        }
        
        // Validate attachments
        for attachment in feedback.attachments {
            if attachment.url.isEmpty {
                throw FeedbackServiceError.invalidFeedback("Attachment URL cannot be empty")
            }
            if attachment.filename.isEmpty {
                throw FeedbackServiceError.invalidFeedback("Attachment filename cannot be empty")
            }
        }
    }
    
    /// Map API errors to FeedbackService errors
    /// - Parameter apiError: API error to map
    /// - Returns: Mapped FeedbackServiceError
    private func mapAPIError(_ apiError: APIServiceError) -> FeedbackServiceError {
        switch apiError {
        case .networkError(let error):
            return .networkError(error)
        case .responseError(let statusCode, let message):
            return .serverError(statusCode: statusCode, message: message)
        case .parsingError(let error):
            return .unknown(error)
        case .unknown(let error):
            return .unknown(error)
        }
    }
}

// MARK: - API Request/Response Models

/// Request model for submitting feedback
public struct SubmitFeedbackRequest: Codable {
    let type: String
    let subject: String
    let content: String
    let deviceInfo: DeviceInfo
    let appVersion: String
    let attachments: [FeedbackAttachment]
    
    public init(
        type: String,
        subject: String,
        content: String,
        deviceInfo: DeviceInfo,
        appVersion: String,
        attachments: [FeedbackAttachment]
    ) {
        self.type = type
        self.subject = subject
        self.content = content
        self.deviceInfo = deviceInfo
        self.appVersion = appVersion
        self.attachments = attachments
    }
}

/// Response model for feedback submission
public struct SubmitFeedbackResponse: Codable {
    let success: Bool
    let feedbackId: String
    let message: String
    
    public init(success: Bool, feedbackId: String, message: String) {
        self.success = success
        self.feedbackId = feedbackId
        self.message = message
    }
}
