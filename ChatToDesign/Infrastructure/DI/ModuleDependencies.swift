// ModuleDependencies.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Foundation

/// 模块依赖
/// 在模块之间共享的依赖
public struct ModuleDependencies {
  // 基础设施服务
  public let loggerService: LoggerService
  public let errorReportingService: ErrorReportingService
  public let configService: ConfigService
  public let analyticsService: AnalyticsService

  // 认证服务
  public let authService: AuthService

  // 仓储
  public let userRepository: UserRepository
  public let chatRepository: ChatRepository
  public let messageRepository: MessageRepository
  public let imageTaskRepository: ImageTaskRepository
  // 状态管理
  public let userStateManager: UserStateManager

  // 服务
  public let storageService: StorageService

  /// API服务
  public let apiService: APIService

  /// 网络服务
  public let networkService: NetworkService

  /// 视频效果服务
  public let videoEffectService: VideoEffectService

  /// Initialization模块依赖
  /// - Parameters:
  ///   - loggerService: 日志服务
  //    ///   - errorReportingService: 错误报告服务
  ///   - configService: 配置服务
  ///   - authService: 认证服务
  ///   - userRepository: 用户仓储
  ///   - chatRepository: 聊天仓储
  ///   - messageRepository: 消息仓储
  ///   - userStateManager: 用户状态管理器
  ///   - storageService: 存储服务
  ///   - apiService: API服务
  ///   - networkService: 网络服务
  ///   - videoEffectService: 视频效果服务
  public init(
    loggerService: LoggerService,
    errorReportingService: ErrorReportingService,
    configService: ConfigService,
    analyticsService: AnalyticsService,
    authService: AuthService,
    userRepository: UserRepository,
    chatRepository: ChatRepository,
    messageRepository: MessageRepository,
    imageTaskRepository: ImageTaskRepository,
    userStateManager: UserStateManager,
    storageService: StorageService,
    apiService: APIService,
    networkService: NetworkService,
    videoEffectService: VideoEffectService
  ) {
    self.loggerService = loggerService
    self.errorReportingService = errorReportingService
    self.configService = configService
    self.analyticsService = analyticsService
    self.authService = authService
    self.userRepository = userRepository
    self.chatRepository = chatRepository
    self.messageRepository = messageRepository
    self.userStateManager = userStateManager

    self.storageService = storageService
    self.apiService = apiService
    self.networkService = networkService
    self.imageTaskRepository = imageTaskRepository
    self.videoEffectService = videoEffectService
  }
}
