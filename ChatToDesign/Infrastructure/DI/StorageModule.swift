// StorageModule.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/20.
//

import Foundation

/// 存储模块，管理存储相关服务
public final class StorageModule {
  // MARK: - Properties

  /// 存储服务
  public let storageService: StorageService

  /// 上传服务
  public let uploadService: UploadService

  /// 文件上传API服务
  public let fileUploadService: FileUploadService

  /// 资产应用服务
  public let assetService: AssetApplicationService

  // MARK: - Initialization

  /// Initialization存储模块
  /// - Parameters:
  ///   - storageService: 存储服务Implementation
  ///   - apiService: API服务Implementation
  ///   - assetService: 资产应用服务Implementation
  public init(
    storageService: StorageService, apiService: APIService, assetService: AssetApplicationService
  ) {
    self.storageService = storageService
    self.fileUploadService = DefaultFileUploadService(apiService: apiService)
    self.assetService = assetService
    self.uploadService = DefaultUploadService(storageService: storageService)
  }
}
