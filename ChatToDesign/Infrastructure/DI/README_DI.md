# 依赖注入架构

## 概述

本项目使用模块化的依赖注入架构，将应用程序按业务功能划分为多个模块，每个模块负责特定的业务功能，并通过明确的接口暴露给其他模块使用。

## 架构设计

```
Infrastructure/DI/
  ├── AppDependencyContainer.swift  // 应用依赖容器
  └── ModuleDependencies.swift      // 模块依赖

Application/
  ├── Auth/                         // 认证模块
  │   └── DI/
  │       └── AuthModule.swift      // 认证模块依赖
  ├── UserProfile/                  // 用户资料模块
  │   └── DI/
  │       └── UserProfileModule.swift
  ├── Chat/                         // 聊天模块
  │   └── DI/
  │       └── ChatModule.swift
  └── Design/                       // 设计生成模块
      └── DI/
          └── DesignModule.swift
```

## 使用方法

### 在应用启动时初始化 (AppDelegate)

```swift
func application(
  _ application: UIApplication,
  didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
) -> Bo<PERSON> {
  // 配置 Firebase
  FirebaseApp.configure()

  // 初始化依赖容器
  let container = AppDependencyContainer.shared

  // 使用依赖
  Task {
    do {
      try await container.configService.fetchConfig()
    } catch {
      Logger.error("Failed to fetch remote config: \(error)")
    }
  }

  // 其他初始化...

  return true
}
```

### 在 ViewModel 中使用

```swift
final class AuthViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var errorMessage: String?

    // 依赖
    private let authService: AuthApplicationService

    // 使用依赖注入
    init(authService: AuthApplicationService = AppDependencyContainer.shared.authModule.authApplicationService) {
        self.authService = authService

        // 使用服务...
    }

    func signIn(email: String, password: String) {
        Task {
            do {
                try await authService.signIn(email: email, password: password)
            } catch {
                // 处理错误...
            }
        }
    }
}
```

### 在 SwiftUI View 中使用

```swift
struct ChatView: View {
    @StateObject private var viewModel = ChatViewModel()

    // 这里不需要知道依赖如何创建，由 ViewModel 负责

    var body: some View {
        // 视图实现...
    }
}

// ViewModel 中使用依赖注入
final class ChatViewModel: ObservableObject {
    // 直接使用模块服务
    private let chatService: ChatService

    init(chatService: ChatService = AppDependencyContainer.shared.chatModule.chatService) {
        self.chatService = chatService
    }

    // 实现方法...
}
```

## 测试支持

这种架构设计非常适合单元测试，因为可以轻松替换任何依赖：

```swift
// 测试代码
func testChatViewModel() {
    // 创建模拟依赖
    let mockChatService = MockChatService()

    // 创建使用模拟依赖的 ViewModel
    let viewModel = ChatViewModel(chatService: mockChatService)

    // 测试逻辑...
}
```

## 优势

1. **按业务划分模块**：依据实际业务功能组织代码，而不是技术层次
2. **清晰的依赖关系**：每个模块明确声明其依赖，便于理解和维护
3. **良好的测试性**：可以轻松替换各模块的依赖进行测试
4. **可扩展性**：新增功能只需添加新模块，不影响现有代码
5. **封装内部实现**：每个模块只暴露必要的服务，隐藏内部实现细节
