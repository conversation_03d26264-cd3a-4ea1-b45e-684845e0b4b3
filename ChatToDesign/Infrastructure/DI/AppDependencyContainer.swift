// AppDependencyContainer.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import FirebaseFirestore
import Foundation
import Moya

/// 应用依赖容器
/// 负责创建和管理所有模块的依赖
public final class AppDependencyContainer {
  // MARK: - 单例

  /// 共享实例
  public static let shared = AppDependencyContainer()

  // MARK: - 核心基础设施依赖

  /// 日志服务
  public let loggerService: LoggerService

  /// 错误报告服务
  public let errorReportingService: ErrorReportingService

  /// 配置服务
  public let configService: ConfigService

  /// 存储服务
  public let storageService: StorageService

  /// 网络服务
  public let networkService: NetworkService

  /// 查询管理器（SWR）
  public let queryManager: QueryManager

  /// 分析服务
  public let analyticsService: AnalyticsService

  // MARK: - 认证服务

  /// 认证服务
  public let authService: AuthService

  // MARK: - 仓储

  /// 用户仓储
  public let userRepository: UserRepository

  /// 消息仓储
  public let messageRepository: MessageRepository

  /// 聊天仓储
  public let chatRepository: ChatRepository

  /// 图片任务仓储
  public let imageTaskRepository: ImageTaskRepository

  /// 资产仓储
  public let assetRepository: AssetRepository

  // MARK: - 共享状态

  /// 用户状态管理器
  private let userStateManager: UserStateManager

  // MARK: - 服务

  /// API服务
  public let apiService: APIService

  /// 视频效果服务
  public let videoEffectService: VideoEffectService

  // MARK: - 模块依赖

  /// 模块依赖
  private lazy var moduleDependencies: ModuleDependencies = {
    return ModuleDependencies(
      loggerService: loggerService,
      errorReportingService: errorReportingService,
      configService: configService,
      analyticsService: analyticsService,
      authService: authService,
      userRepository: userRepository,
      chatRepository: chatRepository,
      messageRepository: messageRepository,
      imageTaskRepository: imageTaskRepository,
      userStateManager: userStateManager,
      storageService: storageService,
      apiService: apiService,
      networkService: networkService,
      videoEffectService: videoEffectService
    )
  }()

  // MARK: - 功能模块

  /// 认证模块
  public lazy var authModule: AuthModule = {
    return AuthModule(dependencies: moduleDependencies)
  }()

  /// 用户模块
  public lazy var userModule: UserModule = {
    return UserModule(dependencies: moduleDependencies)
  }()

  /// 聊天模块
  public lazy var chatModule: ChatModule = {
    return ChatModule(dependencies: moduleDependencies)
  }()

  /// 存储模块
  public lazy var storageModule: StorageModule = {
    return StorageModule(
      storageService: storageService, apiService: apiService, assetService: assetModule.assetService
    )
  }()

  /// 设计模块
  public lazy var designModule: DesignModule = {
    return DesignModule(dependencies: moduleDependencies)
  }()

  /// 视频模块
  public lazy var videoModule: VideoModule = {
    return VideoModule(dependencies: moduleDependencies)
  }()

  /// 订阅模块
  public lazy var subscriptionModule: SubscriptionModule = {
    let subscriptionDependencies = SubscriptionModuleDependencies(
      configService: configService,
      userService: userModule.userService,
      analyticsService: analyticsService
    )
    return SubscriptionModule(dependencies: subscriptionDependencies)
  }()

  /// 资产模块
  public lazy var assetModule: AssetModule = {
    let assetDependencies = AssetModuleDependencies(
      assetRepository: assetRepository,
      apiService: apiService,
      userStateManager: userStateManager,
      uploadConfiguration: .default,
      loggerService: loggerService,
      analyticsService: analyticsService
    )
    return AssetModule(dependencies: assetDependencies)
  }()

  /// 媒体模块
  public lazy var mediaModule: MediaModule = {
    return MediaModule(dependencies: moduleDependencies)
  }()

  /// 支持模块
  public lazy var supportModule: SupportModule = {
    return SupportModule(dependencies: moduleDependencies)
  }()

  // MARK: - Initialization

  private init() {
    // 创建基础设施服务
    self.loggerService = DefaultLoggerAdapter()
    self.errorReportingService = SentryAdapter(
      dsn:
        "https://<EMAIL>/4508976028581888"
    )
    self.configService = FirebaseConfigAdapter()
    self.storageService = FirebaseStorageAdapter()
    self.authService = FirebaseAuthAdapter()
    self.networkService = DefaultNetworkService()

    // 创建分析服务
    self.analyticsService = FirebaseAnalyticsAdapter()

    // 创建查询管理器（需要在主线程上创建）
    self.queryManager = QueryManager.shared  // 临时使用 CacheManager，稍后修复

    // 创建仓储
    let repositoryFactory = FirebaseRepositoryFactory.shared
    self.userRepository = repositoryFactory.makeUserRepository()
    self.chatRepository = repositoryFactory.makeChatRepository()
    self.messageRepository = repositoryFactory.makeMessageRepository()
    self.imageTaskRepository = FirestoreImageTaskRepository()
    self.assetRepository = repositoryFactory.makeAssetRepository()

    // 创建共享状态
    self.userStateManager = UserStateManager.shared

    // 创建服务
    // 创建Firebase token提供者
    let tokenProvider = FirebaseTokenProvider(authService: self.authService)

    // 创建AccessTokenPlugin
    let accessTokenPlugin = AccessTokenPlugin { _ in
      return tokenProvider.getToken()
    }

    // 创建带认证插件的MoyaProvider
    let provider = MoyaProvider<APIEndpoint>(plugins: [accessTokenPlugin])

    self.apiService = DefaultAPIService(provider: provider)

    // 创建视频效果服务
    self.videoEffectService = DefaultVideoEffectService(apiService: self.apiService)

    // Initialization服务
    setupServices()
  }

  private func setupServices() {
    // Initialization日志服务
    Logger.service = loggerService

    // Initialization错误报告服务
    //        errorReportingService.setup()
  }
}
