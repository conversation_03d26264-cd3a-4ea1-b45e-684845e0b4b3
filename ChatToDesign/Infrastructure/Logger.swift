// Logger.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/15.
//

import Foundation

/// Global logger accessor
enum Logger {
  /// Default logger service
  static var service: LoggerService = DefaultLoggerAdapter()

  /// Log debug level message
  static func debug(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    let context = LogContext(file: file, function: function, line: line)
    service.debug(message, context: context)
  }

  /// Log info level message
  static func info(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    let context = LogContext(file: file, function: function, line: line)
    service.info(message, context: context)
  }

  /// Log warning level message
  static func warning(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    let context = LogContext(file: file, function: function, line: line)
    service.warning(message, context: context)
  }

  /// Log error level message
  static func error(
    _ message: String, file: String = #file, function: String = #function, line: Int = #line
  ) {
    let context = LogContext(file: file, function: function, line: line)
    service.error(message, context: context)
  }

  /// Log custom level message
  static func log(
    _ message: String, level: LogLevel, file: String = #file, function: String = #function,
    line: Int = #line
  ) {
    let context = LogContext(file: file, function: function, line: line)
    service.log(message, level: level, context: context)
  }
}
