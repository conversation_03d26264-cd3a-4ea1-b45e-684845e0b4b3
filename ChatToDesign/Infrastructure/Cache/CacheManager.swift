//
//  CacheManager.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/20.
//

import CommonCrypto
import Foundation
import UIKit

// MARK: - Cache Configuration

/// Cache configuration
public struct CacheConfiguration {
  /// Memory cache size limit (bytes)
  public let maxMemoryCacheSize: Int
  /// Cache maximum retention time (seconds)
  public let maxCacheAge: TimeInterval
  /// Memory cache item count limit
  public let memoryCacheCountLimit: Int
  /// Cache namespace
  public let namespace: String

  /// Default configuration
  public static let `default` = CacheConfiguration(
    maxMemoryCacheSize: 100 * 1024 * 1024,  // 100MB
    maxCacheAge: 24 * 60 * 60,  // 1 day
    memoryCacheCountLimit: 2000,
    namespace: "default"
  )

  public init(
    maxMemoryCacheSize: Int,
    maxCacheAge: TimeInterval,
    memoryCacheCountLimit: Int,
    namespace: String
  ) {
    self.maxMemoryCacheSize = maxMemoryCacheSize
    self.maxCacheAge = maxCacheAge
    self.memoryCacheCountLimit = memoryCacheCountLimit
    self.namespace = namespace
  }
}

// MARK: - Cache Key

/// Cache key, supports namespace
public struct CacheKey {
  public let namespace: String
  public let key: String

  /// Complete cache key
  public var fullKey: String {
    return "\(namespace):\(key)"
  }

  public init(namespace: String, key: String) {
    self.namespace = namespace
    self.key = key
  }
}

// MARK: - Cache Item

/// Cache item
public struct CacheItem<T: Codable>: Codable {
  public let value: T
  public let timestamp: Date
  public let key: String

  /// Check if data is expired
  public func isStale(maxAge: TimeInterval) -> Bool {
    Date().timeIntervalSince(timestamp) > maxAge
  }

  /// Check if data is still valid (for stale-while-revalidate)
  public func isValid(staleTime: TimeInterval) -> Bool {
    Date().timeIntervalSince(timestamp) <= staleTime
  }
}

/// Cache manager
/// Responsible for memory cache management
public actor CacheManager {

  // MARK: - Shared Instances

  /// Default shared instance
  public static let shared = CacheManager(configuration: .default)

  // MARK: - Properties

  private let memoryCache = NSCache<NSString, CacheWrapper>()
  private let configuration: CacheConfiguration

  // MARK: - Initialization

  /// Use specified configurationInitialization
  public init(configuration: CacheConfiguration) {
    self.configuration = configuration

    // Set memory cache limit
    memoryCache.totalCostLimit = configuration.maxMemoryCacheSize
    memoryCache.countLimit = configuration.memoryCacheCountLimit

    Task { await setupCacheCleanup() }
  }

  // MARK: - Public Methods

  /// Check if cache exists
  /// - Parameters:
  ///   - key: Cache key
  ///   - type: Data type
  /// - Returns: Returns true if cache exists, otherwise returns false
  public func hasCache<T: Codable>(for key: String, type: T.Type) async -> Bool {
    let cacheKey = NSString(string: key)

    // Check memory cache
    if let wrapper = memoryCache.object(forKey: cacheKey),
      wrapper.value is CacheItem<T>
    {
      return true
    }

    return false
  }

  /// Get cached data
  /// - Parameters:
  ///   - key: Cache key
  ///   - type: Data type
  /// - Returns: Cache item, returns nil if not exists
  public func getCache<T: Codable>(for key: String, type: T.Type) async -> CacheItem<T>? {
    let cacheKey = NSString(string: key)

    // Check memory cache
    if let wrapper = memoryCache.object(forKey: cacheKey),
      let cacheItem = wrapper.value as? CacheItem<T>
    {
      Logger.debug("Get data from memory cache: \(key)")
      return cacheItem
    }

    Logger.debug("Cache miss: \(key)")
    return nil
  }

  /// Set cached data
  /// - Parameters:
  ///   - key: Cache key
  ///   - value: Data to be cached
  public func setCache<T: Codable>(for key: String, value: T) async {
    let cacheItem = CacheItem(value: value, timestamp: Date(), key: key)
    let cacheKey = NSString(string: key)

    // Save to memory cache
    let wrapper = CacheWrapper(value: cacheItem)
    memoryCache.setObject(wrapper, forKey: cacheKey)

    Logger.debug("Cache data: \(key)")
  }

  /// Remove specified cache
  /// - Parameter key: Cache key
  public func removeCache(for key: String) async {
    let cacheKey = NSString(string: key)

    // Remove from memory cache
    memoryCache.removeObject(forKey: cacheKey)

    Logger.debug("Remove cache: \(key)")
  }

  /// Clear all cache
  public func clearAllCache() async {
    // Clear memory cache
    memoryCache.removeAllObjects()

    Logger.debug("Clear all cache")
  }

  /// Get cache size information
  public func getCacheSize() async -> Int {
    return memoryCache.totalCostLimit
  }

  /// Clear memory cache
  private func clearMemoryCache() {
    memoryCache.removeAllObjects()
  }
}

// MARK: - Private Methods

extension CacheManager {

  private func setupCacheCleanup() {
    // Listen for memory warnings
    NotificationCenter.default.addObserver(
      forName: UIApplication.didReceiveMemoryWarningNotification,
      object: nil,
      queue: .main
    ) { [weak self] _ in
      Task {
        await self?.clearMemoryCache()
        Logger.debug("Received memory warning, clear memory cache")
      }
    }
  }
}

// MARK: - Cache Wrapper

/// Cache wrapper for NSCache
private class CacheWrapper {
  let value: Any

  init(value: Any) {
    self.value = value
  }
}

// MARK: - Cache Factory

// MARK: - Video Cache Manager

/// Manager specifically for video caching
/// Combines memory cache and file system cache, supports LRU strategy
public actor VideoCacheManager {

  // MARK: - Shared Instance

  public static let shared = VideoCacheManager()

  // MARK: - Configuration

  private struct Configuration {
    static let maxMemoryCacheSize = 50 * 1024 * 1024  // 50MB memory cache
    static let maxDiskCacheSize = 150 * 1024 * 1024  // 150MB disk cache
    static let maxCacheAge: TimeInterval = 7 * 24 * 60 * 60  // 7 days
    static let cacheDirectoryName = "VideoCache"
  }

  // MARK: - Properties

  private let memoryCache = NSCache<NSString, VideoCacheItem>()
  private let fileManager = FileManager.default
  private var accessTimes: [String: Date] = [:]  // LRU tracking
  private let cacheDirectory: URL

  // MARK: - Cache Item

  private class VideoCacheItem: NSObject {
    let url: URL
    let data: Data?
    let createdAt: Date
    let fileSize: Int

    init(url: URL, data: Data? = nil, createdAt: Date = Date(), fileSize: Int = 0) {
      self.url = url
      self.data = data
      self.createdAt = createdAt
      self.fileSize = fileSize
      super.init()
    }
  }

  // MARK: - Initialization

  private init() {
    // Set memory cache limit
    memoryCache.totalCostLimit = Configuration.maxMemoryCacheSize
    memoryCache.countLimit = 50  // Cache up to 50 videos in memory

    // Create cache directory
    let cacheDir = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
    cacheDirectory = cacheDir.appendingPathComponent(
      Configuration.cacheDirectoryName, isDirectory: true)

    // Ensure cache directory exists
    try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)

    // 启动时Clean expired cache
    Task {
      await cleanupExpiredCache()
    }
  }

  // MARK: - Public Methods

  /// Get video cache
  /// - Parameter url: Video URL
  /// - Returns: Cached local file URL, returns nil if not exists
  public func getCachedVideoURL(for url: URL) async -> URL? {
    let cacheKey = cacheKeyForURL(url)

    // Update access time (LRU)
    accessTimes[cacheKey] = Date()

    // 1. Check memory cache
    if let memoryItem = memoryCache.object(forKey: NSString(string: cacheKey)) {
      Logger.debug("Video memory cache hit: \(url.lastPathComponent)")
      return memoryItem.url
    }

    // 2. Check disk cache
    let diskURL = diskCacheURL(for: cacheKey)
    if fileManager.fileExists(atPath: diskURL.path) {
      Logger.debug("Video disk cache hit: \(url.lastPathComponent)")

      // Load disk cache item to memory
      let cacheItem = VideoCacheItem(url: diskURL)
      memoryCache.setObject(cacheItem, forKey: NSString(string: cacheKey))

      return diskURL
    }

    Logger.debug("Video cache miss: \(url.lastPathComponent)")
    return nil
  }

  /// Cache video
  /// - Parameters:
  ///   - url: 原始Video URL
  ///   - data: Video data
  /// - Returns: Cached local file URL
  @discardableResult
  public func cacheVideo(url: URL, data: Data) async -> URL? {
    let cacheKey = cacheKeyForURL(url)
    let diskURL = diskCacheURL(for: cacheKey)

    do {
      // Write to disk
      try data.write(to: diskURL)
      Logger.debug("Video cached to disk: \(url.lastPathComponent) (\(data.count) bytes)")

      // Add to memory cache
      let cacheItem = VideoCacheItem(url: diskURL, data: nil, fileSize: data.count)
      memoryCache.setObject(cacheItem, forKey: NSString(string: cacheKey), cost: data.count)

      // Update access time
      accessTimes[cacheKey] = Date()

      // Check cache size limit
      await cleanupIfNeeded()

      return diskURL
    } catch {
      Logger.error("Video cache failed: \(error)")
      return nil
    }
  }

  /// Preload video (non-blocking)
  /// - Parameter url: Video URL
  public func preloadVideo(url: URL) async {
    // If already cached, return directly
    if await getCachedVideoURL(for: url) != nil {
      return
    }

    // Background download
    Task.detached {
      await self.downloadAndCacheVideo(url: url)
    }
  }

  /// Clear all cache
  public func clearAllCache() async {
    // Clear memory cache
    memoryCache.removeAllObjects()

    // Clear disk cache
    try? fileManager.removeItem(at: cacheDirectory)
    try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)

    // Clear access time records
    accessTimes.removeAll()

    Logger.debug("Cleared all video cache")
  }

  /// Get cache statistics
  public func getCacheStats() async -> (memoryCount: Int, diskSize: Int, totalFiles: Int) {
    let memoryCount = memoryCache.totalCostLimit

    var diskSize = 0
    var totalFiles = 0

    if let enumerator = fileManager.enumerator(
      at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey])
    {
      for case let fileURL as URL in enumerator {
        if let resourceValues = try? fileURL.resourceValues(forKeys: [.fileSizeKey]),
          let fileSize = resourceValues.fileSize
        {
          diskSize += fileSize
          totalFiles += 1
        }
      }
    }

    return (memoryCount: memoryCount, diskSize: diskSize, totalFiles: totalFiles)
  }

  // MARK: - Private Methods

  /// Generate cache key
  private func cacheKeyForURL(_ url: URL) -> String {
    return url.absoluteString.sha256
  }

  /// Get disk cache file URL
  private func diskCacheURL(for cacheKey: String) -> URL {
    return cacheDirectory.appendingPathComponent("\(cacheKey).mp4")
  }

  /// Download and cache video
  private func downloadAndCacheVideo(url: URL) async {
    do {
      let (data, _) = try await URLSession.shared.data(from: url)
      await cacheVideo(url: url, data: data)
    } catch {
      Logger.error("Video download failed: \(url) - \(error)")
    }
  }

  /// Clean expired cache
  private func cleanupExpiredCache() async {
    let now = Date()
    let maxAge = Configuration.maxCacheAge

    guard
      let enumerator = fileManager.enumerator(
        at: cacheDirectory,
        includingPropertiesForKeys: [.creationDateKey, .fileSizeKey]
      )
    else { return }

    for case let fileURL as URL in enumerator {
      do {
        let resourceValues = try fileURL.resourceValues(forKeys: [.creationDateKey])
        if let creationDate = resourceValues.creationDate,
          now.timeIntervalSince(creationDate) > maxAge
        {
          try fileManager.removeItem(at: fileURL)
          Logger.debug("Delete expired cache file: \(fileURL.lastPathComponent)")
        }
      } catch {
        Logger.error("Failed to clean cache file: \(error)")
      }
    }
  }

  /// Clean cache as needed (LRU strategy)
  private func cleanupIfNeeded() async {
    let stats = await getCacheStats()

    // If disk cache exceeds limit, use LRU strategy to clean
    if stats.diskSize > Configuration.maxDiskCacheSize {
      await performLRUCleanup()
    }
  }

  /// Execute LRU cleanup
  private func performLRUCleanup() async {
    // Get all cache files and their access times
    var fileAccessTimes: [(URL, Date)] = []

    guard
      let enumerator = fileManager.enumerator(at: cacheDirectory, includingPropertiesForKeys: [])
    else { return }

    for case let fileURL as URL in enumerator {
      let fileName = fileURL.deletingPathExtension().lastPathComponent
      let accessTime = accessTimes[fileName] ?? Date.distantPast
      fileAccessTimes.append((fileURL, accessTime))
    }

    // Sort by access time (least recently accessed first)
    fileAccessTimes.sort { $0.1 < $1.1 }

    // Delete least recently accessed files until cache size is within limit
    var currentSize = (await getCacheStats()).diskSize
    let targetSize = Int(Double(Configuration.maxDiskCacheSize) * 0.8)  // Clean to 80%

    for (fileURL, _) in fileAccessTimes {
      if currentSize <= targetSize { break }

      do {
        let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
        let fileSize = resourceValues.fileSize ?? 0

        try fileManager.removeItem(at: fileURL)
        currentSize -= fileSize

        // Also remove from memory cache
        let fileName = fileURL.deletingPathExtension().lastPathComponent
        memoryCache.removeObject(forKey: NSString(string: fileName))
        accessTimes.removeValue(forKey: fileName)

        Logger.debug("LRU cleanup delete file: \(fileURL.lastPathComponent)")
      } catch {
        Logger.error("LRU cleanup failed: \(error)")
      }
    }
  }
}

// MARK: - String Extension for SHA256

extension String {
  fileprivate var sha256: String {
    let data = Data(self.utf8)
    let hash = data.withUnsafeBytes { bytes in
      var hash = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
      CC_SHA256(bytes.bindMemory(to: UInt8.self).baseAddress, CC_LONG(data.count), &hash)
      return hash
    }
    return hash.map { String(format: "%02x", $0) }.joined()
  }
}

/// Cache factory for creating cache managers and query managers with different configurations
public class CacheFactory {

  // MARK: - CacheManager Factory Methods

  /// Get default cache manager
  public static var defaultCache: CacheManager {
    return CacheManager.shared
  }

  /// Create cache manager with custom configuration
  public static func createCache(configuration: CacheConfiguration) -> CacheManager {
    return CacheManager(configuration: configuration)
  }

  /// Create cache manager for testing
  public static func createTestCache() -> CacheManager {
    let testConfig = CacheConfiguration(
      maxMemoryCacheSize: 10 * 1024 * 1024,  // 10MB
      maxCacheAge: 60,  // 1 minute
      memoryCacheCountLimit: 100,
      namespace: "test_\(UUID().uuidString)"
    )
    return CacheManager(configuration: testConfig)
  }

  // MARK: - QueryManager Factory Methods

  /// Get default query manager
  @MainActor
  public static var defaultQueryManager: QueryManager {
    return QueryManager.shared
  }

  /// Create query manager using memory-only cache
  @MainActor
  public static func createMemoryOnlyQueryManager() -> QueryManager {
    return QueryManager(cacheManager: defaultCache)
  }

  /// Create query manager using custom cache
  @MainActor
  public static func createQueryManager(cacheManager: CacheManager) -> QueryManager {
    return QueryManager(cacheManager: cacheManager)
  }

  /// Create query manager for testing
  @MainActor
  public static func createTestQueryManager() -> QueryManager {
    let testCache = createTestCache()
    return QueryManager(cacheManager: testCache)
  }
}
