//
//  SWRHook.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/20.
//

import Combine
import Foundation

// MARK: - SWR Hook

/// SWR Hook, provides React SWR-like usage experience
/// Used to simplify SWR usage in ViewModel
@MainActor
public class SWRHook<T: Codable>: ObservableObject {

  // MARK: - Published Properties

  /// Current data
  @Published public private(set) var data: T?

  /// Whether loading (initial load)
  @Published public private(set) var isLoading: Bool = false

  /// Whether refreshing (background refresh)
  @Published public private(set) var isRefreshing: Bool = false

  /// Error message
  @Published public private(set) var error: Error?

  /// Query state
  @Published public private(set) var queryState: QueryState = .idle

  // MARK: - Private Properties

  private let queryManager: QueryManager
  private var cancellables = Set<AnyCancellable>()
  private var queryCancellables = Set<AnyCancellable>()
  private let key: String
  private let maxAge: TimeInterval
  private let staleTime: TimeInterval
  private let networkCall: () async throws -> T

  // MARK: - Initialization

  /// Initialization SWR Hook
  /// - Parameters:
  ///   - key: Query key
  ///   - maxAge: 数据最大有效期（秒）
  ///   - staleTime: 数据过期时间（秒）
  ///   - networkCall: Network request closure
  ///   - queryManager: 查询管理器
  public init(
    key: String,
    maxAge: TimeInterval = 300,
    staleTime: TimeInterval = 3600,
    networkCall: @escaping () async throws -> T,
    queryManager: QueryManager? = nil
  ) {
    self.key = key
    self.maxAge = maxAge
    self.staleTime = staleTime
    self.networkCall = networkCall
    self.queryManager = queryManager ?? QueryManager.shared

    setupInvalidationListener()
  }

  // MARK: - Public Methods

  /// Start query
  public func fetch() {
    // 取消之前的查询请求（但保留失效监听器）
    queryCancellables.removeAll()

    // 设置初始加载状态
    if data == nil {
      isLoading = true
      queryState = .loading
    } else {
      isRefreshing = true
      queryState = .refreshing
    }

    Logger.debug("SWR Hook Start query: \(key), isLoading: \(isLoading)")

    // Start query
    queryManager
      .query(
        key: key,
        maxAge: maxAge,
        staleTime: staleTime,
        networkCall: networkCall
      )
      .receive(on: DispatchQueue.main)
      .sink { [weak self] result in
        self?.handleQueryResult(result)
      }
      .store(in: &queryCancellables)
  }

  /// Manually refresh data
  public func refresh() {
    Task {
      await queryManager.invalidateQueries(keys: [key])
      fetch()
    }
  }

  /// Update local data (optimistic update)
  /// - Parameter newData: New data
  public func mutate(_ newData: T) {
    data = newData
    error = nil
    queryState = .success
  }

  /// Reset state
  public func reset() {
    data = nil
    error = nil
    isLoading = false
    isRefreshing = false
    queryState = .idle
    queryCancellables.removeAll()
    cancellables.removeAll()
  }

  // MARK: - Private Methods

  private func handleQueryResult(_ result: QueryResult<T>) {
    switch result {
    case .cached(let value):
      data = value
      error = nil
      isLoading = false
      isRefreshing = false
      queryState = .success
      Logger.debug("SWR Hook 收到缓存数据: \(key), isLoading: \(isLoading)")

    case .stale(let value):
      data = value
      error = nil
      isLoading = false
      isRefreshing = true
      queryState = .refreshing
      Logger.debug("SWR Hook 收到过期数据，正在刷新: \(key), isLoading: \(isLoading)")

    case .fresh(let value):
      data = value
      error = nil
      isLoading = false
      isRefreshing = false
      queryState = .success


    case .error(let err):
      error = err
      isLoading = false
      isRefreshing = false
      queryState = .error
      Logger.error("SWR Hook 查询错误: \(key), 错误: \(err.localizedDescription)")
    }
  }

  private func setupInvalidationListener() {
    NotificationCenter.default
      .publisher(for: .queriesInvalidated)
      .compactMap { $0.object as? [String] }
      .filter { [weak self] keys in
        guard let self = self else { return false }
        let shouldInvalidate = keys.contains(self.key)
        if shouldInvalidate {
          Logger.debug("SWR Hook 收到失效通知: \(self.key), 失效键列表: \(keys)")
        }
        return shouldInvalidate
      }
      .receive(on: DispatchQueue.main)
      .sink { [weak self] invalidatedKeys in
        guard let self = self else { return }
        Logger.debug("SWR Hook 开始重新获取数据: \(self.key)")
        self.fetch()
      }
      .store(in: &cancellables)
  }
}

// MARK: - SWR Mutation Hook

/// SWR Mutation Hook for handling mutation operations
@MainActor
public class SWRMutationHook<T: Codable>: ObservableObject {

  // MARK: - Published Properties

  /// Mutation result data
  @Published public private(set) var data: T?

  /// Whether mutating
  @Published public private(set) var isMutating: Bool = false

  /// Mutation error
  @Published public private(set) var error: Error?

  // MARK: - Private Properties

  private let queryManager: QueryManager
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Initialization

  public init(queryManager: QueryManager? = nil) {
    self.queryManager = queryManager ?? QueryManager.shared
  }

  // MARK: - Public Methods

  /// Execute mutation operation
  /// - Parameters:
  ///   - key: Mutation key
  ///   - invalidates: List of cache keys to invalidate
  ///   - networkCall: Network request closure
  public func mutate(
    key: String,
    invalidates: [String] = [],
    networkCall: @escaping () async throws -> T
  ) {
    isMutating = true
    error = nil

    queryManager
      .mutate(
        key: key,
        invalidates: invalidates,
        networkCall: networkCall
      )
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          self?.isMutating = false
          if case .failure(let error) = completion {
            self?.error = error
            Logger.error("SWR Mutation 错误: \(key), 错误: \(error.localizedDescription)")
          }
        },
        receiveValue: { [weak self] result in
          self?.data = result
          self?.error = nil
          Logger.debug("SWR Mutation 成功: \(key)")
        }
      )
      .store(in: &cancellables)
  }

  /// Reset state
  public func reset() {
    data = nil
    error = nil
    isMutating = false
    cancellables.removeAll()
  }
}

// MARK: - Convenience Extensions

extension SWRHook {

  /// 便捷的Initialization方法，自动Start query
  /// - Parameters:
  ///   - key: Query key
  ///   - maxAge: 数据最大有效期（秒）
  ///   - staleTime: 数据过期时间（秒）
  ///   - networkCall: Network request closure
  ///   - autoFetch: 是否自动Start query
  /// - Returns: SWR Hook instance
  public static func create(
    key: String,
    maxAge: TimeInterval = 300,
    staleTime: TimeInterval = 3600,
    networkCall: @escaping () async throws -> T,
    autoFetch: Bool = true
  ) -> SWRHook<T> {
    let hook = SWRHook(
      key: key,
      maxAge: maxAge,
      staleTime: staleTime,
      networkCall: networkCall
    )

    if autoFetch {
      hook.fetch()
    }

    return hook
  }
}

// MARK: - Query State Helpers

extension QueryState {

  /// Whether in loading state
  public var isLoading: Bool {
    switch self {
    case .loading, .refreshing:
      return true
    default:
      return false
    }
  }

  /// Whether in success state
  public var isSuccess: Bool {
    return self == .success
  }

  /// 是否Error state
  public var isError: Bool {
    return self == .error
  }
}
