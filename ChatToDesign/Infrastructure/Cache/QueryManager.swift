//
//  QueryManager.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/20.
//

import Combine
import Foundation

// MARK: - Query Result Types

/// Query result type
public enum QueryResult<T> {
  /// Data from cache (fresh)
  case cached(T)
  /// Data from cache (stale, refreshing in background)
  case stale(T)
  /// New data from network
  case fresh(T)
  /// Error state
  case error(Error)
}

/// Query state
public enum QueryState {
  case idle
  case loading
  case refreshing
  case success
  case error
}

// MARK: - Query Manager

/// SWR query manager
/// Implements stale-while-revalidate pattern, supports caching, request deduplication, background refresh
@MainActor
public class QueryManager: ObservableObject {

  // MARK: - Singleton

  public static let shared = QueryManager(cacheManager: CacheManager.shared)

  // MARK: - Properties

  private let cacheManager: CacheManager
  private var runningQueries: Set<String> = []
  private var querySubjects: [String: Any] = [:]

  // MARK: - Initialization

  /// 使用指定Cache managerInitialization
  public init(cacheManager: CacheManager) {
    self.cacheManager = cacheManager
  }

  // MARK: - Public Methods

  /// Execute query, supports stale-while-revalidate pattern
  /// - Parameters:
  ///   - key: Query key
  ///   - maxAge: Maximum data validity period (seconds), background refresh will be triggered after this time
  ///   - staleTime: Data expiration time (seconds), data is considered completely invalid after this time
  ///   - networkCall: Network request closure
  /// - Returns: Publisher of query results
  public func query<T: Codable>(
    key: String,
    maxAge: TimeInterval = 300,  // 5 minutes
    staleTime: TimeInterval = 3600,  // 1 hour
    networkCall: @escaping () async throws -> T
  ) -> AnyPublisher<QueryResult<T>, Never> {

    // Create type-safe key
    let typedKey = "\(key):\(String(describing: T.self))"

    // Simplified request deduplication：如果查询正在进行，直接创建新的查询
    // 这样避免了复杂的类型转换问题，虽然可能有少量重复请求，但保证了类型安全
    let subject = PassthroughSubject<QueryResult<T>, Never>()
    let publisher = subject.eraseToAnyPublisher()

    // Register running query
    runningQueries.insert(typedKey)
    querySubjects[typedKey] = subject

    Task {
      await handleQuery(
        key: key,
        maxAge: maxAge,
        staleTime: staleTime,
        networkCall: networkCall,
        subject: subject,
        typedKey: typedKey
      )
    }

    return publisher
  }

  /// Execute mutation operation, supports cache invalidation
  /// - Parameters:
  ///   - key: Mutation key
  ///   - invalidates: List of cache keys to invalidate
  ///   - networkCall: Network request closure
  /// - Returns: Publisher of mutation results
  public func mutate<T: Codable>(
    key: String,
    invalidates: [String] = [],
    networkCall: @escaping () async throws -> T
  ) -> AnyPublisher<T, Error> {

    return Future { [weak self] promise in
      Task {
        do {
          let result = try await networkCall()

          // Invalidate related cache
          await self?.invalidateQueries(keys: invalidates)

          promise(.success(result))
        } catch {
          promise(.failure(error))
        }
      }
    }
    .eraseToAnyPublisher()
  }

  /// Check if cache exists
  /// - Parameters:
  ///   - key: Query key
  ///   - type: Data type
  /// - Returns: Returns true if cache exists, otherwise returns false
  public func hasCache<T: Codable>(for key: String, type: T.Type) async -> Bool {
    return await cacheManager.hasCache(for: key, type: type)
  }

  /// Manually invalidate query cache
  /// - Parameter keys: List of cache keys to invalidate
  public func invalidateQueries(keys: [String]) async {
    Logger.debug("Start invalidating query cache, key list: \(keys)")

    for key in keys {
      await cacheManager.removeCache(for: key)
      Logger.debug("Invalidate cache: \(key)")
    }

    // Notify related queries to refetch data
    Logger.debug("Send cache invalidation notification, key list: \(keys)")
    NotificationCenter.default.post(
      name: .queriesInvalidated,
      object: keys
    )

    Logger.debug("Cache invalidation completed, key list: \(keys)")
  }

  /// Clear all cache
  public func clearAllCache() async {
    await cacheManager.clearAllCache()
    querySubjects.removeAll()
    runningQueries.removeAll()
    Logger.debug("Clear all query cache")
  }
}

// MARK: - Private Methods

extension QueryManager {

  private func handleQuery<T: Codable>(
    key: String,
    maxAge: TimeInterval,
    staleTime: TimeInterval,
    networkCall: @escaping () async throws -> T,
    subject: PassthroughSubject<QueryResult<T>, Never>,
    typedKey: String
  ) async {

    // 1. Check cache
    if let cached = await cacheManager.getCache(for: key, type: T.self) {
      if cached.isStale(maxAge: maxAge) {
        if cached.isValid(staleTime: staleTime) {
          // Stale-while-revalidate: 返回过期数据，同时后台刷新
          Logger.debug("Return stale data and refresh in background: \(key)")
          subject.send(.stale(cached.value))
          await refreshInBackground(
            key: key, networkCall: networkCall, subject: subject, typedKey: typedKey)
        } else {
          // Data completely expired, direct network request
          Logger.debug("Data completely expired, direct network request: \(key)")
          await fetchFresh(
            key: key, networkCall: networkCall, subject: subject, typedKey: typedKey)
        }
      } else {
        // Data is fresh, return directly
        Logger.debug("Return fresh cached data: \(key)")
        subject.send(.cached(cached.value))
        completeQuery(typedKey: typedKey, subject: subject)
      }
    } else {
      // No cache, direct network request
      Logger.debug("No cache, direct network request: \(key)")
      await fetchFresh(key: key, networkCall: networkCall, subject: subject, typedKey: typedKey)
    }
  }

  private func refreshInBackground<T: Codable>(
    key: String,
    networkCall: @escaping () async throws -> T,
    subject: PassthroughSubject<QueryResult<T>, Never>,
    typedKey: String
  ) async {
    do {
      let result = try await networkCall()
      await cacheManager.setCache(for: key, value: result)
      subject.send(.fresh(result))
      Logger.debug("Background refresh successful: \(key)")
    } catch {
      subject.send(.error(error))
      Logger.error("Background refresh failed: \(key), 错误: \(error.localizedDescription)")
    }

    completeQuery(typedKey: typedKey, subject: subject)
  }

  private func fetchFresh<T: Codable>(
    key: String,
    networkCall: @escaping () async throws -> T,
    subject: PassthroughSubject<QueryResult<T>, Never>,
    typedKey: String
  ) async {
    do {
      let result = try await networkCall()
      await cacheManager.setCache(for: key, value: result)
      subject.send(.fresh(result))
      Logger.debug("Network request successful: \(key)")
    } catch {
      subject.send(.error(error))
      Logger.error("Network request failed: \(key), 错误: \(error.localizedDescription)")
    }

    completeQuery(typedKey: typedKey, subject: subject)
  }

  private func completeQuery<T>(
    typedKey: String,
    subject: PassthroughSubject<QueryResult<T>, Never>
  ) {
    subject.send(completion: .finished)
    querySubjects.removeValue(forKey: typedKey)
    runningQueries.remove(typedKey)
  }
}

// MARK: - Notification Names

extension Notification.Name {
  static let queriesInvalidated = Notification.Name("queriesInvalidated")
}
