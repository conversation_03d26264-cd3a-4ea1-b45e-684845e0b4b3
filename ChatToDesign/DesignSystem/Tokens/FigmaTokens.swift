//
//  FigmaTokens.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

public struct FigmaTokens {

  // MARK: - Color Tokens

  /// Basic Colors
  public static let white = Color(hex: "#FFFFFF")
  public static let background = Color(hex: "#09090b")
  public static let foreground = Color(hex: "#fafafa")
  public static let primary = Color(hex: "#fafafa")
  public static let primaryForeground = Color(hex: "#18181b")

  /// System Colors
  public static let systemBackgroundLightPrimary = Color(hex: "#FFFFFF")
  public static let labelColorDarkPrimary = Color(hex: "#FFFFFF")

  /// Neutral Colors
  public static let neutralWhite = Color(hex: "#FFFFFF")
  public static let muted = Color(hex: "#27272a")
  public static let mutedForeground = Color(hex: "#a1a1aa")
  public static let input = Color(hex: "#27272a")
  public static let sidebarBackground = Color(hex: "#18181b")

  /// Border Colors
  public static let borderMuted = Color(hex: "#27272a")

  /// Brand Colors
  public static let brand = Color(hex: "#536db0")
  public static let colorsPurple = Color(hex: "#AF52DE")
  public static let colorsCyan = Color(hex: "#32ADE6")
  public static let colorsBlue = Color(hex: "#007aff")
  public static let colorsGreen = Color(hex: "#34C759")
  public static let colorsRed = Color(hex: "#FF3B30")

  /// Foundation Colors
  public static let foundationGrayG01 = Color(hex: "#333333")
  public static let foundationGrayG02 = Color(hex: "#252525")

  /// Neutral Blur Colors
  public static let neutralBlurDarkDark8 = Color(hex: "#0C0C0C")
  public static let neutralBlurLight16 = Color(hex: "#FFFFFF")
  public static let neutralBlurLight48 = Color(hex: "#FFFFFF")

  /// Gray Scale Colors
  public static let graysWhite = Color(hex: "#FFFFFF")

  /// Overlay Colors
  public static let overlaysDefault = Color(hex: "#00000033")

  // MARK: - Gradient Tokens

  /// Pro Tier Gradient (E10000 to FCB104 at 98 degrees)
  public static let proTierGradient = LinearGradient(
    gradient: Gradient(colors: [
      Color(hex: "#E10000"),
      Color(hex: "#FCB104"),
    ]),
    startPoint: .topLeading,
    endPoint: .bottomTrailing
  )

  // MARK: - Spacing Tokens

  public static let spacing0 = CGFloat(0)
  public static let spacing05 = CGFloat(2)
  public static let spacing1 = CGFloat(4)
  public static let spacing15 = CGFloat(6)
  public static let spacing2 = CGFloat(8)
  public static let spacing3 = CGFloat(12)
  public static let spacing4 = CGFloat(16)
  public static let spacing5 = CGFloat(20)
  public static let spacing6 = CGFloat(24)
  public static let spacing8 = CGFloat(32)
  public static let spacing10 = CGFloat(40)
  public static let spacing11 = CGFloat(44)
  public static let spacing14 = CGFloat(56)
  public static let spacing16 = CGFloat(64)
  public static let spacing20 = CGFloat(80)
  public static let spacing32 = CGFloat(128)
  public static let spacing36 = CGFloat(144)
  public static let spacing40 = CGFloat(160)
  public static let spacing44 = CGFloat(176)
  public static let spacing48 = CGFloat(192)
  public static let spacing56 = CGFloat(224)

  // MARK: - Padding Tokens

  public static let paddingP1 = CGFloat(4)
  public static let paddingP2 = CGFloat(8)
  public static let paddingP4 = CGFloat(16)
  public static let paddingP6 = CGFloat(24)
  public static let paddingP10 = CGFloat(40)
  public static let paddingPx25 = CGFloat(10)
  public static let paddingPx3 = CGFloat(12)
  public static let paddingPx4 = CGFloat(16)
  public static let paddingPy05 = CGFloat(2)
  public static let paddingPy2 = CGFloat(8)

  // MARK: - Radius Tokens

  public static let radiusSmall = CGFloat(4)
  public static let radiusLg = CGFloat(8)
  public static let radiusXl = CGFloat(12)
  public static let radius2xl = CGFloat(16)
  public static let radiusFull = CGFloat(9999)

  // MARK: - Border Width Tokens

  public static let borderWidth1 = CGFloat(1)
  public static let borderWidth15 = CGFloat(1.5)

  // MARK: - Size Tokens

  public static let textSm = CGFloat(14)
  public static let width4 = CGFloat(16)
  public static let height4 = CGFloat(16)
  public static let width6 = CGFloat(24)
  public static let height6 = CGFloat(24)
  public static let height10 = CGFloat(40)
  public static let containerLg = CGFloat(512)

  // MARK: - Font Weight Tokens

  public static let fontWeightMedium = Font.Weight.medium  // 500
  public static let fontWeightSemibold = Font.Weight.semibold  // 600

  // MARK: - Font Size Tokens

  public static let fontSize2xl = CGFloat(24)

  // MARK: - Font Leading Tokens

  public static let fontLeading8 = CGFloat(32)
  public static let leadingTight = CGFloat(20)

  // MARK: - Font Tracking Tokens

  public static let fontTrackingTight = CGFloat(-0.4)

  // MARK: - Typography Tokens

  // Inter Font Family
  public static let text2xlMedium = Font.custom("Inter", size: 24).weight(.medium)
  public static let textXlSemibold = Font.custom("Inter", size: 20).weight(.semibold)
  public static let textBaseMedium = Font.custom("Inter", size: 16).weight(.medium)
  public static let textBaseSemibold = Font.custom("Inter", size: 16).weight(.semibold)
  public static let textSmMedium = Font.custom("Inter", size: 14).weight(.medium)
  public static let textSmSemibold = Font.custom("Inter", size: 14).weight(.semibold)
  public static let textSmNormal = Font.custom("Inter", size: 14).weight(.regular)
  public static let textXsNormal = Font.custom("Inter", size: 12).weight(.regular)

  // Geist Font Family
  public static let componentButton = Font.custom("Geist", size: 14).weight(.medium)
  public static let textSmall = Font.custom("Geist", size: 14).weight(.regular)
  public static let textSmall500 = Font.custom("Geist", size: 14).weight(.medium)
  public static let textExtraSmall600 = Font.custom("Geist", size: 12).weight(.semibold)

  // SF Pro Font Family (iOS System Fonts)
  public static let headlineRegular = Font.custom("SF Pro", size: 17).weight(.semibold)  // weight: 590
  public static let bodyRegular = Font.custom("SF Pro", size: 17).weight(.regular)
  public static let bodyEmphasized = Font.custom("SF Pro", size: 17).weight(.semibold)  // weight: 590
  public static let calloutRegular = Font.custom("SF Pro", size: 16).weight(.regular)
  public static let footnoteRegular = Font.custom("SF Pro", size: 13).weight(.regular)

  // Poppins Font Family
  public static let buttonXL = Font.custom("Poppins", size: 18).weight(.medium)
}
