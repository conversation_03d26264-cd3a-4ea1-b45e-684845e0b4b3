//
//  ExploreTokens.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

/// Design tokens specific to the Explore module
/// These tokens provide consistent styling for Explore-related components
public struct ExploreTokens {
    
    // MARK: - Backgrounds
    /// Primary background color for the explore page
    public static let primaryBackground = Color(red: 0.035, green: 0.035, blue: 0.043) // #09090b
    
    /// Card background color
    public static let cardBackground = Color.white
    
    /// Placeholder background for loading images
    public static let placeholderBackground = ColorPalette.gray150
    
    /// Overlay background for card content
    public static let overlayBackground = Color.black.opacity(0.14)
    
    /// Profile button background
    public static let profileBackground = Color.white.opacity(0.05)
    
    // MARK: - Shadows & Effects
    /// Card shadow color with appropriate opacity
    public static let cardShadow = ColorPalette.gray200.opacity(0.3)
    
    // MARK: - Interactive Elements
    /// Like button icon color
    public static let likeIcon = Color.white
    
    /// Like button text color
    public static let likeText = Color.white
    
    /// Profile button icon color
    public static let profileIcon = ColorPalette.gray500
    
    // MARK: - Page Layout
    public struct Page {
        /// Header horizontal padding
        public static let headerPadding: CGFloat = 24
        
        /// Header top padding
        public static let headerTopPadding: CGFloat = 8
        
        /// Bottom spacing for navigation bar
        public static let bottomSpacing: CGFloat = 120
        
        /// Background color for the entire page
        public static let background = ExploreTokens.primaryBackground
        
        /// Title text color
        public static let titleColor = ColorTokens.textInverse
    }
    
    // MARK: - Typography
    public struct Typography {
        /// Page title font
        public static let pageTitle = Font.custom("Inter", size: 20).weight(.semibold)
        
        /// Error state title font
        public static let errorTitle = Font.custom("Inter", size: 20).weight(.semibold)
        
        /// Error message font
        public static let errorMessage = Font.custom("Inter", size: 14)
        
        /// Loading message font
        public static let loadingMessage = Font.custom("Inter", size: 16)
        
        /// Empty state title font
        public static let emptyTitle = Font.custom("Inter", size: 24).weight(.semibold)
        
        /// Empty state message font
        public static let emptyMessage = Font.custom("Inter", size: 16)
        
        /// Like count text font
        public static let likeCount = Font.custom("Inter", size: 14).weight(.semibold)
        
        /// Icon font size
        public static let iconFont = Font.system(size: 16)
    }
    
    // MARK: - Card Layout
    public struct Card {
        /// Card corner radius
        public static let cornerRadius: CGFloat = 16
        
        /// Like button padding
        public static let likeButtonPadding = EdgeInsets(top: 4, leading: 8, bottom: 4, trailing: 8)
        
        /// Like button corner radius
        public static let likeButtonCornerRadius: CGFloat = 32
        
        /// Spacing between like icon and text
        public static let likeButtonSpacing: CGFloat = 4
        
        /// Bottom padding for overlay content
        public static let bottomPadding: CGFloat = 8
        
        /// Background color for cards
        public static let background = ExploreTokens.cardBackground
        
        /// Placeholder color for loading images
        public static let placeholder = ExploreTokens.placeholderBackground
        
        /// Overlay background color
        public static let overlayBackground = ExploreTokens.overlayBackground
        
        /// Like icon color
        public static let likeIcon = ExploreTokens.likeIcon
        
        /// Like text color
        public static let likeText = ExploreTokens.likeText
    }
    
    // MARK: - Profile Button
    public struct Profile {
        /// Profile button size
        public static let buttonSize: CGFloat = 40
        
        /// Profile icon size
        public static let iconSize: CGFloat = 16
        
        /// Profile button background
        public static let background = ExploreTokens.profileBackground
        
        /// Profile icon color
        public static let iconColor = ExploreTokens.profileIcon
    }
    
    // MARK: - Grid Layout
    public struct Grid {
        /// Spacing between grid items
        public static let spacing: CGFloat = 16
        
        /// Horizontal padding for the grid
        public static let padding: CGFloat = 24
        
        /// Top padding for grid content
        public static let topPadding: CGFloat = 16
        
        /// Predefined heights for masonry layout
        public static let masonryHeights: [CGFloat] = [224, 160, 224, 160, 200, 180, 240, 160]
        
        /// Number of columns in the grid
        public static let columns = 2
    }
}