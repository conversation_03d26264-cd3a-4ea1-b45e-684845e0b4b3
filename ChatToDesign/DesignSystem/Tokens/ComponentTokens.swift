//
//  ComponentTokens.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

public struct DesignSystem {
    public struct Button {
        // MARK: - Height
        public struct Height {
            public static let small: CGFloat = 32
            public static let medium: CGFloat = 40
            public static let large: CGFloat = 48
            public static let xlarge: CGFloat = 56
        }
        
        // MARK: - Padding
        public struct Padding {
            public static let small: CGFloat = 12
            public static let medium: CGFloat = 16
            public static let large: CGFloat = 20
            public static let xlarge: CGFloat = 24
        }
        
        // MARK: - Corner Radius
        public struct CornerRadius {
            public static let small: CGFloat = 8
            public static let medium: CGFloat = 12
            public static let large: CGFloat = 16
            public static let xlarge: CGFloat = 20
        }
        
        // MARK: - Typography
        public struct Typography {
            public static let small = Font.custom("Inter", size: 12).weight(.medium)
            public static let medium = Font.custom("Inter", size: 14).weight(.medium)
            public static let large = Font.custom("Inter", size: 16).weight(.semibold)
            public static let xlarge = Font.custom("Inter", size: 18).weight(.semibold)
        }
        
        // MARK: - Colors
        public struct Colors {
            // Primary Style
            public static let primaryBackground = ColorTokens.interactive
            public static let primaryForeground = ColorTokens.textInverse
            public static let primaryBackgroundHover = ColorTokens.interactiveHover
            public static let primaryBackgroundDisabled = ColorTokens.interactive.opacity(0.5)
            
            // Secondary Style
            public static let secondaryBackground = Color.clear
            public static let secondaryForeground = ColorTokens.interactive
            public static let secondaryBorder = ColorTokens.interactive
            public static let secondaryBackgroundHover = ColorTokens.interactive.opacity(0.1)
            
            // Ghost Style
            public static let ghostBackground = Color.clear
            public static let ghostForeground = ColorTokens.textPrimary
            public static let ghostBackgroundHover = ColorTokens.backgroundSecondary
            
            // Danger Style
            public static let dangerBackground = ColorTokens.statusError
            public static let dangerForeground = ColorTokens.textInverse
            public static let dangerBackgroundHover = ColorTokens.statusError.opacity(0.8)
            
            // Success Style
            public static let successBackground = ColorTokens.statusSuccess
            public static let successForeground = ColorTokens.textInverse
            public static let successBackgroundHover = ColorTokens.statusSuccess.opacity(0.8)
            
            // Business-specific button styles have been moved to dedicated components:
            // - SuggestionButton maintains its own design tokens
            // - ExploreButton maintains its own design tokens
            // - TabButton maintains its own design tokens
            //
            // This keeps the base DSButton component focused on universal button styles
            // while allowing business components to have their own specialized styling.
        }
        
        // MARK: - Shadow
        public struct Shadow {
            public static let primary = Color.black.opacity(0.1)
            public static let radius: CGFloat = 4
            public static let offset = CGSize(width: 0, height: 2)
        }
    }
    
    // MARK: - Card Component
    public struct Card {
        public struct Layout {
            public static let cornerRadiusSmall: CGFloat = 8
            public static let cornerRadiusMedium: CGFloat = 12
            public static let cornerRadiusLarge: CGFloat = 16
            public static let shadowRadius: CGFloat = 8
            public static let shadowOffset = CGSize(width: 0, height: 4)
        }
        
        public struct Colors {
            public static let backgroundDefault = ColorTokens.surfacePrimary
            public static let backgroundElevated = ColorTokens.surfaceElevated
            public static let shadow = Color.black.opacity(0.1)
            public static let border = ColorTokens.borderTertiary
        }
    }
    
    // MARK: - Image Card Component
    public struct ImageCard {
        public struct Layout {
            public static let cornerRadius: CGFloat = 16
            public static let overlayHeight: CGFloat = 40
            public static let placeholderIconSize: CGFloat = 24
        }
        
        public struct Colors {
            public static let placeholder = ColorPalette.gray150
            public static let placeholderIcon = Color.white.opacity(0.6)
            public static let overlay = Color.black.opacity(0.14)
        }
    }
    
    // MARK: - Grid Layout Component
    public struct Grid {
        public struct Layout {
            public static let spacing: CGFloat = 16
            public static let padding: CGFloat = 24
            public static let masonryHeights: [CGFloat] = [224, 160, 224, 160, 200, 180, 240, 160]
            public static let columns = 2
        }
    }
    
}
