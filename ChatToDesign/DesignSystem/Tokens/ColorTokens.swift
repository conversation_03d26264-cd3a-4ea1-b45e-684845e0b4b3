//
//  ColorTokens.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

public struct ColorTokens {
  // MARK: - Text Colors
  public static let textPrimary = ColorPalette.gray950
  public static let textSecondary = ColorPalette.gray600
  public static let textTertiary = ColorPalette.gray400
  public static let textInverse = ColorPalette.gray50

  // MARK: - Background Colors
  public static let backgroundPrimary = ColorPalette.gray50
  public static let backgroundSecondary = ColorPalette.gray100
  public static let backgroundTertiary = ColorPalette.gray200
  public static let backgroundInverse = ColorPalette.gray950

  // MARK: - Surface Colors
  public static let surfacePrimary = Color.white
  public static let surfaceSecondary = ColorPalette.gray50
  public static let surfaceElevated = Color.white

  // MARK: - Brand & Interactive
  public static let interactive = ColorPalette.brand500
  public static let interactiveHover = ColorPalette.brand600
  public static let interactiveActive = ColorPalette.brand700

  // MARK: - Border Colors
  public static let borderPrimary = ColorPalette.brand500
  public static let borderSecondary = ColorPalette.gray300
  public static let borderTertiary = ColorPalette.gray200

  // MARK: - Status Colors
  public static let statusSuccess = ColorPalette.success500
  public static let statusWarning = ColorPalette.warning500
  public static let statusError = ColorPalette.error500

// 
}
