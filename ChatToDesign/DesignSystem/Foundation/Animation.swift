//
//  Animation.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

public struct AnimationTokens {
    // MARK: - Duration
    public static let durationFast: Double = 0.15
    public static let durationMedium: Double = 0.25
    public static let durationSlow: Double = 0.4
    
    // MARK: - Easing
    public static let easingStandard = Animation.easeInOut(duration: durationMedium)
    public static let easingDecelerate = Animation.easeOut(duration: durationMedium)
    public static let easingAccelerate = Animation.easeIn(duration: durationMedium)
    public static let easingSpring = Animation.spring(response: 0.5, dampingFraction: 0.8)
}