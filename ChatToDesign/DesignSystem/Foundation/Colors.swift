//
//  Colors.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

public struct ColorPalette {
  // MARK: - Gray Scale
  public static let gray50 = Color(hex: "#fafafa")
  public static let gray100 = Color(hex: "#f5f5f5")
  public static let gray150 = Color(hex: "#eeeeee")  // For placeholder backgrounds
  public static let gray200 = Color(hex: "#e5e5e5")
  public static let gray300 = Color(hex: "#d4d4d8")
  public static let gray400 = Color(hex: "#a1a1aa")
  public static let gray500 = Color(hex: "#71717a")
  public static let gray600 = Color(hex: "#52525b")
  public static let gray700 = Color(hex: "#3f3f46")
  public static let gray750 = Color(hex: "#333338")  // Midpoint between gray700 and gray800
  public static let gray800 = Color(hex: "#27272a")
  public static let gray900 = Color(hex: "#18181b")
  public static let gray950 = Color(hex: "#09090b")

  // MARK: - Brand Colors
  public static let brand50 = Color(hex: "#eff6ff")
  public static let brand500 = Color(hex: "#536db0")
  public static let brand600 = Color(hex: "#4c63a8")
  public static let brand700 = Color(hex: "#455996")

  // MARK: - Semantic Colors
  public static let success500 = Color(hex: "#22c55e")
  public static let warning500 = Color(hex: "#eab308")
  public static let error500 = Color(hex: "#ef4444")

  // MARK: - Foundation Colors from Figma
  /// Foundation Grey G01 - #333333
  public static let foundationGrayG01 = Color(hex: "#333333")

  /// Foundation Grey G02 - #252525
  public static let foundationGrayG02 = Color(hex: "#252525")

  // MARK: - Additional Colors from Figma
  /// Purple color - #AF52DE
  public static let purple = Color(hex: "#AF52DE")

  /// Cyan color - #32ADE6
  public static let cyan = Color(hex: "#32ADE6")

  /// Blue color - #007AFF
  public static let blue = Color(hex: "#007AFF")
}

extension Color {
  init(hex: String) {
    let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
    var int: UInt64 = 0
    Scanner(string: hex).scanHexInt64(&int)
    let a: UInt64
    let r: UInt64
    let g: UInt64
    let b: UInt64
    switch hex.count {
    case 3:  // RGB (12-bit)
      (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
    case 6:  // RGB (24-bit)
      (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
    case 8:  // ARGB (32-bit)
      (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
    default:
      (a, r, g, b) = (1, 1, 1, 0)
    }

    self.init(
      .sRGB,
      red: Double(r) / 255,
      green: Double(g) / 255,
      blue: Double(b) / 255,
      opacity: Double(a) / 255
    )
  }
}
