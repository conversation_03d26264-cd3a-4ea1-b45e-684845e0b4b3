//
//  Spacing.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

public struct Spacing {
    public static let space0: CGFloat = 0
    public static let space2: CGFloat = 2     // 0.125rem
    public static let space4: CGFloat = 4     // 0.25rem
    public static let space8: CGFloat = 8     // 0.5rem
    public static let space12: CGFloat = 12   // 0.75rem
    public static let space16: CGFloat = 16   // 1rem
    public static let space20: CGFloat = 20   // 1.25rem
    public static let space24: CGFloat = 24   // 1.5rem
    public static let space32: CGFloat = 32   // 2rem
    public static let space40: CGFloat = 40   // 2.5rem
    public static let space48: CGFloat = 48   // 3rem
    public static let space64: CGFloat = 64   // 4rem
    public static let space80: CGFloat = 80   // 5rem
    public static let space96: CGFloat = 96   // 6rem
    public static let space128: CGFloat = 128 // 8rem
}