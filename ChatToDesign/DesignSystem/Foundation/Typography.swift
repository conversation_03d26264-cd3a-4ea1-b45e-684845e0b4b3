//
//  Typography.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

public struct Typography {
    // MARK: - Display
    public static let display1 = Font.custom("Inter", size: 40).weight(.bold)
    public static let display2 = Font.custom("Inter", size: 32).weight(.bold)
    
    // MARK: - Headline
    public static let headline1 = Font.custom("Inter", size: 28).weight(.semibold)
    public static let headline2 = Font.custom("Inter", size: 24).weight(.semibold)
    public static let headline3 = Font.custom("Inter", size: 20).weight(.semibold)
    
    // MARK: - Body
    public static let bodyLarge = Font.custom("Inter", size: 16).weight(.regular)
    public static let bodyMedium = Font.custom("Inter", size: 14).weight(.regular)
    public static let bodySmall = Font.custom("Inter", size: 12).weight(.regular)
    
    // MARK: - Label
    public static let labelLarge = Font.custom("Inter", size: 14).weight(.medium)
    public static let labelMedium = Font.custom("Inter", size: 12).weight(.medium)
    public static let labelSmall = Font.custom("Inter", size: 10).weight(.medium)
}