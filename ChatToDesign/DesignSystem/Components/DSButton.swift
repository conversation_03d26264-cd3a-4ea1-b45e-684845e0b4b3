//
//  DSButton.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

public struct DSButton: View {
    // MARK: - Enums
    public enum Style {
        case primary
        case secondary
        case ghost
        case danger
        case success
    }
    
    public enum Size {
        case small
        case medium
        case large
        case xlarge
    }
    
    public enum Width {
        case intrinsic
        case full
        case fixed(CGFloat)
    }
    
    public enum IconPosition {
        case leading
        case trailing
        case only
    }
    
    // MARK: - Properties
    let title: String?
    let icon: String?
    let iconPosition: IconPosition
    let style: Style
    let size: Size
    let width: Width
    let isLoading: Bool
    let isDisabled: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    
    // MARK: - Initializer
    public init(
        title: String? = nil,
        icon: String? = nil,
        iconPosition: IconPosition = .leading,
        style: Style = .primary,
        size: Size = .medium,
        width: Width = .intrinsic,
        isLoading: Bool = false,
        isDisabled: Bool = false,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.iconPosition = iconPosition
        self.style = style
        self.size = size
        self.width = width
        self.isLoading = isLoading
        self.isDisabled = isDisabled
        self.action = action
    }
    
    // MARK: - Body
    public var body: some View {
        Button(action: {
            if !isDisabled && !isLoading {
                action()
            }
        }) {
            HStack(spacing: Spacing.space8) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: foregroundColor))
                        .scaleEffect(0.8)
                } else {
                    contentView
                }
            }
            .frame(height: buttonHeight)
            .frame(maxWidth: buttonWidth)
            .padding(.horizontal, horizontalPadding)
            .background(backgroundColor)
            .foregroundColor(foregroundColor)
            .font(buttonFont)
            .cornerRadius(cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
            .shadow(
                color: shadowColor,
                radius: DesignSystem.Button.Shadow.radius,
                x: DesignSystem.Button.Shadow.offset.width,
                y: DesignSystem.Button.Shadow.offset.height
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(AnimationTokens.easingSpring, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isDisabled || isLoading)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(AnimationTokens.easingStandard) {
                isPressed = pressing
            }
        }, perform: {})
    }
    
    // MARK: - Content View
    @ViewBuilder
    private var contentView: some View {
        switch iconPosition {
        case .leading:
            HStack(spacing: Spacing.space8) {
                if let icon = icon {
                    Image(systemName: icon)
                        .imageScale(.small)
                }
                if let title = title {
                    Text(title)
                }
            }
        case .trailing:
            HStack(spacing: Spacing.space8) {
                if let title = title {
                    Text(title)
                }
                if let icon = icon {
                    Image(systemName: icon)
                        .imageScale(.small)
                }
            }
        case .only:
            if let icon = icon {
                Image(systemName: icon)
                    .imageScale(.small)
            }
        }
    }
    
    // MARK: - Computed Properties
    private var buttonHeight: CGFloat {
        switch size {
        case .small: return DesignSystem.Button.Height.small
        case .medium: return DesignSystem.Button.Height.medium
        case .large: return DesignSystem.Button.Height.large
        case .xlarge: return DesignSystem.Button.Height.xlarge
        }
    }
    
    private var buttonWidth: CGFloat? {
        switch width {
        case .intrinsic: return nil
        case .full: return .infinity
        case .fixed(let value): return value
        }
    }
    
    private var horizontalPadding: CGFloat {
        switch size {
        case .small: return DesignSystem.Button.Padding.small
        case .medium: return DesignSystem.Button.Padding.medium
        case .large: return DesignSystem.Button.Padding.large
        case .xlarge: return DesignSystem.Button.Padding.xlarge
        }
    }
    
    private var cornerRadius: CGFloat {
        switch size {
        case .small: return DesignSystem.Button.CornerRadius.small
        case .medium: return DesignSystem.Button.CornerRadius.medium
        case .large: return DesignSystem.Button.CornerRadius.large
        case .xlarge: return DesignSystem.Button.CornerRadius.xlarge
        }
    }
    
    private var buttonFont: Font {
        switch size {
        case .small: return DesignSystem.Button.Typography.small
        case .medium: return DesignSystem.Button.Typography.medium
        case .large: return DesignSystem.Button.Typography.large
        case .xlarge: return DesignSystem.Button.Typography.xlarge
        }
    }
    
    private var backgroundColor: Color {
        if isDisabled {
            return disabledBackgroundColor
        }
        
        if isPressed {
            return hoverBackgroundColor
        }
        
        switch style {
        case .primary: return DesignSystem.Button.Colors.primaryBackground
        case .secondary: return DesignSystem.Button.Colors.secondaryBackground
        case .ghost: return DesignSystem.Button.Colors.ghostBackground
        case .danger: return DesignSystem.Button.Colors.dangerBackground
        case .success: return DesignSystem.Button.Colors.successBackground
        }
    }
    
    private var hoverBackgroundColor: Color {
        switch style {
        case .primary: return DesignSystem.Button.Colors.primaryBackgroundHover
        case .secondary: return DesignSystem.Button.Colors.secondaryBackgroundHover
        case .ghost: return DesignSystem.Button.Colors.ghostBackgroundHover
        case .danger: return DesignSystem.Button.Colors.dangerBackgroundHover
        case .success: return DesignSystem.Button.Colors.successBackgroundHover
        }
    }
    
    private var disabledBackgroundColor: Color {
        switch style {
        case .primary: return DesignSystem.Button.Colors.primaryBackgroundDisabled
        case .secondary: return DesignSystem.Button.Colors.secondaryBackground
        case .ghost: return DesignSystem.Button.Colors.ghostBackground
        case .danger: return DesignSystem.Button.Colors.dangerBackground.opacity(0.5)
        case .success: return DesignSystem.Button.Colors.successBackground.opacity(0.5)
        }
    }
    
    private var foregroundColor: Color {
        if isDisabled {
            return ColorTokens.textTertiary
        }
        
        switch style {
        case .primary: return DesignSystem.Button.Colors.primaryForeground
        case .secondary: return DesignSystem.Button.Colors.secondaryForeground
        case .ghost: return DesignSystem.Button.Colors.ghostForeground
        case .danger: return DesignSystem.Button.Colors.dangerForeground
        case .success: return DesignSystem.Button.Colors.successForeground
        }
    }
    
    private var borderColor: Color {
        switch style {
        case .secondary: return DesignSystem.Button.Colors.secondaryBorder
        default: return Color.clear
        }
    }
    
    private var borderWidth: CGFloat {
        switch style {
        case .secondary: return 1
        default: return 0
        }
    }
    
    private var shadowColor: Color {
        switch style {
        case .primary, .danger, .success: return DesignSystem.Button.Shadow.primary
        default: return Color.clear
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: Spacing.space16) {
        // Primary Buttons
        HStack(spacing: Spacing.space12) {
            DSButton(
                title: "Small",
                style: .primary,
                size: .small,
                action: {}
            )
            
            DSButton(
                title: "Medium",
                style: .primary,
                size: .medium,
                action: {}
            )
            
            DSButton(
                title: "Large",
                style: .primary,
                size: .large,
                action: {}
            )
            
            DSButton(
                title: "XLarge",
                style: .primary,
                size: .xlarge,
                action: {}
            )
        }
        
        // Secondary Buttons
        HStack(spacing: Spacing.space12) {
            DSButton(
                title: "Cancel",
                style: .secondary,
                size: .medium,
                action: {}
            )
            
            DSButton(
                title: "Save",
                icon: "checkmark",
                style: .success,
                size: .medium,
                action: {}
            )
            
            DSButton(
                title: "Delete",
                icon: "trash",
                style: .danger,
                size: .medium,
                action: {}
            )
        }
        
        // Icon-only Buttons
        HStack(spacing: Spacing.space12) {
            DSButton(
                icon: "heart",
                iconPosition: .only,
                style: .ghost,
                size: .small,
                action: {}
            )
            
            DSButton(
                icon: "star",
                iconPosition: .only,
                style: .primary,
                size: .medium,
                action: {}
            )
        }
        
        // Full width and loading states
        VStack(spacing: Spacing.space12) {
            DSButton(
                title: "Full Width Button",
                style: .primary,
                size: .large,
                width: .full,
                action: {}
            )
            
            DSButton(
                title: "Loading...",
                style: .primary,
                size: .medium,
                width: .full,
                isLoading: true,
                action: {}
            )
            
            DSButton(
                title: "Disabled",
                style: .primary,
                size: .medium,
                width: .full,
                isDisabled: true,
                action: {}
            )
        }
    }
    .padding()
}