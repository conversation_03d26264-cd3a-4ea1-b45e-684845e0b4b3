//
//  SelectionButton.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

/// Selection button component for various selectable options
/// Provides consistent styling for selection states across the application
public struct SelectionButton: View {
    public let title: String
    public let systemImage: String?
    public let isSelected: Bool
    public let action: () -> Void
    
    // MARK: - Initialization
    
    public init(
        _ title: String,
        systemImage: String? = nil,
        isSelected: Bool,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.systemImage = systemImage
        self.isSelected = isSelected
        self.action = action
    }
    
    // MARK: - Body
    
    public var body: some View {
        Button(action: action) {
            HStack(spacing: Spacing.space4) {
                if let systemImage = systemImage {
                    Image(systemName: systemImage)
                        .font(.system(size: 14, weight: .medium))
                }
                
                Text(title)
                    .font(Typography.bodyMedium)
            }
            .foregroundColor(isSelected ? SelectionTokens.selectedText : SelectionTokens.unselectedText)
        }
        .padding(.horizontal, Spacing.space16)
        .padding(.vertical, Spacing.space12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? SelectionTokens.selectedBackground : Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(
                            isSelected ? SelectionTokens.selectedBorder : SelectionTokens.unselectedBorder,
                            lineWidth: isSelected ? 2 : 1
                        )
                )
        )
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Design Tokens

private enum SelectionTokens {
    static let selectedBackground = CreateTokens.primaryBackground
    static let selectedBorder = CreateTokens.selectedBorder
    static let unselectedBorder = CreateTokens.unselectedBorder
    static let selectedText = ColorTokens.textInverse
    static let unselectedText = ColorTokens.textSecondary
}