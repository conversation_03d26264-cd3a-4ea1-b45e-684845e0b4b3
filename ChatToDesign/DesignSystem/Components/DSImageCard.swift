//
//  DSImageCard.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import Kingfisher
import SwiftUI

/// A specialized card component for displaying images and videos with overlays
public struct DSImageCard: View {
    
    // MARK: - Types
    
    public enum ContentType {
        case image
        case video
        
        var placeholderIcon: String {
            switch self {
            case .image:
                return "photo"
            case .video:
                return "play.circle.fill"
            }
        }
    }
    
    public enum OverlayStyle {
        case none
        case gradient
        case solid
    }
    
    public enum LoadingState {
        case loading
        case loaded
        case failed
    }
    
    // MARK: - Properties
    
    private let imageURL: URL?
    private let contentType: ContentType
    private let aspectRatio: CGFloat?
    private let overlayStyle: OverlayStyle
    private let overlayContent: AnyView?
    private let onTap: (() -> Void)?
    private let onLongPress: (() -> Void)?
    
    // MARK: - State
    
    @State private var loadingState: LoadingState = .loading
    @State private var isPressed = false
    
    // MARK: - Initialization
    
    /// Creates a new DSImageCard
    /// - Parameters:
    ///   - imageURL: The URL of the image to display
    ///   - contentType: Whether this is an image or video
    ///   - aspectRatio: Optional aspect ratio constraint
    ///   - overlayStyle: Style of overlay background
    ///   - overlayContent: Custom content to overlay on the image
    ///   - onTap: Optional tap handler
    ///   - onLongPress: Optional long press handler
    public init(
        imageURL: URL?,
        contentType: ContentType = .image,
        aspectRatio: CGFloat? = nil,
        overlayStyle: OverlayStyle = .none,
        overlayContent: AnyView? = nil,
        onTap: (() -> Void)? = nil,
        onLongPress: (() -> Void)? = nil
    ) {
        self.imageURL = imageURL
        self.contentType = contentType
        self.aspectRatio = aspectRatio
        self.overlayStyle = overlayStyle
        self.overlayContent = overlayContent
        self.onTap = onTap
        self.onLongPress = onLongPress
    }
    
    // MARK: - Convenience Initializers
    
    /// Creates a DSImageCard with a like button overlay
    public static func withLikeButton(
        imageURL: URL?,
        contentType: ContentType = .image,
        aspectRatio: CGFloat? = nil,
        likeCount: Int,
        isLiked: Bool = false,
        onTap: (() -> Void)? = nil,
        onLikePressed: (() -> Void)? = nil
    ) -> DSImageCard {
        let likeButton = AnyView(
            DSLikeButton(
                count: likeCount,
                isLiked: isLiked,
                onTap: onLikePressed
            )
        )
        
        return DSImageCard(
            imageURL: imageURL,
            contentType: contentType,
            aspectRatio: aspectRatio,
            overlayStyle: .solid,
            overlayContent: likeButton,
            onTap: onTap
        )
    }
    
    // MARK: - Body
    
    public var body: some View {
        Group {
            if onTap != nil || onLongPress != nil {
                Button(action: { onTap?() }) {
                    cardContent
                }
                .buttonStyle(PlainButtonStyle())
                .onLongPressGesture {
                    onLongPress?()
                }
            } else {
                cardContent
            }
        }
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .onPressGesture(
            onPress: { isPressed = true },
            onRelease: { isPressed = false }
        )
        .animation(.easeInOut(duration: 0.15), value: isPressed)
    }
    
    // MARK: - Private Views
    
    private var cardContent: some View {
        ZStack {
            // Background Image
            imageView
            
            // Overlay Content
            if let overlayContent = overlayContent {
                overlayView(content: overlayContent)
            }
        }
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.ImageCard.Layout.cornerRadius))
    }
    
    private var imageView: some View {
        Group {
            if let imageURL = imageURL {
                KFImage(imageURL)
                    .placeholder {
                        placeholderView
                    }
                    .onSuccess { _ in
                        loadingState = .loaded
                    }
                    .onFailure { _ in
                        loadingState = .failed
                    }
                    .resizable()
                    .aspectRatio(aspectRatio, contentMode: .fill)
                    .clipped()
            } else {
                placeholderView
            }
        }
    }
    
    private var placeholderView: some View {
        Rectangle()
            .fill(DesignSystem.ImageCard.Colors.placeholder)
            .overlay(
                Image(systemName: contentType.placeholderIcon)
                    .font(.system(size: DesignSystem.ImageCard.Layout.placeholderIconSize))
                    .foregroundColor(DesignSystem.ImageCard.Colors.placeholderIcon)
            )
    }
    
    private func overlayView(content: AnyView) -> some View {
        VStack {
            Spacer()
            
            HStack {
                Spacer()
                content
                Spacer()
            }
            .padding(.bottom, ExploreTokens.Card.bottomPadding)
        }
        .background(
            overlayBackgroundView
        )
    }
    
    @ViewBuilder
    private var overlayBackgroundView: some View {
        switch overlayStyle {
        case .none:
            Color.clear
        case .gradient:
            LinearGradient(
                colors: [Color.clear, Color.black.opacity(0.6)],
                startPoint: .top,
                endPoint: .bottom
            )
        case .solid:
            DesignSystem.ImageCard.Colors.overlay
        }
    }
}

// MARK: - DSLikeButton Component

private struct DSLikeButton: View {
    let count: Int
    let isLiked: Bool
    let onTap: (() -> Void)?
    
    var body: some View {
        Button(action: { onTap?() }) {
            HStack(spacing: ExploreTokens.Card.likeButtonSpacing) {
                Image(systemName: isLiked ? "heart.fill" : "heart")
                    .font(ExploreTokens.Typography.iconFont)
                    .foregroundColor(ExploreTokens.Card.likeIcon)
                
                Text("\(count)")
                    .font(ExploreTokens.Typography.likeCount)
                    .foregroundColor(ExploreTokens.Card.likeText)
            }
            .padding(ExploreTokens.Card.likeButtonPadding)
            .background(
                RoundedRectangle(cornerRadius: ExploreTokens.Card.likeButtonCornerRadius)
                    .fill(ExploreTokens.Card.overlayBackground)
                    .background(
                        RoundedRectangle(cornerRadius: ExploreTokens.Card.likeButtonCornerRadius)
                            .fill(.ultraThinMaterial)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Helper Extension

private extension View {
    func onPressGesture(onPress: @escaping () -> Void, onRelease: @escaping () -> Void) -> some View {
        self.simultaneousGesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in onPress() }
                .onEnded { _ in onRelease() }
        )
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        // Image card with like button
        DSImageCard.withLikeButton(
            imageURL: URL(string: "https://picsum.photos/300/400"),
            contentType: .image,
            likeCount: 42,
            isLiked: true,
            onTap: {
                print("Image tapped")
            },
            onLikePressed: {
                print("Like pressed")
            }
        )
        .frame(height: 200)
        
        // Video card with custom content
        DSImageCard(
            imageURL: URL(string: "https://picsum.photos/300/300"),
            contentType: .video,
            overlayStyle: .gradient,
            overlayContent: AnyView(
                HStack {
                    Image(systemName: "play.fill")
                        .foregroundColor(.white)
                    Text("Play Video")
                        .foregroundColor(.white)
                        .font(.caption)
                }
                .padding(8)
                .background(Color.black.opacity(0.6))
                .clipShape(Capsule())
            ),
            onTap: {
                print("Video tapped")
            }
        )
        .frame(height: 160)
        
        // Placeholder card
        DSImageCard(
            imageURL: nil,
            contentType: .image
        )
        .frame(height: 120)
    }
    .padding()
    .background(ExploreTokens.Page.background)
}