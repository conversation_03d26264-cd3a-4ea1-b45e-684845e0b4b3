//
//  DSImageThumbnail.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import Kingfisher
import SwiftUI

/// Generic image thumbnail component with loading states and optional overlay actions
/// Provides consistent styling for image display across the application
public struct DSImageThumbnail<OverlayContent: View>: View {
    public let imageUrl: String
    public let isLoading: Bool
    public let height: CGFloat?
    public let cornerRadius: CGFloat
    public let overlayContent: (() -> OverlayContent)?
    
    // MARK: - Initialization
    
    public init(
        imageUrl: String,
        isLoading: Bool = false,
        height: CGFloat? = nil,
        cornerRadius: CGFloat = 16,
        @ViewBuilder overlayContent: @escaping () -> OverlayContent
    ) {
        self.imageUrl = imageUrl
        self.isLoading = isLoading
        self.height = height
        self.cornerRadius = cornerRadius
        self.overlayContent = overlayContent
    }
    
    // MARK: - Body
    
    public var body: some View {
        ZStack {
            if isLoading {
                loadingView
            } else {
                imageView
                
                if overlayContent != nil {
                    overlayView
                }
            }
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(ThumbnailTokens.loadingBackground)
            .frame(height: height ?? 100)
            .overlay(
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: ThumbnailTokens.loadingIndicator))
            )
    }
    
    // MARK: - Image View
    
    private var imageView: some View {
        KFImage(URL(string: imageUrl))
            .placeholder {
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(ThumbnailTokens.placeholderBackground)
                    .frame(height: height ?? 100)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.system(size: 24))
                            .foregroundColor(ThumbnailTokens.placeholderIcon)
                    )
            }
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(height: height ?? 100)
            .clipped()
            .cornerRadius(cornerRadius)
    }
    
    // MARK: - Overlay View
    
    @ViewBuilder
    private var overlayView: some View {
        if let overlayContent = overlayContent {
            ZStack {
                ThumbnailTokens.overlayBackground
                overlayContent()
            }
            .cornerRadius(cornerRadius)
        }
    }
}

// MARK: - Convenience Initializers

public extension DSImageThumbnail where OverlayContent == EmptyView {
    /// Simple image thumbnail without overlay content
    init(
        imageUrl: String,
        isLoading: Bool = false,
        height: CGFloat? = nil,
        cornerRadius: CGFloat = 16
    ) {
        self.imageUrl = imageUrl
        self.isLoading = isLoading
        self.height = height
        self.cornerRadius = cornerRadius
        self.overlayContent = nil
    }
}

public extension DSImageThumbnail where OverlayContent == Button<Image> {
    /// Image thumbnail with delete button overlay
    init(
        imageUrl: String,
        isLoading: Bool = false,
        height: CGFloat? = nil,
        cornerRadius: CGFloat = 16,
        onDelete: @escaping () -> Void
    ) {
        self.imageUrl = imageUrl
        self.isLoading = isLoading
        self.height = height
        self.cornerRadius = cornerRadius
        self.overlayContent = {
            Button(action: onDelete) {
                Image(systemName: "trash")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(ThumbnailTokens.deleteButtonIcon)
                    .frame(width: 24, height: 24)
                    .background(ThumbnailTokens.deleteButtonBackground)
                    .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle()) as! Button<Image>
        }
    }
}

// MARK: - Design Tokens

private struct ThumbnailTokens {
    static let loadingBackground = CreateTokens.loadingBackground
    static let loadingIndicator = ColorTokens.textInverse
    static let placeholderBackground = CreateTokens.loadingBackground
    static let placeholderIcon = ColorTokens.textTertiary
    static let overlayBackground = CreateTokens.overlayBackground
    static let deleteButtonIcon = ColorTokens.textInverse
    static let deleteButtonBackground = CreateTokens.deleteButtonOverlay
}
