//
//  DSMasonryGrid.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

/// A high-performance masonry grid layout component for displaying items with dynamic heights
public struct DSMasonryGrid<Item: Identifiable, ItemView: View>: View {
    
    // MARK: - Properties
    
    private let items: [Item]
    private let columns: Int
    private let spacing: CGFloat
    private let padding: CGFloat
    private let itemBuilder: (Item, Int) -> ItemView
    private let heightProvider: ((Item, Int) -> CGFloat)?
    
    // MARK: - Initialization
    
    /// Creates a new DSMasonryGrid
    /// - Parameters:
    ///   - items: The items to display
    ///   - columns: Number of columns in the grid
    ///   - spacing: Spacing between items
    ///   - padding: Horizontal padding for the grid
    ///   - heightProvider: Optional custom height provider for items
    ///   - itemBuilder: Builder function that creates the view for each item
    public init(
        items: [Item],
        columns: Int = DesignSystem.Grid.Layout.columns,
        spacing: CGFloat = DesignSystem.Grid.Layout.spacing,
        padding: CGFloat = DesignSystem.Grid.Layout.padding,
        heightProvider: ((Item, Int) -> CGFloat)? = nil,
        @ViewBuilder itemBuilder: @escaping (Item, Int) -> ItemView
    ) {
        self.items = items
        self.columns = columns
        self.spacing = spacing
        self.padding = padding
        self.heightProvider = heightProvider
        self.itemBuilder = itemBuilder
    }
    
    // MARK: - Body
    
    public var body: some View {
        ScrollView {
            HStack(alignment: .top, spacing: spacing) {
                ForEach(0..<columns, id: \.self) { columnIndex in
                    LazyVStack(spacing: spacing) {
                        ForEach(itemsForColumn(columnIndex), id: \.element.id) { indexedItem in
                            itemBuilder(indexedItem.element, indexedItem.index)
                                .frame(height: heightForItem(indexedItem.element, at: indexedItem.index))
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
            }
            .padding(.horizontal, padding)
        }
    }
    
    // MARK: - Private Methods
    
    private func itemsForColumn(_ columnIndex: Int) -> [(index: Int, element: Item)] {
        return items.enumerated().compactMap { index, item in
            if index % columns == columnIndex {
                return (index: index, element: item)
            }
            return nil
        }
    }
    
    private func heightForItem(_ item: Item, at index: Int) -> CGFloat {
        if let heightProvider = heightProvider {
            return heightProvider(item, index)
        }
        
        // Default height pattern if no custom provider
        let defaultHeights = DesignSystem.Grid.Layout.masonryHeights
        return defaultHeights[index % defaultHeights.count]
    }
}

// MARK: - Convenience Extensions

public extension DSMasonryGrid {
    
    /// Creates a masonry grid optimized for Explore module
    static func exploreGrid(
        items: [Item],
        @ViewBuilder itemBuilder: @escaping (Item, Int) -> ItemView
    ) -> DSMasonryGrid where Item: ExploreItemProtocol {
        return DSMasonryGrid(
            items: items,
            columns: DesignSystem.Grid.Layout.columns,
            spacing: DesignSystem.Grid.Layout.spacing,
            padding: DesignSystem.Grid.Layout.padding,
            heightProvider: { item, index in
                // Custom height logic for explore items
                let baseHeights = DesignSystem.Grid.Layout.masonryHeights
                return baseHeights[index % baseHeights.count]
            },
            itemBuilder: itemBuilder
        )
    }
    
    /// Creates a uniform grid with equal heights
    static func uniformGrid(
        items: [Item],
        itemHeight: CGFloat,
        @ViewBuilder itemBuilder: @escaping (Item, Int) -> ItemView
    ) -> DSMasonryGrid {
        return DSMasonryGrid(
            items: items,
            heightProvider: { _, _ in itemHeight },
            itemBuilder: itemBuilder
        )
    }
}

// MARK: - Protocol for Explore Items

public protocol ExploreItemProtocol: Identifiable {
    var isVideo: Bool { get }
    var likeCount: Int { get }
}

// MARK: - Responsive Grid

public struct ResponsiveMasonryGrid<Item: Identifiable, ItemView: View>: View {
    
    private let items: [Item]
    private let spacing: CGFloat
    private let padding: CGFloat
    private let itemBuilder: (Item, Int) -> ItemView
    
    // MARK: - Responsive Properties
        
    private var columns: Int {
        return 2
    }
    
    // MARK: - Initialization
    
    public init(
        items: [Item],
        spacing: CGFloat = DesignSystem.Grid.Layout.spacing,
        padding: CGFloat = DesignSystem.Grid.Layout.padding,
        @ViewBuilder itemBuilder: @escaping (Item, Int) -> ItemView
    ) {
        self.items = items
        self.spacing = spacing
        self.padding = padding
        self.itemBuilder = itemBuilder
    }
    
    // MARK: - Body
    
    public var body: some View {
        DSMasonryGrid(
            items: items,
            columns: columns,
            spacing: spacing,
            padding: padding,
            itemBuilder: itemBuilder
        )
    }
}

// MARK: - Performance Optimized Grid

public struct LazyMasonryGrid<Item: Identifiable, ItemView: View>: View {
    
    private let items: [Item]
    private let columns: Int
    private let spacing: CGFloat
    private let padding: CGFloat
    private let itemBuilder: (Item, Int) -> ItemView
    private let loadMoreThreshold: Int
    private let onLoadMore: (() -> Void)?
    
    // MARK: - Initialization
    
    public init(
        items: [Item],
        columns: Int = DesignSystem.Grid.Layout.columns,
        spacing: CGFloat = DesignSystem.Grid.Layout.spacing,
        padding: CGFloat = DesignSystem.Grid.Layout.padding,
        loadMoreThreshold: Int = 5,
        onLoadMore: (() -> Void)? = nil,
        @ViewBuilder itemBuilder: @escaping (Item, Int) -> ItemView
    ) {
        self.items = items
        self.columns = columns
        self.spacing = spacing
        self.padding = padding
        self.loadMoreThreshold = loadMoreThreshold
        self.onLoadMore = onLoadMore
        self.itemBuilder = itemBuilder
    }
    
    // MARK: - Body
    
    public var body: some View {
        DSMasonryGrid(
            items: items,
            columns: columns,
            spacing: spacing,
            padding: padding
        ) { item, index in
            itemBuilder(item, index)
                .onAppear {
                    if shouldLoadMore(at: index) {
                        onLoadMore?()
                    }
                }
        }
    }
    
    // MARK: - Private Methods
    
    private func shouldLoadMore(at index: Int) -> Bool {
        return index >= items.count - loadMoreThreshold
    }
}

// MARK: - Preview

#Preview {
    struct PreviewItem: Identifiable, ExploreItemProtocol {
        let id = UUID()
        let title: String
        let isVideo: Bool
        let likeCount: Int
    }
    
    let sampleItems = [
        PreviewItem(title: "Item 1", isVideo: false, likeCount: 12),
        PreviewItem(title: "Item 2", isVideo: true, likeCount: 34),
        PreviewItem(title: "Item 3", isVideo: false, likeCount: 56),
        PreviewItem(title: "Item 4", isVideo: false, likeCount: 78),
        PreviewItem(title: "Item 5", isVideo: true, likeCount: 90),
        PreviewItem(title: "Item 6", isVideo: false, likeCount: 123),
    ]
    
    return NavigationView {
        DSMasonryGrid.exploreGrid(items: sampleItems) { item, index in
            DSCard(style: .elevated, onTap: {
                print("Tapped \(item.title)")
            }) {
                VStack(alignment: .leading, spacing: 8) {
                    if item.isVideo {
                        Image(systemName: "play.rectangle.fill")
                            .font(.largeTitle)
                            .foregroundColor(.blue)
                    } else {
                        Image(systemName: "photo")
                            .font(.largeTitle)
                            .foregroundColor(.green)
                    }
                    
                    Text(item.title)
                        .font(.headline)
                    
                    HStack {
                        Image(systemName: "heart")
                        Text("\(item.likeCount)")
                            .font(.caption)
                        Spacer()
                    }
                    .foregroundColor(.secondary)
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
        .navigationTitle("Masonry Grid")
    }
    .background(ColorTokens.backgroundPrimary)
}
