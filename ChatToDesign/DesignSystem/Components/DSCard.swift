//
//  DSCard.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

/// A unified card component that provides consistent styling across the app
public struct DSCard<Content: View>: View {
    
    // MARK: - Types
    
    public enum Style {
        case `default`
        case elevated
        case outlined
        
        var backgroundColor: Color {
            switch self {
            case .default:
                return DesignSystem.Card.Colors.backgroundDefault
            case .elevated:
                return DesignSystem.Card.Colors.backgroundElevated
            case .outlined:
                return DesignSystem.Card.Colors.backgroundDefault
            }
        }
        
        var borderColor: Color? {
            switch self {
            case .default, .elevated:
                return nil
            case .outlined:
                return DesignSystem.Card.Colors.border
            }
        }
        
        var shadowRadius: CGFloat {
            switch self {
            case .default:
                return 0
            case .elevated:
                return DesignSystem.Card.Layout.shadowRadius
            case .outlined:
                return 0
            }
        }
    }
    
    public enum Size {
        case small
        case medium
        case large
        case custom(width: CGFloat?, height: CGFloat?)
        
        var cornerRadius: CGFloat {
            switch self {
            case .small:
                return DesignSystem.Card.Layout.cornerRadiusSmall
            case .medium:
                return DesignSystem.Card.Layout.cornerRadiusMedium
            case .large, .custom:
                return DesignSystem.Card.Layout.cornerRadiusLarge
            }
        }
    }
    
    // MARK: - Properties
    
    private let style: Style
    private let size: Size
    private let content: () -> Content
    private let onTap: (() -> Void)?
    private let onLongPress: (() -> Void)?
    
    // MARK: - State
    
    @State private var isPressed = false
    
    // MARK: - Initialization
    
    /// Creates a new DSCard
    /// - Parameters:
    ///   - style: The visual style of the card
    ///   - size: The size configuration of the card
    ///   - onTap: Optional tap handler
    ///   - onLongPress: Optional long press handler
    ///   - content: The content to display inside the card
    public init(
        style: Style = .default,
        size: Size = .medium,
        onTap: (() -> Void)? = nil,
        onLongPress: (() -> Void)? = nil,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.style = style
        self.size = size
        self.onTap = onTap
        self.onLongPress = onLongPress
        self.content = content
    }
    
    // MARK: - Body
    
    public var body: some View {
        Group {
            if onTap != nil || onLongPress != nil {
                Button(action: { onTap?() }) {
                    cardContent
                }
                .buttonStyle(PlainButtonStyle())
                .onLongPressGesture {
                    onLongPress?()
                }
                .scaleEffect(isPressed ? 0.98 : 1.0)
                .onPressGesture(
                    onPress: { isPressed = true },
                    onRelease: { isPressed = false }
                )
            } else {
                cardContent
            }
        }
    }
    
    // MARK: - Private Views
    
    private var cardContent: some View {
        content()
            .frame(
                width: customWidth,
                height: customHeight
            )
            .background(style.backgroundColor)
            .overlay(
                RoundedRectangle(cornerRadius: size.cornerRadius)
                    .stroke(style.borderColor ?? Color.clear, lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: size.cornerRadius))
            .shadow(
                color: DesignSystem.Card.Colors.shadow,
                radius: style.shadowRadius,
                x: DesignSystem.Card.Layout.shadowOffset.width,
                y: DesignSystem.Card.Layout.shadowOffset.height
            )
            .animation(.easeInOut(duration: 0.15), value: isPressed)
    }
    
    // MARK: - Helper Properties
    
    private var customWidth: CGFloat? {
        if case .custom(let width, _) = size {
            return width
        }
        return nil
    }
    
    private var customHeight: CGFloat? {
        if case .custom(_, let height) = size {
            return height
        }
        return nil
    }
}

// MARK: - Custom Button Style Helper

private extension View {
    func onPressGesture(onPress: @escaping () -> Void, onRelease: @escaping () -> Void) -> some View {
        self.simultaneousGesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in onPress() }
                .onEnded { _ in onRelease() }
        )
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        // Default card
        DSCard {
            VStack(spacing: 8) {
                Text("Default Card")
                    .font(.headline)
                Text("This is a default card with basic styling")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
        }
        
        // Elevated card with tap
        DSCard(style: .elevated, onTap: {
            print("Elevated card tapped")
        }) {
            HStack {
                Image(systemName: "star.fill")
                    .foregroundColor(.yellow)
                Text("Elevated Card")
                    .font(.headline)
                Spacer()
            }
            .padding()
        }
        
        // Outlined card
        DSCard(style: .outlined, size: .large) {
            VStack {
                Image(systemName: "photo")
                    .font(.largeTitle)
                    .foregroundColor(.blue)
                Text("Outlined Card")
                    .font(.title2)
                    .fontWeight(.semibold)
            }
            .padding()
        }
        
        // Custom size card
        DSCard(
            style: .elevated,
            size: .custom(width: 200, height: 100)
        ) {
            Text("Custom Size")
                .font(.headline)
        }
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}