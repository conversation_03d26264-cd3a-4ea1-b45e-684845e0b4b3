//
//  ShimmerEffect.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//

import SwiftUI

/// A view modifier that adds a shimmer effect for loading states
struct ShimmerEffect: ViewModifier {
  @State private var isAnimating = false
  
  func body(content: Content) -> some View {
    content
      .overlay(
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(colors: [
                Color.clear,
                Color.white.opacity(0.3),
                Color.clear
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .rotationEffect(.degrees(30))
          .offset(x: isAnimating ? 200 : -200)
          .animation(
            Animation.linear(duration: 1.5)
              .repeatForever(autoreverses: false),
            value: isAnimating
          )
      )
      .clipped()
      .onAppear {
        isAnimating = true
      }
  }
}

extension View {
  /// Adds a shimmer effect to the view
  func shimmer() -> some View {
    modifier(ShimmerEffect())
  }
}
