//
//  FileUploadModels.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/20.
//

import Foundation

// MARK: - File Upload Request

/// File upload request model
public struct FileUploadRequest: Codable {
  /// MIME type
  public let mimeType: String

  /// Base64 encoded file data
  public let base64Data: String

  /// File path prefix
  public let prefix: String

  /// Filename
  public let fileName: String

  /// Initialize file upload request
  /// - Parameters:
  ///   - mimeType: MIME type, e.g. "image/jpeg"
  ///   - base64Data: Base64 encoded file data
  ///   - prefix: File path prefix, e.g. "uploads/images"
  ///   - fileName: Filename, e.g. "profile-picture"
  public init(mimeType: String, base64Data: String, prefix: String, fileName: String) {
    self.mimeType = mimeType
    self.base64Data = base64Data
    self.prefix = prefix
    self.fileName = fileName
  }

  /// Create upload request from file data
  /// - Parameters:
  ///   - data: File data
  ///   - mimeType: MI<PERSON> type
  ///   - prefix: File path prefix
  ///   - fileName: Filename
  /// - Returns: File upload request
  public static func from(data: Data, mimeType: String, prefix: String, fileName: String)
    -> FileUploadRequest
  {
    let base64Data = data.base64EncodedString()
    return FileUploadRequest(
      mimeType: mimeType,
      base64Data: base64Data,
      prefix: prefix,
      fileName: fileName
    )
  }
}

// MARK: - File Upload Response

/// File upload response model
public struct FileUploadResponse: Codable {
  /// Uploaded file URL
  public let url: String

  /// Initialize file upload response
  /// - Parameter url: File URL
  public init(url: String) {
    self.url = url
  }

  /// Get URL object
  public var urlObject: URL? {
    return URL(string: url)
  }
}

// MARK: - File Upload Error

/// File upload error
public enum FileUploadError: Error, LocalizedError {
  /// Invalid file data
  case invalidFileData
  /// Invalid MIME type
  case invalidMimeType
  /// File too large
  case fileTooLarge(maxSize: Int)
  /// Unsupported file type
  case unsupportedFileType(String)
  /// Network error
  case networkError(Error)
  /// API error
  case apiError(statusCode: Int, message: String)
  /// Parsing error
  case parsingError(Error)
  /// Unknown error
  case unknown(Error)

  public var errorDescription: String? {
    switch self {
    case .invalidFileData:
      return "Invalid file data"
    case .invalidMimeType:
      return "Invalid MIME type"
    case .fileTooLarge(let maxSize):
      return "File too large, maximum supported \(maxSize) bytes"
    case .unsupportedFileType(let type):
      return "Unsupported file type: \(type)"
    case .networkError(let error):
      return "Network error: \(error.localizedDescription)"
    case .apiError(let statusCode, let message):
      return "API error (\(statusCode)): \(message)"
    case .parsingError(let error):
      return "Parsing error: \(error.localizedDescription)"
    case .unknown(let error):
      return "Unknown error: \(error.localizedDescription)"
    }
  }
}

// MARK: - Upload with Asset Models

/// 上传文件并创建资产请求模型
public struct UploadWithAssetRequest: Codable {
  /// MIME类型
  public let mimeType: String

  /// Base64编码的文件数据
  public let base64Data: String

  /// 文件路径前缀
  public let prefix: String

  /// 文件名
  public let fileName: String

  /// 来源类型：AI生成 或 用户上传
  public let sourceType: SourceType

  /// 关联的任务ID（仅AI生成内容）
  public let sourceTaskId: String?

  /// AI生成的prompt（仅AI生成内容）
  public let generationPrompt: String?

  /// 标签（可选）
  public let tags: [String]?

  /// 描述（可选）
  public let description: String?

  /// 元数据（可选）
  public let metadata: [String: AnyCodable]?

  /// 资产状态（可选，默认为active）
  public let status: AssetStatus?

  /// 是否公开（可选，默认为false）
  public let isPublic: Bool?

  /// 初始化上传文件并创建资产请求
  /// - Parameters:
  ///   - mimeType: MIME类型
  ///   - base64Data: Base64编码的文件数据
  ///   - prefix: 文件路径前缀
  ///   - fileName: 文件名
  ///   - sourceType: 来源类型
  ///   - sourceTaskId: 关联的任务ID
  ///   - generationPrompt: AI生成的prompt
  ///   - tags: 标签
  ///   - description: 描述
  ///   - metadata: 元数据
  ///   - status: 资产状态
  ///   - isPublic: 是否公开
  public init(
    mimeType: String,
    base64Data: String,
    prefix: String,
    fileName: String,
    sourceType: SourceType,
    sourceTaskId: String? = nil,
    generationPrompt: String? = nil,
    tags: [String]? = nil,
    description: String? = nil,
    metadata: [String: AnyCodable]? = nil,
    status: AssetStatus? = nil,
    isPublic: Bool? = nil
  ) {
    self.mimeType = mimeType
    self.base64Data = base64Data
    self.prefix = prefix
    self.fileName = fileName
    self.sourceType = sourceType
    self.sourceTaskId = sourceTaskId
    self.generationPrompt = generationPrompt
    self.tags = tags
    self.description = description
    self.metadata = metadata
    self.status = status
    self.isPublic = isPublic
  }

  /// 从文件数据创建上传并创建资产请求
  /// - Parameters:
  ///   - data: 文件数据
  ///   - mimeType: MIME类型
  ///   - prefix: 文件路径前缀
  ///   - fileName: 文件名
  ///   - sourceType: 来源类型
  ///   - sourceTaskId: 关联的任务ID
  ///   - generationPrompt: AI生成的prompt
  ///   - tags: 标签
  ///   - description: 描述
  ///   - metadata: 元数据
  ///   - status: 资产状态
  ///   - isPublic: 是否公开
  /// - Returns: 上传文件并创建资产请求
  public static func from(
    data: Data,
    mimeType: String,
    prefix: String,
    fileName: String,
    sourceType: SourceType,
    sourceTaskId: String? = nil,
    generationPrompt: String? = nil,
    tags: [String]? = nil,
    description: String? = nil,
    metadata: [String: AnyCodable]? = nil,
    status: AssetStatus? = nil,
    isPublic: Bool? = nil
  ) -> UploadWithAssetRequest {
    let base64Data = data.base64EncodedString()
    return UploadWithAssetRequest(
      mimeType: mimeType,
      base64Data: base64Data,
      prefix: prefix,
      fileName: fileName,
      sourceType: sourceType,
      sourceTaskId: sourceTaskId,
      generationPrompt: generationPrompt,
      tags: tags,
      description: description,
      metadata: metadata,
      status: status,
      isPublic: isPublic
    )
  }
}

// MARK: - Asset Status and Source Type Enums

/// 资产状态枚举
public enum AssetStatus: String, Codable, CaseIterable {
  case active = "active"
  case archived = "archived"
  case deleted = "deleted"
}

/// 来源类型枚举
public enum SourceType: String, Codable, CaseIterable {
  case aiGenerated = "ai_generated"
  case userUpload = "user_upload"
}

/// 资产响应模型 (已弃用，使用 UserAsset)
@available(*, deprecated, renamed: "UserAsset")
public typealias AssetResponse = UserAsset

/// AnyCodable类型，用于处理任意类型的JSON数据
public struct AnyCodable: Codable {
  public let value: Any

  public init<T>(_ value: T?) {
    self.value = value ?? ()
  }
}

extension AnyCodable: Equatable {
  public static func == (lhs: AnyCodable, rhs: AnyCodable) -> Bool {
    switch (lhs.value, rhs.value) {
    case is (Void, Void):
      return true
    case let (lhs as Bool, rhs as Bool):
      return lhs == rhs
    case let (lhs as Int, rhs as Int):
      return lhs == rhs
    case let (lhs as Int8, rhs as Int8):
      return lhs == rhs
    case let (lhs as Int16, rhs as Int16):
      return lhs == rhs
    case let (lhs as Int32, rhs as Int32):
      return lhs == rhs
    case let (lhs as Int64, rhs as Int64):
      return lhs == rhs
    case let (lhs as UInt, rhs as UInt):
      return lhs == rhs
    case let (lhs as UInt8, rhs as UInt8):
      return lhs == rhs
    case let (lhs as UInt16, rhs as UInt16):
      return lhs == rhs
    case let (lhs as UInt32, rhs as UInt32):
      return lhs == rhs
    case let (lhs as UInt64, rhs as UInt64):
      return lhs == rhs
    case let (lhs as Float, rhs as Float):
      return lhs == rhs
    case let (lhs as Double, rhs as Double):
      return lhs == rhs
    case let (lhs as String, rhs as String):
      return lhs == rhs
    case let (lhs as [String: AnyCodable], rhs as [String: AnyCodable]):
      return lhs == rhs
    case let (lhs as [AnyCodable], rhs as [AnyCodable]):
      return lhs == rhs
    default:
      return false
    }
  }
}

extension AnyCodable {
  public init(from decoder: Decoder) throws {
    let container = try decoder.singleValueContainer()

    if container.decodeNil() {
      self.init(())
    } else if let bool = try? container.decode(Bool.self) {
      self.init(bool)
    } else if let int = try? container.decode(Int.self) {
      self.init(int)
    } else if let uint = try? container.decode(UInt.self) {
      self.init(uint)
    } else if let double = try? container.decode(Double.self) {
      self.init(double)
    } else if let string = try? container.decode(String.self) {
      self.init(string)
    } else if let array = try? container.decode([AnyCodable].self) {
      self.init(array)
    } else if let dictionary = try? container.decode([String: AnyCodable].self) {
      self.init(dictionary)
    } else {
      throw DecodingError.dataCorruptedError(
        in: container, debugDescription: "AnyCodable value cannot be decoded")
    }
  }

  public func encode(to encoder: Encoder) throws {
    var container = encoder.singleValueContainer()

    switch value {
    case is Void:
      try container.encodeNil()
    case let bool as Bool:
      try container.encode(bool)
    case let int as Int:
      try container.encode(int)
    case let int8 as Int8:
      try container.encode(int8)
    case let int16 as Int16:
      try container.encode(int16)
    case let int32 as Int32:
      try container.encode(int32)
    case let int64 as Int64:
      try container.encode(int64)
    case let uint as UInt:
      try container.encode(uint)
    case let uint8 as UInt8:
      try container.encode(uint8)
    case let uint16 as UInt16:
      try container.encode(uint16)
    case let uint32 as UInt32:
      try container.encode(uint32)
    case let uint64 as UInt64:
      try container.encode(uint64)
    case let float as Float:
      try container.encode(float)
    case let double as Double:
      try container.encode(double)
    case let string as String:
      try container.encode(string)
    case let array as [AnyCodable]:
      try container.encode(array)
    case let dictionary as [String: AnyCodable]:
      try container.encode(dictionary)
    default:
      let context = EncodingError.Context(
        codingPath: container.codingPath, debugDescription: "AnyCodable value cannot be encoded")
      throw EncodingError.invalidValue(value, context)
    }
  }
}

// MARK: - Asset Query Models

/// 资产列表查询参数
public struct AssetListQuery: Codable {
  /// 页码（从1开始）
  public let page: Int

  /// 每页数量（1-100）
  public let limit: Int

  /// MIME类型过滤 (Web标准) - 支持通配符如 "image/*"
  public let type: String?

  /// 来源类型过滤
  public let sourceType: SourceType?

  /// 状态过滤
  public let status: AssetStatus?

  /// 标签过滤（逗号分隔）
  public let tags: String?

  /// 搜索关键词（在文件名或描述中搜索）
  public let search: String?

  /// 初始化资产列表查询参数
  /// - Parameters:
  ///   - page: 页码，默认为1
  ///   - limit: 每页数量，默认为20
  ///   - type: MIME类型过滤
  ///   - sourceType: 来源类型过滤
  ///   - status: 状态过滤
  ///   - tags: 标签过滤
  ///   - search: 搜索关键词
  public init(
    page: Int = 1,
    limit: Int = 20,
    type: String? = nil,
    sourceType: SourceType? = nil,
    status: AssetStatus? = nil,
    tags: String? = nil,
    search: String? = nil
  ) {
    self.page = max(1, page)
    self.limit = min(100, max(1, limit))
    self.type = type
    self.sourceType = sourceType
    self.status = status
    self.tags = tags
    self.search = search
  }
}

/// 分页信息
public struct PaginationInfo: Codable {
  /// 当前页码
  public let page: Int

  /// 每页数量
  public let limit: Int

  /// 总数量
  public let total: Int

  /// 总页数
  public let totalPages: Int

  /// 是否有下一页
  public let hasNext: Bool

  /// 是否有上一页
  public let hasPrev: Bool

  /// 初始化分页信息
  /// - Parameters:
  ///   - page: 当前页码
  ///   - limit: 每页数量
  ///   - total: 总数量
  public init(page: Int, limit: Int, total: Int) {
    self.page = page
    self.limit = limit
    self.total = total
    self.totalPages = max(1, Int(ceil(Double(total) / Double(limit))))
    self.hasNext = page < totalPages
    self.hasPrev = page > 1
  }
}

/// 用户资产查询结果
public struct UserAssetsResult: Codable {
  /// 资产列表
  public let assets: [UserAsset]

  /// 分页信息
  public let pagination: PaginationInfo

  /// 初始化用户资产查询结果
  /// - Parameters:
  ///   - assets: 资产列表
  ///   - pagination: 分页信息
  public init(assets: [UserAsset], pagination: PaginationInfo) {
    self.assets = assets
    self.pagination = pagination
  }
}


// MARK: - Asset Create and Update Models

/// 创建资产数据模型（不包含id和createdAt）
public struct CreateAssetData: Codable {
  // MARK: - 存储信息

  /// 缩略图URL（可选）
  public let thumbnailUrl: String?

  /// 存储路径
  public let path: String

  /// 存储桶名称
  public let bucket: String

  // MARK: - Web 标准字段 (遵循 File API)

  /// 文件名
  public let name: String

  /// 文件大小 (bytes)
  public let size: Int

  /// MIME 类型
  public let type: String

  /// 访问 URL
  public let url: String

  // MARK: - 业务分类字段

  /// 来源类型：AI生成 或 用户上传
  public let sourceType: SourceType

  /// 关联的任务ID（仅AI生成内容）
  public let sourceTaskId: String?

  /// AI生成的prompt（仅AI生成内容）
  public let generationPrompt: String?

  // MARK: - 社交统计字段

  /// 点赞数（默认为0）
  public let likeCount: Int

  /// 收藏数（默认为0）
  public let favoriteCount: Int

  // MARK: - 元数据字段

  /// 标签数组
  public let tags: [String]?

  /// 资产描述
  public let description: String?

  /// 扩展元数据
  public let metadata: [String: AnyCodable]?

  // MARK: - 状态管理

  /// 资产状态（默认为active）
  public let status: AssetStatus?

  /// 是否公开访问（默认为false）
  public let isPublic: Bool?

  /// 初始化创建资产数据
  public init(
    thumbnailUrl: String? = nil,
    path: String,
    bucket: String,
    name: String,
    size: Int,
    type: String,
    url: String,
    sourceType: SourceType,
    sourceTaskId: String? = nil,
    generationPrompt: String? = nil,
    likeCount: Int = 0,
    favoriteCount: Int = 0,
    tags: [String]? = nil,
    description: String? = nil,
    metadata: [String: AnyCodable]? = nil,
    status: AssetStatus? = .active,
    isPublic: Bool? = false
  ) {
    self.thumbnailUrl = thumbnailUrl
    self.path = path
    self.bucket = bucket
    self.name = name
    self.size = size
    self.type = type
    self.url = url
    self.sourceType = sourceType
    self.sourceTaskId = sourceTaskId
    self.generationPrompt = generationPrompt
    self.likeCount = likeCount
    self.favoriteCount = favoriteCount
    self.tags = tags
    self.description = description
    self.metadata = metadata
    self.status = status
    self.isPublic = isPublic
  }
}

/// 更新资产数据模型
public struct UpdateAssetData: Codable {
  /// 文件名更新
  public let name: String?

  /// 来源类型更新
  public let sourceType: SourceType?

  /// 关联的任务ID更新
  public let sourceTaskId: String?

  /// AI生成的prompt更新
  public let generationPrompt: String?

  /// 点赞数更新（通常由系统自动更新）
  public let likeCount: Int?

  /// 收藏数更新（通常由系统自动更新）
  public let favoriteCount: Int?

  /// 标签更新
  public let tags: [String]?

  /// 描述更新
  public let description: String?

  /// 元数据更新
  public let metadata: [String: AnyCodable]?

  /// 状态更新
  public let status: AssetStatus?

  /// 是否公开更新
  public let isPublic: Bool?

  /// 初始化更新资产数据
  public init(
    name: String? = nil,
    sourceType: SourceType? = nil,
    sourceTaskId: String? = nil,
    generationPrompt: String? = nil,
    likeCount: Int? = nil,
    favoriteCount: Int? = nil,
    tags: [String]? = nil,
    description: String? = nil,
    metadata: [String: AnyCodable]? = nil,
    status: AssetStatus? = nil,
    isPublic: Bool? = nil
  ) {
    self.name = name
    self.sourceType = sourceType
    self.sourceTaskId = sourceTaskId
    self.generationPrompt = generationPrompt
    self.likeCount = likeCount
    self.favoriteCount = favoriteCount
    self.tags = tags
    self.description = description
    self.metadata = metadata
    self.status = status
    self.isPublic = isPublic
  }
}

// MARK: - Asset Delete Response

/// 资产删除响应模型
public struct AssetDeleteResponse: Codable {
  /// 成功消息
  public let message: String

  /// 被删除的资产ID
  public let id: String

  /// 初始化资产删除响应
  /// - Parameters:
  ///   - message: 成功消息
  ///   - id: 被删除的资产ID
  public init(message: String, id: String) {
    self.message = message
    self.id = id
  }
}

// MARK: - File Upload Configuration

/// 文件上传配置
public struct FileUploadConfiguration {
  /// 最大文件大小（字节）
  public let maxFileSize: Int

  /// 最小文件大小（字节）
  public let minFileSize: Int

  /// 支持的MIME类型
  public let supportedMimeTypes: Set<String>

  /// 默认文件路径前缀
  public let defaultPrefix: String

  /// 最大图片大小（字节）
  public let maxImageSize: Int

  /// 最大视频大小（字节）
  public let maxVideoSize: Int

  /// 默认配置
  public static let `default` = FileUploadConfiguration(
    maxFileSize: 100 * 1024 * 1024,  // 100MB
    minFileSize: 1,  // 1 byte
    supportedMimeTypes: [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "video/mp4",
      "video/quicktime",
      "audio/mpeg",
      "audio/wav",
      "application/pdf",
      "text/plain",
    ],
    defaultPrefix: "uploads/images",
    maxImageSize: 50 * 1024 * 1024,  // 50MB
    maxVideoSize: 100 * 1024 * 1024  // 100MB
  )

  /// 初始化文件上传配置
  /// - Parameters:
  ///   - maxFileSize: 最大文件大小
  ///   - minFileSize: 最小文件大小
  ///   - supportedMimeTypes: 支持的MIME类型
  ///   - defaultPrefix: 默认文件路径前缀
  ///   - maxImageSize: 最大图片大小
  ///   - maxVideoSize: 最大视频大小
  public init(
    maxFileSize: Int,
    minFileSize: Int = 1,
    supportedMimeTypes: Set<String>,
    defaultPrefix: String,
    maxImageSize: Int? = nil,
    maxVideoSize: Int? = nil
  ) {
    self.maxFileSize = maxFileSize
    self.minFileSize = minFileSize
    self.supportedMimeTypes = supportedMimeTypes
    self.defaultPrefix = defaultPrefix
    self.maxImageSize = maxImageSize ?? maxFileSize
    self.maxVideoSize = maxVideoSize ?? maxFileSize
  }

  /// 验证文件是否符合配置要求
  /// - Parameters:
  ///   - data: 文件数据
  ///   - mimeType: MIME类型
  /// - Throws: FileUploadError 如果文件不符合要求
  public func validate(data: Data, mimeType: String) throws {
    // 检查文件大小
    if data.count > maxFileSize {
      throw FileUploadError.fileTooLarge(maxSize: maxFileSize)
    }

    // 检查MIME类型
    if !supportedMimeTypes.contains(mimeType) {
      throw FileUploadError.unsupportedFileType(mimeType)
    }
  }
}
