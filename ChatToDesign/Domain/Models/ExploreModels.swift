//
//  ExploreModels.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/7.
//

import Foundation

// MARK: - Explore Query

/// Explore query parameters
public struct ExploreQuery: Codable {
  /// Page number
  public let page: Int

  /// Items per page
  public let limit: Int

  /// Source type: all, ai_generated, user_uploaded
  public let sourceType: String

  /// Sort method: latest, popular, trending
  public let sortBy: String

  /// Initialize Explore query parameters
  /// - Parameters:
  ///   - page: Page number, default 1
  ///   - limit: Items per page, default 20
  ///   - sourceType: Source type, default "all"
  ///   - sortBy: Sort method, default "latest"
  public init(
    page: Int = 1,
    limit: Int = 20,
    sourceType: String = "all",
    sortBy: String = "latest"
  ) {
    self.page = page
    self.limit = limit
    self.sourceType = sourceType
    self.sortBy = sortBy
  }
}

// MARK: - Explore Response

/// Explore response data
public struct ExploreResponse: Codable {
  /// Content item list
  public let items: [ExploreItem]

  /// Initialize Explore response
  /// - Parameter items: Content item list
  public init(items: [ExploreItem]) {
    self.items = items
  }
}

// MARK: - Explore Item

/// Explore item data
public struct ExploreItem: Identifiable, Codable, Hashable {
  /// Item unique identifier
  public let id: String

  /// File type (MIME type)
  public let type: String

  /// Source type: ai_generated or user_uploaded
  public let sourceType: String

  /// User display name
  public let userDisplayName: String?

  /// User avatar URL
  public let userPhotoURL: String?

  /// Content URL
  public let url: String

  /// Thumbnail URL
  public let thumbnailUrl: String?

  /// File size
  public let size: Int

  /// AI generation prompt (AI generated content only)
  public let generationPrompt: String?

  /// Like count
  public let likeCount: Int

  /// Favorite count
  public let favoriteCount: Int

  /// Creation time
  public let createdAt: String

  /// Initialize Explore item
  /// - Parameters:
  ///   - id: Item unique identifier
  ///   - type: File type
  ///   - sourceType: Source type
  ///   - userDisplayName: User display name
  ///   - userPhotoURL: User avatar URL
  ///   - url: Content URL
  ///   - thumbnailUrl: Thumbnail URL
  ///   - size: File size
  ///   - generationPrompt: AI generation prompt
  ///   - likeCount: Like count
  ///   - favoriteCount: Favorite count
  ///   - createdAt: Creation time
  public init(
    id: String,
    type: String,
    sourceType: String,
    userDisplayName: String? = nil,
    userPhotoURL: String? = nil,
    url: String,
    thumbnailUrl: String? = nil,
    size: Int,
    generationPrompt: String? = nil,
    likeCount: Int = 0,
    favoriteCount: Int = 0,
    createdAt: String
  ) {
    self.id = id
    self.type = type
    self.sourceType = sourceType
    self.userDisplayName = userDisplayName
    self.userPhotoURL = userPhotoURL
    self.url = url
    self.thumbnailUrl = thumbnailUrl
    self.size = size
    self.generationPrompt = generationPrompt
    self.likeCount = likeCount
    self.favoriteCount = favoriteCount
    self.createdAt = createdAt
  }
  
  // MARK: - Computed Properties
  
  /// Whether it's video content
  public var isVideo: Bool {
    return type.hasPrefix("video/")
  }

  /// Whether it's image content
  public var isImage: Bool {
    return type.hasPrefix("image/")
  }

  /// Whether it's AI generated content
  public var isAIGenerated: Bool {
    return sourceType == "ai_generated"
  }

  /// Whether it's user uploaded content
  public var isUserUploaded: Bool {
    return sourceType == "user_uploaded"
  }

  /// Formatted file size
  public var formattedSize: String {
    let formatter = ByteCountFormatter()
    formatter.allowedUnits = [.useMB, .useKB, .useBytes]
    formatter.countStyle = .file
    return formatter.string(fromByteCount: Int64(size))
  }

  /// Formatted creation time
  public var formattedCreatedAt: String? {
    let formatter = ISO8601DateFormatter()
    guard let date = formatter.date(from: createdAt) else { return nil }

    let displayFormatter = DateFormatter()
    displayFormatter.dateStyle = .medium
    displayFormatter.timeStyle = .short
    return displayFormatter.string(from: date)
  }

  /// Relative time display (e.g., 2 hours ago)
  public var relativeTimeString: String? {
    let formatter = ISO8601DateFormatter()
    guard let date = formatter.date(from: createdAt) else { return nil }

    let relativeFormatter = RelativeDateTimeFormatter()
    relativeFormatter.unitsStyle = .abbreviated
    return relativeFormatter.localizedString(for: date, relativeTo: Date())
  }
  
  // MARK: - Hashable
  
  public func hash(into hasher: inout Hasher) {
    hasher.combine(id)
  }
  
  public static func == (lhs: ExploreItem, rhs: ExploreItem) -> Bool {
    return lhs.id == rhs.id
  }
}

// MARK: - Source Type Enum

/// Source type enumeration
public enum ExploreSourceType: String, CaseIterable {
  case all = "all"
  case aiGenerated = "ai_generated"
  case userUploaded = "user_uploaded"

  /// Display name
  public var displayName: String {
    switch self {
    case .all:
      return "All"
    case .aiGenerated:
      return "AI Generated"
    case .userUploaded:
      return "User Uploaded"
    }
  }
}

// MARK: - Sort By Enum

/// Sort method enumeration
public enum ExploreSortBy: String, CaseIterable {
  case latest = "latest"
  case popular = "popular"
  case trending = "trending"

  /// Display name
  public var displayName: String {
    switch self {
    case .latest:
      return "Latest"
    case .popular:
      return "Popular"
    case .trending:
      return "Trending"
    }
  }
}
