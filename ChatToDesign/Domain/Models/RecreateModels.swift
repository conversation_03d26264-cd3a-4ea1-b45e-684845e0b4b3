//
//  RecreateModels.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/4.
//

import Foundation

// MARK: - CreateVideoFromTemplateParams

/// CreateVideoFromTemplatePage parameters encapsulation
public struct CreateVideoFromTemplateParams {
  public let templateId: String
  public let demoUrl: String
  public let inputImageUrls: [String]
  public let inputImageLimits: Int
  public let inputGuide: String
  public let demoPrompt: String
  
  public init(
    templateId: String,
    demoUrl: String,
    inputImageUrls: [String],
    inputImageLimits: Int,
    inputGuide: String,
    demoPrompt: String
  ) {
    self.templateId = templateId
    self.demoUrl = demoUrl
    self.inputImageUrls = inputImageUrls
    self.inputImageLimits = inputImageLimits
    self.inputGuide = inputGuide
    self.demoPrompt = demoPrompt
  }
}

// MARK: - OriginalTaskData

/// Original task data structure (parsed from metadata)
public struct OriginalTaskData: Codable {
  public let inputData: InputData
  
  public init(inputData: InputData) {
    self.inputData = inputData
  }
  
  public struct InputData: Codable {
    public let template: String?
    public let inputImageUrl: [String]?
    public let prompt: String?
    public let aspectRatio: String?
    public let imageCount: Int?
    
    public init(
      template: String? = nil,
      inputImageUrl: [String]? = nil,
      prompt: String? = nil,
      aspectRatio: String? = nil,
      imageCount: Int? = nil
    ) {
      self.template = template
      self.inputImageUrl = inputImageUrl
      self.prompt = prompt
      self.aspectRatio = aspectRatio
      self.imageCount = imageCount
    }
  }
}
