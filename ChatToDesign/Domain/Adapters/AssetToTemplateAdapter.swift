//
//  AssetToTemplateAdapter.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/4.
//

import Foundation

/// Asset to template page parameters adapter
public struct AssetToTemplateAdapter {

  // MARK: - Public Methods

  /// Convert UserAsset to CreateVideoFromTemplatePage required parameters
  public static func convert(from asset: UserAsset) -> CreateVideoFromTemplateParams {
    let originalTaskData = extractOriginalTaskData(from: asset.metadata)

    let params = CreateVideoFromTemplateParams(
      templateId: extractTemplateId(from: originalTaskData, asset: asset),
      demoUrl: extractDemoUrl(from: asset),
      inputImageUrls: extractInputImageUrls(from: originalTaskData, asset: asset),
      inputImageLimits: extractInputImageLimits(from: originalTaskData, asset: asset),
      inputGuide: generateInputGuide(for: asset),
      demoPrompt: extractDemoPrompt(from: originalTaskData, asset: asset)
    )

    return validateAndSanitize(params)
  }

  // MARK: - Private Methods

  /// Extract original task data from metadata
  private static func extractOriginalTaskData(from metadata: [String: AnyCodable]?)
    -> OriginalTaskData?
  {
    guard let metadata = metadata,
      let originalTaskDataValue = metadata["originalTaskData"]?.value,
      let originalTaskDataDict = originalTaskDataValue as? [String: Any],
      let inputDataDict = originalTaskDataDict["inputData"] as? [String: Any]
    else {
      return nil
    }

    let inputData = OriginalTaskData.InputData(
      template: inputDataDict["template"] as? String,
      inputImageUrl: inputDataDict["inputImageUrl"] as? [String],
      prompt: inputDataDict["prompt"] as? String,
      aspectRatio: inputDataDict["aspectRatio"] as? String,
      imageCount: inputDataDict["imageCount"] as? Int
    )

    return OriginalTaskData(inputData: inputData)
  }

  /// Extract template ID
  private static func extractTemplateId(from taskData: OriginalTaskData?, asset: UserAsset)
    -> String
  {
    Logger.debug("asset.id: \(asset.id)")
    if let template = taskData?.inputData.template, !template.isEmpty {
      Logger.debug("extractTemplateId: \(template)")
      return template
    }
    return "recreate_\(asset.id)"
  }

  /// Extract demo URL
  private static func extractDemoUrl(from asset: UserAsset) -> String {
    return asset.url
  }

  /// Extract input image URLs
  private static func extractInputImageUrls(from taskData: OriginalTaskData?, asset: UserAsset)
    -> [String]
  {
    if let inputImageUrls = taskData?.inputData.inputImageUrl, !inputImageUrls.isEmpty {
      return inputImageUrls
    }

    // Fallback: decide based on asset type
    if asset.type.hasPrefix("image/") {
      return [asset.url]
    } else if asset.type.hasPrefix("video/") {
      return [asset.thumbnailUrl ?? asset.url]
    }

    return [asset.url]
  }

  /// Extract input image limits count
  private static func extractInputImageLimits(
    from taskData: OriginalTaskData?, asset: UserAsset
  ) -> Int {
    if let imageCount = taskData?.inputData.imageCount, imageCount > 0 {
      return imageCount
    }

    // Provide default values based on asset type
    return asset.type.hasPrefix("video/") ? 4 : 1
  }

  /// Generate input guide text
  private static func generateInputGuide(for asset: UserAsset) -> String {
    if asset.type.hasPrefix("image/") {
      return "Recreate video content based on this image. You can upload new images or modify using the original image"
    } else if asset.type.hasPrefix("video/") {
      return "Recreate similar content based on this video. Upload related images to generate new videos"
    }

    return "Recreate content by uploading images to generate new videos"
  }

  /// Extract demo prompt
  private static func extractDemoPrompt(from taskData: OriginalTaskData?, asset: UserAsset)
    -> String
  {
    // Prioritize using prompt from original task data
    if let prompt = taskData?.inputData.prompt, !prompt.isEmpty {
      return prompt
    }

    // Fallback: use asset's generation prompt
    if let generationPrompt = asset.generationPrompt, !generationPrompt.isEmpty {
      return generationPrompt
    }

    // Last fallback: generate default prompt
    return generateDefaultPrompt(for: asset)
  }

  /// Generate default prompt
  private static func generateDefaultPrompt(for asset: UserAsset) -> String {
    if asset.type.hasPrefix("image/") {
      return "Create an interesting video based on the uploaded image, adding dynamic effects and transitions"
    } else if asset.type.hasPrefix("video/") {
      return "Recreate similar style video content, maintaining the original visual characteristics"
    }

    return "Create compelling video content"
  }

  /// Validate and sanitize parameters
  private static func validateAndSanitize(_ params: CreateVideoFromTemplateParams)
    -> CreateVideoFromTemplateParams
  {
    let validTemplateId = params.templateId.isEmpty ? "default_template" : params.templateId

    let validImageUrls = params.inputImageUrls.compactMap { url -> String? in
      guard !url.isEmpty, URL(string: url) != nil else { return nil }
      return url
    }

    let validLimits = max(1, min(params.inputImageLimits, 10))

    let validDemoUrl = params.demoUrl.isEmpty ? "" : params.demoUrl
    let validInputGuide = params.inputGuide.isEmpty ? "Upload images to create video" : params.inputGuide
    let validDemoPrompt = params.demoPrompt.isEmpty ? "Create video content" : params.demoPrompt

    Logger.debug("🔧 CreateDetailPage: params: \(params)")
    return CreateVideoFromTemplateParams(
      templateId: validTemplateId,
      demoUrl: validDemoUrl,
      inputImageUrls: validImageUrls,
      inputImageLimits: validLimits,
      inputGuide: validInputGuide,
      demoPrompt: validDemoPrompt
    )
  }
}
