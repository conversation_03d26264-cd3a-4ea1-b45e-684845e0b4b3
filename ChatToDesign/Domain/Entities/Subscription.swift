//
//  Subscription.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/15.
//

import Foundation

/// Subscription tier
public enum SubscriptionTier: String, Codable, CaseIterable {
    case free = "free"
    case premium = "premium"
    case pro = "pro"
    
    /// Display name
    public var displayName: String {
        switch self {
        case .free:
            return "Free"
        case .premium:
            return "Premium"
        case .pro:
            return "Pro"
        }
    }

    /// Monthly generation quota
    public var monthlyGenerationQuota: Int {
        switch self {
        case .free:
            return 10
        case .premium:
            return 100
        case .pro:
            return Int.max
        }
    }
    
    /// Whether can access premium features
    public var canAccessPremiumFeatures: Bool {
        return self != .free
    }
}

/// Subscription status
public enum SubscriptionStatus: String, Codable {
    case active = "active"
    case expired = "expired"
    case cancelled = "cancelled"
    case inGracePeriod = "in_grace_period"
    case inBillingRetryPeriod = "in_billing_retry_period"
    case unknown = "unknown"
    
    /// Whether in active state
    public var isActive: Bool {
        switch self {
        case .active, .inGracePeriod, .inBillingRetryPeriod:
            return true
        case .expired, .cancelled, .unknown:
            return false
        }
    }
}

/// Subscription entity
public struct Subscription: Identifiable, Codable, Equatable {
    /// Subscription unique identifier
    public let id: String

    /// Product identifier
    public let productId: String

    /// Subscription status
    public let status: SubscriptionStatus

    /// Subscription tier
    public let tier: SubscriptionTier

    /// Purchase date
    public let purchaseDate: Date

    /// Expiration date
    public let expirationDate: Date?

    /// Whether will auto-renew
    public let willRenew: Bool

    /// Whether in trial period
    public let isInTrialPeriod: Bool

    /// Trial end date
    public let trialEndDate: Date?

    /// Creation time
    public let createdAt: Date

    /// Update time
    public let updatedAt: Date

    /// Initialize subscription
    public init(
        id: String,
        productId: String,
        status: SubscriptionStatus,
        tier: SubscriptionTier,
        purchaseDate: Date,
        expirationDate: Date? = nil,
        willRenew: Bool = false,
        isInTrialPeriod: Bool = false,
        trialEndDate: Date? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.productId = productId
        self.status = status
        self.tier = tier
        self.purchaseDate = purchaseDate
        self.expirationDate = expirationDate
        self.willRenew = willRenew
        self.isInTrialPeriod = isInTrialPeriod
        self.trialEndDate = trialEndDate
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
    
    /// Whether is active subscription
    public var isActive: Bool {
        return status.isActive
    }

    /// Whether expiring soon (within 7 days)
    public var isExpiringSoon: Bool {
        guard let expirationDate = expirationDate else { return false }
        let sevenDaysFromNow = Date().addingTimeInterval(7 * 24 * 60 * 60)
        return expirationDate <= sevenDaysFromNow
    }

    /// Days remaining
    public var daysRemaining: Int? {
        guard let expirationDate = expirationDate else { return nil }
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: Date(), to: expirationDate)
        return components.day
    }
}

// MARK: - Convenience Constructors
extension Subscription {
    /// Create free subscription
    public static func free(userId: String) -> Subscription {
        return Subscription(
            id: "free_\(userId)",
            productId: "free",
            status: .active,
            tier: .free,
            purchaseDate: Date(),
            expirationDate: nil,
            willRenew: false,
            isInTrialPeriod: false,
            trialEndDate: nil
        )
    }
}
