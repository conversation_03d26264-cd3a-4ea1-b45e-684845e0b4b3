import Foundation

// Represents a design item in a browseable list.
// Fetched initially for the home screen grid/list.
struct DesignCard: Identifiable, Codable, Hashable {
  // The unique identifier, shared with DesignDetail.
  let id: String
  let title: String
  // URL for the preview image shown in the list.
  let previewImageURL: URL?

  // Optional: User-specific persistent state if absolutely needed for card display.
  // Typically fetched with the detail instead to keep card loading fast.
  // var isFavorite: Bool?
}

extension DesignCard {
  // Mock data for SwiftUI Previews or testing
  static let mockData = [
    DesignCard(
      id: "trending_card_1", title: "Cyberpunk Alley",
      previewImageURL: URL(
        string:
          "https://twitter-r2.a1d.ai/demo/trending_card_1.png"
      )),
    DesignCard(
      id: "trending_card_2", title: "Watercolor Portrait",
      previewImageURL: URL(
        string:
          "https://twitter-r2.a1d.ai/demo/trending_card_2.png"
      )),
    DesignCard(
      id: "trending_card_3", title: "Abstract Geometry",
      previewImageURL: URL(
        string:
          "https://twitter-r2.a1d.ai/demo/trending_card_3.png"
      )),
    DesignCard(
      id: "trending_card_4", title: "Cyberpunk Alley",
      previewImageURL: URL(
        string:
          "https://twitter-r2.a1d.ai/demo/trending_card_4.png"
      )),
    DesignCard(
      id: "trending_card_5", title: "Watercolor Portrait",
      previewImageURL: URL(
        string:
          "https://twitter-r2.a1d.ai/demo/trending_card_5.png"
      )),
    DesignCard(
      id: "trending_card_6", title: "Abstract Geometry",
      previewImageURL: URL(
        string:
          "https://twitter-r2.a1d.ai/demo/trending_card_6.png"
      )),
    DesignCard(
      id: "trending_card_7", title: "Watercolor Portrait",
      previewImageURL: URL(
        string:
          "https://twitter-r2.a1d.ai/demo/trending_card_7.png"
      )),
    DesignCard(
      id: "trending_card_8", title: "Abstract Geometry",
      previewImageURL: URL(
        string:
          "https://twitter-r2.a1d.ai/demo/trending_card_8.png"
      )),
  ]
}
