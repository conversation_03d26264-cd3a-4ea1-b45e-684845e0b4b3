//
//  UserAsset.swift
//  ChatToDesign
//
//  Created by Refactoring on 2025/1/7.
//

import Foundation

/// User asset entity
/// Represents digital assets owned by users (images, videos, etc.)
public struct UserAsset: Identifiable, Codable, Equatable {
  // MARK: - Core Identity

  /// Asset unique identifier
  public let id: String

  /// Owner user ID
  public let userId: String

  // MARK: - Storage Information

  /// Thumbnail URL
  public let thumbnailUrl: String?

  /// Storage path
  public let path: String

  /// Storage bucket name
  public let bucket: String

  // MARK: - File Properties

  /// Filename
  public let name: String

  /// File size (bytes)
  public let size: Int

  /// MIME type
  public let type: String

  /// Access URL
  public let url: String

  // MARK: - Business Properties

  /// Source type: AI generated or user uploaded
  public let sourceType: SourceType

  /// Associated task ID (AI generated content only)
  public let sourceTaskId: String?

  /// AI generation prompt (AI generated content only)
  public let generationPrompt: String?

  // MARK: - Social Statistics

  /// Like count
  public let likeCount: Int

  /// Favorite count
  public let favoriteCount: Int

  // MARK: - Metadata

  /// Tags array
  public let tags: [String]?

  /// Asset description
  public let description: String?

  /// Extended metadata
  public let metadata: [String: AnyCodable]?

  // MARK: - Timestamps

  /// Creation time
  public let createdAt: Date

  /// Update time
  public let updatedAt: Date

  // MARK: - Status Management

  /// Asset status
  public let status: AssetStatus?

  /// Whether publicly accessible
  public let isPublic: Bool?
}

// MARK: - Business Logic
extension UserAsset {
  /// Whether it's an image asset
  public var isImage: Bool {
    return type.hasPrefix("image/")
  }

  /// Whether it's a video asset
  public var isVideo: Bool {
    return type.hasPrefix("video/")
  }

  /// Whether it's AI generated content
  public var isAIGenerated: Bool {
    return sourceType == .aiGenerated
  }

  /// Whether it can be recreated (based on template)
  public var canRecreate: Bool {
    guard isAIGenerated,
      let metadata = metadata,
      let originalTaskData = metadata["originalTaskData"],
      let taskDataDict = originalTaskData.value as? [String: Any],
      let inputData = taskDataDict["inputData"] as? [String: Any],
      let template = inputData["template"] as? String,
      !template.isEmpty
    else {
      return false
    }
    // Check if contains valid template information
    return true
  }

  /// Whether it's in active status
  public var isActive: Bool {
    return status == .active || status == nil
  }

  /// Get file extension
  public var fileExtension: String {
    return URL(fileURLWithPath: name).pathExtension.lowercased()
  }

  /// Get formatted file size
  public var formattedSize: String {
    return ByteCountFormatter.string(fromByteCount: Int64(size), countStyle: .file)
  }
}

// MARK: - Convenience Properties
extension UserAsset {
  /// Get access URL object
  public var urlObject: URL? {
    return URL(string: url)
  }

  /// Get thumbnail URL object
  public var thumbnailUrlObject: URL? {
    guard let thumbnailUrl = thumbnailUrl else { return nil }
    return URL(string: thumbnailUrl)
  }

  /// Get display name (prefer description, otherwise use filename)
  public var displayName: String {
    return description?.isEmpty == false ? description! : name
  }
}

// MARK: - Factory Methods
extension UserAsset {
  /// Create user uploaded asset
  /// - Parameters:
  ///   - userId: User ID
  ///   - name: Filename
  ///   - size: File size
  ///   - type: MIME type
  ///   - url: Access URL
  ///   - path: Storage path
  ///   - bucket: Storage bucket
  ///   - thumbnailUrl: Thumbnail URL
  ///   - tags: Tags
  ///   - description: Description
  /// - Returns: UserAsset instance
  public static func createUserUpload(
    userId: String,
    name: String,
    size: Int,
    type: String,
    url: String,
    path: String,
    bucket: String,
    thumbnailUrl: String? = nil,
    tags: [String]? = nil,
    description: String? = nil
  ) -> UserAsset {
    return UserAsset(
      id: UUID().uuidString,
      userId: userId,
      thumbnailUrl: thumbnailUrl,
      path: path,
      bucket: bucket,
      name: name,
      size: size,
      type: type,
      url: url,
      sourceType: .userUpload,
      sourceTaskId: nil,
      generationPrompt: nil,
      likeCount: 0,
      favoriteCount: 0,
      tags: tags,
      description: description,
      metadata: nil,
      createdAt: Date(),
      updatedAt: Date(),
      status: .active,
      isPublic: false
    )
  }

  /// Create AI generated asset
  /// - Parameters:
  ///   - userId: User ID
  ///   - name: Filename
  ///   - size: File size
  ///   - type: MIME type
  ///   - url: Access URL
  ///   - path: Storage path
  ///   - bucket: Storage bucket
  ///   - sourceTaskId: Associated task ID
  ///   - generationPrompt: Generation prompt
  ///   - thumbnailUrl: Thumbnail URL
  ///   - metadata: Metadata
  /// - Returns: UserAsset instance
  public static func createAIGenerated(
    userId: String,
    name: String,
    size: Int,
    type: String,
    url: String,
    path: String,
    bucket: String,
    sourceTaskId: String,
    generationPrompt: String?,
    thumbnailUrl: String? = nil,
    metadata: [String: AnyCodable]? = nil
  ) -> UserAsset {
    return UserAsset(
      id: UUID().uuidString,
      userId: userId,
      thumbnailUrl: thumbnailUrl,
      path: path,
      bucket: bucket,
      name: name,
      size: size,
      type: type,
      url: url,
      sourceType: .aiGenerated,
      sourceTaskId: sourceTaskId,
      generationPrompt: generationPrompt,
      likeCount: 0,
      favoriteCount: 0,
      tags: nil,
      description: nil,
      metadata: metadata,
      createdAt: Date(),
      updatedAt: Date(),
      status: .active,
      isPublic: false
    )
  }
}

// MARK: - Custom Decoding
extension UserAsset {
  enum CodingKeys: String, CodingKey {
    case id, userId, thumbnailUrl, path, bucket, name, size, type, url
    case sourceType, sourceTaskId, generationPrompt, likeCount, favoriteCount
    case tags, description, metadata, createdAt, updatedAt, status, isPublic
  }

  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)

    // Basic fields
    id = try container.decode(String.self, forKey: .id)
    userId = try container.decode(String.self, forKey: .userId)
    thumbnailUrl = try container.decodeIfPresent(String.self, forKey: .thumbnailUrl)
    path = try container.decode(String.self, forKey: .path)
    bucket = try container.decode(String.self, forKey: .bucket)
    name = try container.decode(String.self, forKey: .name)
    size = try container.decode(Int.self, forKey: .size)
    type = try container.decode(String.self, forKey: .type)
    url = try container.decode(String.self, forKey: .url)

    // Business fields
    sourceType = try container.decode(SourceType.self, forKey: .sourceType)
    sourceTaskId = try container.decodeIfPresent(String.self, forKey: .sourceTaskId)
    generationPrompt = try container.decodeIfPresent(String.self, forKey: .generationPrompt)
    likeCount = try container.decode(Int.self, forKey: .likeCount)
    favoriteCount = try container.decode(Int.self, forKey: .favoriteCount)

    // Optional fields
    tags = try container.decodeIfPresent([String].self, forKey: .tags)
    description = try container.decodeIfPresent(String.self, forKey: .description)
    metadata = try container.decodeIfPresent([String: AnyCodable].self, forKey: .metadata)
    status = try container.decodeIfPresent(AssetStatus.self, forKey: .status)
    isPublic = try container.decodeIfPresent(Bool.self, forKey: .isPublic)

    // Time fields - handle multiple formats
    if let createdAtTimestamp = try? container.decode(Double.self, forKey: .createdAt) {
      // Server returns timestamp
      createdAt = Date(timeIntervalSince1970: createdAtTimestamp)
    } else if let createdAtString = try? container.decode(String.self, forKey: .createdAt) {
      // Server returns ISO string
      let formatter = ISO8601DateFormatter()
      createdAt = formatter.date(from: createdAtString) ?? Date()
    } else {
      createdAt = Date()
    }

    if let updatedAtTimestamp = try? container.decode(Double.self, forKey: .updatedAt) {
      // Server returns timestamp
      updatedAt = Date(timeIntervalSince1970: updatedAtTimestamp)
    } else if let updatedAtString = try? container.decode(String.self, forKey: .updatedAt) {
      // Server returns ISO string
      let formatter = ISO8601DateFormatter()
      updatedAt = formatter.date(from: updatedAtString) ?? Date()
    } else {
      updatedAt = Date()
    }
  }
}
