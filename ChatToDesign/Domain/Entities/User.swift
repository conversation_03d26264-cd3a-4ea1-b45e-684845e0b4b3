// User.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Foundation

/// User status
public enum UserStatus: String, Codable {
  /// Active
  case active
  /// Email verified
  case verified
  /// Disabled
  case disabled
  /// Deleted
  case deleted
}

/// User entity
/// Represents a user account in the application
public struct User: Identifiable, Equatable, Codable {
  /// User unique identifier
  public let id: String

  /// Email address
  public let email: String

  /// Display name
  public var displayName: String

  /// Avatar URL
  public var photoURL: URL?

  /// User status
  public var status: UserStatus

  /// Creation time
  public let createdAt: Date

  /// Last login time
  public var lastLogin: Date

  /// Bio
  public var bio: String?

  /// Location/region
  public var location: String?

  /// Preferences
  public var preferences: UserPreferences

  /// Whether user is verified
  public var isVerified: Bool {
    return status == .verified
  }

  /// Whether user is active
  public var isActive: Bool {
    return status == .active || status == .verified
  }

  /// Whether user is activated
  /// - Returns: true if user is in active state
  public func isActivated() -> <PERSON><PERSON> {
    return status == .active || status == .verified
  }

  /// Create updated copy of user
  /// - Parameter updateHandler: Update handler
  /// - Returns: Updated user
  public func updated(using updateHandler: (inout User) -> Void) -> User {
    var copy = self
    updateHandler(&copy)
    return copy
  }

}

/// User preferences
public struct UserPreferences: Codable, Equatable {
  /// Theme
  public var theme: String

  /// Language
  public var language: String

  /// Notifications enabled
  public var notificationsEnabled: Bool

  /// Default preferences
  public static let `default` = UserPreferences(
    theme: "system",
    language: Locale.current.language.languageCode?.identifier ?? "en",
    notificationsEnabled: true
  )

  /// Initialize user preferences
  /// - Parameters:
  ///   - theme: Theme, defaults to "system"
  ///   - language: Language, defaults to system language
  ///   - notificationsEnabled: Notifications enabled state, defaults to true
  public init(
    theme: String = "system",
    language: String = Locale.current.language.languageCode?.identifier ?? "en",
    notificationsEnabled: Bool = true
  ) {
    self.theme = theme
    self.language = language
    self.notificationsEnabled = notificationsEnabled
  }
}

// MARK: - Factory methods
extension User {
  /// Create new user
  /// - Parameters:
  ///   - id: User ID
  ///   - email: Email
  ///   - displayName: Display name
  ///   - photoURL: Avatar URL, optional
  /// - Returns: User entity
  public static func create(
    id: String,
    email: String,
    displayName: String,
    photoURL: URL? = nil
  ) -> User {
    let now = Date()
    return User(
      id: id,
      email: email,
      displayName: displayName,
      photoURL: photoURL,
      status: .active,
      createdAt: now,
      lastLogin: now,
      bio: nil,
      location: nil,
      preferences: UserPreferences.default
    )
  }
}
