import Foundation

// Represents the detailed, persistent state of a design.
// Fetched from the server when a user selects a DesignCard, using the shared ID.
struct DesignDetail: Identifiable, Codable, Hashable {
  // The unique identifier, shared with DesignCard.
  let id: String
  let title: String
  let description: String
  // URL for the main, potentially higher-resolution image.
  let primaryImageURL: URL?
  // Information about the author/creator.
  let author: Author?
  // The original prompt used to generate this base design (if applicable).
  let originalPrompt: String?
  // URLs of reference images persistently associated with this design record (if any).
  let originalReferenceImageUrls: [URL]?
  // Optional ID of a chat session persistently linked to this design.
  let associatedChatId: String?

  // User-specific persistent state, fetched along with the detail.
  // Using Optional Bools for flexibility if the state isn't always available (e.g., anonymous users).
  var isLiked: Bool?
  var isBookmarked: Bool?
}

// Represents the author of the design.
struct Author: Identifiable, Codable, Hashable {
  let id: String
  let name: String
  let avatarURL: URL?
}

// --- Mock Data ---

extension DesignDetail {
  // Mock data for SwiftUI Previews or testing
  static var mockData: [DesignDetail] =
    [
      DesignDetail(
        id: "trending_card_1",
        title: "Cyberpunk Alley",
        description:
          "A stunning visualization of a futuristic city alley, bathed in neon light and rain.",
        primaryImageURL: URL(string: "https://twitter-r2.a1d.ai/demo/trending_card_1.png"),  // Use same URL as preview for mock
        author: Author.mockData,  // Use the mock author
        originalPrompt:
          "Futuristic cyberpunk alleyway at night, heavy rain, neon signs reflecting on wet ground, cinematic lighting, high detail",
        originalReferenceImageUrls: [URL(string: "https://example.com/ref/cyber_ref_example.jpg")!],  // Example reference
        associatedChatId: "chat-abc-123",  // Example chat ID
        isLiked: true,  // Example state
        isBookmarked: false  // Example state
      ),
      DesignDetail(
        id: "trending_card_2",
        title: "Cyberpunk Alley",
        description:
          "A stunning visualization of a futuristic city alley, bathed in neon light and rain.",
        primaryImageURL: URL(string: "https://twitter-r2.a1d.ai/demo/trending_card_2.png"),  // Use same URL as preview for mock
        author: Author.mockData,  // Use the mock author
        originalPrompt:
          "Futuristic cyberpunk alleyway at night, heavy rain, neon signs reflecting on wet ground, cinematic lighting, high detail",
        originalReferenceImageUrls: [URL(string: "https://example.com/ref/cyber_ref_example.jpg")!],  // Example reference
        associatedChatId: "chat-abc-123",  // Example chat ID
        isLiked: true,  // Example state
        isBookmarked: false  // Example state
      ),
      DesignDetail(
        id: "trending_card_3",
        title: "Cyberpunk Alley",
        description:
          "A stunning visualization of a futuristic city alley, bathed in neon light and rain.",
        primaryImageURL: URL(string: "https://twitter-r2.a1d.ai/demo/trending_card_3.png"),  // Use same URL as preview for mock
        author: Author.mockData,  // Use the mock author
        originalPrompt:
          "Futuristic cyberpunk alleyway at night, heavy rain, neon signs reflecting on wet ground, cinematic lighting, high detail",
        originalReferenceImageUrls: [URL(string: "https://example.com/ref/cyber_ref_example.jpg")!],  // Example reference
        associatedChatId: "chat-abc-123",  // Example chat ID
        isLiked: true,  // Example state
        isBookmarked: false  // Example state
      ),
      DesignDetail(
        id: "trending_card_4",
        title: "Cyberpunk Alley",
        description:
          "A stunning visualization of a futuristic city alley, bathed in neon light and rain.",
        primaryImageURL: URL(string: "https://twitter-r2.a1d.ai/demo/trending_card_4.png"),  // Use same URL as preview for mock
        author: Author.mockData,  // Use the mock author
        originalPrompt:
          "Futuristic cyberpunk alleyway at night, heavy rain, neon signs reflecting on wet ground, cinematic lighting, high detail",
        originalReferenceImageUrls: [URL(string: "https://example.com/ref/cyber_ref_example.jpg")!],  // Example reference
        associatedChatId: "chat-abc-123",  // Example chat ID
        isLiked: true,  // Example state
        isBookmarked: false  // Example state
      ),
      DesignDetail(
        id: "trending_card_5",
        title: "Cyberpunk Alley",
        description:
          "A stunning visualization of a futuristic city alley, bathed in neon light and rain.",
        primaryImageURL: URL(string: "https://twitter-r2.a1d.ai/demo/trending_card_5.png"),  // Use same URL as preview for mock
        author: Author.mockData,  // Use the mock author
        originalPrompt:
          "Futuristic cyberpunk alleyway at night, heavy rain, neon signs reflecting on wet ground, cinematic lighting, high detail",
        originalReferenceImageUrls: [URL(string: "https://example.com/ref/cyber_ref_example.jpg")!],  // Example reference
        associatedChatId: "chat-abc-123",  // Example chat ID
        isLiked: true,  // Example state
        isBookmarked: false  // Example state
      ),
      DesignDetail(
        id: "trending_card_6",
        title: "Cyberpunk Alley",
        description:
          "A stunning visualization of a futuristic city alley, bathed in neon light and rain.",
        primaryImageURL: URL(string: "https://twitter-r2.a1d.ai/demo/trending_card_6.png"),  // Use same URL as preview for mock
        author: Author.mockData,  // Use the mock author
        originalPrompt:
          "Futuristic cyberpunk alleyway at night, heavy rain, neon signs reflecting on wet ground, cinematic lighting, high detail",
        originalReferenceImageUrls: [URL(string: "https://example.com/ref/cyber_ref_example.jpg")!],  // Example reference
        associatedChatId: "chat-abc-123",  // Example chat ID
        isLiked: true,  // Example state
        isBookmarked: false  // Example state
      ),
      DesignDetail(
        id: "trending_card_7",
        title: "Cyberpunk Alley",
        description:
          "A stunning visualization of a futuristic city alley, bathed in neon light and rain.",
        primaryImageURL: URL(string: "https://twitter-r2.a1d.ai/demo/trending_card_7.png"),  // Use same URL as preview for mock
        author: Author.mockData,  // Use the mock author
        originalPrompt:
          "Futuristic cyberpunk alleyway at night, heavy rain, neon signs reflecting on wet ground, cinematic lighting, high detail",
        originalReferenceImageUrls: [URL(string: "https://example.com/ref/cyber_ref_example.jpg")!],  // Example reference
        associatedChatId: "chat-abc-123",  // Example chat ID
        isLiked: true,  // Example state
        isBookmarked: false  // Example state
      ),
      DesignDetail(
        id: "trending_card_8",
        title: "Cyberpunk Alley",
        description:
          "A stunning visualization of a futuristic city alley, bathed in neon light and rain.",
        primaryImageURL: URL(string: "https://twitter-r2.a1d.ai/demo/trending_card_8.png"),  // Use same URL as preview for mock
        author: Author.mockData,  // Use the mock author
        originalPrompt:
          "Futuristic cyberpunk alleyway at night, heavy rain, neon signs reflecting on wet ground, cinematic lighting, high detail",
        originalReferenceImageUrls: [URL(string: "https://example.com/ref/cyber_ref_example.jpg")!],  // Example reference
        associatedChatId: "chat-abc-123",  // Example chat ID
        isLiked: true,  // Example state
        isBookmarked: false  // Example state
      ),
    ]
}

extension Author {
  // Mock data for SwiftUI Previews or testing
  static let mockData = Author(
    id: "author-42",
    name: "AI Concept Artist",
    avatarURL: URL(string: "https://example.com/avatar/ai_artist.png")
  )
}
