//
//  UserTier.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/18.
//

import Foundation

/// User tier enumeration
/// Defines user subscription tiers for permission management
public enum UserTier: String, Codable, CaseIterable {
  /// Free user
  case free = "free"
  /// Pro user
  case pro = "pro_tier"

  /// Display name
  public var displayName: String {
    switch self {
    case .free:
      return "Free"
    case .pro:
      return "Pro"
    }
  }

  /// Tier level (for comparison)
  public var tierLevel: Int {
    switch self {
    case .free:
      return 0
    case .pro:
      return 1
    }
  }

  /// Description
  public var description: String {
    switch self {
    case .free:
      return "Basic features with usage limitations"
    case .pro:
      return "Unlock all premium features with unlimited usage"
    }
  }

  /// Whether user is paid
  public var isPaid: Bool {
    return self != .free
  }

  /// Compare tier levels
  /// - Parameter other: Another tier
  /// - Returns: Whether current tier is at least the specified tier
  public func isAtLeast(_ other: UserTier) -> Bool {
    return self.tierLevel >= other.tierLevel
  }
}

// MARK: - Comparable
extension UserTier: Comparable {
  public static func < (lhs: UserTier, rhs: UserTier) -> Bool {
    return lhs.tierLevel < rhs.tierLevel
  }
}
