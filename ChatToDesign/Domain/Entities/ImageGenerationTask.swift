import FirebaseFirestore  // Import if using Timestamp directly
// import FirebaseFirestoreSwift // For @DocumentID and @ServerTimestamp - Not needed since SDK 10.17.0
import Foundation

// Matches the status strings used in the backend Firestore documents
public enum TaskStatus: String, Decodable, CaseIterable {
  case pending
  case processing
  case succeeded
  case failed
  case no_result = "no_result"  // Explicit raw value if it differs
  case deleted
  case unknown  // Default case for safety

  // Handle potential unknown status values during decoding
  public init(from decoder: Decoder) throws {
    let container = try decoder.singleValueContainer()
    let rawValue = try container.decode(String.self)
    self = TaskStatus(rawValue: rawValue) ?? .unknown
  }
}

public struct ImageGenerationTask: Decodable, Identifiable {
  // Use @DocumentID to automatically map the Firestore document ID to this property
  @DocumentID public var id: String?  // Enable @DocumentID

  public var taskId: String  // Often same as document ID (id), keep if used explicitly in backend logic
  public var designId: String?
  public var userId: String
  public var status: TaskStatus
  public var prompt: String?
  public var inputImageUrls: [String]?  // Assumes backend stores as an array of strings
  public var resultImageUrls: [String]?  // Assumes backend stores as an array of strings
  public var errorMessage: String?
  public var progress: Int?  // Progress value from 0 to 100

  // Optional: Add timestamp fields if they exist in Firestore and are needed
  // Requires importing FirebaseFirestore (already imported via FirebaseFirestoreSwift implicitly)
  @ServerTimestamp public var createdAt: Timestamp?  // Enable @ServerTimestamp
  @ServerTimestamp public var updatedAt: Timestamp?  // Enable @ServerTimestamp

  // We might need explicit CodingKeys if not using @DocumentID and field names differ
  // Keep CodingKeys if field names in Firestore differ from Swift property names
  // Ensure 'id' is NOT included here as @DocumentID handles it.
  enum CodingKeys: String, CodingKey {
    // Explicitly exclude 'id' because @DocumentID handles it.
    case taskId = "taskId"  // Keep explicit mapping if Firestore name differs, e.g., "task_id"
    case userId = "userId"  // e.g., "user_id"
    case status
    case prompt
    case inputImageUrls = "inputImageUrls"  // e.g., "input_image_urls"
    case resultImageUrls = "resultImageUrls"  // e.g., "result_image_urls"
    case errorMessage = "errorMessage"  // e.g., "error_message"
    case progress
    case createdAt = "createdAt"  // Enable if field name exists and needs mapping, e.g., "created_at"
    case updatedAt = "updatedAt"  // Enable if field name exists and needs mapping, e.g., "updated_at"
  }

  // REMOVE the custom init(from decoder: Decoder)
  // FirestoreSwift can synthesize the decoding logic thanks to @DocumentID and @ServerTimestamp

  // Keep the non-Decoder init for convenience
  public init(
    id: String? = nil, taskId: String, userId: String, status: TaskStatus, prompt: String? = nil,
    inputImageUrls: [String]? = nil, resultImageUrls: [String]? = nil, errorMessage: String? = nil,
    designId: String? = nil, progress: Int? = nil,
    createdAt: Timestamp? = nil, updatedAt: Timestamp? = nil  // Add timestamps if needed for manual creation
  ) {
    self.id = id
    self.taskId = taskId
    self.userId = userId
    self.status = status
    self.prompt = prompt
    self.inputImageUrls = inputImageUrls
    self.resultImageUrls = resultImageUrls
    self.errorMessage = errorMessage
    self.designId = designId
    self.progress = progress
    self.createdAt = createdAt  // Assign if added to init
    self.updatedAt = updatedAt  // Assign if added to init
  }
}
