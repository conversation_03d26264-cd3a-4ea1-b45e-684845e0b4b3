//
//  CreationItem.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/8.
//

import FirebaseFirestore
import Foundation

// MARK: - Creation Status Enum

/// Unified creation content status representation
public enum CreationStatus: Equatable {
  case pending
  case processing(progress: Int?)
  case completed
  case failed(error: String?)

  public var displayText: String {
    switch self {
    case .pending: return "Queued"
    case .processing(let progress):
      if let progress = progress {
        return "Generating \(progress)%"
      }
      return "Generating"
    case .completed: return "Completed"
    case .failed: return "Generation Failed"
    }
  }
}

// MARK: - Creation Item Enum

/// Unified creation content representation model
public enum CreationItem: Identifiable, Equatable {
  case asset(UserAsset)
  case imageTask(ImageGenerationTask)
  case videoTask(VideoGenerationTask)

  public var id: String {
    switch self {
    case .asset(let asset): return asset.id
    case .imageTask(let task): return task.taskId
    case .videoTask(let task): return task.taskId
    }
  }

  // MARK: - Equatable Implementation
  public static func == (lhs: CreationItem, rhs: CreationItem) -> Bool {
    switch (lhs, rhs) {
    case (.asset(let lhsAsset), .asset(let rhsAsset)):
      return lhsAsset.id == rhsAsset.id
    case (.imageTask(let lhsTask), .imageTask(let rhsTask)):
      return lhsTask.taskId == rhsTask.taskId
    case (.videoTask(let lhsTask), .videoTask(let rhsTask)):
      return lhsTask.taskId == rhsTask.taskId
    default:
      return false
    }
  }
}

// MARK: - Creation Item Extensions

extension CreationItem {
  /// Thumbnail URL
  public var thumbnailUrl: String? {
    switch self {
    case .asset(let asset):
      return asset.thumbnailUrl ?? asset.url
    case .imageTask(let task):
      return task.inputImageUrls?.first
    case .videoTask(let task):
      return task.inputImageUrl ?? task.coverImageUrl
    }
  }

  /// Creation content status
  public var status: CreationStatus {
    switch self {
    case .asset: return .completed
    case .imageTask(let task): return mapTaskStatus(task.status, progress: task.progress)
    case .videoTask(let task): return mapTaskStatus(task.status, progress: task.progress)
    }
  }

  /// Creation time
  public var createdAt: Date? {
    switch self {
    case .asset(let asset): return asset.createdAt
    case .imageTask(let task): return task.createdAt?.dateValue()
    case .videoTask(let task): return task.createdAt?.dateValue()
    }
  }

  /// Whether completed
  public var isCompleted: Bool {
    switch self {
    case .asset: return true
    case .imageTask(let task): return task.status == .succeeded
    case .videoTask(let task): return task.status == .succeeded
    }
  }

  /// Whether can retry
  public var canRetry: Bool {
    switch self {
    case .asset: return false
    case .imageTask(let task): return task.status == .failed
    case .videoTask(let task): return task.status == .failed
    }
  }
}

// MARK: - Helper Functions

/// Map task status to creation status
private func mapTaskStatus(_ taskStatus: TaskStatus, progress: Int?) -> CreationStatus {
  switch taskStatus {
  case .pending:
    return .pending
  case .processing:
    return .processing(progress: progress)
  case .succeeded:
    return .completed
  case .failed:
    return .failed(error: nil)
  case .no_result:
    return .failed(error: "No result")
  case .deleted:
    return .failed(error: "Deleted")
  case .unknown:
    return .failed(error: "Unknown status")
  }
}
