chats/
  {chatId}/
    messages/
      {messageId}/
        id: String
        chatId: String
        sender: {
          id: String
          type: String (user/ai/system)
        }
        type: String (user/ai/system/error)
        status: String (sending/sent/receiving/received/error/deleted)
        content: {
          text: String
          styleType: String (plainText/markdown)
          attachments: [
            {
              id: String
              type: String
              url: String
              thumbnailUrl: String?
              filename: String
              fileSize: Number
              mimeType: String
              isUploaded: Boolean
              uploadProgress: Number
            }
          ]
          attributes: {key: value}
        }
        timestamp: {
          created: Timestamp
          updated: Timestamp
        }
        isRead: Boolean
        metadata: {key: value}