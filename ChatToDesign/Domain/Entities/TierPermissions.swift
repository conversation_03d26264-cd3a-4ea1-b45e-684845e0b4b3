//
//  TierPermissions.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/18.
//

import Foundation

/// Tier permissions configuration
/// Defines specific permissions for each user tier
public struct TierPermissions: Codable, Equatable {

    // MARK: - Core Permissions

    /// Whether has watermark
    public let hasWatermark: Bool

    /// Whether can use HD image generation
    public let canUseHDImageGeneration: Bool

    /// Whether can use HD video generation
    public let canUseHDVideoGeneration: Bool

    /// Whether has priority queue
    public let hasPriorityQueue: Bool

    /// Whether can use advanced models
    public let canUseAdvancedModels: Bool

    // MARK: - Initialization
    
    public init(
        hasWatermark: Bool,
        canUseHDImageGeneration: Bool,
        canUseHDVideoGeneration: Bool,
        hasPriorityQueue: Bool,
        canUseAdvancedModels: Bool
    ) {
        self.hasWatermark = hasWatermark
        self.canUseHDImageGeneration = canUseHDImageGeneration
        self.canUseHDVideoGeneration = canUseHDVideoGeneration
        self.hasPriorityQueue = hasPriorityQueue
        self.canUseAdvancedModels = canUseAdvancedModels
    }
    
    // MARK: - Static Factory Methods

    /// Get corresponding permission configuration based on user tier
    /// - Parameter tier: User tier
    /// - Returns: Corresponding permission configuration
    public static func permissions(for tier: UserTier) -> TierPermissions {
        switch tier {
        case .free:
            return TierPermissions(
                hasWatermark: true,
                canUseHDImageGeneration: false,
                canUseHDVideoGeneration: false,
                hasPriorityQueue: false,
                canUseAdvancedModels: false
            )
            
        case .pro:
            return TierPermissions(
                hasWatermark: false,
                canUseHDImageGeneration: true,
                canUseHDVideoGeneration: true,
                hasPriorityQueue: true,
                canUseAdvancedModels: true
            )
        }
    }
}

// MARK: - Convenience Properties
extension TierPermissions {

    /// Whether can remove watermark
    public var canRemoveWatermark: Bool {
        return !hasWatermark
    }

    /// Whether has any premium feature
    public var hasAnyPremiumFeature: Bool {
        return canRemoveWatermark ||
               canUseHDImageGeneration ||
               canUseHDVideoGeneration ||
               hasPriorityQueue ||
               canUseAdvancedModels
    }

    /// Permission summary description
    public var summary: String {
        var features: [String] = []

        if canRemoveWatermark {
            features.append("No Watermark")
        }
        if canUseHDImageGeneration {
            features.append("HD Images")
        }
        if canUseHDVideoGeneration {
            features.append("HD Videos")
        }
        if hasPriorityQueue {
            features.append("Priority Queue")
        }
        if canUseAdvancedModels {
            features.append("Advanced Models")
        }

        return features.isEmpty ? "Basic Features" : features.joined(separator: ", ")
    }
}

// MARK: - Predefined Configurations
extension TierPermissions {

    /// Free tier permission configuration
    public static let free = TierPermissions.permissions(for: .free)

    /// Pro tier permission configuration
    public static let pro = TierPermissions.permissions(for: .pro)
}
