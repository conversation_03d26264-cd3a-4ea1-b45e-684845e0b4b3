// Message.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Foundation

/// Message type
public enum MessageType: String, Codable {
 /// User message
 case user
 /// AI message
 case ai
 /// System message
 case system
 /// Error message
 case error
}

/// Message status
public enum MessageStatus: String, Codable {
 /// Sending
 case sending
 /// Sent
 case sent
 /// Receiving
 case receiving
 /// Received
 case received
 /// Error
 case error
 /// Deleted
 case deleted
}

/// Message content
public struct MessageContent: Codable, Equatable {
 /// Text content
 public var text: String

 /// Style type
 public var styleType: StyleType

 /// Media attachments
 public var attachments: [MediaAttachment]?

 /// Extended attributes
 public var attributes: [String: String]?

 /// Content style
 public enum StyleType: String, Codable {
   /// Plain text
   case plainText
   /// Markdown
   case markdown
 }

 /// Create plain text content
 public static func text(_ content: String) -> MessageContent {
   return MessageContent(text: content, styleType: .plainText)
 }

 /// Create Markdown content
 public static func markdown(_ content: String) -> MessageContent {
   return MessageContent(text: content, styleType: .markdown)
 }

 /// Check if it's composite content (contains both text and media)
 public var isComposite: Bool {
   return !text.isEmpty && attachments != nil && !(attachments?.isEmpty ?? true)
 }
}
 
/// Message entity
/// Represents a message in a chat
public struct Message: Identifiable, Codable, Equatable {
    /// Message unique identifier
    public var id: String

    /// Chat ID this message belongs to
    public let chatId: String

    /// Sender information
    public let sender: Sender

    /// Message type
    public let type: MessageType

    /// Message status
    public var status: MessageStatus

    /// Message content
    public var content: MessageContent

    /// Reply message ID, optional
    public var replyMessageId: String?

    /// Metadata for storing other custom attributes
    public var metadata: [String: String]?

    /// Time-related information
    public let timestamp: Timestamp

    /// Whether message is read
    public var isRead: Bool

    /// Whether message contains media
    public var hasMedia: Bool {
        return content.attachments != nil && !(content.attachments?.isEmpty ?? true)
    }

    /// Whether message contains images
    public var hasImage: Bool {
        return content.attachments?.contains { $0.type == .image } ?? false
    }

    /// Whether message is delivered
    public var isDelivered: Bool {
        return status == .sent || status == .received
    }

    /// Whether message is in error state
    public var hasError: Bool {
        return status == .error
    }
    
    /// Sender information
    public struct Sender: Codable, Equatable {
        /// Sender ID
          public let id: String

        /// Sender type
        public let type: SenderType

        /// Sender type
        public enum SenderType: String, Codable {
            /// User
            case user
            /// AI
            case ai
            /// System
            case system
        }

        /// Whether sender is AI
        public var isAI: Bool {
            return type == .ai
        }

        /// Create user sender
        public static func user(_ id: String) -> Sender {
            return Sender(id: id, type: .user)
        }

        /// Create AI sender
        public static func ai() -> Sender {
            return Sender(id: "ai", type: .ai)
        }

        /// Create system sender
        public static func system() -> Sender {
            return Sender(id: "system", type: .system)
        }
    }
 
 /// Timestamp information
 public struct Timestamp: Codable, Equatable {
   /// Creation time
   public let created: Date

   /// Update time
   public var updated: Date

   /// Create timestamp
   public static func now() -> Timestamp {
     let now = Date()
     return Timestamp(created: now, updated: now)
   }
 }
}

/// Media attachment
public struct MediaAttachment: Codable, Equatable, Identifiable {
 /// Attachment unique identifier
 public let id: String

 /// Media type
 public let type: MediaType

 /// Media URL
 public let url: URL

 /// Thumbnail URL, optional
 public let thumbnailUrl: URL?

 /// Filename
 public let filename: String

 /// File size (bytes)
 public let fileSize: Int

 /// MIME type
 public let mimeType: String

 /// Whether upload is completed
 public var isUploaded: Bool

 /// Upload progress (0-1), 0 means not uploaded, 1 means upload completed, < 0 means upload failed
 public var uploadProgress: Double

 /// Create a media attachment
 /// - Parameters:
 ///   - id: Unique identifier
 ///   - type: Media type
 ///   - url: Media URL
 ///   - thumbnailUrl: Thumbnail URL
 ///   - filename: Filename
 ///   - fileSize: File size
 ///   - mimeType: MIME type
 ///   - isUploaded: Whether uploaded
 ///   - uploadProgress: Upload progress
 public init(
   id: String = UUID().uuidString,
   type: MediaType,
   url: URL,
   thumbnailUrl: URL? = nil,
   filename: String,
   fileSize: Int,
   mimeType: String,
   isUploaded: Bool = false,
   uploadProgress: Double = 0.0
 ) {
   self.id = id
   self.type = type
   self.url = url
   self.thumbnailUrl = thumbnailUrl
   self.filename = filename
   self.fileSize = fileSize
   self.mimeType = mimeType
   self.isUploaded = isUploaded
   self.uploadProgress = uploadProgress
 }
}

// MARK: - Factory methods
extension Message {
 /// Create user message
 /// - Parameters:
 ///   - chatId: Chat ID
 ///   - userId: User ID
 ///   - text: Message text
 ///   - styleType: Content style type, defaults to plainText
 ///   - attachments: Media attachments, optional
 /// - Returns: Message entity
 public static func createUserMessage(
   chatId: String,
   userId: String,
   text: String,
   styleType: MessageContent.StyleType = .plainText,
   attachments: [MediaAttachment]? = nil,
   replyMessageId: String? = nil
 ) -> Message {
   return Message(
     id: UUID().uuidString,
     chatId: chatId,
     sender: .user(userId),
     type: .user,
     status: .sending,
     content: MessageContent(
       text: text,
       styleType: styleType,
       attachments: attachments
     ),
     replyMessageId: replyMessageId,
     metadata: nil,
     timestamp: .now(),
     isRead: true
   )
 }
 
 /// Create AI message
 /// - Parameters:
 ///   - chatId: Chat ID
 ///   - text: Message text
 ///   - styleType: Content style type, defaults to plainText
 ///   - attachments: Media attachments, optional
 /// - Returns: Message entity
 public static func createAIMessage(
   chatId: String,
   text: String,
   styleType: MessageContent.StyleType = .plainText,
   attachments: [MediaAttachment]? = nil,
   replyMessageId: String? = nil
 ) -> Message {
   return Message(
     id: UUID().uuidString,
     chatId: chatId,
     sender: .ai(),
     type: .ai,
     status: .sending,
     content: MessageContent(
       text: text,
       styleType: styleType,
       attachments: attachments
     ), 
     replyMessageId: replyMessageId,
     metadata: nil,
     timestamp: .now(),
     isRead: false
   )
 }
 
 /// Create AI Markdown message
 /// - Parameters:
 ///   - chatId: Chat ID
 ///   - markdown: Markdown content
 ///   - attachments: Media attachments, optional
 /// - Returns: Message entity
 public static func createAIMarkdownMessage(
   chatId: String,
   markdown: String,
   attachments: [MediaAttachment]? = nil,
   replyMessageId: String? = nil
 ) -> Message {
   return Message(
     id: UUID().uuidString,
     chatId: chatId,
     sender: .ai(),
     type: .ai,
     status: .sending,
     content: MessageContent(
       text: markdown,
       styleType: .markdown,
       attachments: attachments
     ),
     replyMessageId: replyMessageId,
     metadata: nil,
     timestamp: .now(),
     isRead: false
   )
 }
 
 /// Create system message
 /// - Parameters:
 ///   - chatId: Chat ID
 ///   - text: Message content
 /// - Returns: Message entity
 public static func createSystemMessage(
   chatId: String,
   text: String
 ) -> Message {
   return Message(
     id: UUID().uuidString,
     chatId: chatId,
     sender: .system(),
     type: .system,
     status: .sent,
     content: .text(text),
     replyMessageId: nil,
     metadata: nil,
     timestamp: .now(),
     isRead: true
   )
 }
 
 /// Create error message
 /// - Parameters:
 ///   - chatId: Chat ID
 ///   - text: Error message
 /// - Returns: Message entity
 public static func createErrorMessage(
   chatId: String,
   text: String
 ) -> Message {
   return Message(
     id: UUID().uuidString,
     chatId: chatId,
     sender: .system(),
     type: .error,
     status: .error,
     content: .text(text),
     replyMessageId: nil,
     metadata: nil,
     timestamp: .now(),
     isRead: true
   )
 }
} 
