//
//  Entitlement.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/15.
//

import Foundation

/// Entitlement type
public enum EntitlementType: String, Codable, CaseIterable {
  case proTier = "pro_tier"

  /// Display name
  public var displayName: String {
    switch self {
    case .proTier:
      return "Pro"
    }
  }

  /// Description
  public var description: String {
    switch self {
    case .proTier:
      return "Unlock all premium features: remove watermark, HD generation, priority queue, advanced models"
    }
  }
}

/// Entitlement entity
public struct Entitlement: Identifiable, Codable, Equatable {
  /// Entitlement unique identifier
  public let id: String

  /// Entitlement identifier (entitlement identifier in RevenueCat)
  public let identifier: String

  /// Entitlement type
  public let type: EntitlementType

  /// Whether active
  public let isActive: Bool

  /// Whether will renew
  public let willRenew: Bool

  /// Latest purchase date
  public let latestPurchaseDate: Date?

  /// Expiration date
  public let expirationDate: Date?

  /// Product identifier
  public let productIdentifier: String?

  /// Whether in trial period
  public let isInTrialPeriod: Bool

  /// Creation time
  public let createdAt: Date

  /// Update time
  public let updatedAt: Date

  /// Initialize entitlement
  public init(
    id: String,
    identifier: String,
    type: EntitlementType,
    isActive: Bool,
    willRenew: Bool = false,
    latestPurchaseDate: Date? = nil,
    expirationDate: Date? = nil,
    productIdentifier: String? = nil,
    isInTrialPeriod: Bool = false,
    createdAt: Date = Date(),
    updatedAt: Date = Date()
  ) {
    self.id = id
    self.identifier = identifier
    self.type = type
    self.isActive = isActive
    self.willRenew = willRenew
    self.latestPurchaseDate = latestPurchaseDate
    self.expirationDate = expirationDate
    self.productIdentifier = productIdentifier
    self.isInTrialPeriod = isInTrialPeriod
    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }

  /// Whether expiring soon
  public var isExpiringSoon: Bool {
    guard let expirationDate = expirationDate else { return false }
    let threeDaysFromNow = Date().addingTimeInterval(3 * 24 * 60 * 60)
    return expirationDate <= threeDaysFromNow
  }

  /// Days remaining
  public var daysRemaining: Int? {
    guard let expirationDate = expirationDate else { return nil }
    let calendar = Calendar.current
    let components = calendar.dateComponents([.day], from: Date(), to: expirationDate)
    return max(0, components.day ?? 0)
  }
}

// MARK: - Entitlement Set
public struct EntitlementSet: Codable, Equatable {
  /// All entitlements
  public let entitlements: [Entitlement]

  /// Last updated time
  public let lastUpdated: Date

  /// Initialize entitlement set
  public init(entitlements: [Entitlement], lastUpdated: Date = Date()) {
    self.entitlements = entitlements
    self.lastUpdated = lastUpdated
  }

  /// Check if has specific entitlement
  public func hasEntitlement(_ type: EntitlementType) -> Bool {
    return entitlements.first { $0.type == type && $0.isActive } != nil
  }

  /// Get specific entitlement
  public func getEntitlement(_ type: EntitlementType) -> Entitlement? {
    return entitlements.first { $0.type == type }
  }

  /// Get all active entitlements
  public var activeEntitlements: [Entitlement] {
    return entitlements.filter { $0.isActive }
  }

  /// Whether has any active entitlement
  public var hasAnyActiveEntitlement: Bool {
    return !activeEntitlements.isEmpty
  }
}

// MARK: - Convenience Constructors
extension EntitlementSet {
  /// Create empty entitlement set
  public static var empty: EntitlementSet {
    return EntitlementSet(entitlements: [])
  }

  /// Create free user entitlement set
  public static var free: EntitlementSet {
    return EntitlementSet(entitlements: [])
  }
}
