import FirebaseFirestore  // Import if using Timestamp directly
// import FirebaseFirestoreSwift // For @DocumentID and @ServerTimestamp - Not needed since SDK 10.17.0
import Foundation

/// Video generation task entity
/// Matches the video-generation-tasks collection structure in backend Firestore
public struct VideoGenerationTask: Decodable, Identifiable {
  // Use @DocumentID to automatically map the Firestore document ID to this property
  @DocumentID public var id: String?  // Enable @DocumentID

  public var taskId: String  // Often same as document ID (id), keep if used explicitly in backend logic
  public var userId: String
  public var status: TaskStatus
  public var errorMessage: String?
  public var batchId: String?
  public var progress: Int?  // Progress value from 0 to 100

  // Provider information
  public var provider: String?  // Provider used (e.g., "302ai_vidu", "runwayml")
  public var providerTaskId: String?  // External provider's task ID
  public var inputData: [String: AnyCodable]?  // Additional input parameters (JSON)
  public var resultData: [String: AnyCodable]?  // Additional result data (JSON)

  // Input URLs
  public var inputImageUrl: String?  // input: Input image URL for video generation

  // Result URLs
  public var coverImageUrl: String?  // result: Cover image URL
  public var videoUrl: String?  // result: Video URL

  // Optional: Add timestamp fields if they exist in Firestore and are needed
  // Requires importing FirebaseFirestore (already imported via FirebaseFirestoreSwift implicitly)
  @ServerTimestamp public var createdAt: Timestamp?  // Enable @ServerTimestamp
  @ServerTimestamp public var updatedAt: Timestamp?  // Enable @ServerTimestamp

  // We might need explicit CodingKeys if not using @DocumentID and field names differ
  // Keep CodingKeys if field names in Firestore differ from Swift property names
  // Ensure 'id' is NOT included here as @DocumentID handles it.
  enum CodingKeys: String, CodingKey {
    // Explicitly exclude 'id' because @DocumentID handles it.
    case taskId = "taskId"
    case userId = "userId"
    case status
    case errorMessage = "errorMessage"
    case batchId = "batchId"
    case progress
    case provider
    case providerTaskId = "providerTaskId"
    case inputData = "inputData"
    case resultData = "resultData"
    case inputImageUrl = "inputImageUrl"
    case coverImageUrl = "coverImageUrl"
    case videoUrl = "videoUrl"
    case createdAt = "createdAt"
    case updatedAt = "updatedAt"
  }

  // REMOVE the custom init(from decoder: Decoder)
  // FirestoreSwift can synthesize the decoding logic thanks to @DocumentID and @ServerTimestamp

  // Keep the non-Decoder init for convenience
  public init(
    id: String? = nil,
    taskId: String,
    userId: String,
    status: TaskStatus,
    errorMessage: String? = nil,
    batchId: String? = nil,
    progress: Int? = nil,
    provider: String? = nil,
    providerTaskId: String? = nil,
    inputData: [String: AnyCodable]? = nil,
    resultData: [String: AnyCodable]? = nil,
    inputImageUrl: String? = nil,
    coverImageUrl: String? = nil,
    videoUrl: String? = nil,
    createdAt: Timestamp? = nil,
    updatedAt: Timestamp? = nil
  ) {
    self.id = id
    self.taskId = taskId
    self.userId = userId
    self.status = status
    self.errorMessage = errorMessage
    self.batchId = batchId
    self.progress = progress
    self.provider = provider
    self.providerTaskId = providerTaskId
    self.inputData = inputData
    self.resultData = resultData
    self.inputImageUrl = inputImageUrl
    self.coverImageUrl = coverImageUrl
    self.videoUrl = videoUrl
    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }

  /// Get video URLs (compatibility method)
  public var resultVideoUrls: [String]? {
    if let videoUrl = videoUrl {
      return [videoUrl]
    }
    return nil
  }
}
