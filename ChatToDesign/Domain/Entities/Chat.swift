// Chat.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Foundation

/// Chat error
public enum ChatError: Error {
 /// Chat not found
 case notFound
 /// Access denied
 case accessDenied
 /// Invalid operation
 case invalidOperation
 /// Invalid message content
 case invalidMessageContent
 /// Message creation failed
 case messageCreationFailed
 /// Message persistence failed
 case messagePersistenceFailed(Error)
 /// LLM service error
 case llmServiceError(Error)
 /// Chat archived
 case chatArchived
 /// Chat quota exceeded
 case quotaExceeded
 /// Media upload failed
 case mediaUploadFailed(Error)
 
 public var localizedDescription: String {
   switch self {
   case .notFound:
     return "Chat not found"
   case .accessDenied:
     return "Access denied"
   case .invalidOperation:
     return "Invalid operation"
   case .invalidMessageContent:
     return "Invalid message content"
   case .messageCreationFailed:
     return "Message creation failed"
   case .messagePersistenceFailed(let error):
     return "Message persistence failed: \(error.localizedDescription)"
   case .llmServiceError(let error):
     return "LLM service error: \(error.localizedDescription)"
   case .chatArchived:
     return "Chat is archived and cannot be modified"
   case .quotaExceeded:
     return "Chat quota exceeded"
   case .mediaUploadFailed(let error):
     return "Media upload failed: \(error.localizedDescription)"
   }
 }
}

/// Chat status
public enum ChatStatus: String, Codable, Equatable {
 /// Active
 case active
 /// Completed
 case completed
 /// Archived
 case archived
 /// Error
 case error
 /// Deleted
 case deleted
}

/// AI model type
public enum AIModel: Codable, Equatable, Hashable {
 /// Gemini model
 case gemini
 /// Custom model
 case custom(String)
 
 public init(from decoder: Decoder) throws {
   let container = try decoder.singleValueContainer()
   let rawValue = try container.decode(String.self)
   
   if rawValue.hasPrefix("custom_") {
     self = .custom(rawValue.replacingOccurrences(of: "custom_", with: ""))
   } else if rawValue == "gemini" {
     self = .gemini
   } else {
     self = .gemini
   }
 }
 
 public func encode(to encoder: Encoder) throws {
   var container = encoder.singleValueContainer()
   switch self {
   case .gemini:
     try container.encode("gemini")
   case .custom(let name):
     try container.encode("custom_\(name)")
   }
 }
 
 public func hash(into hasher: inout Hasher) {
   switch self {
   case .gemini:
     hasher.combine("gemini")
   case .custom(let name):
     hasher.combine("custom_\(name)")
   }
 }
}

/// Chat visibility
public enum ChatVisibility: String, Codable, Equatable {
 /// Private (only visible to creator)
 case `private`
 /// Shared (visible to specified users)
 case shared
 /// Public (visible to everyone)
 case `public`
}

/// Chat parameters
public struct ChatParameters: Codable, Hashable {
 /// System prompt
 public var systemPrompt: String?
 /// Temperature parameter (0.0-1.0)
 public var temperature: Double?
 /// Model generation parameters
 public var modelParameters: [String: AnyHashable]?
 /// Maximum response length
 public var maxResponseLength: Int?
 
 public init(
   systemPrompt: String? = nil,
   temperature: Double? = 0.7,
   modelParameters: [String: AnyHashable]? = nil,
   maxResponseLength: Int? = 1000
 ) {
   self.systemPrompt = systemPrompt
   self.temperature = temperature
   self.modelParameters = modelParameters
   self.maxResponseLength = maxResponseLength
 }
 
 enum CodingKeys: String, CodingKey {
   case systemPrompt
   case temperature
   case maxResponseLength
 }
 
 public init(from decoder: Decoder) throws {
   let container = try decoder.container(keyedBy: CodingKeys.self)
   systemPrompt = try container.decodeIfPresent(String.self, forKey: .systemPrompt)
   temperature = try container.decodeIfPresent(Double.self, forKey: .temperature)
   maxResponseLength = try container.decodeIfPresent(Int.self, forKey: .maxResponseLength)
   modelParameters = nil
 }
 
 public func encode(to encoder: Encoder) throws {
   var container = encoder.container(keyedBy: CodingKeys.self)
   try container.encodeIfPresent(systemPrompt, forKey: .systemPrompt)
   try container.encodeIfPresent(temperature, forKey: .temperature)
   try container.encodeIfPresent(maxResponseLength, forKey: .maxResponseLength)
 }
 
 public static func == (lhs: ChatParameters, rhs: ChatParameters) -> Bool {
   return lhs.systemPrompt == rhs.systemPrompt &&
          lhs.temperature == rhs.temperature &&
          lhs.maxResponseLength == rhs.maxResponseLength
 }
 
 public func hash(into hasher: inout Hasher) {
   hasher.combine(systemPrompt)
   hasher.combine(temperature)
   hasher.combine(maxResponseLength)
 }
}

/// Chat entity
/// Represents a conversation between user and AI
public struct Chat: Identifiable, Codable, Hashable {
 /// Chat unique identifier
 public var id: String?

 /// User ID
 public let userId: String

 /// Chat title
 public var title: String

 /// Chat description
 public var description: String?

 /// Creation time
 public var createdAt: Date?

 /// Update time
 public var updatedAt: Date?

 /// Chat status
 public var status: ChatStatus

 /// AI model
 public var model: AIModel

 /// Message count
 public var messageCount: Int

 /// Parent chat ID for branching
 public var parentChatId: String?

 /// Visibility
 public var visibility: ChatVisibility

 /// Shared user IDs
 public var sharedWithUserIds: [String]?

 /// Fork count
 public var forkCount: Int

 /// Tags
 public var tags: [String]?

 /// Chat parameters
 public var parameters: ChatParameters?

 /// Whether chat is editable
 public var isEditable: Bool {
   return status == .active
 }
 
 /// Whether chat is completed
 public var isCompleted: Bool {
   return status == .completed || status == .archived
 }

 /// Whether chat has error
 public var hasError: Bool {
   return status == .error
 }

 /// Whether chat is a fork
 public var isFork: Bool {
   return parentChatId != nil
 }

 /// Whether chat is shared
 public var isShared: Bool {
   return visibility == .shared && (sharedWithUserIds?.isEmpty == false)
 }

 /// Whether chat is public
 public var isPublic: Bool {
   return visibility == .public
 }

 /// Whether chat is private
 public var isPrivate: Bool {
   return visibility == .private
 }
 
 /// Check if user has access permission
 /// - Parameter userId: User ID
 /// - Returns: true if user has permission
 public func canAccess(by userId: String) -> Bool {
   if self.userId == userId {
     return true
   }

   if visibility == .public {
     return true
   }

   if visibility == .shared && sharedWithUserIds?.contains(userId) == true {
     return true
   }

   return false
 }

 /// Check if user has edit permission
 /// - Parameter userId: User ID
 /// - Returns: true if user has permission
 public func canEdit(by userId: String) -> Bool {
   return self.userId == userId && isEditable
 }

 /// Can be archived
 /// - Returns: true if can be archived
 public func canBeArchived() -> Bool {
   return status == .active || status == .completed
 }
 
 public static func == (lhs: Chat, rhs: Chat) -> Bool {
   return lhs.id == rhs.id &&
          lhs.userId == rhs.userId &&
          lhs.title == rhs.title &&
          lhs.description == rhs.description &&
          lhs.createdAt == rhs.createdAt &&
          lhs.updatedAt == rhs.updatedAt &&
          lhs.status == rhs.status &&
          lhs.model == rhs.model &&
          lhs.messageCount == rhs.messageCount &&
          lhs.parentChatId == rhs.parentChatId &&
          lhs.visibility == rhs.visibility &&
          lhs.sharedWithUserIds == rhs.sharedWithUserIds &&
          lhs.forkCount == rhs.forkCount &&
          lhs.tags == rhs.tags &&
          lhs.parameters == rhs.parameters
 }
 
 public func hash(into hasher: inout Hasher) {
   hasher.combine(id)
   hasher.combine(userId)
   hasher.combine(title)
   hasher.combine(description)
   hasher.combine(createdAt)
   hasher.combine(updatedAt)
   hasher.combine(status)
   hasher.combine(model) // AIModel should conform to Hashable
   hasher.combine(messageCount)
   hasher.combine(parentChatId)
   hasher.combine(visibility)
   hasher.combine(sharedWithUserIds)
   hasher.combine(forkCount)
   hasher.combine(tags)
   hasher.combine(parameters)
 }
}

// MARK: - Factory methods
extension Chat {
 /// Create new chat
 /// - Parameters:
 ///   - userId: User ID
 ///   - title: Chat title
 ///   - description: Chat description, optional
 ///   - model: AI model, defaults to Gemini
 ///   - visibility: Visibility, defaults to private
 ///   - parameters: Chat parameters, optional
 /// - Returns: Chat entity
 public static func create(
   userId: String,
   title: String,
   description: String? = nil,
   model: AIModel = .gemini,
   visibility: ChatVisibility = .private,
   parameters: ChatParameters? = nil
 ) -> Chat {
   return Chat(
     id: UUID().uuidString,
     userId: userId,
     title: title,
     description: description,
     createdAt: Date(),
     updatedAt: Date(),
     status: .active,
     model: model,
     messageCount: 0,
     parentChatId: nil,
     visibility: visibility,
     sharedWithUserIds: nil,
     forkCount: 0,
     tags: nil,
     parameters: parameters
   )
 }
 
 /// Create chat fork
 /// - Parameters:
 ///   - fromChat: Source chat
 ///   - userId: User ID
 ///   - title: New title, if nil uses source chat title with "(Copy)"
 /// - Returns: Chat entity
 public static func createFork(
   fromChat: Chat,
   userId: String,
   title: String? = nil
 ) -> Chat {
   let now = Date()
   return Chat(
     id: UUID().uuidString,
     userId: userId,
     title: title ?? "\(fromChat.title) (Copy)",
     description: fromChat.description,
     createdAt: now,
     updatedAt: now,
     status: .active,
     model: fromChat.model,
     messageCount: 0,
     parentChatId: fromChat.id,
     visibility: .private,
     sharedWithUserIds: nil,
     forkCount: 0,
     tags: fromChat.tags,
     parameters: fromChat.parameters
   )
 }
} 
