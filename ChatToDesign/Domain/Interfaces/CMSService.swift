//
//  CMSService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//

import Foundation

// MARK: - CMS Data Models

/// Use case type for CMS items
public enum UseCaseType: String, Codable, CaseIterable {
  case imageOnly = "image_only"
  case promptWithImage = "prompt_with_image"
  case multiImage = "multi_image"
  case promptOnly = "prompt_only"
}

/// Represents a CMS item from the ghibli-cms API
public struct ImageTemplateItem: Identifiable, Codable, Hashable {
  public let id: String
  public let name: String
  public let outputUrl: String
  public let groupRank: Int
  public let inGroupRank: Int
  public let category: String
  public let isDisabled: Bool
  public let prompt: String
  public let usecaseType: UseCaseType

  public init(
    id: String, name: String, outputUrl: String, groupRank: Int, inGroupRank: Int, category: String,
    isDisabled: Bool, prompt: String, usecaseType: UseCaseType
  ) {
    self.id = id
    self.name = name
    self.outputUrl = outputUrl
    self.groupRank = groupRank
    self.inGroupRank = inGroupRank
    self.category = category
    self.isDisabled = isDisabled
    self.prompt = prompt
    self.usecaseType = usecaseType
  }

  enum CodingKeys: String, CodingKey {
    case id, name, category, prompt, outputUrl, groupRank, inGroupRank, isDisabled, usecaseType
  }
}

/// Represents a grouped category of CMS items
public struct CMSCategory: Identifiable, Hashable {
  public let id: String
  public let name: String
  public let items: [ImageTemplateItem]

  public init(name: String, items: [ImageTemplateItem]) {
    self.id = name
    self.name = name
    self.items = items.sorted { $0.inGroupRank < $1.inGroupRank }
  }
}

// MARK: - Video Effect Data Models

/// Video effect provider type
public enum VideoEffectProvider: String, Codable, CaseIterable {
  case polloAI = "pollo_ai"
  case vidu = "vidu"
}

/// API response model for video use case CMS
public struct VideoUseCaseResponse: Codable {
  public let id: String
  public let name: String
  public let template: String
  public let inputInstruction: String
  public let detail: String
  public let videoUrl: String
  public let coverImgUrl: String
  public let provider: String
  public let category: String
  public let isHot: Bool
  public let isNew: Bool
  public let imageCount: Int
  public let prompt: String

  enum CodingKeys: String, CodingKey {
    case id, name, template, provider, category, isHot, isNew, prompt, videoUrl, coverImgUrl,
      imageCount, detail, inputInstruction
  }
}

/// Represents a video effect from AI video providers
public struct VideoEffect: Identifiable, Codable, Hashable {
  public let id: String
  public let name: String
  public let videoUrl: String
  public let posterUrl: String
  public let isHot: Bool
  public let isNew: Bool
  public let category: String
  public let urlType: String
  public let provider: VideoEffectProvider
  public let imageCount: Int

  // Extended fields for vidu provider (simplified to strings)
  public let detail: String?
  public let inputInstruction: String?
  public let prompt: String?
  public let template: String?

  public init(
    id: String, name: String, videoUrl: String, posterUrl: String,
    isHot: Bool, isNew: Bool, category: String, urlType: String = "video",
    provider: VideoEffectProvider = .vidu, imageCount: Int = 1,
    detail: String? = nil, inputInstruction: String? = nil,
    prompt: String? = nil, template: String? = nil
  ) {
    self.id = id
    self.name = name
    self.videoUrl = videoUrl
    self.posterUrl = posterUrl
    self.isHot = isHot
    self.isNew = isNew
    self.category = category
    self.urlType = urlType
    self.provider = provider
    self.imageCount = imageCount
    self.detail = detail
    self.inputInstruction = inputInstruction
    self.prompt = prompt
    self.template = template
  }

  enum CodingKeys: String, CodingKey {
    case id, name, category, provider, prompt, template
    case videoUrl = "videoUrl"
    case posterUrl = "posterUrl"
    case isHot = "isHot"
    case isNew = "isNew"
    case urlType = "urlType"
    case imageCount = "image_count"
    case detail = "detail"
    case inputInstruction = "input_instruction"
  }
}

/// Represents a grouped category of video effects
public struct VideoEffectCategory: Identifiable, Hashable {
  public let id: String
  public let name: String
  public let effects: [VideoEffect]
  public let count: Int

  public init(name: String, effects: [VideoEffect]) {
    self.id = name
    self.name = name
    self.effects = effects
    self.count = effects.count
  }
}

// MARK: - CMS Service Protocol

/// CMS Service Protocol for fetching content management system data
public protocol CMSService {
  /// Fetches all CMS items from the ghibli-cms API
  /// - Returns: Array of ImageTemplateItem objects
  /// - Throws: CMSServiceError if the request fails
  func fetchImageTemplateItems() async throws -> [ImageTemplateItem]

  /// Fetches CMS items grouped by category
  /// - Returns: Array of CMSCategory objects with items sorted by rank
  /// - Throws: CMSServiceError if the request fails
  func fetchCMSCategories() async throws -> [CMSCategory]
}

// MARK: - Video Effect Service Protocol

/// Video Effect Service Protocol for fetching video effects data
public protocol VideoEffectService {
  /// Fetches all video effects from API
  /// - Returns: Array of VideoEffect objects
  /// - Throws: VideoEffectServiceError if API request fails
  func fetchVideoEffects() async throws -> [VideoEffect]

  /// Fetches video effects grouped by category
  /// - Returns: Array of VideoEffectCategory objects
  /// - Throws: VideoEffectServiceError if API request fails
  func fetchVideoEffectCategories() async throws -> [VideoEffectCategory]
}

/// CMS Service Error Types
public enum CMSServiceError: Error, LocalizedError {
  case networkError(Error)
  case invalidURL
  case invalidResponse
  case decodingError(Error)
  case noData

  public var errorDescription: String? {
    switch self {
    case .networkError(let error):
      return "Network error: \(error.localizedDescription)"
    case .invalidURL:
      return "Invalid CMS API URL"
    case .invalidResponse:
      return "Invalid response from CMS API"
    case .decodingError(let error):
      return "Failed to decode CMS data: \(error.localizedDescription)"
    case .noData:
      return "No data received from CMS API"
    }
  }
}
