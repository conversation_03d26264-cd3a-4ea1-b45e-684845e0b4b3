//
//  APIService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//

import Foundation

/// API service
public protocol APIService {
  /// Process message
  /// - Parameters:
  ///   - chatId: Chat ID
  ///   - messageId: Message ID
  /// - Returns: Processing result
  func processMessage(chatId: String, messageId: String) async throws

  /// Generate design image
  /// - Parameters:
  ///   - request: Design generation request
  /// - Returns: Design generation response
  func generateDesign(request: DesignGenerationRequest) async throws -> DesignGenerationResponse

  /// Upload file
  /// - Parameter request: File upload request
  /// - Returns: File upload response
  func uploadFile(request: FileUploadRequest) async throws -> FileUploadResponse

  /// Upload file and create asset
  /// - Parameter request: Upload file and create asset request
  /// - Returns: Asset response
  func uploadFileWithAsset(request: UploadWithAssetRequest) async throws -> UserAsset

  /// Get user assets list
  /// - Parameter query: Query parameters
  /// - Returns: User assets list response
  func getUserAssets(query: AssetListQuery) async throws -> UserAssetsResult

  /// Delete asset
  /// - Parameter id: Asset ID
  /// - Returns: Delete response
  func deleteAsset(id: String) async throws -> AssetDeleteResponse

  /// Get CMS data
  /// - Returns: CMS data array
  func fetchImageUseCaseCMS() async throws -> [ImageTemplateItem]

  /// Get video use case CMS data
  /// - Returns: Video use case response array
  func fetchVideoUseCaseCMS() async throws -> [VideoUseCaseResponse]

  /// Generate video
  /// - Parameter request: Video generation request
  /// - Returns: Video generation response
  func generateVideo(request: VideoGenerationRequest) async throws -> VideoGenerationResponse

  /// Get Explore data
  /// - Parameter query: Query parameters
  /// - Returns: Explore response data
  func fetchExploreData(query: ExploreQuery) async throws -> ExploreResponse

  /// Get public asset details
  /// - Parameter id: Asset ID
  /// - Returns: User asset details
  func getPublicAssetDetail(id: String) async throws -> UserAsset
}
