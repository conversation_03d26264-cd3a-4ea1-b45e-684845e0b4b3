//
//  AssetRepository.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/20.
//

import Combine
import Foundation

// MARK: - Asset Repository Protocol

/// Asset repository protocol
/// Defines domain interface for asset data access
public protocol AssetRepository {
  /// Get user assets list
  /// - Parameters:
  ///   - userId: User ID
  ///   - query: Query parameters
  /// - Returns: User assets query result
  func getUserAssets(
    userId: String,
    query: AssetListQuery
  ) async throws -> UserAssetsResult

  /// Delete asset (soft delete)
  /// - Parameters:
  ///   - id: Asset ID
  ///   - userId: User ID (for permission verification)
  /// - Returns: Delete response
  func deleteAsset(
    id: String,
    userId: String
  ) async throws -> AssetDeleteResponse

  /// Search user assets
  /// - Parameters:
  ///   - userId: User ID
  ///   - searchTerm: Search keyword
  ///   - type: MIME type filter, optional
  ///   - limit: Result count limit
  /// - Returns: Matching assets list
  func searchAssets(
    userId: String,
    searchTerm: String,
    type: String?,
    limit: Int
  ) async throws -> [UserAsset]

  /// Get assets by tags
  /// - Parameters:
  ///   - userId: User ID
  ///   - tags: Tags list
  ///   - limit: Result count limit
  /// - Returns: Matching assets list
  func getAssetsByTags(
    userId: String,
    tags: [String],
    limit: Int
  ) async throws -> [UserAsset]

  /// Get assets by MIME type
  /// - Parameters:
  ///   - userId: User ID
  ///   - type: MIME type
  ///   - limit: Result count limit
  /// - Returns: Matching assets list
  func getAssetsByType(
    userId: String,
    type: String,
    limit: Int
  ) async throws -> [UserAsset]

  /// Observe user assets list changes
  /// - Parameters:
  ///   - userId: User ID
  ///   - query: Query parameters
  /// - Returns: Assets list changes publisher
  func observeUserAssets(
    userId: String,
    query: AssetListQuery
  ) -> AnyPublisher<UserAssetsResult, Error>
}

// MARK: - Asset Repository Error

/// Asset repository error
public enum AssetRepositoryError: Error, LocalizedError {
  /// User not authenticated
  case userNotAuthenticated
  /// Asset not found
  case assetNotFound
  /// Access denied
  case accessDenied
  /// Database error
  case databaseError(Error)
  /// Unknown error
  case unknown(Error)

  public var errorDescription: String? {
    switch self {
    case .userNotAuthenticated:
      return "User not authenticated"
    case .assetNotFound:
      return "Asset not found"
    case .accessDenied:
      return "Access denied"
    case .databaseError(let error):
      return "Database error: \(error.localizedDescription)"
    case .unknown(let error):
      return "Unknown error: \(error.localizedDescription)"
    }
  }
}
