// MessageRepository.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Combine
import Foundation

/// Message repository error
public enum MessageRepositoryError: Error {
  /// Message not found
  case messageNotFound
  /// Chat not found
  case chatNotFound
  /// Failed to create message
  case failedToCreateMessage
  /// Failed to update message
  case failedToUpdateMessage
  /// Failed to delete message
  case failedToDeleteMessage
  /// Network error
  case networkError(Error)
  /// Database error
  case databaseError(Error)
  /// Unauthorized
  case unauthorized
  /// Other error
  case unknown(Error?)

  public var localizedDescription: String {
    switch self {
    case .messageNotFound:
      return "Message not found"
    case .chatNotFound:
      return "Chat not found"
    case .failedToCreateMessage:
      return "Failed to create message"
    case .failedToUpdateMessage:
      return "Failed to update message"
    case .failedToDeleteMessage:
      return "Failed to delete message"
    case .networkError(let error):
      return "Network error: \(error.localizedDescription)"
    case .databaseError(let error):
      return "Database error: \(error.localizedDescription)"
    case .unauthorized:
      return "Operation unauthorized"
    case .unknown(let error):
      return "Unknown error: \(error?.localizedDescription ?? "No details available")"
    }
  }
}

/// Message repository interface
/// Responsible for persistent storage and retrieval of message data
public protocol MessageRepository {
  /// Get message
  /// - Parameter id: Message ID
  /// - Parameter chatId: Chat ID
  /// - Returns: Message object
  /// - Throws: `MessageRepositoryError`
  func getMessage(id: String, chatId: String) async throws -> Message

  /// Get message list for chat
  /// - Parameters:
  ///   - chatId: Chat ID
  ///   - limit: Return result count limit, default 50
  ///   - startAfter: Pagination start point, optional
  /// - Returns: Message object array
  /// - Throws: `MessageRepositoryError`
  func getMessages(forChatId: String, limit: Int, startAfter: String?) async throws -> [Message]

  /// Save message
  /// - Parameter message: Message object to save
  /// - Returns: Saved message ID
  /// - Throws: `MessageRepositoryError`
  func saveMessage(_ message: Message) async throws -> String

  /// Update message
  /// - Parameter message: Message object to update
  /// - Throws: `MessageRepositoryError`
  func updateMessage(_ message: Message) async throws

  /// Update message status
  /// - Parameters:
  ///   - id: Message ID
  ///   - chatId: Chat ID
  ///   - status: New status
  /// - Throws: `MessageRepositoryError`
  func updateMessageStatus(id: String, chatId: String, status: MessageStatus) async throws

  /// Mark message as read
  /// - Parameters:
  ///   - id: Message ID
  ///   - chatId: Chat ID
  /// - Throws: `MessageRepositoryError`
  func markMessageAsRead(id: String, chatId: String) async throws

  /// Delete message
  /// - Parameters:
  ///   - id: Message ID
  ///   - chatId: Chat ID
  /// - Throws: `MessageRepositoryError`
  func deleteMessage(id: String, chatId: String) async throws

  /// Batch delete messages
  /// - Parameters:
  ///   - ids: Message ID array
  ///   - chatId: Chat ID
  /// - Throws: `MessageRepositoryError`
  func deleteMessages(ids: [String], chatId: String) async throws

  /// Get latest messages for chat
  /// - Parameter chatId: Chat ID
  /// - Parameter limit: Return result count limit, default 1
  /// - Returns: Message object array
  /// - Throws: `MessageRepositoryError`
  func getLatestMessages(forChatId: String, limit: Int) async throws -> [Message]

  /// Get message count for chat
  /// - Parameter chatId: Chat ID
  /// - Returns: Message count
  /// - Throws: `MessageRepositoryError`
  func getMessageCount(forChatId: String) async throws -> Int

  /// Observe message changes
  /// - Parameter chatId: Chat ID
  /// - Returns: Message object array publisher
  func observeMessages(chatId: String) -> AnyPublisher<[Message], Error>

  /// Observe single message changes
  /// - Parameters:
  ///   - id: Message ID
  ///   - chatId: Chat ID
  /// - Returns: Message object publisher
  func observeMessage(id: String, chatId: String) -> AnyPublisher<Message, Error>
} 