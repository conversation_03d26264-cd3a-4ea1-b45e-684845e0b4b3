//
//  VideoTaskRepository.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/15.
//

import Combine
import Foundation

// Protocol defining the interface for accessing video generation task data
public protocol VideoTaskRepository {
  /// Observes changes to a specific video generation task document in Firestore.
  ///
  /// - Parameter taskId: The ID of the task document to observe.
  /// - Returns: A Combine Publisher that emits the `VideoGenerationTask` whenever it changes,
  ///            or completes with an error if the document doesn't exist or another error occurs.
  func observeVideoTask(taskId: String) -> AnyPublisher<VideoGenerationTask, Error>

  /// Fetches a single video generation task by its ID.
  /// - Parameter taskId: The ID of the task to fetch.
  /// - Returns: The requested `VideoGenerationTask`.
  /// - Throws: `VideoTaskRepositoryError.taskNotFound` if the task doesn't exist, or other errors for database/decoding issues.
  func getTask(taskId: String) async throws -> VideoGenerationTask

  /// Fetches a list of video generation tasks for a specific user, ordered by creation date (newest first).
  /// - Parameters:
  ///   - userId: The ID of the user whose tasks to fetch.
  ///   - limit: The maximum number of tasks to return.
  ///   - startAfterTaskId: Optional ID of the task after which to start fetching (for pagination).
  /// - Returns: An array of `VideoGenerationTask` objects.
  /// - Throws: Database or other errors.
  func getTaskList(userId: String, limit: Int, startAfterTaskId: String?) async throws
    -> [VideoGenerationTask]

  /// Observes the task list for a specific user, ordered by creation date (newest first).
  ///
  /// - Parameters:
  ///   - userId: The ID of the user whose tasks to observe.
  ///   - limit: The maximum number of tasks to return in each update.
  /// - Returns: A Combine Publisher that emits an array of `VideoGenerationTask` objects whenever the list updates,
  ///            or completes with an error if an issue occurs.
  func observeTaskList(userId: String, limit: Int) -> AnyPublisher<[VideoGenerationTask], Error>

  /// Updates the status of a specific video generation task.
  ///
  /// - Parameters:
  ///   - taskId: The ID of the task to update.
  ///   - status: The new status to set for the task.
  /// - Throws: Errors if the task status cannot be updated.
  func updateTaskStatus(taskId: String, status: TaskStatus) async throws
}

// Define potential errors specific to the VideoTaskRepository
enum VideoTaskRepositoryError: Error, LocalizedError {
  case taskNotFound
  case decodingError(Error)
  case firestoreError(Error)
  case unknownError(Error)

  var errorDescription: String? {
    switch self {
    case .taskNotFound:
      return "The requested video generation task could not be found."
    case .decodingError(let underlyingError):
      return "Failed to decode video task data: \(underlyingError.localizedDescription)"
    case .firestoreError(let underlyingError):
      return "A Firestore database error occurred: \(underlyingError.localizedDescription)"
    case .unknownError(let underlyingError):
      return "An unknown error occurred: \(underlyingError.localizedDescription)"
    }
  }
}
