// ChatRepository.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Foundation

/// Chat repository error
public enum ChatRepositoryError: Error {
  /// Chat not found
  case chatNotFound
  /// Failed to create chat
  case failedToCreateChat
  /// Failed to update chat
  case failedToUpdateChat
  /// Failed to delete chat
  case failedToDeleteChat
  /// Network error
  case networkError(Error)
  /// Database error
  case databaseError(Error)
  /// Unauthorized
  case unauthorized
  /// Chat archived, cannot modify
  case chatArchived
  /// Chat quota exceeded
  case quotaExceeded
  /// Other error
  case unknown(Error?)

  public var localizedDescription: String {
    switch self {
    case .chatNotFound:
      return "Chat not found"
    case .failedToCreateChat:
      return "Failed to create chat"
    case .failedToUpdateChat:
      return "Failed to update chat"
    case .failedToDeleteChat:
      return "Failed to delete chat"
    case .networkError(let error):
      return "Network error: \(error.localizedDescription)"
    case .databaseError(let error):
      return "Database error: \(error.localizedDescription)"
    case .unauthorized:
      return "Operation unauthorized"
    case .chatArchived:
      return "Chat is archived and cannot be modified"
    case .quotaExceeded:
      return "Chat quota limit reached"
    case .unknown(let error):
      return "Unknown error: \(error?.localizedDescription ?? "No details available")"
    }
  }
}

/// Chat repository interface
/// Responsible for persistent storage and retrieval of chat data
public protocol ChatRepository {
  /// Get chat by specified ID
  /// - Parameter id: Chat ID
  /// - Returns: Chat object
  /// - Throws: `ChatRepositoryError`
  func getChat(id: String) async throws -> Chat

  /// Get all chat list for user
  /// - Parameters:
  ///   - userId: User ID
  ///   - limit: Limit return count, default 50
  ///   - startAfter: Pagination start position, optional
  ///   - status: Filter chats with specific status, optional
  /// - Returns: Chat list
  /// - Throws: `ChatRepositoryError`
  func getUserChats(
    userId: String,
    limit: Int,
    startAfter: String?,
    status: ChatStatus?
  ) async throws -> [Chat]

  /// Save chat
  /// - Parameter chat: Chat object to save
  /// - Returns: Saved chat ID
  /// - Throws: `ChatRepositoryError`
  func saveChat(_ chat: Chat) async throws -> String

  /// Update chat
  /// - Parameter chat: Chat object to update
  /// - Throws: `ChatRepositoryError`
  func updateChat(_ chat: Chat) async throws

  /// Update chat title
  /// - Parameters:
  ///   - chatId: Chat ID
  ///   - title: New title
  /// - Throws: `ChatRepositoryError`
  func updateChatTitle(chatId: String, title: String) async throws

  /// Update chat description
  /// - Parameters:
  ///   - chatId: Chat ID
  ///   - description: New description
  /// - Throws: `ChatRepositoryError`
  func updateChatDescription(chatId: String, description: String?) async throws

  /// Update chat status
  /// - Parameters:
  ///   - chatId: Chat ID
  ///   - status: New status
  /// - Throws: `ChatRepositoryError`
  func updateChatStatus(chatId: String, status: ChatStatus) async throws

  /// Increment chat message count
  /// - Parameter chatId: Chat ID
  /// - Throws: `ChatRepositoryError`
  func incrementMessageCount(chatId: String) async throws

  /// Update chat visibility
  /// - Parameters:
  ///   - chatId: Chat ID
  ///   - visibility: New visibility
  ///   - sharedWithUserIds: Shared user ID list, must provide if visibility is .shared
  /// - Throws: `ChatRepositoryError`
  func updateChatVisibility(
    chatId: String,
    visibility: ChatVisibility,
    sharedWithUserIds: [String]?
  ) async throws

  /// Delete chat (mark as deleted)
  /// - Parameter id: Chat ID to delete
  /// - Throws: `ChatRepositoryError`
  func deleteChat(id: String) async throws

  /// Get chat message count
  /// - Parameter chatId: Chat ID
  /// - Returns: Message count
  /// - Throws: `ChatRepositoryError`
  func getChatMessageCount(chatId: String) async throws -> Int

  /// Check if user can access chat
  /// - Parameters:
  ///   - chatId: Chat ID
  ///   - userId: User ID
  /// - Returns: true if can access
  /// - Throws: `ChatRepositoryError`
  func canUserAccessChat(chatId: String, userId: String) async throws -> Bool

  /// Check if user can edit chat
  /// - Parameters:
  ///   - chatId: Chat ID
  ///   - userId: User ID
  /// - Returns: true if can edit
  /// - Throws: `ChatRepositoryError`
  func canUserEditChat(chatId: String, userId: String) async throws -> Bool
  
  /// Get fork count
  /// - Parameter chatId: Parent chat ID
  /// - Returns: Fork count
  /// - Throws: `ChatRepositoryError`
  func getForkCount(chatId: String) async throws -> Int

  /// Increment fork count
  /// - Parameter chatId: Parent chat ID
  /// - Throws: `ChatRepositoryError`
  func incrementForkCount(chatId: String) async throws
}
