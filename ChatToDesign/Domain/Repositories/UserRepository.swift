// UserRepository.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
//

import Foundation

/// User repository error
public enum UserRepositoryError: Error {
  /// User not found
  case userNotFound
  /// Failed to create user
  case failedToCreateUser
  /// Failed to update user
  case failedToUpdateUser
  /// Failed to delete user
  case failedToDeleteUser
  /// Network error
  case networkError(Error)
  /// Database error
  case databaseError(Error)
  /// Unauthorized
  case unauthorized
  /// Other error
  case unknown(Error?)

  public var localizedDescription: String {
    switch self {
    case .userNotFound:
      return "User not found"
    case .failedToCreateUser:
      return "Failed to create user"
    case .failedToUpdateUser:
      return "Failed to update user"
    case .failedToDeleteUser:
      return "Failed to delete user"
    case .networkError(let error):
      return "Network error: \(error.localizedDescription)"
    case .databaseError(let error):
      return "Database error: \(error.localizedDescription)"
    case .unauthorized:
      return "Operation unauthorized"
    case .unknown(let error):
      return "Unknown error: \(error?.localizedDescription ?? "No details available")"
    }
  }
}

/// User repository interface
/// Responsible for persistent storage and retrieval of user data
public protocol UserRepository {
  /// Get user by specified ID
  /// - Parameter id: User ID
  /// - Returns: User object, returns nil if not found
  /// - Throws: `UserRepositoryError`
  func getUser(id: String) async throws -> User

  /// Find user by email
  /// - Parameter email: User email
  /// - Returns: User object, returns nil if not found
  /// - Throws: `UserRepositoryError`
  func findUserByEmail(email: String) async throws -> User?

  /// Save user
  /// - Parameter user: User object to save
  /// - Returns: Saved user ID
  /// - Throws: `UserRepositoryError`
  func saveUser(_ user: User) async throws -> String

  /// Update user
  /// - Parameter user: User object to update
  /// - Throws: `UserRepositoryError`
  func updateUser(_ user: User) async throws

  /// Update user last login time
  /// - Parameter id: User ID
  /// - Throws: `UserRepositoryError`
  func updateLastLogin(id: String) async throws

  /// Delete user
  /// - Parameter id: User ID to delete
  /// - Throws: `UserRepositoryError`
  func deleteUser(id: String) async throws

  /// Check if user exists
  /// - Parameter id: User ID
  /// - Returns: true if exists, false otherwise
  /// - Throws: `UserRepositoryError`
  func userExists(id: String) async throws -> Bool
} 