//
//  FeedbackService.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/20.
//

import Foundation

/// Service for handling user feedback operations
public protocol FeedbackService {
    
    /// Submit user feedback
    /// - Parameter feedback: The feedback to submit
    /// - Throws: FeedbackServiceError if submission fails
    func submitFeedback(_ feedback: Feedback) async throws
}

/// Errors that can occur during feedback operations
public enum FeedbackServiceError: Error, LocalizedError {
    /// Network connection error
    case networkError(Error)
    
    /// Server returned an error
    case serverError(statusCode: Int, message: String)
    
    /// Invalid feedback data
    case invalidFeedback(String)
    
    /// Feedback content too long
    case contentTooLong(maxLength: Int)
    
    /// Too many attachments
    case tooManyAttachments(maxCount: Int)
    
    /// Unknown error
    case unknown(Error)
    
    /// Localized error description
    public var errorDescription: String? {
        switch self {
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .serverError(let statusCode, let message):
            return "Server error (\(statusCode)): \(message)"
        case .invalidFeedback(let reason):
            return "Invalid feedback: \(reason)"
        case .contentTooLong(let maxLength):
            return "Feedback content is too long. Maximum length is \(maxLength) characters."
        case .tooManyAttachments(let maxCount):
            return "Too many attachments. Maximum allowed is \(maxCount)."
        case .unknown(let error):
            return "Unknown error: \(error.localizedDescription)"
        }
    }
}
