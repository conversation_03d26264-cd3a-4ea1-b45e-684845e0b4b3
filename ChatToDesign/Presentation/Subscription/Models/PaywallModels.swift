import Foundation

// MARK: - Paywall Feature Model

/// 功能对比数据模型
public struct PaywallFeature: Identifiable, Codable, Equatable {
  /// 功能唯一标识
  public let id: String

  /// 功能名称
  public let name: String

  /// 免费版是否可用
  public let freeAvailable: Bool

  /// 专业版是否可用
  public let proAvailable: Bool

  /// 功能图标名称（可选）
  public let icon: String?

  /// 功能描述（可选）
  public let description: String?

  /// 初始化功能模型
  /// - Parameters:
  ///   - id: 功能唯一标识
  ///   - name: 功能名称
  ///   - freeAvailable: 免费版是否可用
  ///   - proAvailable: 专业版是否可用
  ///   - icon: 功能图标名称
  ///   - description: 功能描述
  public init(
    id: String,
    name: String,
    freeAvailable: Bool,
    proAvailable: Bool,
    icon: String? = nil,
    description: String? = nil
  ) {
    self.id = id
    self.name = name
    self.freeAvailable = freeAvailable
    self.proAvailable = proAvailable
    self.icon = icon
    self.description = description
  }
}

// MARK: - Paywall Configuration Model

/// Paywall 配置模型
public struct PaywallConfiguration: Codable, Equatable {
  /// 主标题
  public let title: String

  /// 副标题
  public let subtitle: String?

  /// 背景图片URL
  public let backgroundImageUrl: String?

  /// 功能列表
  public let features: [PaywallFeature]

  /// 是否显示关闭按钮
  public let showCloseButton: Bool

  /// 购买按钮文本（可选，如果不提供则使用产品的默认文本）
  public let purchaseButtonText: String?

  /// 法律链接文本
  public let legalLinkText: String?

  /// 初始化配置模型
  /// - Parameters:
  ///   - title: 主标题
  ///   - subtitle: 副标题
  ///   - backgroundImageUrl: 背景图片URL
  ///   - features: 功能列表
  ///   - selectedProduct: 当前选中的产品信息
  ///   - showCloseButton: 是否显示关闭按钮
  ///   - purchaseButtonText: 购买按钮文本
  ///   - legalLinkText: 法律链接文本
  public init(
    title: String,
    subtitle: String? = nil,
    backgroundImageUrl: String? = nil,
    features: [PaywallFeature],
    showCloseButton: Bool = true,
    purchaseButtonText: String? = nil,
    legalLinkText: String? = "How your free trial works"
  ) {
    self.title = title
    self.subtitle = subtitle
    self.backgroundImageUrl = backgroundImageUrl
    self.features = features
    self.showCloseButton = showCloseButton
    self.purchaseButtonText = purchaseButtonText
    self.legalLinkText = legalLinkText
  }
}

// MARK: - Default Configurations

extension PaywallConfiguration {
  /// 默认配置
  public static let `default` = PaywallConfiguration(
    title: "Get the full experience",
    subtitle: nil,
    backgroundImageUrl: nil,
    features: PaywallFeature.defaultFeatures,
    showCloseButton: true,
    purchaseButtonText: "Start free trial",
    legalLinkText: "How your free trial works"
  )
}

extension PaywallFeature {
  /// 默认功能列表
  public static let defaultFeatures: [PaywallFeature] = [
    PaywallFeature(
      id: "credits",
      name: "300+ Credits/monthly",
      freeAvailable: false,
      proAvailable: true,
      icon: "checkmark.circle.fill"
    ),
    PaywallFeature(
      id: "watermark",
      name: "Remove Watermark",
      freeAvailable: false,
      proAvailable: true,
      icon: "checkmark.circle.fill"
    ),
    PaywallFeature(
      id: "queue",
      name: "Advanced Queue",
      freeAvailable: false,
      proAvailable: true,
      icon: "checkmark.circle.fill"
    ),
    PaywallFeature(
      id: "enhancement",
      name: "Video Enhancement",
      freeAvailable: false,
      proAvailable: true,
      icon: "checkmark.circle.fill"
    ),
    PaywallFeature(
      id: "templates",
      name: "Unlimited Templates",
      freeAvailable: false,
      proAvailable: true,
      icon: "checkmark.circle.fill"
    ),
  ]
}

// MARK: - Convenience Methods

extension PaywallFeature {
  /// 是否为专业版独有功能
  public var isProOnly: Bool {
    return proAvailable && !freeAvailable
  }

  /// 是否为免费功能
  public var isFree: Bool {
    return freeAvailable
  }
}

extension PaywallConfiguration {
  /// 专业版独有功能数量
  public var proOnlyFeaturesCount: Int {
    return features.filter { $0.isProOnly }.count
  }

  /// 免费功能数量
  public var freeFeaturesCount: Int {
    return features.filter { $0.isFree }.count
  }
}
