//
//  TrialTimelineStep.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/9.
//

import SwiftUI

/// 试用时间线步骤数据模型
public struct TrialTimelineStep: Identifiable, Equatable {
  
  // MARK: - Properties
  
  /// 唯一标识符
  public let id: String
  
  /// 图标名称
  public let iconName: String
  
  /// 图标背景颜色
  public let iconBackgroundColor: Color
  
  /// 步骤标题
  public let title: String
  
  /// 步骤描述
  public let description: String
  
  /// 是否已完成
  public let isCompleted: Bool
  
  /// 是否显示连接线
  public let showConnector: Bool
  
  // MARK: - Initialization
  
  /// 初始化时间线步骤
  /// - Parameters:
  ///   - id: 唯一标识符
  ///   - iconName: 图标名称
  ///   - iconBackgroundColor: 图标背景颜色
  ///   - title: 步骤标题
  ///   - description: 步骤描述
  ///   - isCompleted: 是否已完成
  ///   - showConnector: 是否显示连接线
  public init(
    id: String,
    iconName: String,
    iconBackgroundColor: Color,
    title: String,
    description: String,
    isCompleted: Bool = false,
    showConnector: Bool = true
  ) {
    self.id = id
    self.iconName = iconName
    self.iconBackgroundColor = iconBackgroundColor
    self.title = title
    self.description = description
    self.isCompleted = isCompleted
    self.showConnector = showConnector
  }
}

// MARK: - Default Timeline Steps

extension TrialTimelineStep {
  /// 默认的试用时间线步骤
  public static let defaultSteps: [TrialTimelineStep] = [
    TrialTimelineStep(
      id: "today",
      iconName: "checkmark",
      iconBackgroundColor: PaywallDesignSystem.Colors.brand,
      title: "Today: Unlock all features",
      description: "You can use all Pro functions with no restrictions.",
      isCompleted: true,
      showConnector: true
    ),
    TrialTimelineStep(
      id: "day2",
      iconName: "bell",
      iconBackgroundColor: PaywallDesignSystem.Colors.muted,
      title: "Day 2: Trail reminder",
      description: "We'll send you a reminder when your trial period is almost over.",
      isCompleted: false,
      showConnector: true
    ),
    TrialTimelineStep(
      id: "day3",
      iconName: "star",
      iconBackgroundColor: PaywallDesignSystem.Colors.muted,
      title: "Day 3: Trail ends",
      description: "Your paid Pro membership starts on\nNovember 4, 2024, You can Cancel before",
      isCompleted: false,
      showConnector: false
    )
  ]
}

// MARK: - Convenience Methods

extension TrialTimelineStep {
  /// 是否为当前步骤
  public var isCurrent: Bool {
    return !isCompleted
  }
  
  /// 图标颜色（根据完成状态）
  public var iconColor: Color {
    return isCompleted ? .white : .white
  }
}
