//
//  SubscriptionPlan.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/9.
//

import Foundation

/// 订阅套餐模型 - 组合 ProductInfo 和 UI 特定信息
public struct SubscriptionPlan: Identifiable, Codable, Equatable {
  /// 套餐唯一标识（来自 ProductInfo）
  public var id: String {
    return productInfo.identifier
  }

  /// 核心产品信息
  public let productInfo: ProductInfo

  /// UI 特定的标题（如 "Annual", "Weekly"）
  public let uiTitle: String

  /// UI 特定的推荐标签
  public let uiBadge: String?

  /// UI 特定的试用信息格式
  public let uiTrialInfo: String

  /// 初始化订阅套餐
  /// - Parameters:
  ///   - productInfo: 核心产品信息
  ///   - uiTitle: UI 显示标题
  ///   - uiBadge: UI 推荐标签
  ///   - uiTrialInfo: UI 试用信息
  public init(
    productInfo: ProductInfo,
    uiTitle: String? = nil,
    uiBadge: String? = nil,
    uiTrialInfo: String? = nil
  ) {
    self.productInfo = productInfo
    self.uiTitle = uiTitle ?? Self.generateUITitle(from: productInfo)
    self.uiBadge = uiBadge ?? Self.generateUIBadge(from: productInfo)
    self.uiTrialInfo = uiTrialInfo ?? Self.generateUITrialInfo(from: productInfo)
  }

  /// 生成 UI 标题
  private static func generateUITitle(from productInfo: ProductInfo) -> String {
    if productInfo.isAnnual {
      return "Annual"
    } else if productInfo.isWeekly {
      return "Weekly"
    } else if productInfo.isMonthly {
      return "Monthly"
    } else {
      return productInfo.displayName
    }
  }

  /// 生成 UI 标签
  private static func generateUIBadge(from productInfo: ProductInfo) -> String? {
    return productInfo.isAnnual ? "Destructive" : nil
  }

  /// 生成 UI 试用信息
  private static func generateUITrialInfo(from productInfo: ProductInfo) -> String {
    if productInfo.hasFreeTrial {
      let trialText =
        "\(productInfo.trialPeriodInfo?.value ?? 0)-\(productInfo.trialPeriodInfo?.unit.rawValue ?? "") free trial"
      let periodText = productInfo.periodInfo?.unit.rawValue.lowercased() ?? ""
      return "\(trialText), then \(productInfo.price)/\(periodText)"
    } else {
      return "\(productInfo.price)/\(productInfo.periodInfo?.unit.rawValue.lowercased() ?? "")"
    }
  }
}

// MARK: - 从 ProductInfo 转换

extension SubscriptionPlan {
  /// 从 ProductInfo 创建 SubscriptionPlan
  /// - Parameter productInfo: RevenueCat 产品信息
  /// - Returns: 订阅套餐模型
  public static func from(_ productInfo: ProductInfo) -> SubscriptionPlan {

    let uiTitle = generateUITitle(from: productInfo)
    let uiTrialInfo = generateUITrialInfo(from: productInfo)
    let uiBadge = generateUIBadge(from: productInfo)

    return SubscriptionPlan(
      productInfo: productInfo,
      uiTitle: uiTitle,
      uiBadge: uiBadge,
      uiTrialInfo: uiTrialInfo
    )
  }

  /// 从 ProductInfo 数组创建 SubscriptionPlan 数组
  /// - Parameter products: RevenueCat 产品信息数组
  /// - Returns: 订阅套餐模型数组
  public static func from(_ products: [ProductInfo]) -> [SubscriptionPlan] {
    return products.map { from($0) }
  }
}
