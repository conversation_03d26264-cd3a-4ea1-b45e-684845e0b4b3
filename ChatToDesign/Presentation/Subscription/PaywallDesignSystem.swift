import SwiftUI

/// PaywallView 设计系统
/// 基于 Figma 设计定义的颜色、字体、间距等设计令牌
public struct PaywallDesignSystem {

  // MARK: - Colors

  /// 颜色系统
  public struct Colors {
    /// 主色调 - 蓝色
    public static let brand = Color(hex: "#536db0")

    /// 背景色 - 深黑
    public static let background = Color(hex: "#09090b")

    /// 卡片背景 - 深灰
    public static let cardBackground = Color(hex: "#27272a")

    public static let primaryForeground = Color(hex: "#18181b")

    /// 输入框背景
    public static let input = Color(hex: "#27272a")

    /// 静音背景
    public static let muted = Color(hex: "#27272a")

    /// 弹窗背景
    public static let sheetBackground = Color(hex: "333333")

    /// 主要文本色 - 白色
    public static let foreground = Color(hex: "#fafafa")

    /// 次要文本色 - 灰色
    public static let mutedForeground = Color(hex: "#a1a1aa")

    /// 标签主要颜色 - 白色
    public static let labelPrimary = Color(hex: "#ffffff")

    /// 系统背景主要颜色 - 白色
    public static let systemBackgroundPrimary = Color(hex: "#ffffff")

    /// 中性模糊深色
    public static let neutralBlurDark = Color(hex: "#0c0c0c")

    /// 白色
    public static let white = Color(hex: "#ffffff")

    /// 卡片边框色 - 渐变边框，使用白色30%透明度加8%整体透明度
    public static let cardBorder = Colors.white.opacity(0.30 * 0.08)

    /// 灰色
    public static let gray = Color(hex: "#808080")
  }

  // MARK: - Gradients

  /// 渐变系统
  public struct Gradients {
    /// Pro 标签渐变背景
    /// 基于 CSS: linear-gradient(98deg, #E10000 -2.37%, #FCB104 101.53%)
    public static let proLabelGradient = LinearGradient(
      gradient: Gradient(colors: [
        Color(red: 0.882, green: 0.0, blue: 0.0),  // #E10000
        Color(red: 0.988, green: 0.694, blue: 0.024),  // #FCB104
      ]),
      startPoint: UnitPoint(x: -0.0237, y: 0.98),
      endPoint: UnitPoint(x: 1.0153, y: 0.98)
    )
  }

  // MARK: - Typography

  /// 字体系统
  public struct Typography {
    /// 标题字体 - Playfair Display Medium 24pt
    public static let title = Font.custom("Playfair Display", size: 24)
      .weight(.medium)

    /// 小号中等字体 - Inter Medium 14pt
    /// 对应 CSS: font-size: 14px, font-weight: 500, line-height: 20px
    public static let textSmallMedium = Font.custom("Inter", size: 14)
      .weight(.medium)

    /// 小号常规字体 - Inter Regular 14pt
    public static let textSmallNormal = Font.custom("Inter", size: 14)
      .weight(.regular)

    /// 小号半粗字体 - Inter Semi Bold 14pt
    public static let textSmallSemibold = Font.custom("Inter", size: 14)
      .weight(.semibold)

    /// 2XL 字体 - 24pt
    public static let text2XL = Font.system(size: 24)
  }

  // MARK: - Spacing

  /// 间距系统
  public struct Spacing {
    /// 1 = 4pt
    public static let spacing1: CGFloat = 4

    /// 2 = 8pt
    public static let spacing2: CGFloat = 8

    /// 3 = 12pt
    public static let spacing3: CGFloat = 12

    /// 4 = 16pt
    public static let spacing4: CGFloat = 16

    /// 5 = 20pt
    public static let spacing5: CGFloat = 20

    /// 6 = 24pt
    public static let spacing6: CGFloat = 24

    /// 8 = 32pt
    public static let spacing8: CGFloat = 32

    /// 10 = 40pt
    public static let spacing10: CGFloat = 40

    /// 11 = 44pt
    public static let spacing11: CGFloat = 44

    /// 36 = 144pt
    public static let spacing36: CGFloat = 144
  }

  // MARK: - Radius

  /// 圆角系统
  public struct Radius {
    /// 2XL = 16pt
    public static let radius2XL: CGFloat = 16

    /// 完全圆角 = 9999pt
    public static let radiusFull: CGFloat = 9999
  }

  // MARK: - Dimensions

  /// 尺寸系统
  public struct Dimensions {
    /// 宽度 W-6 = 24pt
    public static let width6: CGFloat = 24

    /// 高度 H-6 = 24pt
    public static let height6: CGFloat = 24

    /// 高度 H-10 = 40pt
    public static let height10: CGFloat = 40
  }

  // MARK: - Padding

  /// 内边距系统
  public struct Padding {
    /// X轴内边距 px-4 = 16pt
    public static let paddingX4: CGFloat = 16

    /// Y轴内边距 py-2 = 8pt
    public static let paddingY2: CGFloat = 8
  }

  // MARK: - Font Tracking

  /// 字体间距系统
  public struct FontTracking {
    /// 紧密间距 = -0.4pt
    public static let tight: CGFloat = -0.4
  }

  // MARK: - Font Leading

  /// 行高系统
  public struct FontLeading {
    /// 行高 5 = 20pt (对应 CSS line-height: 20px)
    public static let leading5: CGFloat = 20

    /// 行高 8 = 32pt
    public static let leading8: CGFloat = 32
  }

  // MARK: - Font Weight

  /// 字体粗细系统
  public struct FontWeight {
    /// 中等粗细 = 500
    public static let medium: Font.Weight = .medium
  }
}

// MARK: - Convenience Extensions

extension PaywallDesignSystem.Colors {
  /// 功能可用状态颜色 - 品牌色
  public static let featureAvailable = brand

  /// 功能不可用状态颜色 - 深灰
  public static let featureUnavailable = Color(hex: "#313135")
}

extension PaywallDesignSystem.Typography {
  /// 标题样式（带字间距）
  public static var titleWithTracking: Font {
    return title
  }

  /// Pro标签专用字体样式
  /// 对应 CSS: Inter, 14px, font-weight: 500, line-height: 20px, letter-spacing: 0px
  public static var proLabel: Font {
    return Font.custom("Inter", size: 14).weight(.medium)
  }
}

// MARK: - Design Tokens for Specific Components

extension PaywallDesignSystem {
  /// 功能对比表格设计令牌
  public struct FeatureComparison {
    /// 卡片背景色（带透明度）
    public static let cardBackground = Colors.cardBackground.opacity(0.7)

    /// 表头高度
    public static let headerHeight: CGFloat = Dimensions.height10 + Spacing.spacing1

    /// 功能行高度
    public static let rowHeight: CGFloat = Dimensions.height10 + Spacing.spacing1
  }

  /// 购买按钮设计令牌
  public struct PurchaseButton {
    /// 按钮背景色
    public static let backgroundColor = Colors.brand

    /// 按钮高度
    public static let height = Dimensions.height10

    /// 按钮圆角
    public static let cornerRadius = Radius.radiusFull

    /// 按钮内边距
    public static let paddingHorizontal = Padding.paddingX4
    public static let paddingVertical = Padding.paddingY2
  }

  /// 背景设计令牌
  public struct Background {
    /// 渐变起始位置
    public static let gradientStartLocation: CGFloat = 0.5

    /// 渐变结束位置
    public static let gradientEndLocation: CGFloat = 1.0

    /// 模糊半径
    public static let blurRadius: CGFloat = 30

    /// 模糊层透明度
    public static let blurOpacity: Double = 0.08
  }
}
