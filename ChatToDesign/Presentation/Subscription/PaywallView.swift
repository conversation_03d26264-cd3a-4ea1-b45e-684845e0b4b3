//
//  PaywallView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/17.
//

import RevenueCat
import SwiftUI

/// 自定义 Paywall 视图
/// 提供完整的事件跟踪和自定义 UI
public struct PaywallView: View {

  // MARK: - Properties

  /// 展示选项
  public let options: PaywallPresentationOptions

  /// 关闭回调
  public let onDismiss: () -> Void

  /// 购买完成回调
  public let onPurchaseCompleted: ((Subscription) -> Void)?

  /// 恢复完成回调
  public let onRestoreCompleted: ((Subscription?) -> Void)?

  /// 视图模型
  @StateObject private var viewModel = PaywallViewModel()

  /// 分析跟踪器
  @StateObject private var analyticsTracker = SimplePaywallAnalyticsTracker()

  /// 环境变量
  @Environment(\.dismiss) private var dismiss
  @Environment(\.colorScheme) private var colorScheme

  // MARK: - Initialization

  /// 初始化 Paywall 视图
  /// - Parameters:
  ///   - options: 展示选项
  ///   - onDismiss: 关闭回调
  ///   - onPurchaseCompleted: 购买完成回调
  ///   - onRestoreCompleted: 恢复完成回调
  public init(
    options: PaywallPresentationOptions = .sheet(),
    onDismiss: @escaping () -> Void,
    onPurchaseCompleted: ((Subscription) -> Void)? = nil,
    onRestoreCompleted: ((Subscription?) -> Void)? = nil
  ) {
    self.options = options
    self.onDismiss = onDismiss
    self.onPurchaseCompleted = onPurchaseCompleted
    self.onRestoreCompleted = onRestoreCompleted
  }

  // MARK: - Body

  public var body: some View {
    ZStack {
      // 背景层
      PaywallBackgroundView(
        imageUrl: nil,  // 使用默认背景
        enableAnimation: true
      )

      if viewModel.isLoading && viewModel.availablePlans.isEmpty {
        // 加载状态
        loadingView
      } else if let error = viewModel.errorMessage, viewModel.availablePlans.isEmpty {
        // 错误状态
        errorView(error)
      } else {
        // 主要内容
        newMainContentView
      }
    }
    .ignoresSafeArea()
    .alert("错误", isPresented: $viewModel.showErrorAlert) {
      Button("确定") {
        viewModel.clearError()
      }
    } message: {
      Text(viewModel.errorMessage ?? "发生未知错误")
    }
    .alert("成功", isPresented: $viewModel.showSuccessAlert) {
      Button("确定") {
        viewModel.clearSuccess()
        analyticsTracker.trackPaywallDismissed(reason: .purchaseCompleted)
        handleDismiss()
      }
    } message: {
      Text(viewModel.successMessage ?? "操作成功")
    }

    .onAppear {
      setupPaywall()
    }
    .onDisappear {
      if !viewModel.showSuccessAlert {
        analyticsTracker.trackPaywallDismissed(reason: .userAction)
      }
    }
  }

  // MARK: - Private Views

  /// 背景视图
  private var backgroundView: some View {
    LinearGradient(
      colors: [
        Color(.systemBackground),
        Color(.systemGroupedBackground),
      ],
      startPoint: .top,
      endPoint: .bottom
    )
    .ignoresSafeArea()
  }

  /// 加载视图
  private var loadingView: some View {
    VStack(spacing: 20) {
      ProgressView()
        .scaleEffect(1.5)
        .tint(.accentColor)

      Text("正在加载产品信息...")
        .font(.headline)
        .foregroundColor(.secondary)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }

  /// 错误视图
  private func errorView(_ message: String) -> some View {
    VStack(spacing: 20) {
      Image(systemName: "exclamationmark.triangle")
        .font(.system(size: 50))
        .foregroundColor(.orange)

      Text("加载失败")
        .font(.title2)
        .fontWeight(.bold)

      Text(message)
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
        .padding(.horizontal)

      Button("重试") {
        Task {
          await viewModel.loadProducts()
        }
      }
      .buttonStyle(.borderedProminent)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }

  /// 新的主要内容视图
  private var newMainContentView: some View {
    ZStack {
      VStack(spacing: 0) {
        // 头部视图
        PaywallHeaderView(
          title: "Get the full experience",
          subtitle: nil,
          showCloseButton: viewModel.shouldShowCloseButton,
          onClose: {
            analyticsTracker.trackPaywallDismissed(reason: .userAction)
            handleDismiss()
          },
          enableAnimation: true
        )

        Spacer()

        // 功能对比视图
        FeatureComparisonView(
          features: viewModel.features,
          enableAnimation: true
        )
        .padding(.horizontal, PaywallDesignSystem.Spacing.spacing6)
        .padding(.bottom, PaywallDesignSystem.Spacing.spacing6)

        Spacer()
      }

      // 底部购买区域 - 悬浮在底部
      PaywallFooterView(
        viewModel: viewModel,
        onPurchase: {
          handlePurchase()
        },
        onPlanSelected: { plan in
          handlePlanSelection(plan)
        },
        enableAnimation: true
      )
    }
    .transition(
      .asymmetric(
        insertion: .opacity.combined(with: .scale(scale: 0.95)),
        removal: .opacity.combined(with: .scale(scale: 1.05))
      ))
  }

  // MARK: - Event Handlers

  /// 设置付费墙
  private func setupPaywall() {
    analyticsTracker.trackPaywallPresented(trigger: "manual")
    viewModel.paywallService.sendEvent(.presented)

    // 只在视图首次出现时加载产品
    if viewModel.availablePlans.isEmpty && !viewModel.isLoading {
      Task {
        await viewModel.loadProducts()

        // 如果加载失败且没有产品，使用模拟产品进行测试
        if viewModel.availablePlans.isEmpty && viewModel.errorMessage != nil {
          print("PaywallView: 使用模拟产品进行测试")
          // 这里可以设置一些模拟产品用于 UI 测试
          // 注意：这只是为了 UI 展示，实际购买仍然需要真实的 RevenueCat 配置
        }

        // 产品加载完成
        print("PaywallView: 产品加载完成，共 \(viewModel.availablePlans.count) 个产品")
      }
    }
  }

  /// 处理关闭
  private func handleDismiss() {
    onDismiss()
    dismiss()
  }

  /// 处理购买操作
  private func handlePurchase() {
    let planToPurchase: SubscriptionPlan?

    if let selectedPlan = viewModel.selectedPlan {
      planToPurchase = selectedPlan
    } else if let firstPlan = viewModel.availablePlans.first {
      planToPurchase = firstPlan
    } else {
      planToPurchase = nil
    }

    guard let plan = planToPurchase else {
      // 如果没有产品，先加载产品
      Task {
        await viewModel.loadProducts()
        if let selectedPlan = viewModel.selectedPlan {
          await viewModel.purchaseProduct(selectedPlan.id)
        }
      }
      return
    }

    analyticsTracker.trackPurchaseButtonTapped(
      productId: plan.productInfo.identifier,
      productName: plan.productInfo.displayName,
      price: plan.productInfo.price
    )

    Task {
      await viewModel.purchaseProduct(plan.id)
    }
  }

  /// 处理产品选择
  /// - Parameter product: 选中的产品
  private func handlePlanSelection(_ plan: SubscriptionPlan) {
    // 更新视图模型中的产品信息
    viewModel.updateSelectedPlan(plan)

    // 记录分析事件
    analyticsTracker.trackProductSelected(
      productId: plan.productInfo.identifier,
      productName: plan.productInfo.displayName,
      price: plan.productInfo.price
    )

    Logger.info("PaywallView: 用户选择产品 - \(plan.productInfo.displayName)")
  }
}

// MARK: - 便利初始化器

extension PaywallView {
  /// 创建条件展示的 Paywall（基于权限）
  /// - Parameters:
  ///   - requiredEntitlement: 需要的权限
  ///   - onDismiss: 关闭回调
  /// - Returns: 配置好的 PaywallView
  public static func conditional(
    requiredEntitlement: EntitlementType,
    onDismiss: @escaping () -> Void
  ) -> PaywallView {
    return PaywallView(
      options: .conditional(requiredEntitlement: requiredEntitlement),
      onDismiss: onDismiss
    )
  }

  /// 创建条件展示的 Paywall（基于功能）
  /// - Parameters:
  ///   - requiredFeature: 需要的功能
  ///   - onDismiss: 关闭回调
  /// - Returns: 配置好的 PaywallView
  public static func conditional(
    requiredFeature: AppFeature,
    onDismiss: @escaping () -> Void
  ) -> PaywallView {
    return PaywallView(
      options: .conditional(requiredFeature: requiredFeature),
      onDismiss: onDismiss
    )
  }

  /// 创建 Sheet 模式的 Paywall
  /// - Parameters:
  ///   - offeringId: 特定的 Offering ID
  ///   - onDismiss: 关闭回调
  /// - Returns: 配置好的 PaywallView
  public static func sheet(
    offeringId: String? = nil,
    onDismiss: @escaping () -> Void
  ) -> PaywallView {
    return PaywallView(
      options: .sheet(offeringId: offeringId),
      onDismiss: onDismiss
    )
  }

  /// 创建全屏模式的 Paywall
  /// - Parameters:
  ///   - offeringId: 特定的 Offering ID
  ///   - onDismiss: 关闭回调
  /// - Returns: 配置好的 PaywallView
  public static func fullScreen(
    offeringId: String? = nil,
    onDismiss: @escaping () -> Void
  ) -> PaywallView {
    return PaywallView(
      options: .fullScreen(offeringId: offeringId),
      onDismiss: onDismiss
    )
  }
}

// MARK: - SwiftUI 预览

#Preview {
  PaywallView(
    onDismiss: {}
  )
}

// MARK: - Simple Analytics Tracker

/// 简化的付费墙分析跟踪器
@MainActor
final class SimplePaywallAnalyticsTracker: ObservableObject {

  private var presentationStartTime: Date?

  init() {}

  func trackPaywallPresented(
    trigger: String = "unknown", offeringId: String? = nil, userId: String? = nil
  ) {
    presentationStartTime = Date()
    print("PaywallAnalyticsTracker: 付费墙展示 - trigger: \(trigger)")
  }

  func trackPaywallDismissed(reason: PaywallDismissReason = .userAction, userId: String? = nil) {
    let viewDuration = calculateViewDuration()
    print("PaywallAnalyticsTracker: 付费墙关闭 - reason: \(reason.rawValue), duration: \(viewDuration)s")
  }

  func trackProductViewed(
    productId: String, productName: String, price: String, userId: String? = nil
  ) {
    print("PaywallAnalyticsTracker: 产品查看 - \(productName)")
  }

  func trackProductSelected(
    productId: String, productName: String, price: String, userId: String? = nil
  ) {
    print("PaywallAnalyticsTracker: 产品选择 - \(productName)")
  }

  func trackPurchaseButtonTapped(
    productId: String, productName: String, price: String, userId: String? = nil
  ) {
    print("PaywallAnalyticsTracker: 购买按钮点击 - \(productName)")
  }

  func trackRestoreButtonTapped(userId: String? = nil) {
    print("PaywallAnalyticsTracker: 恢复购买按钮点击")
  }

  func trackPaywallError(error: Error, context: String = "unknown", userId: String? = nil) {
    print("PaywallAnalyticsTracker: 付费墙错误 - \(error.localizedDescription)")
  }

  private func calculateViewDuration() -> TimeInterval {
    guard let startTime = presentationStartTime else { return 0 }
    return Date().timeIntervalSince(startTime)
  }
}

/// 付费墙关闭原因
enum PaywallDismissReason: String, CaseIterable {
  case userAction = "user_action"
  case purchaseCompleted = "purchase_completed"
  case restoreCompleted = "restore_completed"
  case systemDismiss = "system_dismiss"
  case error = "error"
}
