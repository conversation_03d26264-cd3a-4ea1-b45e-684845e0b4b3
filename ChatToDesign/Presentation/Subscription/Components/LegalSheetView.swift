//
//  LegalSheetView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/9.
//

import SwiftUI

/// 法律条款弹窗视图
/// 展示"How your free trial works"的详细信息
public struct LegalSheetView: View {

  // MARK: - Properties

  /// 是否显示弹窗
  @Binding var isPresented: Bool

  /// 时间线步骤数据
  private let timelineSteps = TrialTimelineStep.defaultSteps

  // MARK: - Initialization

  /// 初始化法律条款弹窗
  /// - Parameter isPresented: 是否显示弹窗的绑定
  public init(isPresented: Binding<Bool>) {
    self._isPresented = isPresented
  }

  // MARK: - Body

  public var body: some View {
    ZStack {
      // 背景
      sheetBackground

      // 主要内容
      VStack(spacing: 0) {
        // 头部区域
        headerSection

        // 时间线内容
        timelineSection

        Spacer()
      }
      .padding(.horizontal, PaywallDesignSystem.Spacing.spacing6)
    }
    .ignoresSafeArea(.container, edges: .bottom)
  }

  // MARK: - Background

  /// 弹窗背景
  private var sheetBackground: some View {
    RoundedRectangle(cornerRadius: 24, style: .continuous)
      .fill(PaywallDesignSystem.Colors.sheetBackground)
      .ignoresSafeArea(.container, edges: .bottom)
  }

  // MARK: - Header Section

  /// 头部区域
  private var headerSection: some View {
    VStack(spacing: 0) {

      // 标题
      headerTitle
        .padding(.top, PaywallDesignSystem.Spacing.spacing6)
    }
  }

  /// 头部标题
  private var headerTitle: some View {
    Text("How your free trial works")
      .font(.custom("Inter", size: 16).weight(.semibold))
      .foregroundColor(PaywallDesignSystem.Colors.foreground)
      .multilineTextAlignment(.center)
  }

  // MARK: - Timeline Section

  /// 时间线区域
  private var timelineSection: some View {
    VStack(spacing: PaywallDesignSystem.Spacing.spacing6) {
      ForEach(timelineSteps) { step in
        TimelineStepView(step: step)
      }
    }
    .padding(.top, PaywallDesignSystem.Spacing.spacing6)
    .padding(.vertical, PaywallDesignSystem.Spacing.spacing6)
  }
}
