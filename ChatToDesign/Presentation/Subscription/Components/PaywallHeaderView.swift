import SwiftUI

/// Paywall 头部视图
/// 包含关闭按钮和标题部分
public struct PaywallHeaderView: View {

  // MARK: - Properties

  /// 标题文本
  let title: String

  /// 副标题文本
  let subtitle: String?

  /// 是否显示关闭按钮
  let showCloseButton: Bool

  /// 关闭按钮点击回调
  let onClose: () -> Void

  /// 是否启用动画
  let enableAnimation: Bool

  /// 状态变量
  @State private var titleVisible = false
  @State private var closeButtonVisible = false

  // MARK: - Initialization

  /// 初始化头部视图
  /// - Parameters:
  ///   - title: 标题文本
  ///   - subtitle: 副标题文本
  ///   - showCloseButton: 是否显示关闭按钮
  ///   - onClose: 关闭按钮点击回调
  ///   - enableAnimation: 是否启用动画
  public init(
    title: String,
    subtitle: String? = nil,
    showCloseButton: Bool = true,
    onClose: @escaping () -> Void,
    enableAnimation: Bool = true
  ) {
    self.title = title
    self.subtitle = subtitle
    self.showCloseButton = showCloseButton
    self.onClose = onClose
    self.enableAnimation = enableAnimation
  }

  // MARK: - Body

  public var body: some View {
    VStack(spacing: 0) {
      // 状态栏占位
      statusBarSpacer

      // 头部内容
      headerContent
    }
  }

  // MARK: - Status Bar Spacer

  /// 状态栏占位空间
  private var statusBarSpacer: some View {
    Rectangle()
      .fill(Color.clear)
      .frame(height: 47)  // 状态栏高度
  }

  // MARK: - Header Content

  /// 头部内容
  private var headerContent: some View {
    ZStack {
      // 关闭按钮
      if showCloseButton {
        closeButton
      }

      // 标题部分
      titleSection
    }
    .padding(.horizontal, PaywallDesignSystem.Spacing.spacing5)
    .onAppear {
      if enableAnimation {
        // 延迟显示动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
          withAnimation(.easeInOut(duration: 0.6)) {
            titleVisible = true
          }
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
          withAnimation(.easeInOut(duration: 0.4)) {
            closeButtonVisible = true
          }
        }
      } else {
        titleVisible = true
        closeButtonVisible = true
      }
    }
  }

  // MARK: - Close Button

  /// 关闭按钮
  private var closeButton: some View {
    HStack {
      Button(action: onClose) {
        Image(systemName: "xmark")
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(PaywallDesignSystem.Colors.foreground)
          .frame(
            width: PaywallDesignSystem.Dimensions.width6,
            height: PaywallDesignSystem.Dimensions.height6
          )
          .contentShape(Rectangle())
      }
      .opacity(closeButtonVisible ? 1.0 : 0.0)
      .scaleEffect(closeButtonVisible ? 1.0 : 0.8)
      .buttonStyle(CloseButtonStyle())

      Spacer()
    }
  }

  // MARK: - Title Section

  /// 标题部分
  private var titleSection: some View {
    VStack(spacing: PaywallDesignSystem.Spacing.spacing2) {
      // 主标题
      Text(title)
        .font(PaywallDesignSystem.Typography.title)
        .foregroundColor(PaywallDesignSystem.Colors.foreground)
        .multilineTextAlignment(.center)
        .tracking(PaywallDesignSystem.FontTracking.tight)
        .opacity(titleVisible ? 1.0 : 0.0)
        .offset(y: titleVisible ? 0 : 20)

      // 副标题
      if let subtitle = subtitle {
        Text(subtitle)
          .font(PaywallDesignSystem.Typography.textSmallNormal)
          .foregroundColor(PaywallDesignSystem.Colors.mutedForeground)
          .multilineTextAlignment(.center)
          .opacity(titleVisible ? 1.0 : 0.0)
          .offset(y: titleVisible ? 0 : 15)
      }
    }
    .padding(.top, PaywallDesignSystem.Spacing.spacing6)
  }
}

// MARK: - Close Button Style

/// 关闭按钮样式
private struct CloseButtonStyle: ButtonStyle {
  func makeBody(configuration: Configuration) -> some View {
    configuration.label
      .scaleEffect(configuration.isPressed ? 0.9 : 1.0)
      .opacity(configuration.isPressed ? 0.7 : 1.0)
      .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
  }
}

// MARK: - Convenience Initializers

extension PaywallHeaderView {
  /// 创建简单标题的头部视图
  /// - Parameters:
  ///   - title: 标题文本
  ///   - onClose: 关闭回调
  /// - Returns: 配置好的头部视图
  public static func simple(
    title: String,
    onClose: @escaping () -> Void
  ) -> PaywallHeaderView {
    return PaywallHeaderView(
      title: title,
      subtitle: nil,
      showCloseButton: true,
      onClose: onClose,
      enableAnimation: true
    )
  }

  /// 创建带副标题的头部视图
  /// - Parameters:
  ///   - title: 标题文本
  ///   - subtitle: 副标题文本
  ///   - onClose: 关闭回调
  /// - Returns: 配置好的头部视图
  public static func withSubtitle(
    title: String,
    subtitle: String,
    onClose: @escaping () -> Void
  ) -> PaywallHeaderView {
    return PaywallHeaderView(
      title: title,
      subtitle: subtitle,
      showCloseButton: true,
      onClose: onClose,
      enableAnimation: true
    )
  }

  /// 创建无关闭按钮的头部视图
  /// - Parameters:
  ///   - title: 标题文本
  ///   - subtitle: 副标题文本
  /// - Returns: 配置好的头部视图
  public static func withoutCloseButton(
    title: String,
    subtitle: String? = nil
  ) -> PaywallHeaderView {
    return PaywallHeaderView(
      title: title,
      subtitle: subtitle,
      showCloseButton: false,
      onClose: {},
      enableAnimation: true
    )
  }
}

// MARK: - Preview

#Preview {
  ZStack {
    PaywallDesignSystem.Colors.background
      .ignoresSafeArea()

    PaywallHeaderView(
      title: "Get the full experience",
      subtitle: nil,
      showCloseButton: true,
      onClose: {
        print("Close button tapped")
      }
    )
  }
}

#Preview("With Subtitle") {
  ZStack {
    PaywallDesignSystem.Colors.background
      .ignoresSafeArea()

    PaywallHeaderView(
      title: "Get the full experience",
      subtitle: "Unlock all premium features",
      showCloseButton: true,
      onClose: {
        print("Close button tapped")
      }
    )
  }
}

#Preview("Without Close Button") {
  ZStack {
    PaywallDesignSystem.Colors.background
      .ignoresSafeArea()

    PaywallHeaderView.withoutCloseButton(
      title: "Get the full experience",
      subtitle: "Unlock all premium features"
    )
  }
}
