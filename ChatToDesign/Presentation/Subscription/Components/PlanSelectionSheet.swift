//
//  PlanSelectionSheet.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/9.
//

import SwiftUI

/// 套餐选择弹窗
public struct PlanSelectionSheet: View {

  // MARK: - Properties

  /// 可用套餐列表
  let availablePlans: [SubscriptionPlan]

  /// 当前选中的套餐
  let selectedPlan: SubscriptionPlan?

  /// 是否显示弹窗
  @Binding var isPresented: Bool

  /// 套餐选择回调
  let onPlanSelected: (SubscriptionPlan) -> Void

  // MARK: - Initialization

  /// 初始化套餐选择弹窗
  /// - Parameters:
  ///   - availablePlans: 可用套餐列表
  ///   - selectedPlan: 当前选中的套餐
  ///   - isPresented: 是否显示弹窗
  ///   - onPlanSelected: 套餐选择回调
  public init(
    availablePlans: [SubscriptionPlan],
    selectedPlan: SubscriptionPlan?,
    isPresented: Binding<Bool>,
    onPlanSelected: @escaping (SubscriptionPlan) -> Void
  ) {
    self.availablePlans = availablePlans
    self.selectedPlan = selectedPlan
    self._isPresented = isPresented
    self.onPlanSelected = onPlanSelected
  }

  // MARK: - Body

  public var body: some View {
    ZStack {
      // 背景
      sheetBackground

      // 内容
      VStack(spacing: 0) {
        // 套餐列表
        planListSection
      }
      .padding(.bottom, 24)
    }
  }

  // MARK: - Sheet Components

  /// 弹窗背景
  private var sheetBackground: some View {
    RoundedRectangle(cornerRadius: 24, style: .continuous)
      .fill(SheetDesign.backgroundColor)
      .ignoresSafeArea(.container, edges: .bottom)
  }

  /// 套餐列表部分
  private var planListSection: some View {
    VStack(spacing: 24) {
      planCardsGrid
    }
    .padding(.horizontal, 24)
  }

  /// 加载视图
  private var loadingView: some View {
    VStack(spacing: 16) {
      ProgressView()
        .tint(.white)
        .scaleEffect(1.2)

      Text("加载套餐中...")
        .font(.custom("Inter", size: 14).weight(.medium))
        .foregroundColor(.white.opacity(0.7))
    }
    .frame(height: 176)
  }

  /// 套餐卡片网格
  private var planCardsGrid: some View {
    HStack(spacing: 16) {
      ForEach(availablePlans) { plan in
        PlanCard(
          plan: plan,
          isSelected: isSelected(plan),
          onTap: {
            handlePlanSelection(plan)
          }
        )
        .frame(maxWidth: .infinity)
      }
    }
    .frame(height: 176)
  }

  /// 弹窗底部
  private var sheetFooter: some View {
    VStack {
      // 底部指示器
      RoundedRectangle(cornerRadius: 2.5)
        .fill(SheetDesign.dragIndicatorColor)
        .frame(width: 134, height: 5)
        .padding(.bottom, 8)
    }
    .padding(.top, 24)
  }

  // MARK: - Event Handlers

  // MARK: - Helper Methods

  /// 检查套餐是否被选中
  /// - Parameter plan: 要检查的套餐
  /// - Returns: 是否被选中
  private func isSelected(_ plan: SubscriptionPlan) -> Bool {
    return selectedPlan?.id == plan.id
  }

  /// 处理套餐选择
  /// - Parameter plan: 选中的套餐
  private func handlePlanSelection(_ plan: SubscriptionPlan) {
    // 添加触觉反馈
    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
    impactFeedback.impactOccurred()

    // 延迟关闭弹窗，让用户看到选择效果
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
      onPlanSelected(plan)
      isPresented = false
    }

    Logger.info("PlanSelectionSheet: 用户选择套餐 - \(plan.productInfo.displayName)")
  }
}

// MARK: - Sheet Design System

/// 弹窗设计系统
private enum SheetDesign {
  static let backgroundColor = Color(hex: "#333333")
  static let dragIndicatorColor = Color(hex: "#252525")
}
