import SwiftUI

/// Paywall 背景视图
/// 包含模糊背景图片、渐变遮罩层和模糊效果层
public struct PaywallBackgroundView: View {
  
  // MARK: - Properties
  
  /// 背景图片URL
  let imageUrl: String?
  
  /// 是否启用动画
  let enableAnimation: Bool
  
  /// 状态变量
  @State private var imageLoaded = false
  
  // MARK: - Initialization
  
  /// 初始化背景视图
  /// - Parameters:
  ///   - imageUrl: 背景图片URL
  ///   - enableAnimation: 是否启用动画
  public init(
    imageUrl: String? = nil,
    enableAnimation: Bool = true
  ) {
    self.imageUrl = imageUrl
    self.enableAnimation = enableAnimation
  }
  
  // MARK: - Body
  
  public var body: some View {
    ZStack {
      // 基础背景色
      PaywallDesignSystem.Colors.background
        .ignoresSafeArea()
      
      // 背景图片层
      backgroundImageLayer
      
      // 渐变遮罩层
      gradientOverlayLayer
      
      // 顶部模糊效果层
      topBlurLayer
      
      // 底部模糊效果层
      bottomBlurLayer
    }
    .ignoresSafeArea()
  }
  
  // MARK: - Background Image Layer
  
  /// 背景图片层
  private var backgroundImageLayer: some View {
    Group {
      if let imageUrl = imageUrl, let url = URL(string: imageUrl) {
        AsyncImage(url: url) { phase in
          switch phase {
          case .success(let image):
            image
              .resizable()
              .aspectRatio(contentMode: .fill)
              .clipped()
              .opacity(imageLoaded ? 1.0 : 0.0)
              .onAppear {
                if enableAnimation {
                  withAnimation(.easeInOut(duration: 0.5)) {
                    imageLoaded = true
                  }
                } else {
                  imageLoaded = true
                }
              }
          case .failure(_):
            // 加载失败时显示默认背景
            defaultBackgroundImage
          case .empty:
            // 加载中显示默认背景
            defaultBackgroundImage
          @unknown default:
            defaultBackgroundImage
          }
        }
      } else {
        // 没有提供URL时显示默认背景
        defaultBackgroundImage
      }
    }
  }
  
  /// 默认背景图片
  private var defaultBackgroundImage: some View {
    Image("designImage")
      .resizable()
      .aspectRatio(contentMode: .fill)
      .clipped()
  }
  
  // MARK: - Gradient Overlay Layer
  
  /// 渐变遮罩层
  private var gradientOverlayLayer: some View {
    LinearGradient(
      gradient: Gradient(stops: [
        .init(
          color: PaywallDesignSystem.Colors.neutralBlurDark.opacity(0),
          location: PaywallDesignSystem.Background.gradientStartLocation
        ),
        .init(
          color: PaywallDesignSystem.Colors.neutralBlurDark.opacity(0.8),
          location: PaywallDesignSystem.Background.gradientEndLocation
        )
      ]),
      startPoint: .top,
      endPoint: .bottom
    )
  }
  
  // MARK: - Blur Effect Layers
  
  /// 顶部模糊效果层
  private var topBlurLayer: some View {
    Rectangle()
      .fill(
        PaywallDesignSystem.Colors.neutralBlurDark
          .opacity(PaywallDesignSystem.Background.blurOpacity)
      )
      .blur(radius: PaywallDesignSystem.Background.blurRadius)
      .frame(height: 153)
      .offset(x: -64, y: -17)
      .clipped()
      .allowsHitTesting(false)
  }
  
  /// 底部模糊效果层
  private var bottomBlurLayer: some View {
    VStack {
      Spacer()
      
      Rectangle()
        .fill(
          PaywallDesignSystem.Colors.neutralBlurDark
            .opacity(PaywallDesignSystem.Background.blurOpacity)
        )
        .blur(radius: PaywallDesignSystem.Background.blurRadius)
        .frame(height: 302)
        .offset(x: -52)
        .clipped()
        .allowsHitTesting(false)
    }
  }
}

// MARK: - Backdrop Blur Effect

/// 自定义背景模糊修饰符
private struct BackdropBlurModifier: ViewModifier {
  let radius: CGFloat
  
  func body(content: Content) -> some View {
    content
      .background(
        Rectangle()
          .fill(.ultraThinMaterial)
          .blur(radius: radius)
      )
  }
}

extension View {
  /// 添加背景模糊效果
  /// - Parameter radius: 模糊半径
  /// - Returns: 应用模糊效果的视图
  func backdropBlur(radius: CGFloat) -> some View {
    self.modifier(BackdropBlurModifier(radius: radius))
  }
}

// MARK: - Preview

#Preview {
  PaywallBackgroundView(
    imageUrl: nil,
    enableAnimation: true
  )
}

#Preview("With Custom Image") {
  PaywallBackgroundView(
    imageUrl: "https://example.com/background.jpg",
    enableAnimation: false
  )
}
