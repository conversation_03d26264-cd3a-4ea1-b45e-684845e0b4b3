//
//  TimelineStepView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/9.
//

import SwiftUI

/// 时间线步骤视图组件
public struct TimelineStepView: View {

  // MARK: - Properties

  /// 时间线步骤数据
  let step: TrialTimelineStep

  // MARK: - Initialization

  /// 初始化时间线步骤视图
  /// - Parameter step: 时间线步骤数据
  public init(step: TrialTimelineStep) {
    self.step = step
  }

  // MARK: - Body

  public var body: some View {
    HStack(alignment: .top, spacing: PaywallDesignSystem.Spacing.spacing4) {
      // 左侧图标和连接线
      iconSection

      // 右侧内容
      contentSection
    }
  }

  // MARK: - Icon Section

  /// 图标部分
  private var iconSection: some View {
    VStack(spacing: PaywallDesignSystem.Spacing.spacing2) {
      // 图标圆圈
      iconCircle

      // 连接线
      if step.showConnector {
        connector
      }
    }
  }

  /// 图标圆圈
  private var iconCircle: some View {
    ZStack {
      // 背景圆圈
      Circle()
        .fill(step.iconBackgroundColor)
        .frame(width: 36, height: 36)
      // .overlay(
      //   Circle()
      //     .stroke(PaywallDesignSystem.Colors.foreground, lineWidth: 1)
      // )

      // 图标
      iconImage
    }
  }

  /// 图标图像
  private var iconImage: some View {
    Group {
      switch step.iconName {
      case "checkmark":
        checkmarkIcon
      case "bell":
        bellIcon
      case "star":
        starIcon
      default:
        Image(systemName: step.iconName)
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(step.iconColor)
      }
    }
  }

  /// 对勾图标
  private var checkmarkIcon: some View {
    Image(systemName: "checkmark")
      .font(.system(size: 20, weight: .bold))
      .foregroundColor(.white)
  }

  /// 铃铛图标
  private var bellIcon: some View {
    Image(systemName: "bell")
      .font(.system(size: 20, weight: .medium))
      .foregroundColor(.white)
  }

  /// 星星图标
  private var starIcon: some View {
    Image(systemName: "star")
      .font(.system(size: 20, weight: .medium))
      .foregroundColor(.white)
  }

  /// 连接线
  private var connector: some View {
    Rectangle()
      .fill(PaywallDesignSystem.Colors.foreground)
      .frame(width: 6, height: 48)
      .cornerRadius(PaywallDesignSystem.Radius.radius2XL)
  }

  // MARK: - Content Section

  /// 内容部分
  private var contentSection: some View {
    VStack(alignment: .leading, spacing: PaywallDesignSystem.Spacing.spacing1) {
      // 标题
      titleText

      // 描述
      descriptionText
    }
    .frame(maxWidth: .infinity, alignment: .leading)
  }

  /// 标题文本
  private var titleText: some View {
    Text(step.title)
      .font(PaywallDesignSystem.Typography.textSmallMedium)
      .foregroundColor(PaywallDesignSystem.Colors.foreground)
      .multilineTextAlignment(.leading)
  }

  /// 描述文本
  private var descriptionText: some View {
    Group {
      if step.id == "day3" {
        // 特殊处理第三步的描述，包含"Cancel before"链接
        day3DescriptionText
      } else {
        Text(step.description)
          .font(PaywallDesignSystem.Typography.textSmallNormal)
          .foregroundColor(PaywallDesignSystem.Colors.mutedForeground)
          .multilineTextAlignment(.leading)
          .fixedSize(horizontal: false, vertical: true)
      }
    }
  }

  /// 第三步特殊描述文本
  private var day3DescriptionText: some View {
    VStack(alignment: .leading, spacing: 0) {
      Text("Your paid Pro membership starts on")
        .font(PaywallDesignSystem.Typography.textSmallNormal)
        .foregroundColor(PaywallDesignSystem.Colors.mutedForeground)

      HStack(spacing: 0) {
        Text("\(threeDaysFromNowFormatted), You can cancel before")
          .font(PaywallDesignSystem.Typography.textSmallNormal)
          .foregroundColor(PaywallDesignSystem.Colors.mutedForeground)

        // Text("Cancel before")
        //   .font(PaywallDesignSystem.Typography.textSmallNormal)
        //   .foregroundColor(PaywallDesignSystem.Colors.foreground)
        // .underline()
      }
    }
  }

  /// 计算三天后的日期并格式化
  private var threeDaysFromNowFormatted: String {
    let threeDaysFromNow = Calendar.current.date(byAdding: .day, value: 3, to: Date()) ?? Date()
    let formatter = DateFormatter()
    formatter.dateFormat = "MMMM d, yyyy"
    formatter.locale = Locale(identifier: "en_US")
    return formatter.string(from: threeDaysFromNow)
  }
}
