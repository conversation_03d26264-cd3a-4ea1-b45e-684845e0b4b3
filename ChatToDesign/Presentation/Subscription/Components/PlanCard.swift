//
//  PlanCard.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/9.
//

import SwiftUI

/// 套餐卡片组件
public struct PlanCard: View {

  // MARK: - Properties

  /// 套餐信息
  let plan: SubscriptionPlan

  /// 是否被选中
  let isSelected: Bool

  /// 点击回调
  let onTap: () -> Void

  // MARK: - Body

  public var body: some View {
    Button(action: onTap) {
      cardContent
    }
    .buttonStyle(PlanCardButtonStyle())
    .accessibilityLabel(plan.productInfo.displayName)
    .accessibilityAddTraits(isSelected ? .isSelected : [])
  }

  // MARK: - Card Content

  /// 卡片内容
  private var cardContent: some View {
    ZStack {
      // 背景和边框
      RoundedRectangle(cornerRadius: 16)
        .fill(PaywallDesignSystem.Colors.cardBackground)
        .overlay(
          RoundedRectangle(cornerRadius: 16)
            .stroke(borderColor, lineWidth: 1)
        )

      // 主要内容
      VStack(alignment: .leading, spacing: 16) {
        // 套餐信息
        planInfoSection

        // 推荐标签（如果有）
        if plan.uiBadge != nil {
          badgeSection
        }

        Spacer()

        // 试用信息
        trialInfoSection
      }
      .padding(16)
      .frame(maxWidth: .infinity, alignment: .leading)
      .frame(height: 176)  // 根据 Figma 设计

      // 选中标记（右上角）
      if isSelected {
        selectedIndicator
      }
    }
  }

  // MARK: - Sections

  /// 套餐信息部分
  private var planInfoSection: some View {
    VStack(alignment: .leading, spacing: 4) {
      // 套餐标题
      Text(plan.uiTitle)
        .font(.custom("Inter", size: 14).weight(.medium))
        .foregroundColor(.white)
        .lineLimit(1)

      // 价格
      Text(plan.productInfo.price)
        .font(.custom("Inter", size: 24).weight(.medium))
        .foregroundColor(.white)
        .lineLimit(1)
    }
  }

  /// 推荐标签部分
  private var badgeSection: some View {
    HStack {
      if let badge = plan.uiBadge {
        Text(badge)
          .font(.custom("Geist", size: 12).weight(.semibold))
          .foregroundColor(.white)
          .padding(.horizontal, 10)
          .padding(.vertical, 2)
          .background(PaywallDesignSystem.Colors.brand)
          .clipShape(Capsule())
      }
      Spacer()
    }
  }

  /// 试用信息部分
  private var trialInfoSection: some View {
    Text(plan.uiTrialInfo)
      .font(.custom("Inter", size: 12).weight(.regular))
      .foregroundColor(PaywallDesignSystem.Colors.mutedForeground)
      .multilineTextAlignment(.leading)
      .lineLimit(nil)
      .fixedSize(horizontal: false, vertical: true)
  }

  /// 选中指示器
  private var selectedIndicator: some View {
    VStack {
      HStack {
        Spacer()

        Circle()
          .fill(PaywallDesignSystem.Colors.brand)
          .frame(width: 24, height: 24)
          .overlay(
            Image(systemName: "checkmark")
              .font(.system(size: 12, weight: .bold))
              .foregroundColor(.white)
          )
          .shadow(
            color: Color.black.opacity(0.1),
            radius: 3, x: 0, y: 1
          )
          .shadow(
            color: Color.black.opacity(0.06),
            radius: 2, x: 0, y: 1
          )
      }
      Spacer()
    }
    .padding(EdgeInsets(top: -6, leading: 0, bottom: 0, trailing: -6))
  }

  // MARK: - Computed Properties

  /// 边框颜色
  private var borderColor: Color {
    if isSelected {
      return PaywallDesignSystem.Colors.brand
    } else {
      return Color.clear
    }
  }
}

// MARK: - Button Style

/// 套餐卡片按钮样式
private struct PlanCardButtonStyle: ButtonStyle {
  func makeBody(configuration: Configuration) -> some View {
    configuration.label
      .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
      .opacity(configuration.isPressed ? 0.9 : 1.0)
      .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
  }
}
