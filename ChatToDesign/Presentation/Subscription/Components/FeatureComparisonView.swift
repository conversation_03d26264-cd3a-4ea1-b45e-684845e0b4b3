import SwiftUI

/// 功能对比表格视图
/// 包含透明卡片背景、白色边框和模糊效果
public struct FeatureComparisonView: View {

  // MARK: - Properties

  /// 功能列表
  let features: [PaywallFeature]

  /// 是否启用动画
  let enableAnimation: Bool

  /// 状态变量
  @State private var cardVisible = false
  @State private var featuresVisible = false

  // MARK: - Initialization

  /// 初始化功能对比视图
  /// - Parameters:
  ///   - features: 功能列表
  ///   - enableAnimation: 是否启用动画
  public init(
    features: [PaywallFeature],
    enableAnimation: Bool = true
  ) {
    self.features = features
    self.enableAnimation = enableAnimation
  }

  // MARK: - Body

  public var body: some View {
    VStack(spacing: 0) {
      // 表头
      comparisonTableHeader

      // 功能行
      featuresSection
    }
    .padding(PaywallDesignSystem.Spacing.spacing4)
    .background(cardBackground)
    .overlay(cardBorder)
    .clipShape(RoundedRectangle(cornerRadius: PaywallDesignSystem.Radius.radius2XL))
    .backdropBlur(radius: 5)
    .opacity(cardVisible ? 1.0 : 0.0)
    .scaleEffect(cardVisible ? 1.0 : 0.95)
    .onAppear {
      if enableAnimation {
        withAnimation(.easeInOut(duration: 0.6).delay(0.4)) {
          cardVisible = true
        }

        withAnimation(.easeInOut(duration: 0.8).delay(0.6)) {
          featuresVisible = true
        }
      } else {
        cardVisible = true
        featuresVisible = true
      }
    }
  }

  // MARK: - Card Background

  /// 卡片背景
  private var cardBackground: some View {
    PaywallDesignSystem.FeatureComparison.cardBackground
  }

  /// 卡片边框
  private var cardBorder: some View {
    RoundedRectangle(cornerRadius: PaywallDesignSystem.Radius.radius2XL)
      .stroke(
        PaywallDesignSystem.Colors.cardBorder,
        lineWidth: 1
      )
  }

  // MARK: - Table Header

  /// 对比表格头部
  private var comparisonTableHeader: some View {
    HStack(spacing: 0) {
      // 功能名称列 - 占更多空间
      Text("Benefit")
        .font(PaywallDesignSystem.Typography.textSmallMedium)
        .foregroundColor(PaywallDesignSystem.Colors.foreground)
        .lineLimit(1)
        .frame(maxWidth: .infinity, alignment: .leading)

      // Free 列 - 固定宽度
      Text("Free")
        .font(PaywallDesignSystem.Typography.textSmallMedium)
        .foregroundColor(PaywallDesignSystem.Colors.foreground)
        .frame(width: 80)

      // Pro 列 - 固定宽度
      HStack {
        Spacer()
        Text("Pro")
          .font(PaywallDesignSystem.Typography.proLabel)
          .foregroundColor(PaywallDesignSystem.Colors.foreground)
          .padding(.horizontal, 8)
          .padding(.vertical, 2)
          .background(
            RoundedRectangle(cornerRadius: PaywallDesignSystem.Radius.radiusFull)
              .fill(PaywallDesignSystem.Gradients.proLabelGradient)
          )
        Spacer()
      }
      .frame(width: 80)
    }
    .frame(height: PaywallDesignSystem.FeatureComparison.headerHeight)
  }

  // MARK: - Features Section

  /// 功能列表部分
  private var featuresSection: some View {
    VStack(spacing: 0) {
      ForEach(Array(features.enumerated()), id: \.element.id) { index, feature in
        FeatureRowView(
          feature: feature,
          enableAnimation: enableAnimation,
          animationDelay: enableAnimation ? Double(index) * 0.1 + 0.8 : 0
        )
        .opacity(featuresVisible ? 1.0 : 0.0)
        .offset(x: featuresVisible ? 0 : 20)
      }
    }
  }
}

// MARK: - Feature Row View

/// 功能行视图
public struct FeatureRowView: View {

  // MARK: - Properties

  /// 功能数据
  let feature: PaywallFeature

  /// 是否启用动画
  let enableAnimation: Bool

  /// 动画延迟
  let animationDelay: Double

  /// 状态变量
  @State private var rowVisible = false

  // MARK: - Initialization

  /// 初始化功能行视图
  /// - Parameters:
  ///   - feature: 功能数据
  ///   - enableAnimation: 是否启用动画
  ///   - animationDelay: 动画延迟
  public init(
    feature: PaywallFeature,
    enableAnimation: Bool = true,
    animationDelay: Double = 0
  ) {
    self.feature = feature
    self.enableAnimation = enableAnimation
    self.animationDelay = animationDelay
  }

  // MARK: - Body

  public var body: some View {
    HStack(spacing: 0) {
      // 功能名称 - 占更多空间
      Text(feature.name)
        .font(PaywallDesignSystem.Typography.textSmallNormal)
        .foregroundColor(PaywallDesignSystem.Colors.mutedForeground)
        .lineLimit(1)
        .frame(maxWidth: .infinity, alignment: .leading)

      // Free 状态 - 固定宽度
      HStack {
        Spacer()
        FeatureStatusIcon(
          isAvailable: feature.freeAvailable,
          size: 16
        )
        Spacer()
      }
      .frame(width: 80)

      // Pro 状态 - 固定宽度
      HStack {
        Spacer()
        FeatureStatusIcon(
          isAvailable: feature.proAvailable,
          size: 16
        )
        Spacer()
      }
      .frame(width: 80)
    }
    .frame(height: PaywallDesignSystem.FeatureComparison.rowHeight)
    .opacity(rowVisible ? 1.0 : 0.0)
    .offset(x: rowVisible ? 0 : 10)
    .onAppear {
      if enableAnimation {
        withAnimation(.easeInOut(duration: 0.4).delay(animationDelay)) {
          rowVisible = true
        }
      } else {
        rowVisible = true
      }
    }
  }
}

// MARK: - Feature Status Icon

/// 功能状态图标
public struct FeatureStatusIcon: View {

  // MARK: - Properties

  /// 是否可用
  let isAvailable: Bool

  /// 图标大小
  let size: CGFloat

  // MARK: - Body

  public var body: some View {
    Circle()
      .fill(backgroundColor)
      .frame(width: size, height: size)
      .overlay(
        Circle()
          .stroke(borderColor, lineWidth: isAvailable ? 0.444 : 0)
      )
      .overlay(iconContent)
  }

  // MARK: - Computed Properties

  /// 背景颜色
  private var backgroundColor: Color {
    return isAvailable
      ? PaywallDesignSystem.Colors.featureAvailable
      : PaywallDesignSystem.Colors.featureUnavailable
  }

  /// 边框颜色
  private var borderColor: Color {
    return isAvailable
      ? PaywallDesignSystem.Colors.cardBorder
      : Color.clear
  }

  /// 图标内容
  private var iconContent: some View {
    Group {
      if isAvailable {
        Image(systemName: "checkmark")
          .font(.system(size: size * 0.55, weight: .medium))
          .foregroundColor(PaywallDesignSystem.Colors.foreground)
      } else {
        Image(systemName: "xmark")
          .font(.system(size: size * 0.55, weight: .medium))
          .foregroundColor(PaywallDesignSystem.Colors.foreground)
      }
    }
  }
}

// MARK: - Preview

#Preview {
  ZStack {
    PaywallDesignSystem.Colors.background
      .ignoresSafeArea()

    FeatureComparisonView(
      features: PaywallFeature.defaultFeatures,
      enableAnimation: true
    )
    .padding()
  }
}

#Preview("Single Feature Row") {
  ZStack {
    PaywallDesignSystem.Colors.background
      .ignoresSafeArea()

    FeatureRowView(
      feature: PaywallFeature(
        id: "test",
        name: "Remove Watermark",
        freeAvailable: false,
        proAvailable: true
      ),
      enableAnimation: false
    )
    .padding()
  }
}

#Preview("Feature Status Icons") {
  ZStack {
    PaywallDesignSystem.Colors.background
      .ignoresSafeArea()

    HStack(spacing: 20) {
      FeatureStatusIcon(isAvailable: true, size: 16)
      FeatureStatusIcon(isAvailable: false, size: 16)
    }
    .padding()
  }
}
