import SwiftUI

/// Paywall 底部视图
/// 包含定价信息、购买按钮和法律链接
public struct PaywallFooterView: View {

  // MARK: - Properties

  /// PaywallViewModel 实例
  @ObservedObject var viewModel: PaywallViewModel

  /// 购买按钮点击回调
  let onPurchase: () -> Void

  /// 产品选择回调
  let onPlanSelected: (SubscriptionPlan) -> Void

  /// 是否启用动画
  let enableAnimation: Bool

  /// 状态变量
  @State private var footerVisible = false
  @State private var contentVisible = false
  @State private var showPlanSelection = false
  @State private var showLegalSheet = false

  // MARK: - Initialization

  /// 初始化底部视图
  /// - Parameters:
  ///   - viewModel: PaywallViewModel 实例
  ///   - onPurchase: 购买按钮点击回调
  ///   - onPlanSelected: 产品选择回调
  ///   - enableAnimation: 是否启用动画
  public init(
    viewModel: PaywallViewModel,
    onPurchase: @escaping () -> Void,
    onPlanSelected: @escaping (SubscriptionPlan) -> Void,
    enableAnimation: Bool = true
  ) {
    self.viewModel = viewModel
    self.onPurchase = onPurchase
    self.onPlanSelected = onPlanSelected
    self.enableAnimation = enableAnimation
  }

  // MARK: - Body

  public var body: some View {
    VStack {
      Spacer()

      footerContent
        .opacity(footerVisible ? 1.0 : 0.0)
        .offset(y: footerVisible ? 0 : 50)
        .onAppear {
          if enableAnimation {
            withAnimation(.easeInOut(duration: 0.6).delay(1.0)) {
              footerVisible = true
            }

            withAnimation(.easeInOut(duration: 0.8).delay(1.2)) {
              contentVisible = true
            }
          } else {
            footerVisible = true
            contentVisible = true
          }
        }
    }
    .sheet(isPresented: $showPlanSelection) {
      PlanSelectionSheet(
        availablePlans: viewModel.availablePlans,
        selectedPlan: viewModel.selectedPlan,
        isPresented: $showPlanSelection,
        onPlanSelected: { plan in
          onPlanSelected(plan)
        }
      )
      .presentationDetents([.height(250)])
      .presentationDragIndicator(.visible)
    }
    .sheet(isPresented: $showLegalSheet) {
      LegalSheetView(isPresented: $showLegalSheet)
        .presentationDetents([.height(424)])
        .presentationDragIndicator(.visible)
    }
  }

  // MARK: - Footer Content

  /// 底部内容
  private var footerContent: some View {
    VStack(spacing: PaywallDesignSystem.Spacing.spacing5) {
      // 定价信息
      pricingInfoSection

      // 购买按钮
      purchaseButtonSection

      // 法律链接
      legalLinkButton(text: viewModel.legalLinkText)
    }
    .padding(.horizontal, PaywallDesignSystem.Spacing.spacing6)
    .padding(.vertical, PaywallDesignSystem.Spacing.spacing6)
    .padding(.bottom, PaywallDesignSystem.Spacing.spacing8)
    .background(footerBackground)
    .clipShape(
      RoundedRectangle(
        cornerRadius: PaywallDesignSystem.Radius.radius2XL,
        style: .continuous
      )
    )
    .overlay(footerBorder)
  }

  // MARK: - Footer Background

  /// 底部背景
  private var footerBackground: some View {
    PaywallDesignSystem.Colors.cardBackground
  }

  /// 底部边框
  private var footerBorder: some View {
    UnevenRoundedRectangle(
      topLeadingRadius: PaywallDesignSystem.Radius.radius2XL,
      topTrailingRadius: PaywallDesignSystem.Radius.radius2XL
    )
    .stroke(
      PaywallDesignSystem.Colors.cardBorder,
      lineWidth: 1
    )
    .clipShape(
      UnevenRoundedRectangle(
        topLeadingRadius: PaywallDesignSystem.Radius.radius2XL,
        topTrailingRadius: PaywallDesignSystem.Radius.radius2XL
      )
    )
  }

  // MARK: - Pricing Info Section

  /// 定价信息部分
  private var pricingInfoSection: some View {
    VStack(spacing: PaywallDesignSystem.Spacing.spacing2) {
      if let selectedPlan = viewModel.selectedPlan {
        // 价格和周期
        HStack(spacing: PaywallDesignSystem.Spacing.spacing1) {
          // 可点击的周期按钮
          Button(action: {
            showPlanSelection = true
          }) {
            Text(selectedPlan.productInfo.periodInfo?.unit.displayName ?? "")
              .font(PaywallDesignSystem.Typography.textSmallNormal)
              .foregroundColor(PaywallDesignSystem.Colors.foreground)
              .underline()
          }
          .buttonStyle(PeriodButtonStyle())

          Text(selectedPlan.productInfo.price)
            .font(PaywallDesignSystem.Typography.textSmallNormal)
            .foregroundColor(PaywallDesignSystem.Colors.foreground)
        }
        .opacity(contentVisible ? 1.0 : 0.0)
        .offset(y: contentVisible ? 0 : 10)

        // 试用信息
        if let trialPeriodInfo = selectedPlan.productInfo.trialPeriodInfo {
          Text(
            "\(trialPeriodInfo.value)-\(trialPeriodInfo.unit.rawValue) free trial, Cancel anytime"
          )
          .font(PaywallDesignSystem.Typography.textSmallNormal)
          .foregroundColor(PaywallDesignSystem.Colors.mutedForeground)
          .lineLimit(1)
          .multilineTextAlignment(.center)
          .frame(maxWidth: .infinity, alignment: .center)
          .opacity(contentVisible ? 1.0 : 0.0)
          .offset(y: contentVisible ? 0 : 15)
        }
      } else {
        // 无产品信息时的占位符
        Text("Loading product info...")
          .font(PaywallDesignSystem.Typography.textSmallNormal)
          .foregroundColor(PaywallDesignSystem.Colors.mutedForeground)
          .opacity(contentVisible ? 1.0 : 0.0)
      }
    }
    .frame(maxWidth: .infinity)
  }

  // MARK: - Purchase Button Section

  /// 购买按钮部分
  private var purchaseButtonSection: some View {
    PurchaseButtonView(
      title: viewModel.purchaseButtonText,
      isLoading: viewModel.isPurchasing,
      action: onPurchase
    )
    .opacity(contentVisible ? 1.0 : 0.0)
    .scaleEffect(contentVisible ? 1.0 : 0.95)
  }

  // MARK: - Legal Link Section

  /// 法律链接按钮
  private func legalLinkButton(text: String) -> some View {
    Button(action: {
      showLegalSheet = true
    }) {
      Text(text)
        .font(PaywallDesignSystem.Typography.textSmallNormal)
        .foregroundColor(PaywallDesignSystem.Colors.foreground)
        .underline()
    }
    .buttonStyle(LegalLinkButtonStyle())
  }
}

// MARK: - Purchase Button View

/// 购买按钮视图
public struct PurchaseButtonView: View {

  // MARK: - Properties

  /// 按钮标题
  let title: String

  /// 是否正在加载
  let isLoading: Bool

  /// 点击回调
  let action: () -> Void

  // MARK: - Body

  public var body: some View {
    Button(action: action) {
      HStack(spacing: PaywallDesignSystem.Spacing.spacing3) {
        if isLoading {
          ProgressView()
            .tint(PaywallDesignSystem.Colors.foreground)
            .scaleEffect(0.8)
        } else {
          Text(title)
            .font(PaywallDesignSystem.Typography.textSmallSemibold)
            .foregroundColor(PaywallDesignSystem.Colors.foreground)
        }
      }
      .frame(maxWidth: .infinity)
      .frame(height: PaywallDesignSystem.PurchaseButton.height)
      .background(PaywallDesignSystem.PurchaseButton.backgroundColor)
      .clipShape(
        RoundedRectangle(
          cornerRadius: PaywallDesignSystem.PurchaseButton.cornerRadius
        )
      )
      .shadow(
        color: Color.black.opacity(0.1),
        radius: 6, x: 0, y: 4
      )
      .shadow(
        color: Color.black.opacity(0.06),
        radius: 4, x: 0, y: 2
      )
    }
    .frame(width: 345)  // 根据 Figma 设计
    .disabled(isLoading)
    .buttonStyle(PurchaseButtonStyle())
  }
}

// MARK: - Button Styles

/// 购买按钮样式
private struct PurchaseButtonStyle: ButtonStyle {
  func makeBody(configuration: Configuration) -> some View {
    configuration.label
      .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
      .opacity(configuration.isPressed ? 0.9 : 1.0)
      .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
  }
}

/// 法律链接按钮样式
private struct LegalLinkButtonStyle: ButtonStyle {
  func makeBody(configuration: Configuration) -> some View {
    configuration.label
      .opacity(configuration.isPressed ? 0.7 : 1.0)
      .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
  }
}

/// 周期按钮样式
private struct PeriodButtonStyle: ButtonStyle {
  func makeBody(configuration: Configuration) -> some View {
    configuration.label
      .opacity(configuration.isPressed ? 0.7 : 1.0)
      .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
  }
}
