//
//  PaywallViewModel.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/17.
//

import Combine
import Foundation
import SwiftUI

/// Paywall 视图模型
/// 处理 Paywall 的状态管理和业务逻辑
@MainActor
public final class PaywallViewModel: ObservableObject {

  // MARK: - Published Properties

  /// 当前 Paywall 状态
  @Published public private(set) var state: PaywallState = .idle

  /// 可用套餐列表
  @Published public private(set) var availablePlans: [SubscriptionPlan] = []

  /// 当前选中的套餐
  @Published public private(set) var selectedPlan: SubscriptionPlan?

  /// Paywall 配置
  @Published public private(set) var configuration: PaywallConfiguration = .default

  /// 是否正在加载
  @Published public private(set) var isLoading: Bool = false

  /// 是否正在购买
  @Published public private(set) var isPurchasing: Bool = false

  /// 是否正在恢复购买
  @Published public private(set) var isRestoring: Bool = false

  /// 错误信息
  @Published public private(set) var errorMessage: String?

  /// 是否显示错误提示
  @Published public var showErrorAlert: Bool = false

  /// 是否显示成功提示
  @Published public var showSuccessAlert: Bool = false

  /// 成功消息
  @Published public private(set) var successMessage: String?

  /// 当前购买的产品ID
  @Published public private(set) var currentPurchasingProductId: String?

  // MARK: - Private Properties

  public let paywallService: PaywallService
  private let subscriptionStateManager: SubscriptionStateManager
  private let analyticsService: AnalyticsService
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Computed Properties

  /// 是否可以进行操作
  public var canPerformActions: Bool {
    return !isLoading && !isPurchasing && !isRestoring
  }

  /// 当前订阅状态
  public var currentSubscription: Subscription? {
    return subscriptionStateManager.currentSubscription
  }

  /// 是否为高级用户
  public var isPremiumUser: Bool {
    return subscriptionStateManager.isPremiumUser
  }

  // MARK: - Initialization

  /// 初始化 Paywall 视图模型
  /// - Parameters:
  ///   - paywallService: Paywall 服务
  ///   - subscriptionStateManager: 订阅状态管理器
  ///   - analyticsService: 分析服务
  public init(
    paywallService: PaywallService = AppDependencyContainer.shared.subscriptionModule
      .paywallService,
    subscriptionStateManager: SubscriptionStateManager = AppDependencyContainer.shared
      .subscriptionModule.subscriptionStateManager,
    analyticsService: AnalyticsService = AppDependencyContainer.shared.analyticsService
  ) {
    self.paywallService = paywallService
    self.subscriptionStateManager = subscriptionStateManager
    self.analyticsService = analyticsService

    setupObservers()
  }

  // MARK: - Public Methods

  /// 购买产品
  /// - Parameter productId: 产品ID
  public func purchaseProduct(_ productId: String) async {
    guard canPerformActions else {
      Logger.warning("PaywallViewModel: 无法购买，当前正在进行其他操作")
      return
    }

    currentPurchasingProductId = productId
    isPurchasing = true
    errorMessage = nil

    Logger.info("PaywallViewModel: 开始购买产品 - \(productId)")

    let result = await paywallService.purchaseProduct(productId)

    isPurchasing = false
    currentPurchasingProductId = nil

    if result.isSuccessful {
      successMessage = "购买成功！感谢您的支持"
      showSuccessAlert = true
      Logger.info("PaywallViewModel: 购买成功 - \(productId)")
    } else if result.wasCancelled {
      Logger.info("PaywallViewModel: 购买被取消 - \(productId)")
      // 用户取消不显示错误
    } else if let error = result.error {
      errorMessage = error.errorDescription ?? "购买失败，请重试"
      showErrorAlert = true
      Logger.error("PaywallViewModel: 购买失败 - \(productId): \(error)")
    }
  }

  /// 恢复购买
  public func restorePurchases() async {
    guard canPerformActions else {
      Logger.warning("PaywallViewModel: 无法恢复购买，当前正在进行其他操作")
      return
    }

    isRestoring = true
    errorMessage = nil

    Logger.info("PaywallViewModel: 开始恢复购买")

    let result = await paywallService.restorePurchases()

    isRestoring = false

    if result.isSuccessful {
      if result.foundPurchases {
        successMessage = "购买恢复成功！"
        showSuccessAlert = true
        Logger.info("PaywallViewModel: 恢复购买成功")
      } else {
        errorMessage = "没有找到可恢复的购买"
        showErrorAlert = true
        Logger.info("PaywallViewModel: 没有找到可恢复的购买")
      }
    } else if let error = result.error {
      errorMessage = error.errorDescription ?? "恢复购买失败，请重试"
      showErrorAlert = true
      Logger.error("PaywallViewModel: 恢复购买失败 - \(error)")
    }
  }

  /// 刷新产品列表
  public func refreshProducts() {
    Task {
      await loadProducts()
    }
  }

  /// 清除错误信息
  public func clearError() {
    errorMessage = nil
    showErrorAlert = false
  }

  /// 清除成功信息
  public func clearSuccess() {
    successMessage = nil
    showSuccessAlert = false
  }

  // MARK: - Private Methods

  /// 设置观察者
  private func setupObservers() {
    // 观察 Paywall 状态变化
    paywallService.statePublisher
      .receive(on: DispatchQueue.main)
      .sink { [weak self] state in
        self?.handleStateChange(state)
      }
      .store(in: &cancellables)

    // 观察 Paywall 事件
    paywallService.eventPublisher
      .receive(on: DispatchQueue.main)
      .sink { [weak self] event in
        self?.handleEvent(event)
      }
      .store(in: &cancellables)

    // 观察订阅状态变化
    subscriptionStateManager.$currentSubscription
      .receive(on: DispatchQueue.main)
      .sink { [weak self] _ in
        self?.objectWillChange.send()
      }
      .store(in: &cancellables)
  }

  /// 加载产品列表
  public func loadProducts() async {
    isLoading = true
    errorMessage = nil

    Task {
      do {
        let products = try await paywallService.getAvailableProducts(offeringId: nil)
        await MainActor.run {
          // 直接转换为 SubscriptionPlan
          self.availablePlans = SubscriptionPlan.from(products)
          self.isLoading = false

          // 自动选中年付套餐
          self.autoSelectAnnualPlan()
        }
        Logger.info("PaywallViewModel: 套餐加载成功，数量: \(self.availablePlans.count)")
      } catch {
        await MainActor.run {
          self.errorMessage = "加载产品失败，请重试"
          self.isLoading = false
          self.showErrorAlert = true
        }
        Logger.error("PaywallViewModel: 产品加载失败 - \(error)")
      }
    }
  }

  /// 处理状态变化
  private func handleStateChange(_ newState: PaywallState) {
    state = newState

    switch newState {
    case .idle:
      isLoading = false
      isPurchasing = false
      isRestoring = false

    case .loadingProducts:
      isLoading = true

    case .productsLoaded(let products):
      // 直接转换为 SubscriptionPlan
      availablePlans = SubscriptionPlan.from(products)
      isLoading = false

      // 自动选中年付套餐
      autoSelectAnnualPlan()

    case .purchasing(let productId):
      isPurchasing = true
      currentPurchasingProductId = productId

    case .restoring:
      isRestoring = true

    case .error(let error):
      isLoading = false
      isPurchasing = false
      isRestoring = false
      errorMessage = error.errorDescription ?? "发生未知错误"
      showErrorAlert = true
    }
  }

  /// 处理事件
  private func handleEvent(_ event: PaywallEvent) {
    switch event {
    case .presented:
      Logger.info("PaywallViewModel: Paywall 已展示")

    case .dismissed:
      Logger.info("PaywallViewModel: Paywall 已关闭")

    case .purchaseStarted(let productId):
      Logger.info("PaywallViewModel: 开始购买 - \(productId)")

    case .purchaseCompleted(let productId, let subscription):
      Logger.info("PaywallViewModel: 购买完成 - \(productId), 订阅层级: \(subscription.tier.displayName)")

    case .purchaseFailed(let productId, let error):
      Logger.error("PaywallViewModel: 购买失败 - \(productId): \(error)")

    case .purchaseCancelled(let productId):
      Logger.info("PaywallViewModel: 购买取消 - \(productId)")

    case .restoreStarted:
      Logger.info("PaywallViewModel: 开始恢复购买")

    case .restoreCompleted(let subscription):
      if let subscription = subscription {
        Logger.info("PaywallViewModel: 恢复购买完成 - 订阅层级: \(subscription.tier.displayName)")
      } else {
        Logger.info("PaywallViewModel: 恢复购买完成 - 无订阅")
      }

    case .restoreFailed(let error):
      Logger.error("PaywallViewModel: 恢复购买失败 - \(error)")
    }
  }

  /// 更新选中的套餐
  /// - Parameter plan: 选中的套餐
  public func updateSelectedPlan(_ plan: SubscriptionPlan) {
    selectedPlan = plan
    Logger.info(
      "PaywallViewModel: 更新选中套餐 - \(plan.productInfo.displayName), 周期: \(plan.productInfo.periodInfo?.unit.displayName ?? "")"
    )
  }

  /// 自动选中年付套餐
  private func autoSelectAnnualPlan() {
    // 优先选择年付套餐
    if let annualPlan = availablePlans.first(where: { $0.productInfo.isAnnual }) {
      updateSelectedPlan(annualPlan)
      Logger.info("PaywallViewModel: 自动选中年付套餐 - \(annualPlan.productInfo.displayName)")
      return
    }

    // 如果没有年付套餐，选择第一个可用套餐
    if let firstPlan = availablePlans.first {
      updateSelectedPlan(firstPlan)
      Logger.info("PaywallViewModel: 自动选中第一个套餐 - \(firstPlan.productInfo.displayName)")
    }
  }

  /// 获取功能列表
  public var features: [PaywallFeature] {
    return configuration.features
  }

  /// 是否应该显示关闭按钮
  public var shouldShowCloseButton: Bool {
    return configuration.showCloseButton
  }

  // MARK: - Footer UI Constants

  /// 购买按钮文本
  public let purchaseButtonText: String = "Start free trial"

  /// 法律链接文本
  public let legalLinkText: String = "How your free trial works"

  /// 获取推荐套餐
  /// - Returns: 推荐的套餐
  public func getRecommendedPlan() -> SubscriptionPlan? {
    return availablePlans.first { $0.productInfo.isAnnual } ?? availablePlans.first
  }

  /// 检查套餐是否被选中
  /// - Parameter plan: 要检查的套餐
  /// - Returns: 是否被选中
  public func isSelected(_ plan: SubscriptionPlan) -> Bool {
    return selectedPlan?.id == plan.id
  }
}
