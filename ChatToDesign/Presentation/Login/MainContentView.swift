import Kingfisher
//
//  MainContentView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/14.
//
import SwiftUI

/// 主内容视图
/// 登录后的主界面，显示聊天列表和用户信息
struct MainContentView: View {
    // MARK: - 状态
    
    /// 视图模型
    @StateObject private var viewModel = MainContentViewModel()
    
    /// 是否显示新建聊天对话框
    @State private var showNewChatDialog = false
    
    /// 新聊天标题
    @State private var newChatTitle = ""
    
    // MARK: - 视图
    
    var body: some View {
        NavigationView {
            VStack {
                // 如果有错误消息，显示错误提示
                if let errorMessage = viewModel.errorMessage {
                    Text(errorMessage)
                        .foregroundColor(.red)
                        .padding()
                }
                
                // 聊天列表
               if viewModel.chats.isEmpty {
                   // 如果没有聊天，显示空状态
                   emptyStateView
               } else {
                   // 显示聊天列表
                   chatListView
               }
            }
            .navigationTitle("聊天设计")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        viewModel.showProfileEditor()
                    }) {
                        UserProfileView(user: viewModel.currentUser)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showNewChatDialog = true
                    }) {
                        Image(systemName: "plus")
                            .font(.system(size: 18, weight: .bold))
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        viewModel.signOut()
                    }) {
                        Image(systemName: "rectangle.portrait.and.arrow.right")
                            .font(.system(size: 18))
                    }
                }
            }
            .sheet(isPresented: $viewModel.showEditProfile) {
                UserProfileEditView()
            }
            .alert("创建新聊天", isPresented: $showNewChatDialog) {
                TextField("聊天标题", text: $newChatTitle)
                Button("取消", role: .cancel) {
                    newChatTitle = ""
                }
                Button("创建") {
                   viewModel.createNewChat(title: newChatTitle)
                   newChatTitle = ""
                }
            } message: {
                Text("请输入新聊天的标题")
            }
            .overlay {
                if viewModel.isLoading {
                    ProgressView()
                        .background(Color.white.opacity(0.7))
                        .cornerRadius(8)
                        .padding()
                }
            }
        }
    }
    
    /// 聊天列表视图
   private var chatListView: some View {
       List {
           ForEach(viewModel.chats, id: \Chat.id) { chat in
               NavigationLink(destination: ExyteChatView(chatId: chat.id!, userId: chat.userId)) {
                   ChatRowView(chat: chat)
               }
           }
       }
       .listStyle(InsetGroupedListStyle())
   }
    
    /// 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "bubble.left.and.bubble.right")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("还没有聊天")
                .font(.title2)
                .fontWeight(.medium)
            
            Text("点击右上角 + 按钮开始新的设计对话")
                .foregroundColor(.gray)
            
            Button(action: {
                showNewChatDialog = true
            }) {
                Text("创建新聊天")
                    .fontWeight(.semibold)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
            }
            .padding(.top)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
}

/// 聊天行视图
struct ChatRowView: View {
   let chat: Chat
   
   var body: some View {
       VStack(alignment: .leading, spacing: 4) {
           Text(chat.title)
               .font(.headline)
               .lineLimit(1)
           
           HStack {
               Text("消息: \(chat.messageCount)")
                   .font(.caption)
                   .foregroundColor(.gray)
               
               Spacer()
               
               if let date = chat.updatedAt {
                   Text(date, style: .date)
                       .font(.caption)
                       .foregroundColor(.gray)
               }
           }
       }
       .padding(.vertical, 4)
   }
}

// 聊天详情视图占位符
struct ChatDetailView: View {
    let chatId: String
    
    var body: some View {
        Text("聊天ID: \(chatId)")
            .navigationTitle("聊天详情")
    }
}
