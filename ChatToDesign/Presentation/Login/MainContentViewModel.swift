//
//  MainContentViewModel.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/14.
//

import Combine
import Foundation

/// 主内容视图模型
/// 处理主界面的状态和逻辑
final class MainContentViewModel: ObservableObject {
    // MARK: - 状态
    
    /// 是否正在加载
    @Published var isLoading = false
    
    /// 错误信息
    @Published var errorMessage: String?
    
    /// 当前用户
    @Published var currentUser: User?
    
    /// 是否显示用户资料编辑页面
    @Published var showEditProfile = false
    
    /// 聊天列表
    @Published var chats: [Chat] = []
    
    // MARK: - 依赖
    
    /// 认证服务
    private let authService: AuthApplicationService
    
    /// 用户服务
    private let userService: UserService
    
    /// 聊天服务
    private let chatService: ChatService
    
    /// 取消令牌集合
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    
    /// 初始化主内容视图模型
    /// - Parameters:
    ///   - authService: 认证服务
    ///   - userService: 用户服务
    ///   - chatService: 聊天服务
    init(
        authService: AuthApplicationService = AppDependencyContainer.shared.authModule.authService,
        userService: UserService = AppDependencyContainer.shared.userModule.userService,
        chatService: ChatService = AppDependencyContainer.shared.chatModule.chatService
    ) {
        self.authService = authService
        self.userService = userService
        self.chatService = chatService
        
        // 设置观察者
        setupObservers()
        
        // 获取当前用户信息和聊天列表
        fetchUserAndChats()
    }
    
    // MARK: - 私有方法
    
    /// 设置观察者
    private func setupObservers() {
        // 监听用户变化
        userService.currentUserPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] user in
                guard let self = self else { return }
                self.currentUser = user
                if let user = user {
                    self.fetchChats(for: user.id)
                }
            }
            .store(in: &cancellables)
    }
    
    /// 获取用户信息和聊天列表
    private func fetchUserAndChats() {
        if let user = userService.currentUser {
            self.currentUser = user
            fetchChats(for: user.id)
        }
    }
    
    /// 获取聊天列表
    /// - Parameter userId: 用户ID
    private func fetchChats(for userId: String) {
        isLoading = true
        
        Task {
            do {
                let chatList = try await chatService.getChatList(
                    forUser: userId,
                    limit: 20,
                    startAfter: nil,
                    status: .active
                )
                
                await MainActor.run {
                    self.chats = chatList
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "获取聊天列表失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    // MARK: - 公开方法
    
    /// 登出
    func signOut() {
        Task {
            do {
                try await authService.signOut()
            } catch {
                await MainActor.run {
                    self.errorMessage = "登出失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    /// 创建新聊天
    /// - Parameter title: 聊天标题
    func createNewChat(title: String) {
        guard let userId = currentUser?.id else {
            errorMessage = "用户未登录"
            return
        }
        
        isLoading = true
        
        Task {
            do {
                let _ = try await chatService.createChat(
                    userId: userId,
                    title: title,
                    description: nil,
                    parameters: nil
                )
                
                await MainActor.run {
                    // 刷新聊天列表
                    self.fetchChats(for: userId)
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "创建聊天失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    /// 显示编辑资料页面
    func showProfileEditor() {
        showEditProfile = true
    }
}
