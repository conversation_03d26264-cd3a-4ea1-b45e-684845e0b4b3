//
//  AuthViewModel.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/14.
//

import Combine
import Foundation

// AuthViewModel.swift
final class AuthViewModel: ObservableObject {
    // 状态
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var isAuthenticated = false
    
    // 依赖
    private let authApplicationService: AuthApplicationService
    private var cancellables = Set<AnyCancellable>()
    
    init(authApplicationService: AuthApplicationService = AppDependencyContainer.shared.authModule.authService) {
        self.authApplicationService = authApplicationService
        
        // 监听认证状态
        authApplicationService.authStatePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                self?.handleAuthState(state)
            }
            .store(in: &cancellables)
    }
    
    private func handleAuthState(_ state: AuthState) {
        switch state {
        case .authenticated:
            self.isAuthenticated = true
            self.isLoading = false
        case .unauthenticated:
            self.isAuthenticated = false
            self.isLoading = false
        case .authenticating:
            self.isLoading = true
        case .error(let error):
            self.errorMessage = error.localizedDescription
            self.isLoading = false
        }
    }
    
    func signInWithEmail(email: String, password: String) {
        guard validate(email: email, password: password) else {
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                try await authApplicationService.signIn(email: email, password: password)
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    isLoading = false
                }
            }
        }
    }
    
    func signInWithGoogle() {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                try await authApplicationService.signInWithGoogle()
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    isLoading = false
                }
            }
        }
    }
    
    private func validate(email: String, password: String) -> Bool {
        guard !email.isEmpty, !password.isEmpty else {
            errorMessage = "请输入邮箱和密码"
            return false
        }
        return true
    }
}