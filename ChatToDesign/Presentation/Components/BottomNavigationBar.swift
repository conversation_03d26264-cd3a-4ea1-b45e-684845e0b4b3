//
//  BottomNavigationBar.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/3.
//

import SwiftUI

/// Tab types for bottom navigation
enum RootTabType: String, CaseIterable {
  case explore = "Explore"
  case create = "Create"
  case profile = "Profile"
}

/// Custom bottom navigation bar matching Figma design
struct BottomNavigationBar: View {
  @Binding var selectedTab: RootTabType

  var body: some View {
    ZStack {
      // Background blur effect
      backgroundBlur

      // Navigation container
      navigationContainer
    }
    .frame(height: 120)  // Total height including safe area
  }

  // MARK: - Background Blur

  private var backgroundBlur: some View {
    // Shadow/blur background matching Figma design
    Rectangle()
      .fill(Color(red: 39 / 255, green: 39 / 255, blue: 42 / 255).opacity(0.9))  // rgba(39,39,42,0.9)
      .blur(radius: 24)  // blur-2xl = 24px
      .frame(width: 327, height: 113)  // Figma dimensions
      .offset(y: -31)  // top: -31px
  }

  // MARK: - Navigation Container

  private var navigationContainer: some View {
    VStack(spacing: 0) {
      Spacer()

      // Bottom navigation bar
      HStack(spacing: 45) {
        // Explore Tab
        BottomTabButton(
          type: .explore,
          isSelected: selectedTab == .explore,
          action: { selectedTab = .explore }
        )

        // Create Tab
        BottomTabButton(
          type: .create,
          isSelected: selectedTab == .create,
          action: { selectedTab = .create }
        )

        // Profile Tab
        BottomTabButton(
          type: .profile,
          isSelected: selectedTab == .profile,
          action: { selectedTab = .profile }
        )
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 10)
      .background(
        // Navigation bar background
        RoundedRectangle(cornerRadius: 44)
          .fill(Color(red: 0.063, green: 0.063, blue: 0.067))  // zinc-900 - 更深的背景色
          .overlay(
            RoundedRectangle(cornerRadius: 44)
              .stroke(Color(red: 0.113, green: 0.113, blue: 0.122), lineWidth: 1)  // zinc-800 - 边框色
          )
          .shadow(
            color: Color.black.opacity(0.1),
            radius: 1.5,  // 3px blur radius / 2 for SwiftUI
            x: 0,
            y: 1
          )
          .shadow(
            color: Color.black.opacity(0.06),
            radius: 1,  // 2px blur radius / 2 for SwiftUI
            x: 0,
            y: 1
          )
      )

      // Home indicator space
      Spacer()
        .frame(height: 34)  // Space for home indicator
    }
  }
}

// MARK: - Tab Button

struct BottomTabButton: View {
  let type: RootTabType
  let isSelected: Bool
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      ZStack {
        // Background circle for selected state
        // if isSelected {
        Circle()
          .fill(Color.white.opacity(0.05))
          .frame(width: 40, height: 40)
        // }

        // Icon
        iconView
      }
    }
    .frame(width: 40, height: 40)
    .animation(.easeInOut(duration: 0.2), value: isSelected)
  }

  @ViewBuilder
  private var iconView: some View {
    switch type {
    case .explore:
      Image("icon-explore")
        .renderingMode(.template)
        .foregroundColor(iconColor)
        .frame(width: 24, height: 24)

    case .create:
      Image("icon-create")
        .renderingMode(.template)
        .foregroundColor(iconColor)
        .frame(width: 24, height: 24)

    case .profile:
      Image("icon-profile")
        .renderingMode(.template)
        .foregroundColor(iconColor)
        .frame(width: 24, height: 24)
    }
  }

  private var iconColor: Color {
    if isSelected {
      return Color.white
    } else {
      return Color.white.opacity(0.7)
    }
  }
}

#Preview {
  VStack {
    Spacer()
    BottomNavigationBar(selectedTab: .constant(.create))
  }
  .background(Color.black)
}
