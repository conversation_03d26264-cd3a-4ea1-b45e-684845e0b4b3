//
//  ExploreItemDetailViewModel.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/7.
//

import Foundation
import SwiftUI

/// ExploreItemDetail ViewModel
/// 负责管理 ExploreItemDetail 页面的数据获取、状态管理和用户交互
/// 支持渐进式数据加载：立即展示基础数据，异步加载完整数据
@MainActor
class ExploreItemDetailViewModel: ObservableObject {

  // MARK: - Published Properties

  /// 完整资产数据（异步加载）
  @Published var fullAsset: UserAsset?

  /// 是否正在加载完整数据
  @Published var isLoadingFullData: Bool = false

  /// 加载错误
  @Published var loadError: Error?

  /// 是否可以重新创建
  @Published var canRecreate: Bool = false

  /// 重新创建数据
  @Published var recreateData: RecreateData?

  // MARK: - Properties

  /// 基础数据（立即可用）
  let exploreItem: ExploreItem

  /// API 服务
  private let apiService: APIService

  // MARK: - Initialization

  /// 初始化 ExploreItemDetailViewModel
  /// - Parameters:
  ///   - exploreItem: ExploreItem 基础数据
  ///   - apiService: API 服务
  init(
    exploreItem: ExploreItem,
    apiService: APIService = AppDependencyContainer.shared.apiService
  ) {
    self.exploreItem = exploreItem
    self.apiService = apiService
  }

  // MARK: - Public Methods

  /// 加载完整资产详情数据
  func loadFullAssetDetail() async {
    isLoadingFullData = true
    loadError = nil

    do {
      let asset = try await apiService.getPublicAssetDetail(id: exploreItem.id)
      self.fullAsset = asset

      // 检查是否支持重新创建
      if let recreateInfo = extractRecreateData(from: asset) {
        self.recreateData = recreateInfo
        self.canRecreate = true
      }

      Logger.info("ExploreItemDetailViewModel: Full asset data loaded for \(exploreItem.id)")
    } catch {
      self.loadError = error
      Logger.error("ExploreItemDetailViewModel: Failed to load full asset data: \(error)")
    }

    isLoadingFullData = false
  }

  /// 处理点赞操作
  func handleLike() {
    // 使用基础数据的点赞功能
    Logger.debug("Like tapped for item: \(exploreItem.id)")
    // TODO: 实现点赞逻辑
  }

  /// 处理分享操作
  func handleShare() {
    // 使用基础数据的分享功能
    Logger.debug("Share tapped for item: \(exploreItem.id)")
    // TODO: 实现分享逻辑
  }

  /// 处理 Create Similar 操作
  func handleRecreate() {
    guard canRecreate, let recreateData = recreateData else {
      Logger.warning("Create Similar not available for item: \(exploreItem.id)")
      return
    }

    // 实现 Create Similar 逻辑
    Logger.debug("Create Similar tapped for item: \(exploreItem.id)")
    // TODO: 导航到 CreateVideoFromTemplatePage
    // 使用 adapter 模式适配当前数据
    NotificationCenter.default.post(
      name: .navigateToCreateVideoFromTemplate,
      object: CreateVideoFromTemplateData(
        templateId: recreateData.templateId,
        inputImageUrls: recreateData.inputImageUrls
      )
    )
  }

  // MARK: - Computed Properties

  /// 获取用户显示名称
  var userDisplayName: String {
    return exploreItem.userDisplayName ?? "Unknown User"
  }

  /// 获取生成提示词
  var generationPrompt: String? {
    // 优先使用完整数据的提示词，否则使用基础数据
    return fullAsset?.generationPrompt ?? exploreItem.generationPrompt
  }

  /// 获取点赞数
  var likeCount: Int {
    // 优先使用完整数据的点赞数，否则使用基础数据
    return fullAsset?.likeCount ?? exploreItem.likeCount
  }

  /// 是否为视频
  var isVideo: Bool {
    return exploreItem.isVideo
  }

  /// 获取内容URL
  var contentUrl: String {
    // 优先使用完整数据的URL，否则使用基础数据
    return fullAsset?.url ?? exploreItem.url
  }

  /// 获取缩略图URL
  var thumbnailUrl: String? {
    // 优先使用完整数据的缩略图，否则使用基础数据
    return fullAsset?.thumbnailUrl ?? exploreItem.thumbnailUrl
  }

  // MARK: - Private Methods

  /// 从完整资产数据中提取重新创建所需的数据
  /// - Parameter asset: 完整资产数据
  /// - Returns: 重新创建数据，如果不支持则返回 nil
  private func extractRecreateData(from asset: UserAsset) -> RecreateData? {
    guard let metadata = asset.metadata else {
      return nil
    }

    guard let originalTaskData = metadata["originalTaskData"] else {
      return nil
    }

    guard let taskDataDict = originalTaskData.value as? [String: Any] else {
      return nil
    }

    // The inputData is nested inside originalTaskData, need to extract it properly
    guard let inputDataWrapper = taskDataDict["inputData"] else {
      return nil
    }

    // Handle AnyCodable wrapper
    let inputDataValue: Any
    if let anyCodable = inputDataWrapper as? AnyCodable {
      inputDataValue = anyCodable.value
    } else {
      inputDataValue = inputDataWrapper
    }

    guard let inputData = inputDataValue as? [String: Any] else {
      return nil
    }

    // Extract template ID, handling AnyCodable wrapper
    let templateValue: Any
    if let templateWrapper = inputData["template"] as? AnyCodable {
      templateValue = templateWrapper.value
    } else if let directTemplate = inputData["template"] {
      templateValue = directTemplate
    } else {
      return nil
    }

    guard let templateId = templateValue as? String, !templateId.isEmpty else {
      return nil
    }

    // Extract input image URLs, handling AnyCodable wrapper
    var inputImageUrls: [String] = []
    if let imagesWrapper = inputData["images"] as? AnyCodable,
      let imagesArray = imagesWrapper.value as? [Any]
    {
      inputImageUrls = imagesArray.compactMap { imageItem in
        if let anyCodable = imageItem as? AnyCodable {
          return anyCodable.value as? String
        }
        return imageItem as? String
      }
    } else if let directImages = inputData["images"] as? [String] {
      inputImageUrls = directImages
    }

    // Also check for inputImageUrl (singular) as fallback
    if inputImageUrls.isEmpty {
      if let singleImageWrapper = inputData["inputImageUrl"] as? AnyCodable,
        let singleImageUrl = singleImageWrapper.value as? String
      {
        inputImageUrls = [singleImageUrl]
      } else if let directSingleImage = inputData["inputImageUrl"] as? String {
        inputImageUrls = [directSingleImage]
      }
    }

    return RecreateData(
      templateId: templateId,
      inputImageUrls: inputImageUrls
    )
  }
}

// MARK: - Supporting Types

/// 重新创建所需的数据
struct RecreateData {
  let templateId: String
  let inputImageUrls: [String]
}

/// 创建视频模板页面数据
struct CreateVideoFromTemplateData {
  let templateId: String
  let inputImageUrls: [String]
}

// MARK: - Notification Names

extension Notification.Name {
  static let navigateToCreateVideoFromTemplate = Notification.Name(
    "navigateToCreateVideoFromTemplate")
}
