//
//  ExploreItemCard.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/7.
//

import Kingfisher
import SwiftUI

/// Explore 项目卡片组件
/// 用于展示单个 Explore 项目的信息
struct ExploreItemCard: View {

  // MARK: - Properties

  /// Explore 项目数据
  let item: ExploreItem

  /// 卡片高度
  let height: CGFloat

  /// 卡片点击回调
  let onTap: (() -> Void)?

  // MARK: - Initialization

  /// 初始化 ExploreItemCard
  /// - Parameters:
  ///   - item: Explore 项目数据
  ///   - height: 卡片高度
  ///   - onTap: 卡片点击回调
  init(item: ExploreItem, height: CGFloat = 200, onTap: (() -> Void)? = nil) {
    self.item = item
    self.height = height
    self.onTap = onTap
  }

  // MARK: - Body

  var body: some View {
    Button(action: {
      onTap?()
    }) {
      ZStack {
        // Background image
        if let thumbnailUrl = item.thumbnailUrl, let url = URL(string: thumbnailUrl) {
          KFImage(url)
            .placeholder {
              Rectangle()
                .fill(Color.gray.opacity(0.3))
                .overlay(
                  Image(systemName: item.isVideo ? "play.circle.fill" : "photo")
                    .font(.system(size: 24))
                    .foregroundColor(.white.opacity(0.6))
                )
            }
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(maxWidth: .infinity, maxHeight: height)
            .clipped()
        } else {
          Rectangle()
            .fill(Color.gray.opacity(0.3))
            .frame(maxWidth: .infinity, maxHeight: height)
            .overlay(
              Image(systemName: item.isVideo ? "play.circle.fill" : "photo")
                .font(.system(size: 24))
                .foregroundColor(.white.opacity(0.6))
            )
        }

        // Heart overlay at bottom center
        VStack {
          Spacer()

          HStack {
            Spacer()

            // Heart with like count
            HStack(spacing: 4) {
              Image(systemName: "heart.fill")
                .font(.system(size: 16))
                .foregroundColor(.white)

              Text("\(item.likeCount)")
                .font(.custom("Inter", size: 14).weight(.semibold))
                .foregroundColor(.white)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
              // Blurred background
              RoundedRectangle(cornerRadius: 32)
                .fill(Color.black.opacity(0.14))
                .background(
                  RoundedRectangle(cornerRadius: 32)
                    .fill(.ultraThinMaterial)
                )
            )

            Spacer()
          }
          .padding(.bottom, 8)
        }
      }
    }
    .buttonStyle(PlainButtonStyle())
    .clipShape(RoundedRectangle(cornerRadius: 16))
  }
}

// MARK: - Preview

#Preview {
  VStack(spacing: 16) {
    // AI 生成的视频项目
    ExploreItemCard(
      item: ExploreItem(
        id: "1",
        type: "video/mp4",
        sourceType: "ai_generated",
        userDisplayName: "John Doe",
        userPhotoURL: nil,
        url: "https://example.com/video.mp4",
        thumbnailUrl: "https://example.com/thumbnail.jpg",
        size: 1_024_000,
        generationPrompt: "A beautiful sunset over the mountains with birds flying in the sky",
        likeCount: 42,
        favoriteCount: 15,
        createdAt: "2025-07-07T10:30:00Z"
      ),
      height: 224
    )

    // 用户上传的图片项目
    ExploreItemCard(
      item: ExploreItem(
        id: "2",
        type: "image/jpeg",
        sourceType: "user_uploaded",
        userDisplayName: "Jane Smith",
        userPhotoURL: nil,
        url: "https://example.com/image.jpg",
        thumbnailUrl: "https://example.com/image_thumb.jpg",
        size: 512000,
        generationPrompt: nil,
        likeCount: 8,
        favoriteCount: 3,
        createdAt: "2025-07-07T09:15:00Z"
      ),
      height: 160
    )
  }
  .padding()
  .background(Color(red: 0.035, green: 0.035, blue: 0.043))
}
