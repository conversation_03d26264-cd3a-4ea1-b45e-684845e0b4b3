//
//  ExplorePageViewModel.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/7.
//

import Combine
import Foundation

/// Explore 页面 ViewModel
/// 负责管理 Explore 页面的数据获取、状态管理和用户交互
@MainActor
class ExplorePageViewModel: ObservableObject {

  // MARK: - Published Properties

  /// Explore 项目列表
  @Published var exploreItems: [ExploreItem] = []

  /// 是否正在加载（首次加载）
  @Published var isLoading: Bool = false

  /// 是否正在刷新（后台刷新）
  @Published var isRefreshing: Bool = false

  /// 错误信息
  @Published var error: Error? = nil

  // Filter state
  /// 选中的来源类型
  @Published var selectedSourceType: String = "all"

  /// 选中的排序方式
  @Published var selectedSortBy: String = "latest"

  /// 是否显示过滤器
  @Published var showFilters: Bool = false

  /// 选中的详情项目（用于 Sheet 展示）
  @Published var selectedDetailItem: ExploreItem?

  // MARK: - Private Properties

  /// SWR Hook 用于数据管理
  private var exploreHook: SWRHook<ExploreResponse>?

  /// API 服务
  private let apiService: APIService

  /// Combine 订阅集合
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Initialization

  /// 初始化 ExplorePageViewModel
  /// - Parameter apiService: API 服务实例，默认使用依赖容器中的实例
  init(apiService: APIService = AppDependencyContainer.shared.apiService) {
    self.apiService = apiService
    setupExploreHook()
  }

  // MARK: - Public Methods

  /// 刷新数据
  func refresh() {
    Logger.debug("ExplorePageViewModel: 手动刷新数据")
    exploreHook?.refresh()
  }

  /// 更新过滤条件
  /// - Parameters:
  ///   - sourceType: 来源类型
  ///   - sortBy: 排序方式
  func updateFilters(sourceType: String, sortBy: String) {
    Logger.debug("ExplorePageViewModel: 更新过滤条件 - sourceType: \(sourceType), sortBy: \(sortBy)")

    // 只有当过滤条件真正改变时才重新设置 Hook
    if selectedSourceType != sourceType || selectedSortBy != sortBy {
      selectedSourceType = sourceType
      selectedSortBy = sortBy
      setupExploreHook()  // 重新设置 Hook 以应用新的过滤条件
    }
  }

  /// 更新来源类型过滤
  /// - Parameter sourceType: 来源类型
  func updateSourceType(_ sourceType: String) {
    updateFilters(sourceType: sourceType, sortBy: selectedSortBy)
  }

  /// 更新排序方式
  /// - Parameter sortBy: 排序方式
  func updateSortBy(_ sortBy: String) {
    updateFilters(sourceType: selectedSourceType, sortBy: sortBy)
  }

  /// 切换过滤器显示状态
  func toggleFilters() {
    showFilters.toggle()
  }

  /// 处理项目点击（立即响应，无需等待 API）
  func handleItemTap(_ item: ExploreItem) {
    Logger.debug("Tapped explore item: \(item.id)")
    selectedDetailItem = item  // 立即设置，触发 Sheet 展示
  }

  /// 重置过滤条件
  func resetFilters() {
    updateFilters(sourceType: "all", sortBy: "latest")
    showFilters = false
  }

  // MARK: - Private Methods

  /// 设置 Explore SWR Hook
  private func setupExploreHook() {
    // 取消之前的订阅
    cancellables.removeAll()

    let query = ExploreQuery(
      page: 1,
      limit: 20,
      sourceType: selectedSourceType,
      sortBy: selectedSortBy
    )

    let cacheKey = "explore_data_\(selectedSourceType)_\(selectedSortBy)"
    Logger.debug("ExplorePageViewModel: 设置 SWR Hook，缓存键: \(cacheKey)")

    exploreHook = SWRHook.create(
      key: cacheKey,
      maxAge: 5 * 60,  // 5分钟后触发后台刷新
      staleTime: 30 * 60,  // 30分钟后数据完全过期
      networkCall: { [weak self] in
        guard let self = self else {
          throw APIServiceError.unknown(
            NSError(
              domain: "ExplorePageViewModel", code: -1,
              userInfo: [NSLocalizedDescriptionKey: "ViewModel deallocated"]))
        }
        Logger.debug("ExplorePageViewModel: 执行网络请求")
        return try await self.apiService.fetchExploreData(query: query)
      },
      autoFetch: true
    )

    observeExploreHookChanges()
  }

  /// 监听 Explore Hook 状态变化
  private func observeExploreHookChanges() {
    // 监听数据变化
    exploreHook?.$data
      .compactMap { $0?.items }
      .receive(on: DispatchQueue.main)
      .sink { [weak self] items in
        Logger.debug("ExplorePageViewModel: 接收到 \(items.count) 个 Explore 项目")
        self?.exploreItems = items
      }
      .store(in: &cancellables)

    // 监听加载状态变化
    exploreHook?.$isLoading
      .receive(on: DispatchQueue.main)
      .sink { [weak self] isLoading in
        Logger.debug("ExplorePageViewModel: 加载状态变化 - isLoading: \(isLoading)")
        self?.isLoading = isLoading
      }
      .store(in: &cancellables)

    // 监听刷新状态变化
    exploreHook?.$isRefreshing
      .receive(on: DispatchQueue.main)
      .sink { [weak self] isRefreshing in
        Logger.debug("ExplorePageViewModel: 刷新状态变化 - isRefreshing: \(isRefreshing)")
        self?.isRefreshing = isRefreshing
      }
      .store(in: &cancellables)

    // 监听错误状态变化
    exploreHook?.$error
      .receive(on: DispatchQueue.main)
      .sink { [weak self] error in
        if let error = error {
          Logger.error("ExplorePageViewModel: 发生错误 - \(error.localizedDescription)")
        }
        self?.error = error
      }
      .store(in: &cancellables)
  }
}

// MARK: - Computed Properties

extension ExplorePageViewModel {

  /// 是否有数据
  var hasData: Bool {
    return !exploreItems.isEmpty
  }

  /// 是否显示空状态
  var shouldShowEmptyState: Bool {
    return !isLoading && !hasData && error == nil
  }

  /// 是否显示错误状态
  var shouldShowErrorState: Bool {
    return error != nil && !hasData
  }

  /// 格式化的错误消息
  var errorMessage: String {
    guard let error = error else { return "" }

    if let apiError = error as? APIServiceError {
      switch apiError {
      case .networkError(let underlyingError):
        return "Network error: \(underlyingError.localizedDescription)"
      case .responseError(let statusCode, let message):
        return "Server error (\(statusCode)): \(message)"
      case .parsingError(let underlyingError):
        return "Data parsing error: \(underlyingError.localizedDescription)"
      case .unknown(let underlyingError):
        return "Unknown error: \(underlyingError.localizedDescription)"
      }
    }

    return error.localizedDescription
  }

  /// 当前过滤条件的显示文本
  var currentFiltersText: String {
    let sourceTypeDisplay =
      ExploreSourceType(rawValue: selectedSourceType)?.displayName ?? selectedSourceType
    let sortByDisplay = ExploreSortBy(rawValue: selectedSortBy)?.displayName ?? selectedSortBy
    return "\(sourceTypeDisplay) • \(sortByDisplay)"
  }
}

// MARK: - Filter Options

extension ExplorePageViewModel {

  /// 所有来源类型选项
  var sourceTypeOptions: [ExploreSourceType] {
    return ExploreSourceType.allCases
  }

  /// 所有排序方式选项
  var sortByOptions: [ExploreSortBy] {
    return ExploreSortBy.allCases
  }
}
