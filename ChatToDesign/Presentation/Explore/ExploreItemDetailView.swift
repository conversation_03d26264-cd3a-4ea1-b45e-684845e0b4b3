//
//  ExploreItemDetailView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/7.
//

import AVKit
import Kingfisher
import SwiftUI

/// ExploreItemDetail View
/// 详情页面组件，完全复刻 CreateDetailPage 的样式和布局
struct ExploreItemDetailView: View {

  // MARK: - Properties

  /// 基础数据（立即可用）
  let exploreItem: ExploreItem

  /// ViewModel 实例
  @StateObject private var viewModel: ExploreItemDetailViewModel

  /// 环境变量
  @Environment(\.dismiss) private var dismiss

  // MARK: - State

  /// 视频播放器
  @State private var player: AVPlayer?

  /// 是否正在播放
  @State private var isPlaying = false

  // MARK: - Initialization

  /// 初始化 ExploreItemDetailView
  /// - Parameter exploreItem: ExploreItem 基础数据
  init(exploreItem: ExploreItem) {
    self.exploreItem = exploreItem
    self._viewModel = StateObject(
      wrappedValue: ExploreItemDetailViewModel(exploreItem: exploreItem))
  }

  // MARK: - Body

  var body: some View {
    GeometryReader { geometry in
      VStack(spacing: 0) {
        customNavigationBar

        // 主内容区域
        ZStack {
          // 背景色/渐变
          Color.black

          // 图片/视频内容（正确尺寸）
          mainContentView

          // 右侧悬浮按钮
          VStack {
            Spacer()
            HStack {
              Spacer()
              LikeShareActionsView(
                likeCount: viewModel.likeCount,
                onLike: viewModel.handleLike,
                onShare: viewModel.handleShare
              )
              .padding(.trailing, 16)
            }
          }
        }

        bottomActionArea
      }
    }
    .background(blurredBackground)
    .navigationBarHidden(true)
    .onAppear {
      setupMediaPlayer()
    }
    .onDisappear {
      cleanupMediaPlayer()
    }
    .task {
      await viewModel.loadFullAssetDetail()
    }
  }

  // MARK: - Layout Components

  /// 背景模糊图片
  private var blurredBackground: some View {
    KFImage(URL(string: viewModel.thumbnailUrl ?? viewModel.contentUrl))
      .resizable()
      .aspectRatio(contentMode: .fill)
      .scaleEffect(1.2)
      .blur(radius: 10)
      .opacity(0.7)
      .ignoresSafeArea()
  }

  /// 自定义导航栏
  private var customNavigationBar: some View {
    HStack {
      Button(action: { dismiss() }) {
        Image(systemName: "xmark")
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(.white)
          .frame(width: 32, height: 32)
          .background(Color.black.opacity(0.5))
          .clipShape(Circle())
      }

      Spacer()
    }
    .padding(.horizontal, 16)
    .padding(.top, 8)
  }

  /// 主内容视图
  private var mainContentView: some View {
    VStack {
      if isVideo {
        videoPlayerView
          .aspectRatio(contentMode: .fill)
          .frame(maxWidth: UIScreen.main.bounds.width, maxHeight: UIScreen.main.bounds.height)
          .clipped()
          .clipShape(RoundedRectangle(cornerRadius: 16))
      } else {
        imageView
          .aspectRatio(contentMode: .fill)
          .frame(maxWidth: UIScreen.main.bounds.width, maxHeight: UIScreen.main.bounds.height)
          .clipped()
          .clipShape(RoundedRectangle(cornerRadius: 16))
      }
    }
  }

  /// 底部操作区域
  private var bottomActionArea: some View {
    VStack(spacing: 0) {
      UserInfoView(
        username: getUserDisplayName(),
        prompt: viewModel.generationPrompt
      )

      // 加载状态指示器
      if viewModel.isLoadingFullData {
        loadingIndicator
      }

      // Create Similar 按钮（仅在支持时显示）
      if viewModel.canRecreate {
        createSimilarButtonArea
      } else {
        // 占位区域，保持布局一致
        Spacer()
          .frame(height: 80)
      }
    }
    .background(Color.black.opacity(0.4))
  }

  /// 图片视图
  private var imageView: some View {
    KFImage(URL(string: viewModel.contentUrl))
      .placeholder {
        Rectangle()
          .fill(Color.gray.opacity(0.3))
          .overlay(
            VStack(spacing: 12) {
              ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)

              Text("Loading image...")
                .font(.custom("Inter", size: 14))
                .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))
            }
          )
      }
      .retry(maxCount: 3)
      .fade(duration: 0.25)
      .resizable()
      .aspectRatio(contentMode: .fit)
  }

  /// 视频播放器视图
  private var videoPlayerView: some View {
    Group {
      if let player = player {
        VideoPlayer(player: player)
          .onTapGesture {
            togglePlayback()
          }
      } else {
        Rectangle()
          .fill(Color.gray.opacity(0.3))
          .overlay(
            VStack(spacing: 12) {
              ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)

              Text("Loading video...")
                .font(.custom("Inter", size: 14))
                .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))
            }
          )
      }
    }
  }

  /// 右侧悬浮按钮
  private var rightFloatingActions: some View {
    VStack {
      Spacer()
      HStack {
        Spacer()
        LikeShareActionsView(
          likeCount: viewModel.likeCount,
          onLike: viewModel.handleLike,
          onShare: viewModel.handleShare
        )
        .padding(.trailing, 16)
      }
    }
  }

  /// 底部信息区域
  private var bottomInfoArea: some View {
    VStack(spacing: 0) {
      // 用户信息和提示词
      UserInfoView(
        username: viewModel.userDisplayName,
        prompt: viewModel.generationPrompt
      )

      // 加载状态指示器
      if viewModel.isLoadingFullData {
        loadingIndicator
      }

      // Create Similar 按钮（仅在支持时显示）
      if viewModel.canRecreate {
        createSimilarButtonArea
      }
    }
    .background(Color.black.opacity(0.4))
  }

  /// 加载状态指示器
  private var loadingIndicator: some View {
    HStack {
      ProgressView()
        .scaleEffect(0.8)
        .tint(.white)
      Text("Loading additional features...")
        .font(.caption)
        .foregroundColor(.secondary)
    }
    .padding(.horizontal, 24)
    .padding(.top, 8)
  }

  /// Create Similar 按钮区域 - 基于 Figma 设计
  private var createSimilarButtonArea: some View {
    VStack(spacing: 0) {
      // Container with padding and gap as per Figma design
      VStack(spacing: 16) {
        // Create Similar Button
        ExploreButton(
          title: "Create Similar",
          action: viewModel.handleRecreate
        )
      }
      .padding(.horizontal, 24)
      .padding(.vertical, 0)
    }
    .padding(.bottom, 34)
  }

  // MARK: - Computed Properties

  /// 是否为视频
  private var isVideo: Bool {
    return viewModel.isVideo
  }

  /// 获取用户显示名称
  private func getUserDisplayName() -> String {
    return viewModel.userDisplayName
  }

  // MARK: - Media Player Methods

  /// 设置媒体播放器
  private func setupMediaPlayer() {
    if viewModel.isVideo, let url = URL(string: viewModel.contentUrl) {
      player = AVPlayer(url: url)
      setupLooping()
      player?.play()
      isPlaying = true
    }
  }

  /// 设置循环播放
  private func setupLooping() {
    guard let player = player else { return }

    NotificationCenter.default.addObserver(
      forName: .AVPlayerItemDidPlayToEndTime,
      object: player.currentItem,
      queue: .main
    ) { _ in
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
        player.seek(to: .zero)
        player.play()
      }
    }
  }

  /// 清理媒体播放器
  private func cleanupMediaPlayer() {
    player?.pause()
    NotificationCenter.default.removeObserver(
      self,
      name: .AVPlayerItemDidPlayToEndTime,
      object: player?.currentItem
    )
    player = nil
  }

  /// 切换播放状态
  private func togglePlayback() {
    guard let player = player else { return }

    if isPlaying {
      player.pause()
    } else {
      player.play()
    }
    isPlaying.toggle()
  }
}

// MARK: - Preview

#Preview {
  ExploreItemDetailView(
    exploreItem: ExploreItem(
      id: "1",
      type: "video/mp4",
      sourceType: "ai_generated",
      userDisplayName: "John Doe",
      userPhotoURL: nil,
      url: "https://example.com/video.mp4",
      thumbnailUrl: "https://example.com/thumbnail.jpg",
      size: 1_024_000,
      generationPrompt: "A beautiful sunset over the mountains with birds flying in the sky",
      likeCount: 42,
      favoriteCount: 15,
      createdAt: "2025-07-07T10:30:00Z"
    )
  )
}
