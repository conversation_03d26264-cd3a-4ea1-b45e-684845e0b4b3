import PhotosUI
import SwiftUI

// MARK: - Image Selection Card
struct ImageSelectionCard: View {
  @Binding var selectedImageItem: PhotosPickerItem?
  let displayImageData: Data?
  @Binding var isLoading: Bool

  var body: some View {
    VStack {
      if let data = displayImageData, let uiImage = UIImage(data: data) {
        Image(uiImage: uiImage)
          .resizable()
          .scaledToFit()
          .frame(maxHeight: 350)
          .cornerRadius(AppConstants.mediumCornerRadius)
          .overlay(
            isLoading ? LoadingOverlay(text: "Processing...") : nil
          )
      } else {
        PhotosPicker(
          selection: $selectedImageItem,
          matching: .images,
          photoLibrary: .shared()
        ) {
          ImagePlaceholderView(
            iconName: "photo.on.rectangle.angled",
            text: "Select an image to edit"
          )
          .frame(height: 250)
        }
        .buttonStyle(.plain)
      }
    }
    .padding()
    .background(Color(.secondarySystemGroupedBackground))
    .cornerRadius(AppConstants.largeCornerRadius)
    .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
    .overlay(
      (isLoading && displayImageData == nil) ? LoadingOverlay(text: "Processing...") : nil
    )
  }
}
