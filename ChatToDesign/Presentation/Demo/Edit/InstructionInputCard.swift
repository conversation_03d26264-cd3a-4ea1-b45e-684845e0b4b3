import SwiftUI

// MARK: - Instruction Input Card
struct InstructionInputCard: View {
  @Binding var instructionText: String
  @Binding var isCardFocused: Bool
  @State private var isFocused: Bool = false
  @FocusState private var fieldFocused: Bool

  private let placeholderText = "e.g.: Change the background to a cyberpunk style street"

  var body: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack {
        Label("Edit Instructions", systemImage: "wand.and.stars")
          .font(.system(size: 15, weight: .semibold))
          .foregroundColor(.secondary)

        Spacer()

        if !instructionText.isEmpty {
          Button(action: { instructionText = "" }) {
            Image(systemName: "xmark.circle.fill")
              .foregroundColor(.secondary)
              .font(.system(size: 16))
          }
          .transition(.opacity)
          .buttonStyle(PlainButtonStyle())
          .accessibilityLabel("Clear text")
        }
      }
      .padding(.horizontal, 4)

      ZStack(alignment: .topLeading) {
        if instructionText.isEmpty && !fieldFocused {
          Text(placeholderText)
            .foregroundColor(Color(.placeholderText))
            .padding(.horizontal, 8)
            .padding(.vertical, 12)
            .allowsHitTesting(false)
        }

        TextEditor(text: $instructionText)
          .focused($fieldFocused)
          .scrollContentBackground(.hidden)
          .padding(4)
          .background(
            RoundedRectangle(cornerRadius: 10)
              .fill(Color(.systemGray6))
              .overlay(
                RoundedRectangle(cornerRadius: 10)
                  .stroke(fieldFocused ? Color.accentColor : Color.clear, lineWidth: 2)
              )
          )
          .frame(minHeight: 80, maxHeight: 120)
          .onChange(of: fieldFocused) { newValue in
            withAnimation(.easeInOut(duration: 0.2)) {
              isFocused = newValue
              isCardFocused = newValue
            }
          }
      }

      HStack {
        Spacer()

        Text("\(instructionText.count)/300")
          .font(.caption)
          .foregroundColor(
            instructionText.count > 250
              ? (instructionText.count > 300 ? .red : .orange) : .secondary)
      }
      .padding(.horizontal, 4)
    }
    .padding()
    .background(Color(.secondarySystemGroupedBackground))
    .clipShape(RoundedRectangle(cornerRadius: 16))
    .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
  }
}
