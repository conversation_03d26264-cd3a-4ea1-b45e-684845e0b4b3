import PhotosUI
import SwiftUI
import UIKit

struct EditContentView: View {
  @ObservedObject var viewModel: ContentViewModel
  @State private var selectedPhotos: [PhotosPickerItem] = []

  var body: some View {
    VStack(spacing: 18) {
      // Image selection area
      // Edit instruction input
      InstructionInputCard(
        instructionText: $viewModel.editInstruction,
        isCardFocused: $viewModel.isInputCardFocused
      )
      VStack(alignment: .leading, spacing: 12) {
        HStack {
          Label("Source Images", systemImage: "photo.stack")
            .font(.system(size: 15, weight: .semibold))
            .foregroundColor(.secondary)

          Text("(Max 4 images)")
            .font(.caption)
            .foregroundColor(.secondary)

          Spacer()

          // Add image button
          let canAddMore = viewModel.canAddMoreImages
          PhotosPicker(
            selection: $selectedPhotos,
            maxSelectionCount: viewModel.maxSourceImagesCount - viewModel.sourceImages.count,
            matching: .images,
            photoLibrary: .shared()
          ) {
            Label("Add", systemImage: "plus.circle")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(canAddMore ? .accentColor : .gray)
          }
          .disabled(!canAddMore)
        }
        .padding(.horizontal, 4)

        // Source image display area
        if !viewModel.sourceImages.isEmpty {
          ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
              ForEach(viewModel.sourceImages.indices, id: \.self) { index in
                if let uiImage = UIImage(data: viewModel.sourceImages[index]) {
                  ZStack(alignment: .topTrailing) {
                    ImageThumbnail(
                      image: uiImage,
                      isSelected: index == 0  // First image as primary selected image
                    )

                    // 优化后的删除按钮 - 符合Apple HIG
                    Button(action: {
                      viewModel.removeSourceImage(at: index)
                    }) {
                      Image(systemName: "minus.circle.fill")
                        .font(.system(size: 20))
                        .symbolRenderingMode(.palette)
                        .foregroundStyle(.white, Color.red)
                        .background(Color.white.opacity(0.2))
                        .clipShape(Circle())
                        .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
                    }
                    .padding(4)
                    .offset(x: 5, y: -5)
                    .buttonStyle(BorderlessButtonStyle())
                    .accessibilityLabel("Remove image")
                  }
                  .padding(2)
                }
              }
            }
          }
          .frame(height: 100)
          .padding(.horizontal, 4)
        } else {
          Text("Please add images for editing")
            .font(.system(size: 15))
            .foregroundColor(.secondary)
            .padding()
            .frame(maxWidth: .infinity, alignment: .center)
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 10))
        }
      }
      .padding()
      .background(Color(.secondarySystemGroupedBackground))
      .clipShape(RoundedRectangle(cornerRadius: 16))
      .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
      .onChange(of: selectedPhotos) { newItems in
        Task {
          // Load selected image data
          for item in newItems {
            if let data = try? await item.loadTransferable(type: Data.self) {
              await MainActor.run {
                viewModel.addSourceImage(data)
              }
            }
          }
          // Clear selection for next time
          await MainActor.run {
            selectedPhotos = []
          }
        }
      }

      // Result image display
      VStack(alignment: .leading, spacing: 12) {
        HStack {
          Label("Result", systemImage: "photo")
            .font(.system(size: 15, weight: .semibold))
            .foregroundColor(.secondary)

          Spacer()
        }
        .padding(.horizontal, 4)

        if let data = viewModel.imageData, let uiImage = UIImage(data: data) {
          ZStack {
            Image(uiImage: uiImage)
              .resizable()
              .scaledToFit()
              .frame(maxHeight: 350)
              .clipShape(RoundedRectangle(cornerRadius: 12))
              .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
              .overlay(
                RoundedRectangle(cornerRadius: 12)
                  .stroke(Color(.systemGray5), lineWidth: 1)
              )
              .overlay(
                viewModel.isLoading
                  ? ZStack {
                    Color.black.opacity(0.5)
                      .clipShape(RoundedRectangle(cornerRadius: 12))
                    LoadingOverlay(text: "Processing...")
                  } : nil
              )
          }
          .animation(.spring(response: 0.3), value: !viewModel.isLoading)
        } else {
          EmptyImagePlaceholder(
            iconName: "photo.artframe",
            text: "Add source images and edit",
            isLoading: viewModel.isLoading
          )
        }
      }
      .padding()
      .background(Color(.secondarySystemGroupedBackground))
      .clipShape(RoundedRectangle(cornerRadius: 16))
      .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
    }
  }
}

// MARK: - Image Thumbnail
struct ImageThumbnail: View {
  let image: UIImage
  let isSelected: Bool

  var body: some View {
    Image(uiImage: image)
      .resizable()
      .scaledToFill()
      .frame(width: 80, height: 80)
      .clipShape(RoundedRectangle(cornerRadius: 10))
      .overlay(
        RoundedRectangle(cornerRadius: 10)
          .stroke(isSelected ? Color.accentColor : Color.clear, lineWidth: 3)
      )
      .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
  }
}
