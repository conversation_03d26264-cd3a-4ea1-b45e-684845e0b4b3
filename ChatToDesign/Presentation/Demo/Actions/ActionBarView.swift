import SwiftUI

// MARK: - Action Bar View
struct ActionBarView: View {
  let mode: AppMode
  @Binding var isLoading: Bool
  let canPerformAction: <PERSON><PERSON>
  @Binding var resultImageData: Data?
  @Binding var isInputCardFocused: Bool

  let performAction: () -> Void
  let saveAction: () -> Void
  let editAction: () -> Void

  var body: some View {
    VStack(spacing: 16) {
      ActionButton(
        isLoading: isLoading,
        mode: mode,
        isDisabled: !canPerformAction || isLoading,
        action: performAction
      )

      if resultImageData != nil && !isLoading && !isInputCardFocused {
        HStack(spacing: 16) {
          if mode == .generate {
            EditButton(action: editAction)
          }

          SaveButton(action: saveAction)
        }
      }
    }
    .padding(.horizontal, 20)
    .padding(.top, 16)
    .padding(.bottom, 16)
    .background(Material.regularMaterial)
    .clipShape(RoundedRectangle(cornerRadius: 20))
    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 2)
    .padding(.horizontal, 16)
    .padding(.bottom, 8)
    .animation(.easeInOut(duration: 0.2), value: resultImageData != nil)
    .animation(.easeInOut(duration: 0.2), value: isInputCardFocused)
  }
}

// MARK: - Edit Button
struct EditButton: View {
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      HStack(spacing: 8) {
        Image(systemName: "slider.horizontal.3")
          .font(.system(size: 15, weight: .medium))
        Text("Edit")
          .font(.system(size: 15, weight: .semibold))
      }
      .frame(maxWidth: .infinity)
      .padding(.vertical, 12)
      .background(Color.blue)
      .foregroundColor(.white)
      .clipShape(RoundedRectangle(cornerRadius: 10))
    }
    .buttonStyle(ButtonScaleEffect())
    .accessibilityLabel("Edit Image")
    .accessibilityHint("Switch to edit mode with this image")
  }
}

// MARK: - Button Scale Effect
struct ButtonScaleEffect: ButtonStyle {
  func makeBody(configuration: Configuration) -> some View {
    configuration.label
      .scaleEffect(configuration.isPressed ? 0.95 : 1)
      .animation(.spring(response: 0.3, dampingFraction: 0.7), value: configuration.isPressed)
  }
}
