import SwiftUI

// MARK: - Save Button
struct SaveButton: View {
  let action: () -> Void

  var body: some View {
    Button {
      action()
      #if os(iOS)
        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
      #endif
    } label: {
      HStack(spacing: 8) {
        Image(systemName: "square.and.arrow.down")
          .font(.system(size: 15, weight: .medium))
        Text("Save")
          .font(.system(size: 15, weight: .semibold))
      }
      .frame(maxWidth: .infinity)
      .padding(.vertical, 12)
      .background(Color(.systemGray5))
      .foregroundColor(.primary)
      .clipShape(RoundedRectangle(cornerRadius: 10))
    }
    .buttonStyle(ButtonScaleEffect())
    .accessibilityLabel("Save to Photos")
    .accessibilityHint("Save the generated image to your photo library")
    .transition(.opacity.animation(.easeInOut))
  }
}
