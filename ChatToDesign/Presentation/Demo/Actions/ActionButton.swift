import SwiftUI

// MARK: - Action Button
struct ActionButton: View {
  let isLoading: Bool
  let mode: AppMode
  let isDisabled: Bool
  let action: () -> Void

  var body: some View {
    Button {
      action()
      #if os(iOS)
        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
      #endif
    } label: {
      HStack(spacing: 10) {
        if isLoading {
          ProgressView()
            .tint(.white)
            .scaleEffect(0.8)
        } else {
          Image(systemName: mode == .generate ? "sparkles" : "wand.and.stars")
            .font(.system(size: 18, weight: .semibold))
        }

        Text(isLoading ? "Processing..." : (mode == .generate ? "Generate Image" : "Apply Edit"))
          .font(.system(size: 17, weight: .bold))
      }
      .frame(maxWidth: .infinity)
      .frame(height: 54)
      .background(isDisabled ? Color.accentColor.opacity(0.6) : Color.accentColor)
      .foregroundColor(.white)
      .clipShape(RoundedRectangle(cornerRadius: 14))
      .shadow(
        color: isDisabled ? Color.clear : Color.accentColor.opacity(0.4), radius: 8, x: 0, y: 4)
    }
    .buttonStyle(ButtonScaleEffect())
    .disabled(isDisabled)
    .animation(.easeInOut(duration: 0.2), value: isLoading)
    .accessibilityLabel(mode == .generate ? "Generate Image" : "Apply Edit")
    .accessibilityHint(
      mode == .generate ? "Create a new image from your prompt" : "Apply changes to the image")
  }
}
