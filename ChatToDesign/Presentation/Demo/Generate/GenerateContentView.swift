import SwiftUI

// MARK: - Generate Content View
struct GenerateContentView: View {
  @ObservedObject var viewModel: ContentViewModel

  var body: some View {
    VStack(spacing: 20) {
      PromptInputCard(
        promptText: $viewModel.generationPrompt,
        isCardFocused: $viewModel.isInputCardFocused
      )

      ImageResultView(
        imageData: $viewModel.imageData,
        isLoading: $viewModel.isLoading,
        placeholderIcon: "wand.and.stars.inverse",
        placeholderText: "Your creation will appear here"
      )
    }
  }
}

// MARK: - Image Result View
struct ImageResultView: View {
  @Binding var imageData: Data?
  @Binding var isLoading: Bool
  let placeholderIcon: String
  let placeholderText: String

  var body: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack {
        Label("Result", systemImage: "photo")
          .font(.system(size: 15, weight: .semibold))
          .foregroundColor(.secondary)

        Spacer()
      }
      .padding(.horizontal, 4)

      if let data = imageData, let uiImage = UIImage(data: data) {
        ZStack(alignment: .topTrailing) {
          Image(uiImage: uiImage)
            .resizable()
            .scaledToFit()
            .frame(maxHeight: 350)
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
            .overlay(
              RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray5), lineWidth: 1)
            )
            .overlay(
              isLoading
                ? ZStack {
                  Color.black.opacity(0.5)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                  LoadingOverlay(text: "Processing...")
                } : nil
            )
            .transition(.opacity.combined(with: .scale))
        }
        .animation(.spring(response: 0.3), value: imageData != nil)
      } else {
        EmptyImagePlaceholder(
          iconName: placeholderIcon, text: placeholderText, isLoading: isLoading)
      }
    }
    .padding()
    .background(Color(.secondarySystemGroupedBackground))
    .clipShape(RoundedRectangle(cornerRadius: 16))
    .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
  }
}

// MARK: - Empty Image Placeholder
struct EmptyImagePlaceholder: View {
  let iconName: String
  let text: String
  let isLoading: Bool

  var body: some View {
    ZStack {
      RoundedRectangle(cornerRadius: 12)
        .fill(Color(.systemGray6))
        .frame(height: 250)
        .overlay(
          RoundedRectangle(cornerRadius: 12)
            .stroke(Color(.systemGray5), lineWidth: 1)
        )

      if isLoading {
        LoadingOverlay(text: "Processing...")
      } else {
        VStack(spacing: 16) {
          Image(systemName: iconName)
            .font(.system(size: 40))
            .foregroundColor(.secondary)

          Text(text)
            .font(.system(size: 15, weight: .medium))
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
        }
      }
    }
  }
}
