import SwiftUI

// MARK: - Prompt Input Card
struct PromptInputCard: View {
  @Binding var promptText: String
  @Binding var isCardFocused: Bool
  @State private var isFocused: Bool = false
  @FocusState private var fieldFocused: Bool

  private let placeholderText =
    "Enter your creative idea... e.g.: A corgi wearing a suit reading newspaper in a café"

  var body: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack {
        Label("Your Idea", systemImage: "lightbulb")
          .font(.system(size: 15, weight: .semibold))
          .foregroundColor(.secondary)

        Spacer()

        if !promptText.isEmpty {
          Button(action: { promptText = "" }) {
            Image(systemName: "xmark.circle.fill")
              .foregroundColor(.secondary)
              .font(.system(size: 16))
          }
          .transition(.opacity)
          .buttonStyle(PlainButtonStyle())
          .accessibilityLabel("Clear text")
        }
      }
      .padding(.horizontal, 4)

      ZStack(alignment: .topLeading) {
        if promptText.isEmpty && !fieldFocused {
          Text(placeholderText)
            .foregroundColor(Color(.placeholderText))
            .padding(.horizontal, 8)
            .padding(.vertical, 12)
            .allowsHitTesting(false)
        }

        TextEditor(text: $promptText)
          .focused($fieldFocused)
          .scrollContentBackground(.hidden)
          .padding(4)
          .background(
            RoundedRectangle(cornerRadius: 10)
              .fill(Color(.systemGray6))
              .overlay(
                RoundedRectangle(cornerRadius: 10)
                  .stroke(fieldFocused ? Color.accentColor : Color.clear, lineWidth: 2)
              )
          )
          .frame(minHeight: 100, maxHeight: 150)
          .onChange(of: fieldFocused) { newValue in
            withAnimation(.easeInOut(duration: 0.2)) {
              isFocused = newValue
              isCardFocused = newValue
            }
          }
      }

      HStack {
        Spacer()

        Text("\(promptText.count)/500")
          .font(.caption)
          .foregroundColor(
            promptText.count > 400 ? (promptText.count > 500 ? .red : .orange) : .secondary)
      }
      .padding(.horizontal, 4)
    }
    .padding()
    .background(Color(.secondarySystemGroupedBackground))
    .clipShape(RoundedRectangle(cornerRadius: 16))
    .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
  }
}
