import SwiftUI

// MARK: - App Mode Enum
enum AppMode: String, CaseIterable, Identifiable {
  case generate = "✨ Generate"
  case edit = "🎨 Edit"
  var id: String { self.rawValue }
}

// MARK: - Error Enum
enum AppError: LocalizedError {
  case missingInput(String)
  case apiError(String)
  case unknown

  var errorDescription: String? {
    switch self {
    case .missingInput(let msg): return msg
    case .apiError(let msg): return msg
    case .unknown: return "An unknown error occurred"
    }
  }
}
