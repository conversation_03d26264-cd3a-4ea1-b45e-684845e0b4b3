import SwiftUI

// MARK: - Image Placeholder View
struct ImagePlaceholderView: View {
  let iconName: String
  let text: String

  var body: some View {
    VStack(spacing: 12) {
      Image(systemName: iconName)
        .font(.system(size: 40))
        .foregroundColor(.secondary.opacity(0.6))
      Text(text)
        .font(.callout)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(Color(.systemGray5))
    .cornerRadius(AppConstants.mediumCornerRadius)
  }
}

// MARK: - Loading Overlay
struct LoadingOverlay: View {
  let text: String?

  var body: some View {
    ZStack {
      Rectangle()
        .fill(.ultraThinMaterial)

      VStack(spacing: 15) {
        ProgressView()
          .controlSize(.large)
        if let text = text {
          Text(text)
            .font(.caption)
            .foregroundColor(.secondary)
        }
      }
    }
    .cornerRadius(AppConstants.mediumCornerRadius)
    .transition(.opacity.animation(.easeInOut))
  }
}

// MARK: - Confirmation Overlay
struct ConfirmationOverlay: View {
  let message: String

  var body: some View {
    Text(message)
      .font(.footnote)
      .padding(.horizontal, 12)
      .padding(.vertical, 8)
      .background(.thickMaterial)
      .foregroundColor(.secondary)
      .cornerRadius(AppConstants.mediumCornerRadius)
      .shadow(radius: 5)
      .transition(.opacity.combined(with: .scale(scale: 0.9)).animation(.spring()))
  }
}
