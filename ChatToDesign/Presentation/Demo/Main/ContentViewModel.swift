import Combine
import PhotosUI  // Might be needed by ImageSaver or dependencies
import SwiftUI  // For Data, UIImage, Color etc. (Consider importing specific frameworks if needed)
import os

// 定义历史记录项的数据结构
struct ImageHistoryItem: Identifiable {
  let id = UUID()
  let imageData: Data
  let prompt: String
  let timestamp: Date
}

@MainActor  // Mark class as MainActor since it publishes UI state
class ContentViewModel: ObservableObject {

  // MARK: - Published Properties (UI State)
  @Published var selectedMode: AppMode = .generate
  @Published var imageData: Data?
  @Published var isLoading: Bool = false
  @Published var showingErrorAlert: Bool = false
  @Published var errorMessage: String = ""
  @Published var showingSaveConfirmation: Bool = false
  @Published var isInputCardFocused: Bool = false  // 添加输入卡片焦点状态

  // MARK: - History Lists
  @Published var generatedImagesHistory: [ImageHistoryItem] = []
  @Published var editedImagesHistory: [ImageHistoryItem] = []
  @Published var isHistoryPanelVisible: Bool = false

  // MARK: - Image Generation State
  @Published var generationPrompt: String = ""

  // MARK: - Image Edit State
  @Published var editInstruction: String = ""
  @Published var selectedSourceImage: Data?
  @Published var sourceImages: [Data] = []  // 存储多个源图片
  @Published var isImagePickerVisible: Bool = false

  // 最大允许的源图片数量
  let maxSourceImagesCount = 4

  // MARK: - Services
  private let networkService = SimulatedNetworkService()
  private let imageSaver = ImageSaver()

  // MARK: - Private State
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Computed Properties
  var canPerformAction: Bool {
    guard !isLoading else { return false }
    switch selectedMode {
    case .generate:
      return !generationPrompt.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    case .edit:
      return (imageData != nil || !sourceImages.isEmpty)
        && !editInstruction.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
  }

  // 是否可以添加更多图片
  var canAddMoreImages: Bool {
    return sourceImages.count < maxSourceImagesCount
  }

  // MARK: - Initialization
  init() {
    Logger.debug("ContentViewModel Initialized")
    setupImageSaver()
    observeModeChange()
  }

  deinit {
    Logger.debug("ContentViewModel Deinitialized")
  }

  // MARK: - Setup
  private func setupImageSaver() {
    imageSaver.onSuccess = { [weak self] in
      // Ensure UI updates are on the main thread
      Task { @MainActor [weak self] in
        self?.showingSaveConfirmation = true
      }
    }
    imageSaver.onError = { [weak self] error in
      Task { @MainActor [weak self] in
        self?.errorMessage = "Save failed: \(error.localizedDescription)"
        self?.showingErrorAlert = true
      }
    }
  }

  private func observeModeChange() {
    $selectedMode
      .dropFirst()  // Ignore the initial value
      .sink { [weak self] newMode in
        Logger.info("Mode changed to: \(newMode)")
        self?.clearInputs()
      }
      .store(in: &cancellables)
  }

  // MARK: - Actions
  func performPrimaryAction() {
    guard canPerformAction else {
      errorMessage = "Please enter a prompt or instruction."
      Logger.warning("Cannot perform action, condition not met.")
      return
    }

    isLoading = true
    errorMessage = ""

    // Dismiss keyboard
    #if os(iOS)
      UIApplication.shared.sendAction(
        #selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    #endif

    Task {
      do {
        let resultData: Data?

        if selectedMode == .generate {
          Logger.info("Generating image with prompt: \(generationPrompt)")
          resultData = try await networkService.generateImage(prompt: generationPrompt)
        } else {
          Logger.info("Editing image with instruction: \(editInstruction)")
          // 确保有图片数据可用于编辑
          if let firstImage = sourceImages.first {
            // 使用第一张源图片作为主图
            resultData = try await networkService.editImage(
              imageData: firstImage,
              instruction: editInstruction
            )
          } else if let sourceImage = imageData {
            // 兼容旧逻辑，使用当前显示的图片
            resultData = try await networkService.editImage(
              imageData: sourceImage,
              instruction: editInstruction
            )
          } else {
            throw AppError.apiError("No source image available for editing")
          }
        }

        guard let finalData = resultData, !finalData.isEmpty else {
          throw AppError.apiError("Operation returned no data")
        }

        // 更新共享图片数据
        self.imageData = finalData

        // 添加到历史记录
        if selectedMode == .generate {
          addToGeneratedHistory(imageData: finalData, prompt: generationPrompt)
        } else {
          addToEditedHistory(imageData: finalData, instruction: editInstruction)
        }

      } catch {
        print("Action failed: \(error)")
        errorMessage = "An unexpected error occurred: \(error.localizedDescription)"
        Logger.error("Action failed: \(error)")
        showingErrorAlert = true
      }

      self.isLoading = false
    }
  }

  // MARK: - Mode Switching Actions
  func switchToEditMode(withImage imageData: Data) {
    self.sourceImages = []  // 清空已有的源图片
    self.sourceImages.append(imageData)  // 添加当前图片为第一个源图片
    self.imageData = imageData
    self.selectedMode = .edit
  }

  func saveImage() {
    guard let data = imageData, let uiImage = UIImage(data: data) else {
      Task { @MainActor [weak self] in
        self?.errorMessage = "No image to save"
        self?.showingErrorAlert = true
      }
      return
    }
    Logger.info("Requesting save for result image.")
    imageSaver.writeToPhotoAlbum(image: uiImage)
  }

  // MARK: - Source Image Management

  /// 添加新的源图片
  func addSourceImage(_ imageData: Data) {
    if sourceImages.count < maxSourceImagesCount {
      sourceImages.append(imageData)
    }
  }

  /// 移除指定索引的源图片
  func removeSourceImage(at index: Int) {
    guard index >= 0, index < sourceImages.count else { return }
    sourceImages.remove(at: index)
  }

  // MARK: - History Management

  /// 添加生成的图片到历史记录
  private func addToGeneratedHistory(imageData: Data, prompt: String) {
    let historyItem = ImageHistoryItem(
      imageData: imageData,
      prompt: prompt,
      timestamp: Date()
    )
    generatedImagesHistory.insert(historyItem, at: 0)  // 最新的在最前面

    // 可选：限制历史记录数量
    if generatedImagesHistory.count > 50 {
      generatedImagesHistory.removeLast()
    }
  }

  /// 添加编辑的图片到历史记录
  private func addToEditedHistory(imageData: Data, instruction: String) {
    let historyItem = ImageHistoryItem(
      imageData: imageData,
      prompt: instruction,
      timestamp: Date()
    )
    editedImagesHistory.insert(historyItem, at: 0)  // 最新的在最前面

    // 可选：限制历史记录数量
    if editedImagesHistory.count > 50 {
      editedImagesHistory.removeLast()
    }
  }

  /// 从历史记录中选择图片进行编辑
  func selectImageForEditing(historyItem: ImageHistoryItem) {
    self.sourceImages = [historyItem.imageData]
    self.imageData = historyItem.imageData
    self.selectedMode = .edit
    // 可选：将提示文本复制到编辑框
    self.editInstruction = ""
  }

  /// 切换历史面板的可见性
  func toggleHistoryPanel() {
    isHistoryPanelVisible.toggle()
  }

  private func clearInputs() {
    generationPrompt = ""
    editInstruction = ""
    sourceImages = []
    imageData = nil
  }
}
