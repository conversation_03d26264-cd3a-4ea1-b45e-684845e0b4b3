import PhotosUI
import SwiftUI

// MARK: - Main ContentView
struct ContentView: View {
  // Use StateObject to manage the ViewModel's lifecycle
  @StateObject private var viewModel = ContentViewModel()
  @Environment(\.colorScheme) private var colorScheme

  var body: some View {
    NavigationView {
      ZStack(alignment: .bottom) {
        // 主内容区
        VStack(spacing: 0) {
          DemoMainContentView(
            selectedMode: $viewModel.selectedMode,
            viewModel: viewModel
          )
        }
        .background(Color(.systemGroupedBackground))

        // 动作按钮区
        ActionBarView(
          mode: viewModel.selectedMode,
          isLoading: $viewModel.isLoading,
          canPerformAction: viewModel.canPerformAction,
          resultImageData: $viewModel.imageData,
          isInputCardFocused: $viewModel.isInputCardFocused,
          performAction: viewModel.performPrimaryAction,
          saveAction: viewModel.saveImage,
          editAction: {
            if let imageData = viewModel.imageData {
              viewModel.switchToEditMode(withImage: imageData)
            }
          }
        )
        .padding(.bottom, 8)
      }
      .navigationTitle("AI Designer")
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .principal) {
          Text("AI Designer")
            .font(.system(size: 18, weight: .semibold))
        }

        ToolbarItem(placement: .navigationBarTrailing) {
          Button(action: viewModel.toggleHistoryPanel) {
            Image(systemName: "clock.arrow.circlepath")
              .foregroundColor(.primary)
          }
        }
      }
      .alert("Error", isPresented: $viewModel.showingErrorAlert) {
        Button("OK", role: .cancel) {}
      } message: {
        Text(viewModel.errorMessage)
      }
      .overlay {
        if viewModel.showingSaveConfirmation {
          ConfirmationBanner(message: "保存成功")
            .transition(.move(edge: .top).combined(with: .opacity))
            .animation(.spring(response: 0.3), value: viewModel.showingSaveConfirmation)
            .onAppear {
              DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                withAnimation {
                  viewModel.showingSaveConfirmation = false
                }
              }
            }
        }
      }
      .sheet(isPresented: $viewModel.isHistoryPanelVisible) {
        HistoryPanelView(viewModel: viewModel)
      }
    }
  }
}

// MARK: - History Panel View
struct HistoryPanelView: View {
  @ObservedObject var viewModel: ContentViewModel
  @State private var selectedTab = 0
  @Environment(\.dismiss) private var dismiss

  var body: some View {
    NavigationView {
      VStack {
        Picker("历史类型", selection: $selectedTab) {
          Text("生成历史").tag(0)
          Text("编辑历史").tag(1)
        }
        .pickerStyle(SegmentedPickerStyle())
        .padding()

        if selectedTab == 0 {
          // 生成历史
          if viewModel.generatedImagesHistory.isEmpty {
            EmptyHistoryView(message: "暂无生成历史")
          } else {
            ImageHistoryListView(
              historyItems: viewModel.generatedImagesHistory,
              onSelect: { item in
                viewModel.selectImageForEditing(historyItem: item)
                dismiss()
              }
            )
          }
        } else {
          // 编辑历史
          if viewModel.editedImagesHistory.isEmpty {
            EmptyHistoryView(message: "暂无编辑历史")
          } else {
            ImageHistoryListView(
              historyItems: viewModel.editedImagesHistory,
              onSelect: { item in
                viewModel.selectImageForEditing(historyItem: item)
                dismiss()
              }
            )
          }
        }
      }
      .navigationTitle("历史记录")
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("关闭") {
            dismiss()
          }
        }
      }
    }
  }
}

// MARK: - Empty History View
struct EmptyHistoryView: View {
  let message: String

  var body: some View {
    VStack(spacing: 16) {
      Image(systemName: "photo.on.rectangle.angled")
        .font(.system(size: 50))
        .foregroundColor(.secondary)

      Text(message)
        .font(.headline)
        .foregroundColor(.secondary)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(Color(.systemGroupedBackground))
  }
}

// MARK: - Image History List View
struct ImageHistoryListView: View {
  let historyItems: [ImageHistoryItem]
  let onSelect: (ImageHistoryItem) -> Void

  private let columns = [
    GridItem(.adaptive(minimum: 150), spacing: 12)
  ]

  var body: some View {
    ScrollView {
      LazyVGrid(columns: columns, spacing: 12) {
        ForEach(historyItems) { item in
          HistoryItemCell(item: item)
            .onTapGesture {
              onSelect(item)
            }
        }
      }
      .padding()
    }
  }
}

// MARK: - History Item Cell
struct HistoryItemCell: View {
  let item: ImageHistoryItem

  var body: some View {
    VStack(alignment: .leading, spacing: 6) {
      if let uiImage = UIImage(data: item.imageData) {
        Image(uiImage: uiImage)
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(height: 150)
          .cornerRadius(10)
          .clipped()
      } else {
        Rectangle()
          .fill(Color.gray.opacity(0.3))
          .frame(height: 150)
          .cornerRadius(10)
          .overlay(
            Image(systemName: "photo")
              .foregroundColor(.secondary)
          )
      }

      Text(item.prompt)
        .font(.caption)
        .lineLimit(1)
        .foregroundColor(.primary)

      Text(formatDate(item.timestamp))
        .font(.caption2)
        .foregroundColor(.secondary)
    }
    .padding(6)
    .background(
      RoundedRectangle(cornerRadius: 12)
        .fill(Color(.secondarySystemGroupedBackground))
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    )
  }

  private func formatDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateFormat = "MM-dd HH:mm"
    return formatter.string(from: date)
  }
}

// MARK: - Confirmation Banner
struct ConfirmationBanner: View {
  let message: String

  var body: some View {
    VStack {
      HStack(spacing: 10) {
        Image(systemName: "checkmark.circle.fill")
          .foregroundColor(.green)
          .font(.system(size: 18, weight: .semibold))

        Text(message)
          .font(.system(size: 15, weight: .medium))
          .foregroundColor(.primary)
      }
      .padding(.vertical, 10)
      .padding(.horizontal, 16)
      .background(
        RoundedRectangle(cornerRadius: 25)
          .fill(Color(.systemBackground))
          .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 4)
      )

      Spacer()
    }
    .padding(.top, 50)
  }
}

// MARK: - Preview Provider
struct ContentView_Previews: PreviewProvider {
  static var previews: some View {
    ContentView()
  }
}
