import PhotosUI
import SwiftUI

// MARK: - Main Content View
struct DemoMainContentView: View {

  @Binding var selectedMode: AppMode
  @ObservedObject var viewModel: ContentViewModel

  init(selectedMode: Binding<AppMode>, viewModel: ContentViewModel) {
    self._selectedMode = selectedMode
    self.viewModel = viewModel
    Logger.debug("DemoMainContentView initialized")
  }

  var body: some View {
    VStack(spacing: 0) {
      ModeSwitcher(selectedMode: $selectedMode)
        .padding(.horizontal)
        .padding(.bottom)

      ScrollView {
        VStack(spacing: 20) {
          if selectedMode == .generate {
            GenerateContentView(viewModel: viewModel)
          } else {
            EditContentView(viewModel: viewModel)
          }
        }
        .padding(.horizontal)
        .padding(.bottom, 130)
      }
    }
  }
}
