import SwiftUI

// MARK: - Mode Switcher
struct ModeSwitcher: View {
  @Binding var selectedMode: AppMode
  @Namespace private var animation

  var body: some View {
    HStack(spacing: 0) {
      ForEach(AppMode.allCases) { mode in
        ModeButton(
          mode: mode,
          isSelected: selectedMode == mode,
          namespace: animation
        ) {
          withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            selectedMode = mode
            #if os(iOS)
              UIImpactFeedbackGenerator(style: .light).impactOccurred()
            #endif
          }
        }
      }
    }
    .background(Color(.systemGray6))
    .clipShape(RoundedRectangle(cornerRadius: 12))
    .padding(.top, 8)
  }
}

struct ModeButton: View {
  let mode: AppMode
  let isSelected: Bool
  var namespace: Namespace.ID
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      HStack(spacing: 6) {
        Text(mode.rawValue)
          .font(.system(size: 15, weight: isSelected ? .bold : .medium))
          .foregroundColor(isSelected ? .primary : .secondary)
          .padding(.vertical, 10)
          .padding(.horizontal, 12)
      }
      .frame(maxWidth: .infinity)
      .background(
        ZStack {
          if isSelected {
            RoundedRectangle(cornerRadius: 10)
              .fill(Color(.systemBackground))
              .matchedGeometryEffect(id: "background", in: namespace)
              .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
          }
        }
      )
      .contentShape(Rectangle())
    }
    .buttonStyle(PlainButtonStyle())
    .accessibility(label: Text(mode.rawValue))
    .accessibility(hint: Text("Switch to \(mode.rawValue) mode"))
    .accessibility(addTraits: isSelected ? .isSelected : [])
  }
}
