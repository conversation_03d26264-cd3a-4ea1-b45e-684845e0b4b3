import SwiftUI

// MARK: - Simulated Network Service
struct SimulatedNetworkService {
  private let imageFilenames = [
    "gpt-response-1743843508851.png",
    "gpt-response-1743846981327.png",
    "gpt-response-1743847468080.png",
    "gpt-response-1743847790661.png",
    "gpt-response-1743849407296.png",
    "gpt-response-1743850116504.png",
    "gpt-response-1743851464349.png",
    "gpt-response-1743851897885.png",
    "gpt-response-1743852495328.png",
    "gpt-response-1743854196076.png",
    "gpt-response-1743854311331.png",
    "gpt-response-1743854898770.png",
    "gpt-response-1743855418813.jpeg",
    "gpt-response-1743866811625.png",
    "gpt-response-1743867180340.png",
    "gpt-response-1743867787077.png",
    "gpt-response-1743913581256.png",
    "gpt-response-1744047620319.png",
    "gpt-response-1744049880568.png",
    "gpt-response-1744896732792.png",
  ]
  private let baseUrl = "https://twitter-r2.a1d.ai/"

  func generateImage(prompt: String) async throws -> Data? {
    // Simulate network delay
    try await Task.sleep(nanoseconds: UInt64.random(in: 500_000_000...1_500_000_000))

    Logger.debug("Simulating generate for: \(prompt)")
    if prompt.lowercased().contains("error") {
      throw AppError.apiError("Simulated generation failure")
    }

    return await fetchRandomImageData()
  }

  func editImage(imageData: Data, instruction: String) async throws -> Data? {
    // Simulate network delay
    try await Task.sleep(nanoseconds: UInt64.random(in: 500_000_000...1_500_000_000))

    Logger.debug("Simulating edit with instruction: \(instruction)")
    if instruction.lowercased().contains("fail") {
      throw AppError.apiError("Simulated edit failure")
    }

    return await fetchRandomImageData()
  }

  private func fetchRandomImageData() async -> Data? {
    guard let randomFilename = imageFilenames.randomElement() else { return nil }
    let urlString = baseUrl + randomFilename
    guard let url = URL(string: urlString) else { return nil }
    do {
      let (data, _) = try await URLSession.shared.data(from: url)
      if UIImage(data: data) != nil {
        return data
      } else {
        return nil
      }
    } catch {
      // Log the error if fetching fails
      Logger.error("Failed to fetch placeholder image: \(error)")
      return nil
    }
  }
}
