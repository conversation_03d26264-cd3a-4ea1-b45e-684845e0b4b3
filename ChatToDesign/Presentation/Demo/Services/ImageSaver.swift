import Photos
import SwiftUI

// MARK: - Image Saving Helper
class ImageSaver: NSObject, ObservableObject {
  var onSuccess: (() -> Void)?
  var onError: ((Error) -> Void)?

  func writeToPhotoAlbum(image: UIImage) {
    PHPhotoLibrary.requestAuthorization(for: .addOnly) { [weak self] status in
      guard let self = self else { return }
      if status == .authorized {
        UIImageWriteToSavedPhotosAlbum(image, self, #selector(self.saveCompleted), nil)
      } else {
        DispatchQueue.main.async {
          self.onError?(
            NSError(
              domain: "PhotoLibrary", code: PHPhotoLibrary.authorizationStatus().rawValue,
              userInfo: [NSLocalizedDescriptionKey: "Photo library write permission required"]))
        }
      }
    }
  }

  @objc func saveCompleted(
    _ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer
  ) {
    DispatchQueue.main.async {
      if let error = error {
        self.onError?(error)
      } else {
        self.onSuccess?()
        #if os(iOS)
          UINotificationFeedbackGenerator().notificationOccurred(.success)
        #endif
      }
    }
  }
}
