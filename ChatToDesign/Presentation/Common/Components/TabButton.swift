//
//  TabButton.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

/// 专用于Tab切换的按钮组件
/// 用于替代原来的 DSButton.tab 样式
public struct TabButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    
    public init(title: String, isSelected: Bool, action: @escaping () -> Void) {
        self.title = title
        self.isSelected = isSelected
        self.action = action
    }
    
    public var body: some View {
        Button(action: action) {
            Text(title)
                .font(DesignSystem.Button.Typography.small)
                .foregroundColor(isSelected ? TabTokens.selectedTextColor : TabTokens.unselectedTextColor)
                .padding(.horizontal, DesignSystem.Button.Padding.small)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(isSelected ? TabTokens.selectedBackgroundColor : TabTokens.unselectedBackgroundColor)
                )
                .overlay(
                    Capsule()
                        .stroke(isSelected ? TabTokens.selectedBorderColor : Color.clear, lineWidth: 1)
                )
                .scaleEffect(isPressed ? 0.98 : 1.0)
                .animation(AnimationTokens.easingSpring, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(AnimationTokens.easingStandard) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

// MARK: - Design Tokens
extension TabButton {
    private struct TabTokens {
        static let selectedBackgroundColor = ColorPalette.gray800
        static let unselectedBackgroundColor = Color.clear
        static let selectedTextColor = ColorTokens.textInverse
        static let unselectedTextColor = ColorTokens.textTertiary
        static let selectedBorderColor = ColorTokens.textInverse
    }
}
