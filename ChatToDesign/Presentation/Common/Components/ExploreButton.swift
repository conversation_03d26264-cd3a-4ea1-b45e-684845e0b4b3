//
//  ExploreButton.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

/// 专用于探索功能的按钮组件
/// 用于替代原来的 DSButton.explore 样式
public struct ExploreButton: View {
    let title: String
    let action: () -> Void
    
    @State private var isPressed = false
    
    public init(title: String = "Create Similar", action: @escaping () -> Void) {
        self.title = title
        self.action = action
    }
    
    public var body: some View {
        Button(action: action) {
            Text(title)
                .font(.custom("Geist", size: 14).weight(.medium))
                .foregroundColor(ExploreTokens.textColor)
                .frame(height: DesignSystem.Button.Height.medium)
                .frame(maxWidth: .infinity)
                .background(ExploreTokens.backgroundColor)
                .cornerRadius(DesignSystem.Button.CornerRadius.medium)
                .scaleEffect(isPressed ? 0.98 : 1.0)
                .animation(AnimationTokens.easingSpring, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(AnimationTokens.easingStandard) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

// MARK: - Design Tokens
extension ExploreButton {
    private struct ExploreTokens {
        static let backgroundColor = ColorPalette.gray50
        static let textColor = ColorPalette.gray900
    }
}
