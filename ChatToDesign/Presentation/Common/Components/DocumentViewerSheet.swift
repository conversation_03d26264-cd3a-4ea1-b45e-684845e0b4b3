//
//  DocumentViewerSheet.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/20.
//

import SwiftUI

/// Full-screen document viewer sheet for legal documents
struct DocumentViewerSheet: View {
  let documentType: DocumentType
  @Environment(\.dismiss) private var dismiss
  @State private var isLoading = false
  @State private var error: DocumentError?
  @State private var showCloseButton = false

  var body: some View {
    NavigationView {
      ZStack {
        // Background
        Color(red: 0.035, green: 0.035, blue: 0.043)  // #09090b
          .ignoresSafeArea()

        if let error = error {
          errorView(error)
        } else {
          documentContentView
        }

        // Loading overlay
        if isLoading {
          loadingOverlay
        }

        // Close button with delay
        if showCloseButton {
          closeButtonOverlay
        }
      }
    }
    .navigationViewStyle(StackNavigationViewStyle())
    .onAppear {
      // Show close button after 2 seconds delay (following user preference)
      DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
        withAnimation(.easeInOut(duration: 0.3)) {
          showCloseButton = true
        }
      }
    }
  }

  // MARK: - Content Views

  private var documentContentView: some View {
    DocumentWebView(
      documentType: documentType,
      isLoading: $isLoading,
      error: $error
    )
    .navigationTitle(documentType.title)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarHidden(true)  // Hide default navigation bar for custom close button
  }

  private var loadingOverlay: some View {
    ZStack {
      Color.black.opacity(0.3)
        .ignoresSafeArea()

      VStack(spacing: 16) {
        ProgressView()
          .progressViewStyle(CircularProgressViewStyle(tint: .white))
          .scaleEffect(1.2)

        Text("Loading \(documentType.title)...")
          .font(.custom("Inter", size: 16))
          .foregroundColor(.white)
      }
      .padding(24)
      .background(
        RoundedRectangle(cornerRadius: 12)
          .fill(Color(red: 0.157, green: 0.157, blue: 0.188))  // #27272a
      )
    }
  }

  private var closeButtonOverlay: some View {
    VStack {
      HStack {
        Spacer()

        Button(action: {
          dismiss()
        }) {
          Image(systemName: "xmark")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.white)
            .frame(width: 32, height: 32)
            .background(
              Circle()
                .fill(Color.black.opacity(0.6))
            )
            .overlay(
              Circle()
                .stroke(Color.white.opacity(0.2), lineWidth: 1)
            )
        }
        .padding(.trailing, 20)
        .padding(.top, 20)
      }

      Spacer()
    }
  }

  private func errorView(_ error: DocumentError) -> some View {
    VStack(spacing: 24) {
      Image(systemName: "exclamationmark.triangle")
        .font(.system(size: 48))
        .foregroundColor(.orange)

      VStack(spacing: 12) {
        Text("Unable to Load Document")
          .font(.custom("Inter", size: 20).weight(.semibold))
          .foregroundColor(.white)

        Text(error.localizedDescription)
          .font(.custom("Inter", size: 16))
          .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
          .multilineTextAlignment(.center)
          .padding(.horizontal, 32)
      }

      VStack(spacing: 12) {
        Button(action: {
          // Retry loading
          self.error = nil
          self.isLoading = true
        }) {
          HStack {
            Image(systemName: "arrow.clockwise")
              .font(.system(size: 16))

            Text("Try Again")
              .font(.custom("Inter", size: 16).weight(.medium))
          }
          .foregroundColor(.white)
          .padding(.horizontal, 24)
          .padding(.vertical, 12)
          .background(
            RoundedRectangle(cornerRadius: 8)
              .fill(Color(red: 0.157, green: 0.157, blue: 0.188))  // #27272a
          )
        }

        Button(action: {
          dismiss()
        }) {
          Text("Close")
            .font(.custom("Inter", size: 16))
            .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
        }
      }
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(Color(red: 0.035, green: 0.035, blue: 0.043))  // #09090b
  }
}

// MARK: - Preview

#Preview {
  DocumentViewerSheet(documentType: .termsOfService)
}

#Preview("Privacy Policy") {
  DocumentViewerSheet(documentType: .privacyPolicy)
}

#Preview("Error State") {
  struct ErrorPreview: View {
    @State private var showSheet = true

    var body: some View {
      Color.black
        .fullScreenCover(isPresented: $showSheet) {
          DocumentViewerSheet(documentType: .termsOfService)
            .onAppear {
              // Simulate error for preview
              DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                // This would normally be set by the WebView
              }
            }
        }
    }
  }

  return ErrorPreview()
}
