//
//  SuggestionButton.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

public struct SuggestionButton: View {
    let text: String
    let action: () -> Void
    
    @State private var isPressed = false
    
    public init(text: String, action: @escaping () -> Void) {
        self.text = text
        self.action = action
    }
    
    public var body: some View {
        Button(action: action) {
            Text(text)
                .font(DesignSystem.Button.Typography.small)
                .foregroundColor(SuggestionTokens.textColor)
                .padding(.horizontal, DesignSystem.Button.Padding.small)
                .padding(.vertical, 8)
                .background(SuggestionTokens.backgroundColor)
                .cornerRadius(DesignSystem.Button.CornerRadius.small)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.Button.CornerRadius.small)
                        .stroke(SuggestionTokens.borderColor, lineWidth: 1)
                )
                .scaleEffect(isPressed ? 0.98 : 1.0)
                .animation(AnimationTokens.easingSpring, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(AnimationTokens.easingStandard) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

// MARK: - Design Tokens
extension SuggestionButton {
    private struct SuggestionTokens {
        static let backgroundColor = ColorPalette.gray800
        static let textColor = ColorTokens.textInverse
        static let borderColor = ColorPalette.gray750
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: Spacing.space8) {
        SuggestionButton(text: "A modern living room", action: {})
        SuggestionButton(text: "Cozy bedroom", action: {})
        SuggestionButton(text: "Minimalist kitchen", action: {})
    }
    .padding()
}