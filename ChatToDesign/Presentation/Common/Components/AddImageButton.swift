//
//  AddImageButton.swift
//  ChatToDesign
//
//  Created by DesignSystem
//

import SwiftUI

/// 专用于添加图片的按钮组件
/// 用于替代原来的 DSButton.addImage 工厂方法
public struct AddImageButton: View {
    let title: String
    let height: CGFloat
    let action: () -> Void
    
    @State private var isPressed = false
    
    public init(title: String = "Add Image", height: CGFloat = 100, action: @escaping () -> Void) {
        self.title = title
        self.height = height
        self.action = action
    }
    
    public var body: some View {
        Button(action: action) {
            VStack(spacing: Spacing.space8) {
                Image(systemName: "plus")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(AddImageTokens.iconColor)
                
                Text(title)
                    .font(Typography.labelSmall)
                    .foregroundColor(AddImageTokens.textColor)
            }
            .frame(height: height)
            .frame(maxWidth: .infinity)
            .background(AddImageTokens.backgroundColor)
            .cornerRadius(DesignSystem.Button.CornerRadius.large)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Button.CornerRadius.large)
                    .strokeBorder(
                        AddImageTokens.borderColor,
                        style: StrokeStyle(lineWidth: 1, dash: [5])
                    )
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(AnimationTokens.easingSpring, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(AnimationTokens.easingStandard) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

// MARK: - Design Tokens
extension AddImageButton {
    private struct AddImageTokens {
        static let backgroundColor = ColorPalette.gray800
        static let textColor = ColorTokens.textTertiary
        static let iconColor = ColorTokens.textTertiary
        static let borderColor = ColorPalette.gray750
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: Spacing.space16) {
        AddImageButton(title: "Add Image", height: 80, action: {})
        AddImageButton(title: "Upload Photo", height: 100, action: {})
        AddImageButton(title: "Select Image", height: 120, action: {})
    }
    .padding()
}