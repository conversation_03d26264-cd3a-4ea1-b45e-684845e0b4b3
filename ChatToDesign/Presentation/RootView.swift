// //
// //  RootView.swift
// //  ChatToDesign
// //
// //  Created by <PERSON><PERSON><PERSON> on 2025/3/14.
// //

// // RootView.swift
import Combine
import SwiftUI

/// 应用根视图
/// 负责处理认证状态切换和主内容展示
struct RootView: View {
  // MARK: - 状态

  /// 认证视图模型
  @StateObject private var authViewModel = AuthViewModel()

  /// 主页视图模型 - 需要在这里创建以便在Logo页面中使用
  @StateObject private var homePageViewModel = HomePageViewModel()

  /// 认证状态
  @State private var isAuthenticated = false

  /// 取消令牌集合
  @State private var cancellables = Set<AnyCancellable>()

  // MARK: - 视图

  var body: some View {
    Group {
      if isAuthenticated {
        // 显示主要的 Tab 导航界面
        MainTabView()
      } else {
        LoginView()
          .environmentObject(authViewModel)
      }
    }
    .onAppear {
      // 监听认证状态变化
      setupAuthObserver()
    }
  }

  // MARK: - 私有方法

  /// 设置认证状态观察者
  private func setupAuthObserver() {

    AppDependencyContainer.shared.authModule.authService.authStatePublisher
      .receive(on: DispatchQueue.main)
      .sink { state in
        switch state {
        case .authenticated:
          self.isAuthenticated = true
        case .unauthenticated, .error:
          self.isAuthenticated = false
        case .authenticating:
          // 保持当前状态，等待认证完成
          break
        }
      }
      .store(in: &cancellables)
  }
}
