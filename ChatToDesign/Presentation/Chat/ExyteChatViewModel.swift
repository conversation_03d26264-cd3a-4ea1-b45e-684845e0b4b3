import Combine
import <PERSON><PERSON><PERSON>hat
import Foundation
import GiphyUISDK
import SwiftUI

public typealias ExyteChatUser = ExyteChat.User
public typealias ExyteChatMessage = ExyteChat.Message
public typealias ExyteChatRecording = ExyteChat.Recording
public typealias ExyteChatDraftMessage = ExyteChat.DraftMessage
public typealias ExyteChatReplyMessage = ExyteChat.ReplyMessage

/// ExyteChatViewModel - 将项目域模型转换为Exyte Chat库所需的模型
public class ExyteChatViewModel: ObservableObject {
  // MARK: - Published Properties

  /// Exyte 消息列表
  @Published var exyteMessages: [ExyteChatMessage] = []

  /// 是否正在生成回复
  @Published var isGenerating: Bool = false

  /// 是否显示错误
  @Published var showError: Bool = false

  /// 错误信息
  @Published var errorMessage: String?

  // MARK: - Private Properties

  private let chatId: String
  private let userId: String

  private let chatService: ChatService
  private let userService: UserService
  private let uploadService: UploadService
  private let currentUser: ExyteChatUser
  private let aiUser: ExyteChatUser

  // 订阅存储
  private var cancellables: Set<AnyCancellable> = []

  // MARK: - Initialization

  /// 初始化聊天视图模型
  /// - Parameters:
  ///   - chatId: 聊天ID
  ///   - userId: 用户ID
  ///   - chatService: 聊天服务
  public init(
    chatId: String,
    userId: String,
    chatService: ChatService = AppDependencyContainer.shared.chatModule.chatService,
    userService: UserService = AppDependencyContainer.shared.userModule.userService,
    uploadService: UploadService = AppDependencyContainer.shared.storageModule.uploadService
  ) {
    self.chatId = chatId
    self.userId = userId
    self.chatService = chatService
    self.userService = userService
    self.uploadService = uploadService
    // 创建当前用户
    self.currentUser = ExyteChatUser(
      id: userId,
      name: userService.currentUser?.displayName ?? "",
      avatarURL: userService.currentUser?.photoURL,
      isCurrentUser: userService.currentUser?.id == userId
    )

    // 创建AI用户
    self.aiUser = ExyteChatUser(
      id: "ai",
      name: "AI助手",
      avatarURL: nil,
      isCurrentUser: false
    )

    // 设置观察者
    setupObservers()
  }

  // MARK: - Public Methods

  /// 发送消息
  /// - Parameter draft: 草稿消息
  public func sendMessage(draft: DraftMessage) {
    Task {
      do {
        //
        let id = UUID().uuidString
        ///WARNING: 这里直接Make Message 是为了用户体验，因为ExyteChatMessage.makeMessage 读取的是本地的附件资源， 等附件资源上传到Firebase Storage后，再更新ExyteMessage
        let exyteMessage = await ExyteChatMessage.makeMessage(
          id: id, user: currentUser, status: .sending, draft: draft)

        // 添加到界面显示
        await MainActor.run {
          exyteMessages.append(exyteMessage)
        }

        // 转换为领域消息
        var domainMessage = createDomainMessage(from: exyteMessage)

        // 上传附件（如果有）
        if domainMessage.hasMedia {
          // 这里应该显示上传进度
          domainMessage = try await uploadMessageAttachments(for: domainMessage)
        }
        ///TODO:  WARNING 这里如果有图片资源的话 一定要上传成功才能发送消息
        //                Logger.debug("上传附件成功: \(domainMessage)")

        // 发送用户消息
        let userMessage = try await chatService.sendMessage(message: domainMessage)

        // 获取聊天对象，用于获取系统提示词
        let chat = try await chatService.getChat(id: chatId)

        // 获取AI回复
        await MainActor.run {
          self.isGenerating = true
        }

        _ = try await chatService.getAIReply(
          forMessage: userMessage,
          chatId: chatId,
          systemPrompt: chat.parameters?.systemPrompt
        )

        await MainActor.run {
          self.isGenerating = false
        }
      } catch {
        await handleError(error)
      }
    }
  }

  /// 停止生成
  public func stopGeneration() {
    Task {
      do {
        try await chatService.stopGeneration(chatId: chatId)

        await MainActor.run {
          self.isGenerating = false
        }
      } catch {
        await handleError(error)
      }
    }
  }

  // MARK: - Private Methods

  /// 设置观察者
  private func setupObservers() {
    // 观察消息
    chatService.observeMessages(chatId: chatId)
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          if case .failure(let error) = completion {
            Task { [weak self] in
              await self?.handleError(error)
            }
          }
        },
        receiveValue: { [weak self] domainMessages in
          self?.updateExyteMessages(from: domainMessages)
        }
      )
      .store(in: &cancellables)
  }

  /// 从域模型消息更新Exyte消息
  /// - Parameter domainMessages: 域模型消息列表
  private func updateExyteMessages(from domainMessages: [Message]) {
    // 将从 Firestore 获取的消息（按时间戳降序）反转为升序，以适应 ChatView 的 conversation 类型
    let sortedMessages = domainMessages.sorted { $0.timestamp.created < $1.timestamp.created }

    let exyteMsgs = sortedMessages.map { domainMessage -> ExyteChatMessage in
      // 确定用户
      let user = domainMessage.type == .user ? currentUser : aiUser

      // 确定状态
      let status: ExyteChatMessage.Status? = mapMessageStatus(domainMessage.status)

      // 创建附件
      let attachments = createAttachments(from: domainMessage.content.attachments)

      // 创建消息
      return ExyteChatMessage(
        id: domainMessage.id,
        user: user,
        status: status,
        createdAt: domainMessage.timestamp.created,
        text: domainMessage.content.text,
        attachments: attachments
      )
    }

    self.exyteMessages = exyteMsgs
  }

  /// 创建 Domain Message
  /// - Parameter exyteMessage: Exyte消息
  /// - Returns: 域模型消息
  private func createDomainMessage(from exyteMessage: ExyteChatMessage) -> Message {
    // 转换附件
    var mediaAttachments: [MediaAttachment]? = nil

    if !exyteMessage.attachments.isEmpty {
      mediaAttachments = exyteMessage.attachments.compactMap { attachment -> MediaAttachment? in

        // 创建MediaAttachment
        return MediaAttachment(
          id: attachment.id,
          type: attachment.type == .image ? .image : .video,
          url: attachment.full,
          thumbnailUrl: attachment.thumbnail,
          filename: "attachment_\(attachment.id).\(attachment.type == .image ? "jpg" : "mp4")",
          fileSize: 0,  // 需要实际获取
          mimeType: attachment.type == .image ? "image/jpeg" : "video/mp4",
          isUploaded: false,
          uploadProgress: 0.0
        )
      }
    }

    // 创建域模型消息
    return Message(
      id: exyteMessage.id,
      chatId: chatId,
      sender: .user(userId),
      type: .user,
      status: .sending,
      content: MessageContent(
        text: exyteMessage.text,
        styleType: .plainText,
        attachments: mediaAttachments
      ),
      replyMessageId: exyteMessage.replyMessage?.id,
      metadata: nil,
      timestamp: Message.Timestamp(
        created: exyteMessage.createdAt, updated: exyteMessage.createdAt),
      isRead: true
    )
  }

  /// 创建ExyteMessage
  /// - Parameter domainMessage: 域模型消息
  /// - Returns: Exyte消息
  private func createExyteMessage(from domainMessage: Message) -> ExyteChatMessage {
    // 确定用户
    let user = domainMessage.type == .user ? currentUser : aiUser

    // 确定状态
    let status: ExyteChatMessage.Status? = mapMessageStatus(domainMessage.status)

    // 创建附件
    let attachments = createAttachments(from: domainMessage.content.attachments)

    // 创建回复消息
    let replyDraftMessage: ExyteChatMessage? = exyteMessages.first(where: {
      $0.id == domainMessage.replyMessageId
    })
    let replyMessage =
      replyDraftMessage != nil
      ? ExyteChatReplyMessage(
        id: replyDraftMessage?.id ?? "",
        user: user,
        createdAt: replyDraftMessage?.createdAt ?? Date(),
        text: replyDraftMessage?.text ?? "",
        attachments: replyDraftMessage?.attachments ?? []
      ) : nil

    // 创建消息
    return ExyteChatMessage(
      id: domainMessage.id,
      user: user,
      status: status,
      createdAt: domainMessage.timestamp.created,
      text: domainMessage.content.text,
      attachments: attachments,
      replyMessage: replyMessage
    )
  }

  /// 上传消息附件
  /// - Parameter message: 含有附件的消息
  /// - Returns: 更新后的消息（附件已上传）
  private func uploadMessageAttachments(for message: Message) async throws -> Message {
    guard let attachments = message.content.attachments, !attachments.isEmpty else {
      return message
    }

    var updatedMessage = message
    var updatedAttachments: [MediaAttachment] = []

    for attachment in attachments {
      // 1. 检查是否需要上传
      if attachment.isUploaded {
        updatedAttachments.append(attachment)
        continue
      }
      Logger.debug("附件: \(attachment)")
      do {
        // 2. 获取本地文件数据
        let fileData = try Data(contentsOf: attachment.url)

        // 3. 上传到Storage
        let uploadResult = try await uploadService.uploadMedia(
          data: fileData,
          fileName: attachment.filename,
          mimeType: attachment.mimeType,
          chatId: message.chatId
        )

        Logger.debug("viewModel上传结果: \(uploadResult)")

        // 4. 创建已上传的附件
        let uploadedAttachment = MediaAttachment(
          id: attachment.id,
          type: attachment.type,
          url: uploadResult.remoteURL,
          thumbnailUrl: uploadResult.thumbnailURL ?? attachment.thumbnailUrl,
          filename: attachment.filename,
          fileSize: uploadResult.fileSize,
          mimeType: attachment.mimeType,
          isUploaded: true,
          uploadProgress: 1.0
        )

        Logger.debug("已上传附件: \(uploadedAttachment)")

        updatedAttachments.append(uploadedAttachment)

      } catch {
        // 上传失败，保留原始附件但标记错误
        let failedAttachment = MediaAttachment(
          id: attachment.id,
          type: attachment.type,
          url: attachment.url,
          thumbnailUrl: attachment.thumbnailUrl,
          filename: attachment.filename,
          fileSize: attachment.fileSize,
          mimeType: attachment.mimeType,
          isUploaded: false,
          uploadProgress: -1
        )
        updatedAttachments.append(failedAttachment)
        // 在实际应用中，应该记录错误并通知用户
        Logger.error("无法上传附件: \(error.localizedDescription)")
      }
    }

    // 更新消息的附件
    var updatedContent = message.content
    updatedContent.attachments = updatedAttachments
    updatedMessage.content = updatedContent

    return updatedMessage
  }

  /// 映射消息状态
  /// - Parameter status: 域模型消息状态
  /// - Returns: Exyte消息状态
  private func mapMessageStatus(_ status: MessageStatus) -> ExyteChatMessage.Status? {
    switch status {
    case .sending:
      return .sending
    case .sent, .received:
      return .sent
    case .receiving:
      return .sending
    case .error:
      return nil  // Exyte不直接支持错误状态，需要特殊处理
    case .deleted:
      return nil
    }
  }

  /// 从域模型附件创建Exyte附件
  /// - Parameter mediaAttachments: 域模型媒体附件
  /// - Returns: Exyte附件数组
  private func createAttachments(from mediaAttachments: [MediaAttachment]?) -> [Attachment] {
    guard let attachments = mediaAttachments else { return [] }

    return attachments.compactMap { media -> Attachment? in
      switch media.type {
      case .image:
        return Attachment(id: media.id, url: media.url, type: .image)
      case .video:
        return Attachment(
          id: media.id,
          thumbnail: media.thumbnailUrl ?? media.url,
          full: media.url,
          type: .video
        )
      case .audio:
        return nil  // Exyte暂不直接支持音频附件
      case .other:
        return nil
      }
    }
  }

  /// 处理错误
  /// - Parameter error: 错误
  @MainActor
  private func handleError(_ error: Error) {
    self.isGenerating = false
    self.errorMessage = error.localizedDescription
    self.showError = true
  }
}
