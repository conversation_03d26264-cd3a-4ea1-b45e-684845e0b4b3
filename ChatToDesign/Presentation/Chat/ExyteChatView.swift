import SwiftUI
import ExyteChat

struct ExyteChatView: View {
    @StateObject private var viewModel: ExyteChatViewModel
    @Environment(\.presentationMode) var presentationMode
    
    private let chatId: String
    private let userId: String

    init(chatId: String, userId: String) {
        self.chatId = chatId
        self.userId = userId
        _viewModel = StateObject(
            wrappedValue: ExyteChatViewModel(
                chatId: chatId,
                userId: userId
            )
        )
    }

    var body: some View {
        ZStack {
            // Exyte聊天视图
            ChatView(
                messages: viewModel.exyteMessages,
                chatType: .conversation
            ) { draft in
                viewModel.sendMessage(draft: draft)
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text("聊天")
                        .font(.headline)
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    Button {
                        presentationMode.wrappedValue.dismiss()
                    } label: {
                        Image(systemName: "chevron.left")
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button {
                            // TODO: 分享聊天
                        } label: {
                            Label("分享", systemImage: "square.and.arrow.up")
                        }
                        
                        Button {
                            // TODO: 导出聊天
                        } label: {
                            Label("导出", systemImage: "arrow.down.doc")
                        }
                        
                        Button {
                            // TODO: 分叉聊天
                        } label: {
                            Label("创建副本", systemImage: "doc.on.doc")
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            
            // 生成中覆盖层
            if viewModel.isGenerating {
                VStack {
                    Spacer()
                    
                    HStack {
                        ProgressView()
                            .padding(.leading)
                        
                        Text("AI 正在生成回复...")
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Button("停止") {
                            viewModel.stopGeneration()
                        }
                        .foregroundColor(.red)
                        .padding(.trailing)
                    }
                    .padding(.vertical, 8)
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                    .padding(.horizontal)
                    .padding(.bottom, 60) // 防止被输入框遮挡
                }
            }
        }
        .navigationBarBackButtonHidden(true)
        .alert("错误", isPresented: $viewModel.showError) {
            Button("确定", role: .cancel) {}
        } message: {
            Text(viewModel.errorMessage ?? "未知错误")
        }
    }
}

#Preview {
    NavigationView {
        ExyteChatView(chatId: "preview-chat-id", userId: "preview-user-id")
    }
} 