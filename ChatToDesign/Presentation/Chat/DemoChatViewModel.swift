//// ChatViewModel.swift
//// ChatToDesign
////
//// Created by <PERSON><PERSON><PERSON> on 2025/3/18.
////
//
//import Foundation
//import Combine
//import SwiftUI
//
///// UI 层消息类型
//public struct UIMessage: Identifiable, Equatable {
//    public var id: String
//    public var isUser: Bool
//    public var text: String
//    public var timestamp: Date
//    public var status: MessageStatus
//    public var mediaURLs: [URL]?
//    
//    public init(
//        id: String,
//        isUser: Bool,
//        text: String,
//        timestamp: Date,
//        status: MessageStatus,
//        mediaURLs: [URL]? = nil
//    ) {
//        self.id = id
//        self.isUser = isUser
//        self.text = text
//        self.timestamp = timestamp
//        self.status = status
//        self.mediaURLs = mediaURLs
//    }
//    
//    /// 从领域消息创建UI消息
//    static func fromDomain(_ message: Message) -> UIMessage {
//        let mediaURLs = message.content.attachments?.compactMap { $0.url }
//        
//        return UIMessage(
//            id: message.id,
//            isUser: message.type == .user,
//            text: message.content.text,
//            timestamp: message.timestamp.created,
//            status: message.status,
//            mediaURLs: mediaURLs
//        )
//    }
//    
//    public static func == (lhs: UIMessage, rhs: UIMessage) -> Bool {
//        return lhs.id == rhs.id &&
//               lhs.isUser == rhs.isUser &&
//               lhs.text == rhs.text &&
//               lhs.timestamp == rhs.timestamp &&
//               lhs.status == rhs.status
//    }
//}
//
//public class DemoChatViewModel: ObservableObject {
//    // MARK: - Published Properties
//    
//    /// 当前聊天
//    @Published var chat: Chat?
//    
//    /// 消息列表
//    @Published var messages: [UIMessage] = []
//    
//    /// 是否正在生成回复
//    @Published var isGenerating: Bool = false
//    
//    /// 是否显示错误
//    @Published var showError: Bool = false
//    
//    /// 错误信息
//    @Published var errorMessage: String?
//    
//    /// 正在输入的消息
//    @Published var inputText: String = ""
//    
//    // MARK: - Private Properties
//    
//    private let chatId: String
//    private let userId: String
//    
//    private let chatService: ChatService
//
//    // 订阅存储
//    private var cancellables: Set<AnyCancellable> = []
//    
//    // MARK: - Initialization
//    
//    /// 初始化聊天视图模型
//    /// - Parameters:
//    ///   - chatId: 聊天ID
//    ///   - userId: 用户ID
//    ///   - useCaseFactory: 用例工厂
//    public init(
//        chatId: String,
//        userId: String,
//        chatService: ChatService = AppDependencyContainer.shared.chatModule.chatService
//    ) {
//        self.chatId = chatId
//        self.userId = userId
//        self.chatService = chatService
//        
//        // 开始观察
//        setupObservers()
//        loadInitialData()
//    }
//    
//    // MARK: - Public Methods
//    
//    /// 发送消息
//    public func sendMessage() {
//        let content = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
//        
//        guard !content.isEmpty else {
//            return
//        }
//        
//        Task {
//            do {
//                // 发送用户消息
//                let userMessage = try await chatService.sendMessage(
//                    content: content,
//                    chatId: chatId,
//                    userId: userId
//                )
//                
//                await MainActor.run {
//                    // 清空输入框
//                    self.inputText = ""
//                }
//                
//                // 获取AI回复
//                await MainActor.run {
//                    self.isGenerating = true
//                }
//                
//                _ = try await chatService.getAIReply(
//                    forMessage: userMessage,
//                    chatId: chatId,
//                    systemPrompt: chat?.parameters?.systemPrompt
//                )
//                
//                await MainActor.run {
//                    self.isGenerating = false
//                }
//            } catch {
//                await handleError(error)
//            }
//        }
//    }
//    
//    /// 停止生成
//    public func stopGeneration() {
//        Task {
//            do {
//                try await chatService.stopGeneration(chatId: chatId)
//                
//                await MainActor.run {
//                    self.isGenerating = false
//                }
//            } catch {
//                await handleError(error)
//            }
//        }
//    }
//    
//    // MARK: - Private Methods
//    
//    /// 设置观察者
//    private func setupObservers() {
//        // 观察聊天
//        chatService.observeChat(id: chatId)
//            .receive(on: DispatchQueue.main)
//            .sink(
//                receiveCompletion: { [weak self] completion in
//                    if case .failure(let error) = completion {
//                        Task { [weak self] in
//                            await self?.handleError(error)
//                        }
//                    }
//                },
//                receiveValue: { [weak self] chat in
//                    self?.chat = chat
//                }
//            )
//            .store(in: &cancellables)
//        
//        // 观察消息
//        chatService.observeMessages(chatId: chatId)
//            .receive(on: DispatchQueue.main)
//            .sink(
//                receiveCompletion: { [weak self] completion in
//                    if case .failure(let error) = completion {
//                        Task { [weak self] in
//                            await self?.handleError(error)
//                        }
//                    }
//                },
//                receiveValue: { [weak self] domainMessages in
//                    self?.messages = domainMessages.map { UIMessage.fromDomain($0) }
//                }
//            )
//            .store(in: &cancellables)
//    }
//    
//    /// 加载初始数据
//    private func loadInitialData() {
//        Task {
//            do {
//                let chat = try await chatService.getChat(id: chatId)
//                
//                await MainActor.run {
//                    self.chat = chat
//                }
//            } catch {
//                await handleError(error)
//            }
//        }
//    }
//    
//    /// 处理错误
//    /// - Parameter error: 错误
//    @MainActor
//    private func handleError(_ error: Error) {
//        self.isGenerating = false
//        self.errorMessage = error.localizedDescription
//        self.showError = true
//    }
//}
