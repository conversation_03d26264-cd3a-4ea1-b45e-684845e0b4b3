//// ChatView.swift
//// ChatToDesign
////
//// Created by <PERSON><PERSON><PERSON> on 2025/3/16.
////
//
//import SwiftUI
//
//struct DemoChatView: View {
//    @StateObject private var viewModel: DemoChatViewModel
//    @Environment(\.presentationMode) var presentationMode
//
//    init(chatId: String, userId: String) {
//        _viewModel = StateObject(
//            wrappedValue: DemoChatViewModel(
//                chatId: chatId,
//                userId: userId
//            )
//        )
//    }
//
//    var body: some View {
//        VStack(spacing: 0) {
//            // 消息列表
//            ScrollViewReader { scrollProxy in
//                ScrollView {
//                    LazyVStack(spacing: 12) {
//                        ForEach(viewModel.messages) { message in
//                            MessageView(message: message)
//                                .id(message.id)
//                        }
//                    }
//                    .padding(.horizontal)
//                    .padding(.top, 8)
//                }
//                .onChange(of: viewModel.messages.count) { _ in
//                    if let lastMessage = viewModel.messages.last {
//                        withAnimation {
//                            scrollProxy.scrollTo(lastMessage.id, anchor: .bottom)
//                        }
//                    }
//                }
//            }
//            
//            // 输入区域
//            VStack(spacing: 8) {
//                if viewModel.isGenerating {
//                    HStack {
//                        ProgressView()
//                            .padding(.leading)
//                        
//                        Text("AI 正在生成回复...")
//                            .foregroundColor(.secondary)
//                        
//                        Spacer()
//                        
//                        Button("停止") {
//                            viewModel.stopGeneration()
//                        }
//                        .foregroundColor(.red)
//                        .padding(.trailing)
//                    }
//                    .padding(.vertical, 8)
//                    .background(Color(.systemGray6))
//                    .cornerRadius(8)
//                    .padding(.horizontal)
//                }
//                
//                HStack {
//                    TextField("输入消息...", text: $viewModel.inputText)
//                        .padding(10)
//                        .background(Color(.systemGray6))
//                        .cornerRadius(20)
//                        .padding(.leading)
//                        .disabled(viewModel.isGenerating)
//                    
//                    Button(action: {
//                        viewModel.sendMessage()
//                    }) {
//                        Image(systemName: "arrow.up.circle.fill")
//                            .font(.system(size: 28))
//                            .foregroundColor(.blue)
//                    }
//                    .disabled(viewModel.inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || viewModel.isGenerating)
//                    .padding(.trailing)
//                }
//            }
//            .padding(.vertical, 8)
//            .background(Color(.systemBackground))
//            .overlay(
//                Rectangle()
//                    .frame(height: 0.5)
//                    .foregroundColor(Color(.systemGray4)),
//                alignment: .top
//            )
//        }
//        .navigationBarTitle(Text(viewModel.chat?.title ?? "聊天"), displayMode: .inline)
//        .navigationBarBackButtonHidden(true)
//        .toolbar {
//            ToolbarItem(placement: .navigationBarLeading) {
//                Button {
//                    presentationMode.wrappedValue.dismiss()
//                } label: {
//                    Image(systemName: "chevron.left")
//                }
//            }
//            
//            ToolbarItem(placement: .navigationBarTrailing) {
//                Menu {
//                    Button {
//                        // TODO: 分享聊天
//                    } label: {
//                        Label("分享", systemImage: "square.and.arrow.up")
//                    }
//                    
//                    Button {
//                        // TODO: 导出聊天
//                    } label: {
//                        Label("导出", systemImage: "arrow.down.doc")
//                    }
//                    
//                    Button {
//                        // TODO: 分叉聊天
//                    } label: {
//                        Label("创建副本", systemImage: "doc.on.doc")
//                    }
//                } label: {
//                    Image(systemName: "ellipsis.circle")
//                }
//            }
//        }
//        .alert("错误", isPresented: $viewModel.showError) {
//            Button("确定", role: .cancel) {}
//        } message: {
//            Text(viewModel.errorMessage ?? "未知错误")
//        }
//    }
//}
//
//// 消息气泡视图
//struct MessageView: View {
//    let message: UIMessage
//    
//    var body: some View {
//        HStack {
//            if message.isUser {
//                Spacer()
//                VStack(alignment: .trailing, spacing: 4) {
//                    Text(message.text)
//                        .padding(12)
//                        .background(Color.blue)
//                        .foregroundColor(.white)
//                        .clipShape(RoundedRectangle(cornerRadius: 16))
//                    
//                    if message.status == .sending {
//                        Text("发送中...")
//                            .font(.caption)
//                            .foregroundColor(.secondary)
//                    } else if message.status == .error {
//                        Text("发送失败")
//                            .font(.caption)
//                            .foregroundColor(.red)
//                    }
//                }
//            } else {
//                VStack(alignment: .leading, spacing: 4) {
//                    Text(message.text)
//                        .padding(12)
//                        .background(Color(.systemGray6))
//                        .foregroundColor(.primary)
//                        .clipShape(RoundedRectangle(cornerRadius: 16))
//                    
//                    if message.status == .receiving {
//                        Text("接收中...")
//                            .font(.caption)
//                            .foregroundColor(.secondary)
//                    }
//                }
//                Spacer()
//            }
//        }
//        .padding(.vertical, 4)
//    }
//}
