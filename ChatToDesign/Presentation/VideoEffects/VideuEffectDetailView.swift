//
//  VideuEffectDetailView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import AVKit
import Kingfisher
import Swift<PERSON>

struct VideuEffectDetailView: View {
  let videoEffect: VideoEffect
  @State private var selectedImageUrls: [String] = []
  @State private var showAdvancedSettings = false
  @Environment(\.dismiss) private var dismiss

  // Video generation handler
  @StateObject private var videoGenerationHandler: VideoGenerationHandler = {
    let container = AppDependencyContainer.shared
    return VideoGenerationHandler(
      videoGenerationService: container.videoModule.videoGenerationService,
      assetService: container.assetModule.assetService
    )
  }()

  var body: some View {
    ZStack {
      Color.black.ignoresSafeArea()

      ScrollView {
        VStack(spacing: 24) {
          // 1. 效果预览区域
          EffectPreviewSection(videoEffect: videoEffect)

          // 2. 图片选择区域
          ImageSelectionSection(selectedImageUrls: $selectedImageUrls)

          // 3. Try sample images
          SampleImagesSection(onImageSelected: handleSampleImageSelection)

          // 4. Advanced Settings
          AdvancedSettingsSection(
            isExpanded: $showAdvancedSettings,
            videoEffect: videoEffect
          )

          Spacer(minLength: 100)  // 为底部按钮留空间
        }
        .padding(.horizontal, 16)
      }
    }
    .navigationTitle(videoEffect.name)
    .navigationBarTitleDisplayMode(.inline)
    .toolbarColorScheme(.dark, for: .navigationBar)
    .alert("Error", isPresented: .constant(videoGenerationHandler.errorMessage != nil)) {
      Button("OK") {
        // 清除错误消息的逻辑可以在这里添加
      }
    } message: {
      Text(videoGenerationHandler.errorMessage ?? "")
    }
    .alert("Success", isPresented: .constant(videoGenerationHandler.generatedVideoUrls != nil)) {
      Button("OK") {
        dismiss()
      }
    } message: {
      Text("Video generation completed successfully!")
    }
    .overlay(alignment: .bottom) {
      // 5. 底部 Create 按钮
      CreateButton(
        creditCost: String(videoEffect.imageCount * 4),
        onCreateTapped: handleCreate,
        state: createButtonState
      )
    }
  }

  // MARK: - Computed Properties

  private var createButtonState: CreateButtonState {
    if !videoGenerationHandler.isLoading && videoGenerationHandler.currentTaskId == nil {
      // 初始状态：还没有开始任何操作
      return .create
    } else if videoGenerationHandler.isLoading && !videoGenerationHandler.taskCreatedSuccessfully {
      // 正在创建任务：已开始操作但任务还未创建成功
      return .creatingTask
    } else if videoGenerationHandler.taskCreatedSuccessfully
      && videoGenerationHandler.generatedVideoUrls == nil
    {
      // 任务创建成功，正在生成视频
      return .generating
    } else {
      // 其他情况，默认为初始状态
      return .create
    }
  }

  private func handleSampleImageSelection(_ imageUrl: String) {
    // 将示例图片URL添加到选择列表
    if selectedImageUrls.count < 2 {
      selectedImageUrls.append(imageUrl)
    } else {
      // 如果已经有2张图片，替换第一张
      selectedImageUrls[0] = imageUrl
    }
  }

  private func handleCreate() {
    Logger.info("Create button tapped")

    // 检查是否有选中的图片URL
    guard !selectedImageUrls.isEmpty else {
      Logger.warning("No images selected for video generation")
      // TODO: 显示错误提示
      return
    }

    // 获取模板和提示词
    let template = videoEffect.template ?? "default"
    let prompt = videoEffect.prompt ?? "Generate video effect"

    // 使用 Handler 的方法开始视频生成
    videoGenerationHandler.startGenerationWithImageUrls(
      prompt: prompt,
      imageUrls: selectedImageUrls,
      template: template,
      chatId: nil
    )
  }
}

// MARK: - Effect Preview Section

struct EffectPreviewSection: View {
  let videoEffect: VideoEffect

  @State private var isVideoReady = false
  @State private var player: AVPlayer?
  @State private var isVisible = false
  @State private var statusObserver: NSKeyValueObservation?
  @State private var isVideoCached = false

  var body: some View {
    ZStack {
      // Show poster only if video is not cached or not ready
      if !isVideoCached || !isVideoReady {
        posterImageView
      }

      // Video player (shown when ready)
      if isVideoReady, let player = player {
        VideoPlayer(player: player)
          .allowsHitTesting(false)  // Prevent video player from intercepting taps
          .transition(.opacity)
      }
    }
    .frame(height: 300)
    .clipShape(RoundedRectangle(cornerRadius: 12))
    .onAppear {
      isVisible = true
      loadVideoIfNeeded()
    }
    .onDisappear {
      isVisible = false
      cleanupVideo()
    }
  }

  // MARK: - Poster Image View

  @ViewBuilder
  private var posterImageView: some View {
    if let posterURL = URL(string: videoEffect.posterUrl) {
      KFImage(posterURL)
        .placeholder {
          Rectangle()
            .fill(Color.gray.opacity(0.3))
            .overlay {
              VStack(spacing: 8) {
                Image(systemName: "play.circle")
                  .font(.title)
                  .foregroundColor(.white)
                Text("Loading...")
                  .font(.caption)
                  .foregroundColor(.white)
              }
            }
        }
        .onFailure { error in
          Logger.error("Failed to load video poster: \(error)")
        }
        .retry(maxCount: 3)
        .fade(duration: 0.25)
        .resizable()
        .aspectRatio(contentMode: .fill)
        .frame(height: 300)
    } else {
      Rectangle()
        .fill(Color.gray.opacity(0.3))
        .frame(height: 300)
        .overlay(
          VStack(spacing: 8) {
            Image(systemName: "exclamationmark.triangle")
              .font(.title)
              .foregroundColor(.orange)
            Text("Invalid URL")
              .font(.caption)
              .foregroundColor(.white)
          }
        )
    }
  }

  // MARK: - Video Loading Methods

  private func loadVideoIfNeeded() {
    guard isVisible, player == nil else { return }

    guard let videoURL = URL(string: videoEffect.videoUrl) else {
      Logger.error("Invalid video URL: \(videoEffect.videoUrl)")
      return
    }

    // 使用新的视频缓存管理器
    loadVideoWithCache(url: videoURL)
  }

  private func loadVideoWithCache(url: URL) {
    Task {
      // 1. 检查缓存
      if let cachedURL = await VideoCacheManager.shared.getCachedVideoURL(for: url) {
        await MainActor.run {
          self.isVideoCached = true
          self.createPlayer(with: cachedURL)
        }
        return
      }

      // 2. 下载并缓存
      await MainActor.run {
        self.isVideoCached = false
      }
      await downloadAndCacheVideo(url: url)
    }
  }

  private func downloadAndCacheVideo(url: URL) async {
    do {
      Logger.debug("开始下载视频: \(url.lastPathComponent)")

      // 下载视频数据
      let (data, _) = try await URLSession.shared.data(from: url)

      // 缓存视频
      if let cachedURL = await VideoCacheManager.shared.cacheVideo(url: url, data: data) {
        await MainActor.run {
          Logger.debug("视频下载并缓存成功: \(url.lastPathComponent)")
          self.createPlayer(with: cachedURL)
        }
      } else {
        await MainActor.run {
          Logger.error("视频缓存失败: \(url.lastPathComponent)")
        }
      }
    } catch {
      await MainActor.run {
        Logger.error("视频下载失败: \(url.lastPathComponent) - \(error)")
      }
    }
  }

  private func createPlayer(with url: URL) {
    guard isVisible else { return }

    let playerItem = AVPlayerItem(url: url)
    let newPlayer = AVPlayer(playerItem: playerItem)

    // Configure player for silent looping
    newPlayer.isMuted = true
    newPlayer.actionAtItemEnd = .none

    // Add observer for looping
    NotificationCenter.default.addObserver(
      forName: .AVPlayerItemDidPlayToEndTime,
      object: playerItem,
      queue: .main
    ) { [weak newPlayer] _ in
      newPlayer?.seek(to: .zero)
      newPlayer?.play()
    }

    self.player = newPlayer

    // Wait for player to be ready
    let statusObserver = playerItem.observe(\.status, options: [.new]) { item, _ in
      if item.status == .readyToPlay {
        DispatchQueue.main.async {
          withAnimation(.easeInOut(duration: 0.3)) {
            self.isVideoReady = true
          }
          self.player?.play()
        }
      }
    }

    // Store observer to clean up later
    self.statusObserver = statusObserver
  }

  private func cleanupVideo() {
    player?.pause()
    player = nil
    isVideoReady = false

    // Remove observers
    NotificationCenter.default.removeObserver(self)
    statusObserver?.invalidate()
    statusObserver = nil
  }
}

// MARK: - Image Selection Section

struct ImageSelectionSection: View {
  @Binding var selectedImageUrls: [String]
  @State private var showAssetSelection = false
  @State private var selectedIndex = 0

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      // Section header
      HStack {
        Text("Select Images")
          .font(.headline)
          .foregroundColor(.white)

        Spacer()

        Text("\(selectedImageUrls.count)/2")
          .font(.caption)
          .foregroundColor(.gray)
      }

      // Image selection cards
      HStack(spacing: 12) {
        ForEach(0..<2, id: \.self) { index in
          VideuImageUrlSelectionCard(
            imageUrl: index < selectedImageUrls.count ? selectedImageUrls[index] : nil,
            onTap: { selectImage(at: index) },
            onDelete: index < selectedImageUrls.count ? { deleteImage(at: index) } : nil
          )
        }
      }
    }
    .sheet(isPresented: $showAssetSelection) {
      UserAssetSelectionView(
        onAssetsSelected: { assets in
          if let firstAsset = assets.first {
            addImageUrl(firstAsset.url, at: selectedIndex)
          }
          showAssetSelection = false
        },
        onDismiss: {
          showAssetSelection = false
        },
        maxSelectionCount: 1
      )
    }
  }

  private func selectImage(at index: Int) {
    selectedIndex = index
    showAssetSelection = true
  }

  private func deleteImage(at index: Int) {
    if index < selectedImageUrls.count {
      selectedImageUrls.remove(at: index)
    }
  }

  private func addImageUrl(_ imageUrl: String, at index: Int) {
    if index < selectedImageUrls.count {
      selectedImageUrls[index] = imageUrl
    } else {
      selectedImageUrls.append(imageUrl)
    }
  }
}

struct VideuImageUrlSelectionCard: View {
  let imageUrl: String?
  let onTap: () -> Void
  let onDelete: (() -> Void)?

  var body: some View {
    Button(action: onTap) {
      ZStack {
        RoundedRectangle(cornerRadius: 12)
          .fill(Color.gray.opacity(0.2))
          .aspectRatio(1, contentMode: .fit)  // 保持正方形比例

        if let imageUrl = imageUrl, let url = URL(string: imageUrl) {
          KFImage(url)
            .placeholder {
              RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.3))
                .overlay(
                  ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(0.8)
                )
            }
            .retry(maxCount: 3)
            .fade(duration: 0.25)
            .resizable()
            .aspectRatio(contentMode: .fill)
            .clipShape(RoundedRectangle(cornerRadius: 12))
        } else {
          VStack(spacing: 8) {
            Image(systemName: "square.dashed")
              .font(.title2)
              .foregroundColor(.gray)
            Text("Tap to select")
              .font(.caption)
              .foregroundColor(.gray)
          }
        }
      }
    }
    .buttonStyle(PlainButtonStyle())
    .overlay(
      // Delete button overlay (only show when image exists)
      Group {
        if imageUrl != nil, let onDelete = onDelete {
          VStack {
            HStack {
              Spacer()
              Button(action: onDelete) {
                Image(systemName: "xmark.circle.fill")
                  .font(.system(size: 22))
                  .foregroundColor(.white)
                  .background(
                    Circle()
                      .fill(Color.red.opacity(0.8))
                      .frame(width: 26, height: 26)
                  )
                  .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
              }
              .padding(6)
            }
            Spacer()
          }
        }
      }
    )
  }
}

// MARK: - Sample Images Section

struct SampleImagesSection: View {
  let onImageSelected: (String) -> Void

  // 示例图片的 URL
  private let sampleImages = [
    "https://assets.picadabra.ai/sample_woman_1.jpg",
    "https://assets.picadabra.ai/sample_woman_2.jpg",
    "https://assets.picadabra.ai/sample_man_1.jpg",
    "https://assets.picadabra.ai/sample_man_2.jpg",
  ]

  var body: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack {
        Text("Try sample images")
          .font(.headline)
          .foregroundColor(.white)

        Image(systemName: "info.circle")
          .foregroundColor(.gray)
          .font(.caption)

        Spacer()  // 确保标题左对齐
      }

      HStack(spacing: 12) {
        ForEach(sampleImages, id: \.self) { imageUrl in
          Button(action: { onImageSelected(imageUrl) }) {
            KFImage(URL(string: imageUrl))
              .placeholder {
                RoundedRectangle(cornerRadius: 8)
                  .fill(Color.gray.opacity(0.3))
                  .frame(width: 60, height: 60)
                  .overlay(
                    ProgressView()
                      .progressViewStyle(CircularProgressViewStyle(tint: .white))
                      .scaleEffect(0.6)
                  )
              }
              .retry(maxCount: 3)
              .fade(duration: 0.25)
              .resizable()
              .aspectRatio(contentMode: .fill)
              .frame(width: 60, height: 60)
              .clipShape(RoundedRectangle(cornerRadius: 8))
          }
          .buttonStyle(PlainButtonStyle())
        }

        Spacer()  // 确保图片左对齐
      }
    }
  }
}

// MARK: - Advanced Settings Section

struct AdvancedSettingsSection: View {
  @Binding var isExpanded: Bool
  let videoEffect: VideoEffect

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      Button(action: {
        withAnimation(.easeInOut(duration: 0.3)) {
          isExpanded.toggle()
        }
      }) {
        HStack {
          Text("Advanced Settings")
            .font(.headline)
            .foregroundColor(.white)

          Spacer()

          Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
            .foregroundColor(.gray)
        }
      }

      if isExpanded {
        VStack(alignment: .leading, spacing: 12) {
          // 显示技术参数
          if let detail = videoEffect.detail {
            TechnicalSpecsView(detail: detail)
          }

          // 显示使用指导
          if let instruction = videoEffect.inputInstruction {
            InstructionView(instruction: instruction)
          }
        }
        .transition(.opacity.combined(with: .move(edge: .top)))
      }
    }
  }
}

struct TechnicalSpecsView: View {
  let detail: String

  var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      Text("Technical Specifications")
        .font(.subheadline)
        .fontWeight(.medium)
        .foregroundColor(.white)

      Text(detail)
        .font(.caption)
        .foregroundColor(.gray)
        .multilineTextAlignment(.leading)
    }
  }
}

struct InstructionView: View {
  let instruction: String

  var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      Text("Usage Instructions")
        .font(.subheadline)
        .fontWeight(.medium)
        .foregroundColor(.white)

      Text(instruction)
        .font(.caption)
        .foregroundColor(.gray)
        .multilineTextAlignment(.leading)
    }
  }
}

// MARK: - Create Button State

enum CreateButtonState {
  case create  // 初始状态：Create
  case creatingTask  // 创建任务中：Creating Task
  case generating  // 生成中：Generating
}

// MARK: - Create Button

struct CreateButton: View {
  let creditCost: String
  let onCreateTapped: () -> Void
  let state: CreateButtonState

  var body: some View {
    VStack(spacing: 0) {
      // 添加渐变背景以确保按钮可见性
      Rectangle()
        .fill(
          LinearGradient(
            gradient: Gradient(colors: [Color.black.opacity(0), Color.black.opacity(0.8)]),
            startPoint: .top,
            endPoint: .bottom
          )
        )
        .frame(height: 20)

      Button(action: onCreateTapped) {
        HStack {
          // 根据状态显示不同的内容
          switch state {
          case .create:
            Text("Create")
              .font(.system(size: 16, weight: .semibold))
              .foregroundColor(.white)

          case .creatingTask:
            ProgressView()
              .progressViewStyle(CircularProgressViewStyle(tint: .white))
              .scaleEffect(0.8)
            Text("Creating Task...")
              .font(.system(size: 16, weight: .semibold))
              .foregroundColor(.white)

          case .generating:
            ProgressView()
              .progressViewStyle(CircularProgressViewStyle(tint: .white))
              .scaleEffect(0.8)
            Text("Generating...")
              .font(.system(size: 16, weight: .semibold))
              .foregroundColor(.white)
          }

          Spacer()

          // 只在初始状态显示积分消耗
          if state == .create {
            HStack(spacing: 4) {
              Image(systemName: "circle.fill")
                .foregroundColor(.yellow)
                .font(.system(size: 12))
              Text(creditCost)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white)
            }
          }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
          RoundedRectangle(cornerRadius: 12)
            .fill(buttonBackgroundColor)
        )
      }
      .disabled(state != .create)
      .padding(.horizontal, 16)
      .padding(.bottom, 34)  // 安全区域
      .background(Color.black.opacity(0.8))
    }
  }

  // MARK: - Computed Properties

  private var buttonBackgroundColor: Color {
    switch state {
    case .create:
      return .blue
    case .creatingTask, .generating:
      return Color.gray.opacity(0.6)
    }
  }
}
