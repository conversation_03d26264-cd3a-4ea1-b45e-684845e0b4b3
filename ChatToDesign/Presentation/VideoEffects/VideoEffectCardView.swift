//
//  VideoEffectCardView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import AVKit
import Combine
import Kingfisher
import SwiftUI

struct VideoEffectCardView: View {
  let videoEffect: VideoEffect
  let onTap: (() -> Void)?
  let isInteractive: Bool

  @State private var isVideoReady = false
  @State private var player: AVPlayer?
  @State private var isVisible = false
  @State private var statusObserver: NSKeyValueObservation?
  @State private var isVideoCached = false
  @Environment(\.colorScheme) private var colorScheme

  init(videoEffect: VideoEffect, onTap: (() -> Void)? = nil, isInteractive: Bool = true) {
    self.videoEffect = videoEffect
    self.onTap = onTap
    self.isInteractive = isInteractive
  }

  var body: some View {
    Group {
      if isInteractive {
        Button(action: {
          handleTap()
        }) {
          cardContent
        }
        .buttonStyle(PlainButtonStyle())
      } else {
        cardContent
      }
    }
    .onAppear {
      isVisible = true
      loadVideoIfNeeded()
    }
    .onDisappear {
      isVisible = false
      cleanupVideo()
    }
  }

  // MARK: - Card Content

  private var cardContent: some View {
    ZStack(alignment: .topTrailing) {
      // Main card content
      VStack(spacing: 0) {
        // Video content area
        videoContentView

        // Title section
        titleSection
      }
      .background(
        colorScheme == .dark ? Color(.systemGray6) : Color(.systemBackground)
      )
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .stroke(
            colorScheme == .dark ? Color.white.opacity(0.15) : Color.black.opacity(0.1),
            lineWidth: colorScheme == .dark ? 1.0 : 0.5
          )
      )
      .shadow(
        color: colorScheme == .dark ? Color.black.opacity(0.3) : Color.black.opacity(0.1),
        radius: colorScheme == .dark ? 8 : 4,
        x: 0,
        y: colorScheme == .dark ? 4 : 2
      )

      // Hot/New badges
      badgeView
    }
  }

  // MARK: - Video Content View

  @ViewBuilder
  private var videoContentView: some View {
    ZStack {
      // Show poster only if video is not cached or not ready
      if !isVideoCached || !isVideoReady {
        posterImageView
      }

      // Video player (shown when ready)
      if isVideoReady, let player = player {
        VideoPlayer(player: player)
          .allowsHitTesting(false)  // Prevent video player from intercepting taps
          .transition(.opacity)
      }
    }
    .aspectRatio(1, contentMode: .fill)
    .clipped()
    .cornerRadius(16, corners: [.topLeft, .topRight])
  }

  // MARK: - Poster Image View

  @ViewBuilder
  private var posterImageView: some View {
    if let posterURL = URL(string: videoEffect.posterUrl) {
      KFImage(posterURL)
        .placeholder {
          RoundedRectangle(cornerRadius: 16)
            .fill(Color(.systemGray5))
            .overlay(
              VStack(spacing: 8) {
                Image(systemName: "play.circle")
                  .font(.title)
                  .foregroundColor(.secondary)
                Text("Loading...")
                  .font(.caption)
                  .foregroundColor(.secondary)
              }
            )
        }
        .onFailure { error in
          Logger.error("Failed to load video poster: \(error)")
        }
        .retry(maxCount: 3)
        .fade(duration: 0.25)
        .resizable()
        .aspectRatio(1, contentMode: .fill)
    } else {
      RoundedRectangle(cornerRadius: 16)
        .fill(Color(.systemGray5))
        .aspectRatio(1, contentMode: .fill)
        .overlay(
          VStack(spacing: 8) {
            Image(systemName: "exclamationmark.triangle")
              .font(.title)
              .foregroundColor(.orange)
            Text("Invalid URL")
              .font(.caption)
              .foregroundColor(.secondary)
          }
        )
    }
  }

  // MARK: - Title Section

  private var titleSection: some View {
    VStack(alignment: .leading, spacing: 4) {
      Text(videoEffect.name)
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.primary)
        .lineLimit(2)
        .multilineTextAlignment(.leading)

      Spacer(minLength: 0)
    }
    .padding(.horizontal, 12)
    .padding(.vertical, 8)
    .frame(minHeight: 44)
    .frame(maxWidth: .infinity, alignment: .leading)
  }

  // MARK: - Badge View

  @ViewBuilder
  private var badgeView: some View {
    VStack(alignment: .trailing, spacing: 4) {
      if videoEffect.isHot {
        badgeLabel(text: "Hot", color: .red)
      }

      if videoEffect.isNew {
        badgeLabel(text: "New", color: .green)
      }
    }
    .padding(8)
  }

  private func badgeLabel(text: String, color: Color) -> some View {
    Text(text)
      .font(.system(size: 10, weight: .bold))
      .foregroundColor(.white)
      .padding(.horizontal, 6)
      .padding(.vertical, 2)
      .background(color)
      .cornerRadius(4)
  }

  // MARK: - Action Methods

  private func handleTap() {
    if videoEffect.provider == .vidu {
      // vidu 效果通过 NavigationLink 处理，不在这里处理
      onTap?()
    }
  }

  // MARK: - Video Loading Methods

  private func loadVideoIfNeeded() {
    guard isVisible, player == nil else { return }

    guard let videoURL = URL(string: videoEffect.videoUrl) else {
      Logger.error("Invalid video URL: \(videoEffect.videoUrl)")
      return
    }

    // 使用新的视频缓存管理器
    loadVideoWithCache(url: videoURL)
  }

  private func loadVideoWithCache(url: URL) {
    Task {
      // 1. 检查缓存
      if let cachedURL = await VideoCacheManager.shared.getCachedVideoURL(for: url) {
        await MainActor.run {
          self.isVideoCached = true
          self.createPlayer(with: cachedURL)
        }
        return
      }

      // 2. 下载并缓存
      await MainActor.run {
        self.isVideoCached = false
      }
      await downloadAndCacheVideo(url: url)
    }
  }

  private func downloadAndCacheVideo(url: URL) async {
    do {
      Logger.debug("开始下载视频: \(url.lastPathComponent)")

      // 下载视频数据
      let (data, _) = try await URLSession.shared.data(from: url)

      // 缓存视频
      if let cachedURL = await VideoCacheManager.shared.cacheVideo(url: url, data: data) {
        await MainActor.run {
          Logger.debug("视频下载并缓存成功: \(url.lastPathComponent)")
          self.createPlayer(with: cachedURL)
        }
      } else {
        await MainActor.run {
          Logger.error("视频缓存失败: \(url.lastPathComponent)")
        }
      }
    } catch {
      await MainActor.run {
        Logger.error("视频下载失败: \(url.lastPathComponent) - \(error)")
      }
    }
  }

  private func createPlayer(with url: URL) {
    guard isVisible else { return }

    let playerItem = AVPlayerItem(url: url)
    let newPlayer = AVPlayer(playerItem: playerItem)

    // Configure player for silent looping
    newPlayer.isMuted = true
    newPlayer.actionAtItemEnd = .none

    // Add observer for looping
    NotificationCenter.default.addObserver(
      forName: .AVPlayerItemDidPlayToEndTime,
      object: playerItem,
      queue: .main
    ) { [weak newPlayer] _ in
      newPlayer?.seek(to: .zero)
      newPlayer?.play()
    }

    self.player = newPlayer

    // Wait for player to be ready
    let statusObserver = playerItem.observe(\.status, options: [.new]) { item, _ in
      if item.status == .readyToPlay {
        DispatchQueue.main.async {
          withAnimation(.easeInOut(duration: 0.3)) {
            self.isVideoReady = true
          }
          self.player?.play()
        }
      }
    }

    // Store observer to clean up later
    self.statusObserver = statusObserver
  }

  private func cleanupVideo() {
    player?.pause()
    player = nil
    isVideoReady = false

    // Remove observers
    NotificationCenter.default.removeObserver(self)
    statusObserver?.invalidate()
    statusObserver = nil
  }
}
