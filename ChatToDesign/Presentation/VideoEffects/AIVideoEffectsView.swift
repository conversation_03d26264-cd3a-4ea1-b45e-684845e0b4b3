//
//  AIVideoEffectsView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import SwiftUI
import UIKit

struct AIVideoEffectsView: View {
  @StateObject private var viewModel = AIVideoEffectsViewModel()
  @Environment(\.dismiss) private var dismiss
  @Environment(\.colorScheme) private var colorScheme

  var body: some View {
    ZStack {
      Color(.systemGroupedBackground)
        .ignoresSafeArea()

      VStack(spacing: 0) {
        // Content
        if viewModel.isLoading && viewModel.categories.isEmpty {
          loadingView
        } else if let error = viewModel.error, viewModel.categories.isEmpty {
          errorView(error)
        } else {
          contentView
        }
      }
    }
    .navigationTitle("🎬 AI Video Effects")
    .navigationBarTitleDisplayMode(.inline)
    .toolbar {
      #if DEBUG
        ToolbarItem(placement: .navigationBarTrailing) {
          NavigationLink(destination: VideoCacheDebugView()) {
            Image(systemName: "gear")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.primary)
          }
        }
      #endif
    }
  }

  // MARK: - Content View

  private var contentView: some View {
    ScrollView {
      LazyVStack(spacing: 24) {
        // Category tabs
        categoryTabsView

        // Categories sections
        ForEach(viewModel.filteredCategories, id: \.id) { category in
          AIVideoCategorySectionView(
            category: category,
            onEffectTap: { effect in
              handleEffectTap(effect)
            }
          )
        }
      }
      .padding(.horizontal, 16)
      .padding(.bottom, 32)
    }
    .refreshable {
      viewModel.refresh()
    }
  }

  // MARK: - Category Tabs View

  private var categoryTabsView: some View {
    ScrollView(.horizontal, showsIndicators: false) {
      HStack(spacing: 12) {
        ForEach(viewModel.availableCategories, id: \.self) { category in
          AIVideoCategoryTabButton(
            title: category,
            isSelected: viewModel.selectedCategory == category,
            action: {
              viewModel.selectCategory(category)
            }
          )
        }
      }
      .padding(.horizontal, 16)
    }
  }

  // MARK: - Loading View

  private var loadingView: some View {
    VStack(spacing: 16) {
      ProgressView()
        .scaleEffect(1.2)

      Text("Loading AI Video Effects...")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.secondary)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }

  // MARK: - Error View

  private func errorView(_ error: Error) -> some View {
    VStack(spacing: 16) {
      Image(systemName: "exclamationmark.triangle")
        .font(.system(size: 48))
        .foregroundColor(.orange)

      Text("Failed to Load")
        .font(.system(size: 18, weight: .semibold))
        .foregroundColor(.primary)

      Text(error.localizedDescription)
        .font(.system(size: 14))
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
        .padding(.horizontal, 32)

      Button("Retry") {
        viewModel.refresh()
      }
      .font(.system(size: 16, weight: .medium))
      .foregroundColor(.white)
      .padding(.horizontal, 24)
      .padding(.vertical, 12)
      .background(Color.blue)
      .cornerRadius(8)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }

  // MARK: - Helper Methods

  private func handleEffectTap(_ effect: VideoEffect) {
    // Handle video effect tap - only for non-vidu effects
    Logger.info("Tapped video effect: \(effect.name)")

    if effect.provider == .vidu {
      // vidu 效果通过 NavigationLink 处理，不应该到这里
      Logger.warning("Vidu effect should be handled by NavigationLink")
      return
    }
  }
}

// MARK: - Category Section View

struct AIVideoCategorySectionView: View {
  let category: VideoEffectCategory
  let onEffectTap: (VideoEffect) -> Void

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      // Section header
      HStack {
        HStack(spacing: 8) {
          Text(categoryEmoji(for: category.name))
            .font(.title2)

          Text(category.name)
            .font(.system(size: 20, weight: .semibold))
            .foregroundColor(.primary)
        }

        Spacer()

        NavigationLink(destination: AIVideoEffectsCategoryView(category: category)) {
          Text("View All")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.blue)
        }
      }

      // Horizontal scrolling effects
      ScrollView(.horizontal, showsIndicators: false) {
        LazyHStack(spacing: 12) {
          ForEach(Array(category.effects.enumerated()), id: \.element.id) { index, effect in
            if effect.provider == .vidu {
              NavigationLink(destination: VideuEffectDetailView(videoEffect: effect)) {
                VideoEffectCardView(videoEffect: effect, isInteractive: false)
              }
              .buttonStyle(PlainButtonStyle())
              .frame(width: 160, height: 200)
              .onAppear {
                // 预加载相邻的视频
                preloadAdjacentVideos(currentIndex: index, effects: category.effects)
              }
            } else {
              VideoEffectCardView(videoEffect: effect) {
                onEffectTap(effect)
              }
              .frame(width: 160, height: 200)
              .onAppear {
                // 预加载相邻的视频
                preloadAdjacentVideos(currentIndex: index, effects: category.effects)
              }
            }
          }
        }
        .padding(.horizontal, 16)
      }
      .padding(.horizontal, -16)  // Compensate for parent padding
    }
  }

  private func categoryEmoji(for categoryName: String) -> String {
    switch categoryName {
    case "Interaction": return "🤝"
    case "Appearance": return "✨"
    case "Emotions": return "😊"
    case "Entertainment": return "🎭"
    case "Hero/Villain": return "🦸"
    case "Horror/Fantasy": return "👻"
    case "Xmas": return "🎄"
    default: return "🎬"
    }
  }

  private func preloadAdjacentVideos(currentIndex: Int, effects: [VideoEffect]) {
    // 预加载当前视频前后2个视频
    let preloadRange = max(0, currentIndex - 2)...min(effects.count - 1, currentIndex + 2)

    Task {
      for index in preloadRange {
        if let url = URL(string: effects[index].videoUrl) {
          await VideoPreloadService.shared.preloadVideo(url: url)
        }
      }
    }
  }
}

// MARK: - Category Tab Button

struct AIVideoCategoryTabButton: View {
  let title: String
  let isSelected: Bool
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      Text(title)
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(isSelected ? .white : .primary)
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(
          RoundedRectangle(cornerRadius: 20)
            .fill(isSelected ? Color.blue : Color(.systemGray6))
        )
    }
    .buttonStyle(PlainButtonStyle())
  }
}

// MARK: - Preview

#Preview {
  AIVideoEffectsView()
}
