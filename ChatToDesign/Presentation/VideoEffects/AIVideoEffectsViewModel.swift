//
//  AIVideoEffectsViewModel.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import Combine
import Foundation

@MainActor
class AIVideoEffectsViewModel: ObservableObject {

  // MARK: - Published Properties

  @Published var categories: [VideoEffectCategory] = []
  @Published var selectedCategory: String = "All"
  @Published var isLoading: Bool = false
  @Published var error: Error? = nil

  // MARK: - Private Properties

  private let videoEffectService: VideoEffectService
  private var videoEffectsHook: SWRHook<[VideoEffect]>?
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Computed Properties

  /// Available category names including "All"
  var availableCategories: [String] {
    var cats = ["All"] + categories.map(\.name).sorted()
    return cats
  }

  /// Filtered categories based on selected category
  var filteredCategories: [VideoEffectCategory] {
    if selectedCategory == "All" {
      return categories
    } else {
      return categories.filter { $0.name == selectedCategory }
    }
  }

  // MARK: - Initialization

  init(videoEffectService: VideoEffectService = AppDependencyContainer.shared.videoEffectService) {
    self.videoEffectService = videoEffectService
    setupVideoEffectsHook()
  }

  // MARK: - Public Methods

  func selectCategory(_ category: String) {
    selectedCategory = category
  }

  func refresh() {
    videoEffectsHook?.refresh()
  }

  // MARK: - Private Methods

  private func setupVideoEffectsHook() {
    videoEffectsHook = SWRHook.create(
      key: "video_effects_data",
      maxAge: 60 * 60,  // 1 hour cache
      staleTime: 24 * 60 * 60,  // 24 hours stale time (local data doesn't change often)
      networkCall: { [weak self] in
        guard let self = self else {
          throw VideoEffectServiceError.invalidData
        }
        return try await self.videoEffectService.fetchVideoEffects()
      },
      autoFetch: true
    )

    observeVideoEffectsHookChanges()
  }

  private func observeVideoEffectsHookChanges() {
    guard let hook = videoEffectsHook else { return }

    // Observe data changes
    hook.$data
      .receive(on: DispatchQueue.main)
      .sink { [weak self] videoEffects in
        if let effects = videoEffects {
          self?.processVideoEffectsData(effects)
        }
      }
      .store(in: &cancellables)

    // Observe loading state
    hook.$isLoading
      .receive(on: DispatchQueue.main)
      .sink { [weak self] isLoading in
        self?.isLoading = isLoading
      }
      .store(in: &cancellables)

    // Observe error state
    hook.$error
      .receive(on: DispatchQueue.main)
      .sink { [weak self] error in
        self?.error = error
      }
      .store(in: &cancellables)
  }

  private func processVideoEffectsData(_ videoEffects: [VideoEffect]) {
    // Filter out pollo effects
    let filteredEffects = videoEffects.filter { $0.provider != .polloAI }

    // Group effects by category
    let groupedEffects = Dictionary(grouping: filteredEffects) { $0.category }

    // Convert to VideoEffectCategory objects and sort by category name
    let categories = groupedEffects.map { (categoryName, effects) in
      VideoEffectCategory(name: categoryName, effects: effects)
    }.sorted { $0.name < $1.name }

    self.categories = categories
    Logger.info(
      "Successfully processed \(categories.count) video effect categories (pollo effects filtered out)"
    )
  }
}
