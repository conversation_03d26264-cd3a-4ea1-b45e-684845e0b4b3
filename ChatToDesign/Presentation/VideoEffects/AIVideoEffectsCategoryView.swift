//
//  AIVideoEffectsCategoryView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import SwiftUI
import UIKit

struct AIVideoEffectsCategoryView: View {
  let category: VideoEffectCategory
  @Environment(\.dismiss) private var dismiss
  @Environment(\.colorScheme) private var colorScheme

  var body: some View {
    VStack(spacing: 0) {
      // Content
      contentView
    }
    .background(Color(.systemGroupedBackground))
    .navigationTitle("\(categoryEmoji(for: category.name)) \(category.name)")
    .navigationBarTitleDisplayMode(.inline)
  }

  // MARK: - Content View

  private var contentView: some View {
    ScrollView {
      LazyVStack(spacing: 16) {
        // Category info
        categoryInfoView

        // Effects grid
        effectsGridView
      }
      .padding(.horizontal, 16)
      .padding(.bottom, 32)
    }
  }

  // MARK: - Category Info View

  private var categoryInfoView: some View {
    HStack {
      VStack(alignment: .leading, spacing: 4) {
        Text("\(category.effects.count) Effects")
          .font(.system(size: 16, weight: .semibold))
          .foregroundColor(.primary)

        Text("Tap any effect to view details")
          .font(.system(size: 14))
          .foregroundColor(.secondary)
      }

      Spacer()
    }
    .padding(.vertical, 8)
  }

  // MARK: - Effects Grid View

  private var effectsGridView: some View {
    LazyVGrid(
      columns: [
        GridItem(.flexible(), spacing: 12),
        GridItem(.flexible(), spacing: 12),
      ],
      spacing: 16
    ) {
      ForEach(Array(category.effects.enumerated()), id: \.element.id) { index, effect in
        if effect.provider == .vidu {
          NavigationLink(destination: VideuEffectDetailView(videoEffect: effect)) {
            VideoEffectCardView(videoEffect: effect, isInteractive: false)
          }
          .buttonStyle(PlainButtonStyle())
          .aspectRatio(0.8, contentMode: .fit)
          .onAppear {
            // 预加载网格中的相邻视频
            preloadGridAdjacentVideos(currentIndex: index, effects: category.effects)
          }
        } else {
          VideoEffectCardView(videoEffect: effect) {
            handleEffectTap(effect)
          }
          .aspectRatio(0.8, contentMode: .fit)
          .onAppear {
            // 预加载网格中的相邻视频
            preloadGridAdjacentVideos(currentIndex: index, effects: category.effects)
          }
        }
      }
    }
  }

  // MARK: - Helper Methods

  private func categoryEmoji(for categoryName: String) -> String {
    switch categoryName {
    case "Interaction": return "🤝"
    case "Appearance": return "✨"
    case "Emotions": return "😊"
    case "Entertainment": return "🎭"
    case "Hero/Villain": return "🦸"
    case "Horror/Fantasy": return "👻"
    case "Xmas": return "🎄"
    default: return "🎬"
    }
  }

  private func handleEffectTap(_ effect: VideoEffect) {
    // Handle video effect tap - only for non-vidu effects
    Logger.info("Tapped video effect: \(effect.name)")

    if effect.provider == .vidu {
      // vidu 效果通过 NavigationLink 处理，不应该到这里
      Logger.warning("Vidu effect should be handled by NavigationLink")
      return
    }
  }

  private func preloadGridAdjacentVideos(currentIndex: Int, effects: [VideoEffect]) {
    // 对于网格布局，预加载当前项目周围的视频
    // 考虑2列布局，预加载上下左右的视频
    let columnsCount = 2
    let currentRow = currentIndex / columnsCount
    let currentColumn = currentIndex % columnsCount

    var indicesToPreload: [Int] = []

    // 当前行的另一列
    let otherColumnIndex = currentRow * columnsCount + (1 - currentColumn)
    if otherColumnIndex < effects.count {
      indicesToPreload.append(otherColumnIndex)
    }

    // 上一行的两列
    if currentRow > 0 {
      let prevRowStart = (currentRow - 1) * columnsCount
      indicesToPreload.append(
        contentsOf: [prevRowStart, prevRowStart + 1].filter { $0 < effects.count })
    }

    // 下一行的两列
    let nextRowStart = (currentRow + 1) * columnsCount
    indicesToPreload.append(
      contentsOf: [nextRowStart, nextRowStart + 1].filter { $0 < effects.count })

    Task {
      for index in indicesToPreload {
        if let url = URL(string: effects[index].videoUrl) {
          await VideoPreloadService.shared.preloadVideo(url: url)
        }
      }
    }
  }
}


