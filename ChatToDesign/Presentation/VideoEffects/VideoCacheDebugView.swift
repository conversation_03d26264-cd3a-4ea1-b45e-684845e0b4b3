//
//  VideoCacheDebugView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import SwiftUI

/// 视频缓存调试视图
/// 用于监控和管理视频缓存状态
struct VideoCacheDebugView: View {
  @State private var cacheStats: (memoryCount: Int, diskSize: Int, totalFiles: Int) = (0, 0, 0)
  @State private var preloadStats: (activeDownloads: Int, queuedDownloads: Int) = (0, 0)
  @State private var isRefreshing = false
  
  var body: some View {
    NavigationView {
      List {
        // 缓存统计信息
        Section("Cache Statistics") {
          StatRow(
            title: "Memory Cache",
            value: formatBytes(cacheStats.memoryCount),
            icon: "memorychip"
          )
          
          StatRow(
            title: "Disk Cache",
            value: formatBytes(cacheStats.diskSize),
            icon: "internaldrive"
          )
          
          StatRow(
            title: "Cached Files",
            value: "\(cacheStats.totalFiles)",
            icon: "doc.on.doc"
          )
        }
        
        // 预加载统计信息
        Section("Preload Statistics") {
          StatRow(
            title: "Active Downloads",
            value: "\(preloadStats.activeDownloads)",
            icon: "arrow.down.circle"
          )
          
          StatRow(
            title: "Queued Downloads",
            value: "\(preloadStats.queuedDownloads)",
            icon: "clock"
          )
        }
        
        // 缓存管理操作
        Section("Cache Management") {
          Button(action: {
            Task {
              await clearAllCache()
            }
          }) {
            HStack {
              Image(systemName: "trash")
                .foregroundColor(.red)
              Text("Clear All Cache")
                .foregroundColor(.red)
            }
          }
          
          Button(action: {
            Task {
              await cancelAllPreloads()
            }
          }) {
            HStack {
              Image(systemName: "stop.circle")
                .foregroundColor(.orange)
              Text("Cancel All Preloads")
                .foregroundColor(.orange)
            }
          }
        }
        
        // 缓存配置信息
        Section("Configuration") {
          InfoRow(title: "Max Memory Cache", value: "50 MB")
          InfoRow(title: "Max Disk Cache", value: "150 MB")
          InfoRow(title: "Max Cache Age", value: "7 days")
          InfoRow(title: "Max Concurrent Downloads", value: "3")
        }
      }
      .navigationTitle("Video Cache Debug")
      .navigationBarTitleDisplayMode(.inline)
      .refreshable {
        await refreshStats()
      }
      .onAppear {
        Task {
          await refreshStats()
        }
      }
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("Refresh") {
            Task {
              await refreshStats()
            }
          }
          .disabled(isRefreshing)
        }
      }
    }
  }
  
  // MARK: - Helper Views
  
  private struct StatRow: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
      HStack {
        Image(systemName: icon)
          .foregroundColor(.blue)
          .frame(width: 24)
        
        Text(title)
          .font(.system(size: 16))
        
        Spacer()
        
        Text(value)
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.secondary)
      }
    }
  }
  
  private struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
      HStack {
        Text(title)
          .font(.system(size: 16))
        
        Spacer()
        
        Text(value)
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.secondary)
      }
    }
  }
  
  // MARK: - Helper Methods
  
  private func refreshStats() async {
    isRefreshing = true
    defer { isRefreshing = false }
    
    // 获取缓存统计信息
    let newCacheStats = await VideoCacheManager.shared.getCacheStats()
    let newPreloadStats = await VideoPreloadService.shared.getPreloadStatus()
    
    await MainActor.run {
      self.cacheStats = newCacheStats
      self.preloadStats = newPreloadStats
    }
  }
  
  private func clearAllCache() async {
    await VideoCacheManager.shared.clearAllCache()
    await refreshStats()
  }
  
  private func cancelAllPreloads() async {
    await VideoPreloadService.shared.cancelAllPreloads()
    await refreshStats()
  }
  
  private func formatBytes(_ bytes: Int) -> String {
    let formatter = ByteCountFormatter()
    formatter.allowedUnits = [.useMB, .useKB, .useBytes]
    formatter.countStyle = .file
    return formatter.string(fromByteCount: Int64(bytes))
  }
}

// MARK: - Preview

#Preview {
  VideoCacheDebugView()
}
