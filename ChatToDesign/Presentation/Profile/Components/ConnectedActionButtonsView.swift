//
//  ConnectedActionButtonsView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/3.
//

import SwiftUI

/// Connected action buttons component for CreateDetailPage
struct ConnectedActionButtonsView: View {
  let isDownloading: Bool
  let onRecreate: () -> Void
  let onEnhance: () -> Void
  let onDownload: () -> Void
  let onMore: () -> Void
  
  var body: some View {
    HStack(spacing: 0) {
      // Recreate Button
      ConnectedActionButton(
        iconName: "icon-recreate",
        title: "Re Create",
        position: .leading,
        action: onRecreate
      )
      
      // Enhance Button
      ConnectedActionButton(
        iconName: "icon-enhance",
        title: "Enhance",
        position: .middle,
        action: onEnhance
      )
      
      // Download Button
      ConnectedActionButton(
        icon: "arrow.down",
        title: "Download",
        position: .middle,
        isLoading: isDownloading,
        action: onDownload
      )
      
      // More Button
      ConnectedActionButton(
        icon: "ellipsis",
        title: "More",
        position: .trailing,
        action: onMore
      )
    }
    .padding(.horizontal, 24)
    .padding(.bottom, 34)  // Safe area bottom padding
  }
}

#Preview {
  ConnectedActionButtonsView(
    isDownloading: false,
    onRecreate: { print("Recreate tapped") },
    onEnhance: { print("Enhance tapped") },
    onDownload: { print("Download tapped") },
    onMore: { print("More tapped") }
  )
  .background(Color.black)
}
