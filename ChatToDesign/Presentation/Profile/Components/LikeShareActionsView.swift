//
//  LikeShareActionsView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/3.
//

import SwiftUI

/// Like and Share actions component for CreateDetailPage
struct LikeShareActionsView: View {
  let likeCount: Int
  let onLike: () -> Void
  let onShare: () -> Void

  var body: some View {
    VStack(spacing: 24) {
      // Like Button
      VStack(spacing: 8) {
        But<PERSON>(action: onLike) {
          Image(systemName: "heart")
            .font(.system(size: 24, weight: .medium))
            .foregroundColor(.white)
            .shadow(color: .black.opacity(0.5), radius: 1, x: 0, y: 1)
        }
        .frame(width: 24, height: 24)

        Text("\(likeCount)")
          .font(.custom("Inter", size: 14).weight(.semibold))
          .foregroundColor(.white)
          .shadow(color: .black.opacity(0.5), radius: 1, x: 0, y: 1)
      }

      // Share Button
      VStack(spacing: 8) {
        <PERSON><PERSON>(action: onShare) {
          Image(systemName: "arrowshape.turn.up.right")
            .font(.system(size: 24, weight: .medium))
            .foregroundColor(.white)
            .shadow(color: .black.opacity(0.5), radius: 1, x: 0, y: 1)
        }
        .frame(width: 24, height: 24)

        Text("Share")
          .font(.custom("Inter", size: 14).weight(.semibold))
          .foregroundColor(.white)
          .shadow(color: .black.opacity(0.5), radius: 1, x: 0, y: 1)
      }
    }
    .padding(.trailing, 24)
    .padding(.vertical, 44)
  }
}

#Preview {
  LikeShareActionsView(
    likeCount: 42,
    onLike: { print("Like tapped") },
    onShare: { print("Share tapped") }
  )
  .background(Color.black)
}
