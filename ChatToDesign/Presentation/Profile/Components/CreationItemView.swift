//
//  CreationItemView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/8.
//

import Kingfisher
import SwiftUI

/// 统一的创作内容展示组件
struct CreationItemView: View {
  let item: CreationItem
  let width: CGFloat
  let onRetry: ((CreationItem) -> Void)?
  let onFailedTaskTap: ((CreationItem) -> Void)?
  @State private var showDetailPage = false

  var body: some View {
    Button(action: {
      if item.isCompleted {
        showDetailPage = true
      } else if item.canRetry {
        onFailedTaskTap?(item)
      }
    }) {
      ZStack {
        // 缩略图
        thumbnailImageView

        // 状态覆盖层
        if !item.isCompleted {
          statusOverlayView
        }

        // 重试按钮
        if item.canRetry {
          retryButtonView
        }
      }
    }
    .buttonStyle(PlainButtonStyle())
    .fullScreenCover(isPresented: $showDetailPage) {
      if case .asset(let asset) = item {
        CreateDetailPage(asset: asset)
      }
    }
  }
}

// MARK: - Thumbnail Image View

extension CreationItemView {
  private var thumbnailImageView: some View {
    Group {
      if let thumbnailUrl = item.thumbnailUrl, !thumbnailUrl.isEmpty {
        KFImage(URL(string: thumbnailUrl))
          .placeholder {
            Rectangle()
              .fill(Color.gray.opacity(0.3))
              .overlay(
                Image(systemName: "photo")
                  .foregroundColor(.gray)
                  .font(.title2)
              )
          }
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: width)
          .clipped()
      } else {
        Rectangle()
          .fill(Color.gray.opacity(0.3))
          .frame(width: width)
          .overlay(
            Image(systemName: "photo")
              .foregroundColor(.gray)
              .font(.title2)
          )
      }
    }
    .clipShape(RoundedRectangle(cornerRadius: 16))
  }

  private func randomHeight() -> CGFloat {
    // Simulate different heights like in the design
    let heights: [CGFloat] = [160, 200, 224, 192]
    return heights.randomElement() ?? 200
  }
}

// MARK: - Status Overlay View

extension CreationItemView {
  private var statusOverlayView: some View {
    ZStack {
      // 半透明遮罩
      Color.black.opacity(0.4)

      VStack(spacing: 8) {
        // 进度指示器
        if case .processing(let progress) = item.status {
          if let progress = progress {
            CircularProgressView(progress: Double(progress) / 100.0)
          } else {
            ProgressView()
              .progressViewStyle(CircularProgressViewStyle(tint: .white))
          }
        } else if case .pending = item.status {
          ProgressView()
            .progressViewStyle(CircularProgressViewStyle(tint: .white))
        }

        // 状态文本
        Text(item.status.displayText)
          .font(.caption)
          .foregroundColor(.white)
          .padding(.horizontal, 8)
          .padding(.vertical, 4)
          .background(Color.black.opacity(0.6))
          .clipShape(Capsule())
      }
    }
    .clipShape(RoundedRectangle(cornerRadius: 16))
  }
}

// MARK: - Retry Button View

extension CreationItemView {
  private var retryButtonView: some View {
    VStack {
      Spacer()
      HStack {
        Spacer()
        Button(action: {
          onRetry?(item)
        }) {
          Image(systemName: "arrow.clockwise")
            .foregroundColor(.white)
            .font(.title3)
            .padding(8)
            .background(Color.red.opacity(0.8))
            .clipShape(Circle())
        }
        .padding(.trailing, 8)
        .padding(.bottom, 8)
      }
    }
  }
}

// MARK: - Circular Progress View

struct CircularProgressView: View {
  let progress: Double

  var body: some View {
    ZStack {
      Circle()
        .stroke(Color.white.opacity(0.3), lineWidth: 3)
        .frame(width: 30, height: 30)

      Circle()
        .trim(from: 0, to: progress)
        .stroke(Color.white, style: StrokeStyle(lineWidth: 3, lineCap: .round))
        .frame(width: 30, height: 30)
        .rotationEffect(.degrees(-90))
        .animation(.easeInOut(duration: 0.3), value: progress)
    }
  }
}

// MARK: - Preview

#if DEBUG
  struct CreationItemView_Previews: PreviewProvider {
    static var previews: some View {
      VStack(spacing: 16) {
        // Completed asset
        CreationItemView(
          item: .asset(
            UserAsset.createAIGenerated(
              userId: "user1",
              name: "test.jpg",
              size: 1024,
              type: "image/jpeg",
              url: "https://example.com/image.jpg",
              path: "/images/test.jpg",
              bucket: "assets",
              sourceTaskId: "task1",
              generationPrompt: "A beautiful landscape"
            )),
          width: 150,
          onRetry: nil,
          onFailedTaskTap: nil
        )

        // Processing task
        CreationItemView(
          item: .imageTask(
            ImageGenerationTask(
              taskId: "task2",
              userId: "user1",
              status: .processing,
              progress: 75
            )),
          width: 150,
          onRetry: nil,
          onFailedTaskTap: nil
        )

        // Failed task (clickable to show alert)
        CreationItemView(
          item: .videoTask(
            VideoGenerationTask(
              taskId: "task3",
              userId: "user1",
              status: .failed
            )),
          width: 150,
          onRetry: { item in
            print("Retry tapped for: \(item.id)")
          },
          onFailedTaskTap: { item in
            print("Failed task tapped: \(item.id)")
          }
        )
      }
      .padding()
      .background(Color.black)
    }
  }
#endif
