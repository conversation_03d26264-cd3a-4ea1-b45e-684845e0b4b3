//
//  ConnectedActionButton.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/3.
//

import SwiftUI

/// Button position for connected action buttons
enum ButtonPosition {
  case leading
  case middle
  case trailing
}

/// Connected action button component with custom rounded corners
struct ConnectedActionButton: View {
  let icon: String?
  let iconName: String?
  let title: String
  let position: ButtonPosition
  var isLoading: Bool = false
  let action: () -> Void
  
  // Convenience initializers
  init(icon: String, title: String, position: ButtonPosition, isLoading: Bool = false, action: @escaping () -> Void) {
    self.icon = icon
    self.iconName = nil
    self.title = title
    self.position = position
    self.isLoading = isLoading
    self.action = action
  }
  
  init(iconName: String, title: String, position: ButtonPosition, isLoading: Bool = false, action: @escaping () -> Void) {
    self.icon = nil
    self.iconName = iconName
    self.title = title
    self.position = position
    self.isLoading = isLoading
    self.action = action
  }
  
  var body: some View {
    Button(action: action) {
      VStack(spacing: 8) {
        // Icon
        Group {
          if isLoading {
            ProgressView()
              .progressViewStyle(
                CircularProgressViewStyle(tint: Color(red: 0.631, green: 0.631, blue: 0.667))
              )
              .scaleEffect(0.8)
          } else if let iconName = iconName {
            // Custom image asset
            Image(iconName)
              .resizable()
              .renderingMode(.template)
              .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
              .frame(width: 24, height: 24)
          } else if let icon = icon {
            // System icon
            Image(systemName: icon)
              .font(.system(size: 24))
              .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
          }
        }
        .frame(width: 24, height: 24)

        // Title
        Text(title)
          .font(.custom("Inter", size: 14).weight(.semibold))
          .foregroundColor(.white)
      }
      .frame(maxWidth: .infinity)
      .frame(height: 80)
      .background(Color(red: 0.157, green: 0.157, blue: 0.188))  // #27272a
      .clipShape(
        RoundedCorners(
          topLeft: position == .leading ? 16 : 0,
          topRight: position == .trailing ? 16 : 0,
          bottomLeft: position == .leading ? 16 : 0,
          bottomRight: position == .trailing ? 16 : 0
        )
      )
    }
    .disabled(isLoading)
  }
}

#Preview {
  HStack(spacing: 0) {
    ConnectedActionButton(
      iconName: "icon-recreate",
      title: "Re Create",
      position: .leading,
      action: { print("Recreate") }
    )
    
    ConnectedActionButton(
      icon: "wand.and.stars",
      title: "Enhance",
      position: .middle,
      action: { print("Enhance") }
    )
    
    ConnectedActionButton(
      icon: "arrow.down",
      title: "Download",
      position: .trailing,
      isLoading: false,
      action: { print("Download") }
    )
  }
  .padding()
  .background(Color.black)
}
