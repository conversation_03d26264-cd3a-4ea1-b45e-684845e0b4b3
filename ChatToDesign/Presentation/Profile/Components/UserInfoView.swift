//
//  UserInfoView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/3.
//

import SwiftUI

/// User info component showing username and prompt for CreateDetailPage
struct UserInfoView: View {
  let username: String
  let prompt: String?
  
  var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      // Username
      HStack {
        Text("@\(username)")
          .font(.custom("Inter", size: 14).weight(.semibold))
          .foregroundColor(.white)
        
        Spacer()
      }
      
      // Prompt
      if let prompt = prompt, !prompt.isEmpty {
        Text(prompt)
          .font(.custom("Inter", size: 14))
          .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
          .lineLimit(2)
          .multilineTextAlignment(.leading)
      } else {
        Text(
          "Visualize a futuristic city skyline with flying cars, neon lights, and advanced technology, sho..."
        )
        .font(.custom("Inter", size: 14))
        .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
        .lineLimit(2)
        .multilineTextAlignment(.leading)
      }
    }
    .padding(.horizontal, 24)
    .padding(.bottom, 16)
  }
}

#Preview {
  VStack(spacing: 20) {
    // With prompt
    UserInfoView(
      username: "User123456",
      prompt: "Create a beautiful sunset landscape with mountains and a lake"
    )
    
    // Without prompt (fallback)
    UserInfoView(
      username: "User789012",
      prompt: nil
    )
  }
  .background(Color.black)
}
