//
//  TaskFailureAlert.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/8.
//

import SwiftUI

/// Alert component for failed task interactions
struct TaskFailureAlert: View {
  let item: CreationItem
  let onRetry: () -> Void
  let onDelete: () -> Void
  let onDismiss: () -> Void

  var body: some View {
    ZStack {
      // Background overlay
      Color.black.opacity(0.4)
        .ignoresSafeArea()
        .onTapGesture {
          onDismiss()
        }

      // Alert container
      alertContainer
    }
  }

  private var alertContainer: some View {
    VStack(spacing: 0) {
      // Content area
      VStack(spacing: 8) {
        // Title
        Text("Creation failed")
          .font(.custom("SF Pro", size: 17).weight(.semibold))
          .foregroundColor(.primary)
          .multilineTextAlignment(.center)
          .padding(.top, 19)

        // Message
        Text("A description should be a short,\ncomplete sentence.")
          .font(.custom("SF Pro", size: 13))
          .foregroundColor(.secondary)
          .multilineTextAlignment(.center)
          .padding(.horizontal, 16)
          .padding(.bottom, 19)
      }

      // Action buttons (vertical stack)
      VStack(spacing: 0) {
        // Horizontal divider
        Rectangle()
          .fill(Color.gray.opacity(0.3))
          .frame(height: 0.5)

        // OK button
        Button(action: onDismiss) {
          Text("OK")
            .font(.custom("SF Pro", size: 17))
            .foregroundColor(Color(red: 0, green: 0.478, blue: 1))  // #007aff
            .frame(maxWidth: .infinity, minHeight: 44)
        }

        // Horizontal divider
        Rectangle()
          .fill(Color.gray.opacity(0.3))
          .frame(height: 0.5)

        // Re Create button
        Button(action: {
          onRetry()
          onDismiss()
        }) {
          Text("Re Create")
            .font(.custom("SF Pro", size: 17))
            .foregroundColor(Color(red: 0, green: 0.478, blue: 1))  // #007aff
            .frame(maxWidth: .infinity, minHeight: 44)
        }

        // Horizontal divider
        Rectangle()
          .fill(Color.gray.opacity(0.3))
          .frame(height: 0.5)

        // Delete button
        Button(action: {
          onDelete()
          onDismiss()
        }) {
          Text("Delete")
            .font(.custom("SF Pro", size: 17))
            .foregroundColor(Color(red: 0, green: 0.478, blue: 1))  // #007aff
            .frame(maxWidth: .infinity, minHeight: 44)
        }
      }
    }
    .background(
      RoundedRectangle(cornerRadius: 14)
        .fill(.regularMaterial)
    )
    .frame(width: 270)
    .clipShape(RoundedRectangle(cornerRadius: 14))
  }

  private var taskTypeText: String {
    switch item {
    case .imageTask:
      return "image"
    case .videoTask:
      return "video"
    case .asset:
      return "content"
    }
  }
}

// MARK: - Preview

#if DEBUG
  struct TaskFailureAlert_Previews: PreviewProvider {
    static var previews: some View {
      ZStack {
        Color.gray.opacity(0.2)
          .ignoresSafeArea()

        TaskFailureAlert(
          item: .imageTask(
            ImageGenerationTask(
              taskId: "failed-task",
              userId: "user1",
              status: .failed
            )),
          onRetry: {
            print("Retry tapped")
          },
          onDelete: {
            print("Delete tapped")
          },
          onDismiss: {
            print("Dismiss tapped")
          }
        )
      }
      .preferredColorScheme(.light)

      ZStack {
        Color.gray.opacity(0.2)
          .ignoresSafeArea()

        TaskFailureAlert(
          item: .videoTask(
            VideoGenerationTask(
              taskId: "failed-video-task",
              userId: "user1",
              status: .failed
            )),
          onRetry: {
            print("Retry tapped")
          },
          onDelete: {
            print("Delete tapped")
          },
          onDismiss: {
            print("Dismiss tapped")
          }
        )
      }
      .preferredColorScheme(.dark)
    }
  }
#endif
