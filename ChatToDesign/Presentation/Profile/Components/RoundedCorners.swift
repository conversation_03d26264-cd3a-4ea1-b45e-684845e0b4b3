//
//  RoundedCorners.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/3.
//

import SwiftUI

/// Custom shape for creating rounded corners on specific corners
struct RoundedCorners: Shape {
  var topLeft: CGFloat = 0
  var topRight: CGFloat = 0
  var bottomLeft: CGFloat = 0
  var bottomRight: CGFloat = 0

  func path(in rect: CGRect) -> Path {
    var path = Path()

    let width = rect.size.width
    let height = rect.size.height

    // Top left corner
    path.move(to: CGPoint(x: 0, y: topLeft))
    path.addArc(
      center: CGPoint(x: topLeft, y: topLeft), 
      radius: topLeft, 
      startAngle: Angle(degrees: 180),
      endAngle: Angle(degrees: 270), 
      clockwise: false
    )

    // Top right corner
    path.addLine(to: CGPoint(x: width - topRight, y: 0))
    path.addArc(
      center: CGPoint(x: width - topRight, y: topRight), 
      radius: topRight,
      startAngle: <PERSON><PERSON>(degrees: 270), 
      endAngle: Angle(degrees: 0), 
      clockwise: false
    )

    // Bottom right corner
    path.addLine(to: CGPoint(x: width, y: height - bottomRight))
    path.addArc(
      center: CGPoint(x: width - bottomRight, y: height - bottomRight), 
      radius: bottomRight,
      startAngle: Angle(degrees: 0), 
      endAngle: Angle(degrees: 90), 
      clockwise: false
    )

    // Bottom left corner
    path.addLine(to: CGPoint(x: bottomLeft, y: height))
    path.addArc(
      center: CGPoint(x: bottomLeft, y: height - bottomLeft), 
      radius: bottomLeft,
      startAngle: Angle(degrees: 90), 
      endAngle: Angle(degrees: 180), 
      clockwise: false
    )

    path.closeSubpath()
    return path
  }
}

#Preview {
  VStack(spacing: 20) {
    // Only top-left and bottom-left corners rounded
    Rectangle()
      .fill(Color.blue)
      .frame(width: 100, height: 60)
      .clipShape(RoundedCorners(topLeft: 16, bottomLeft: 16))
    
    // Only top-right and bottom-right corners rounded
    Rectangle()
      .fill(Color.green)
      .frame(width: 100, height: 60)
      .clipShape(RoundedCorners(topRight: 16, bottomRight: 16))
    
    // All corners rounded
    Rectangle()
      .fill(Color.red)
      .frame(width: 100, height: 60)
      .clipShape(RoundedCorners(topLeft: 16, topRight: 16, bottomLeft: 16, bottomRight: 16))
  }
  .padding()
}
