//
//  ProfileSettingsView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/3.
//

import SwiftUI

/// Profile settings view
struct ProfileSettingsView: View {
  @Environment(\.dismiss) private var dismiss
  @StateObject private var viewModel = ProfileSettingsViewModel()

  var body: some View {
    NavigationView {
      ZStack {
        // Background
        Color(red: 0.035, green: 0.035, blue: 0.043)  // #09090b
          .ignoresSafeArea()

        ScrollView {
          VStack(spacing: 24) {
            // Account Section
            settingsSection(title: "Account") {
              settingsRow(
                icon: "person.circle",
                title: "Edit Profile",
                action: {
                  // Handle edit profile
                }
              )

              settingsRow(
                icon: "bell",
                title: "Notifications",
                action: {
                  // Handle notifications
                }
              )
            }

            // Subscription Section
            settingsSection(title: "Subscription") {
              settingsRow(
                icon: "crown",
                title: "Manage Subscription",
                action: {
                  // Handle subscription management
                }
              )

              settingsRow(
                icon: "arrow.clockwise",
                title: "Restore Purchases",
                action: {
                  viewModel.restorePurchases()
                }
              )
            }

            // Support Section
            settingsSection(title: "Support") {
              settingsRow(
                icon: "questionmark.circle",
                title: "Help & FAQ",
                action: {
                  // Handle help
                }
              )

              settingsRow(
                icon: "envelope",
                title: "Contact Support",
                action: {
                  viewModel.showFeedbackForm()
                }
              )
            }

            // Legal Section
            settingsSection(title: "Legal") {
              settingsRow(
                icon: "doc.text",
                title: "Terms of Service",
                action: {
                  viewModel.showTermsOfService()
                }
              )

              settingsRow(
                icon: "hand.raised",
                title: "Privacy Policy",
                action: {
                  viewModel.showPrivacyPolicy()
                }
              )
            }

            // Sign Out
            Button(action: {
              viewModel.signOut()
            }) {
              HStack {
                Image(systemName: "rectangle.portrait.and.arrow.right")
                  .font(.system(size: 16))
                  .foregroundColor(.red)

                Text("Sign Out")
                  .font(.custom("Inter", size: 16).weight(.medium))
                  .foregroundColor(.red)

                Spacer()
              }
              .padding(.horizontal, 16)
              .padding(.vertical, 12)
              .background(Color(red: 0.157, green: 0.157, blue: 0.188))  // #27272a
              .clipShape(RoundedRectangle(cornerRadius: 12))
            }
            .padding(.horizontal, 24)
          }
          .padding(.top, 24)
        }
      }
      .navigationTitle("Settings")
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("Done") {
            dismiss()
          }
          .foregroundColor(.white)
        }
      }
      .fullScreenCover(isPresented: $viewModel.showingTermsOfService) {
        DocumentViewerSheet(documentType: .termsOfService)
      }
      .fullScreenCover(isPresented: $viewModel.showingPrivacyPolicy) {
        DocumentViewerSheet(documentType: .privacyPolicy)
      }
    }
  }

  // MARK: - Helper Views

  private func settingsSection<Content: View>(
    title: String,
    @ViewBuilder content: () -> Content
  ) -> some View {
    VStack(alignment: .leading, spacing: 12) {
      Text(title)
        .font(.custom("Inter", size: 14).weight(.medium))
        .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
        .padding(.horizontal, 24)

      VStack(spacing: 1) {
        content()
      }
      .background(Color(red: 0.157, green: 0.157, blue: 0.188))  // #27272a
      .clipShape(RoundedRectangle(cornerRadius: 12))
      .padding(.horizontal, 24)
    }
  }

  private func settingsRow(
    icon: String,
    title: String,
    action: @escaping () -> Void
  ) -> some View {
    Button(action: action) {
      HStack(spacing: 12) {
        Image(systemName: icon)
          .font(.system(size: 16))
          .foregroundColor(.white)
          .frame(width: 20)

        Text(title)
          .font(.custom("Inter", size: 16))
          .foregroundColor(.white)

        Spacer()

        Image(systemName: "chevron.right")
          .font(.system(size: 12))
          .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
    }
  }
}

// MARK: - Settings ViewModel

@MainActor
final class ProfileSettingsViewModel: ObservableObject {
  @Published var isLoading = false
  @Published var errorMessage: String?
  @Published var showingTermsOfService = false
  @Published var showingPrivacyPolicy = false
  @Published var showingFeedbackForm = false

  private let authService: AuthApplicationService
  private let subscriptionStateManager: SubscriptionStateManager

  init(
    authService: AuthApplicationService? = nil,
    subscriptionStateManager: SubscriptionStateManager? = nil
  ) {
    self.authService = authService ?? AppDependencyContainer.shared.authModule.authService
    self.subscriptionStateManager =
      subscriptionStateManager
      ?? AppDependencyContainer.shared.subscriptionModule.subscriptionStateManager
  }

  func signOut() {
    isLoading = true

    Task {
      do {
        try await authService.signOut()
        await MainActor.run {
          self.isLoading = false
        }
      } catch {
        await MainActor.run {
          self.errorMessage = "Failed to sign out: \(error.localizedDescription)"
          self.isLoading = false
        }
      }
    }
  }

  func restorePurchases() {
    isLoading = true

    Task {
      let success = await subscriptionStateManager.restorePurchases()
      await MainActor.run {
        self.isLoading = false
        if !success {
          self.errorMessage = "Failed to restore purchases. Please try again."
        }
      }
    }
  }

  func showTermsOfService() {
    showingTermsOfService = true
  }

  func showPrivacyPolicy() {
    showingPrivacyPolicy = true
  }

  func showFeedbackForm() {
    showingFeedbackForm = true
  }
}

#Preview {
  ProfileSettingsView()
}
