//
//  ProfilePageViewModel.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/3.
//

import Combine
import Foundation

/// Profile page view model
@MainActor
final class ProfilePageViewModel: ObservableObject {

  // MARK: - Published Properties

  /// Current user
  @Published var user: User?

  /// User creations/assets (AI-generated only) - DEPRECATED
  @Published var userCreations: [UserAsset] = []

  /// Unified creation items (assets + tasks)
  @Published var creationItems: [CreationItem] = []

  /// Loading states
  @Published var isLoadingProfile = false
  @Published var isLoadingCreations = false

  /// Error handling
  @Published var errorMessage: String?

  /// Retry state
  @Published var isRetrying = false

  // MARK: - Computed Properties

  /// Subscription status text
  var subscriptionStatus: String {
    if isPremiumUser {
      return "Pro Member"
    } else {
      return "Free Plan"
    }
  }

  /// Subscription description
  var subscriptionDescription: String {
    if isPremiumUser {
      return "Unlimited access to all features"
    } else {
      return "Upgrade to unlock premium features"
    }
  }

  /// Whether user is premium
  var isPremiumUser: Bool {
    return subscriptionStateManager.isPremiumUser
  }

  /// User credits count
  var creditsCount: Int {
    // This would come from a credits service in a real implementation
    return 200
  }

  // MARK: - Dependencies

  private let userService: UserService
  private let assetService: AssetApplicationService
  private let subscriptionStateManager: SubscriptionStateManager
  private let designGenerationService: DesignGenerationService
  private let videoGenerationService: VideoGenerationService

  // MARK: - Private Properties

  private var cancellables = Set<AnyCancellable>()

  // Separate data sources
  private var userAssets: [UserAsset] = []
  private var imageTasks: [ImageGenerationTask] = []
  private var videoTasks: [VideoGenerationTask] = []

  // Combine subscriptions
  private var assetSubscription: AnyCancellable?
  private var imageTaskSubscription: AnyCancellable?
  private var videoTaskSubscription: AnyCancellable?

  // MARK: - Initialization

  init(
    userService: UserService? = nil,
    assetService: AssetApplicationService? = nil,
    subscriptionStateManager: SubscriptionStateManager? = nil,
    designGenerationService: DesignGenerationService? = nil,
    videoGenerationService: VideoGenerationService? = nil
  ) {
    self.userService = userService ?? AppDependencyContainer.shared.userModule.userService
    self.assetService =
      assetService ?? AppDependencyContainer.shared.assetModule.assetService
    self.subscriptionStateManager =
      subscriptionStateManager
      ?? AppDependencyContainer.shared.subscriptionModule.subscriptionStateManager
    self.designGenerationService =
      designGenerationService ?? AppDependencyContainer.shared.designModule.designGenerationService
    self.videoGenerationService =
      videoGenerationService ?? AppDependencyContainer.shared.videoModule.videoGenerationService

    setupObservers()
  }

  // MARK: - Public Methods

  /// Load user profile data
  func loadUserProfile() {
    isLoadingProfile = true
    errorMessage = nil

    // Get current user from UserService
    let currentUser = userService.currentUser
    self.user = currentUser
    self.isLoadingProfile = false

    if currentUser != nil {
      print("ProfilePageViewModel: User profile loaded successfully")
    } else {
      print("ProfilePageViewModel: No current user found")
    }
  }

  /// Start observing user creations
  func loadUserCreations() {
    observeUserAssets()
    observeImageTasks()
    observeVideoTasks()
  }

  /// Start observing user creations in real-time - DEPRECATED
  private func startObservingUserCreations() {
    isLoadingCreations = true

    assetService.observeUserAssets()
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          self?.isLoadingCreations = false
          if case .failure(let error) = completion {
            self?.errorMessage = "Failed to load creations: \(error.localizedDescription)"
            print("ProfilePageViewModel: Failed to observe user creations - \(error)")
          }
        },
        receiveValue: { [weak self] response in
          // Filter to only show AI-generated assets
          let aiGeneratedAssets = response.assets.filter { $0.isAIGenerated }
          self?.userCreations = aiGeneratedAssets
          self?.isLoadingCreations = false
          self?.errorMessage = nil
          print(
            "ProfilePageViewModel: User creations updated - \(aiGeneratedAssets.count) AI-generated items out of \(response.assets.count) total assets"
          )
        }
      )
      .store(in: &cancellables)
  }

  /// Refresh all data
  func refreshData() {
    loadUserProfile()
    loadUserCreations()
  }

  /// Retry a failed creation task
  func retryCreationTask(item: CreationItem) {
    guard !isRetrying else {
      Logger.warning("ProfilePageViewModel: Retry already in progress")
      return
    }

    Logger.info("ProfilePageViewModel: Retry requested for item: \(item.id)")
    isRetrying = true
    errorMessage = nil

    switch item {
    case .imageTask(let task):
      retryImageTask(task)
    case .videoTask(let task):
      retryVideoTask(task)
    case .asset:
      Logger.warning("ProfilePageViewModel: Cannot retry completed asset")
      isRetrying = false
    }
  }

  /// Retry a failed image generation task
  private func retryImageTask(_ task: ImageGenerationTask) {
    // TODO: Implement image task retry logic
    // This would typically involve creating a new generation request
    // with the same parameters as the failed task
    Logger.info("ProfilePageViewModel: Retrying image task: \(task.taskId)")

    // Simulate retry delay
    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
      self?.isRetrying = false
      self?.errorMessage = "Image task retry not yet implemented. Please try creating a new task."
    }
  }

  /// Retry a failed video generation task
  private func retryVideoTask(_ task: VideoGenerationTask) {
    // TODO: Implement video task retry logic
    // This would typically involve creating a new generation request
    // with the same parameters as the failed task
    Logger.info("ProfilePageViewModel: Retrying video task: \(task.taskId)")

    // Simulate retry delay
    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
      self?.isRetrying = false
      self?.errorMessage = "Video task retry not yet implemented. Please try creating a new task."
    }
  }

  /// Delete a failed creation task
  func deleteCreationTask(item: CreationItem) {
    Logger.info("ProfilePageViewModel: Delete requested for item: \(item.id)")

    switch item {
    case .imageTask(let task):
      deleteImageTask(task)
    case .videoTask(let task):
      deleteVideoTask(task)
    case .asset(let asset):
      deleteAsset(asset)
    }
  }

  /// Delete a failed image generation task
  private func deleteImageTask(_ task: ImageGenerationTask) {
    Logger.info("ProfilePageViewModel: Deleting image task: \(task.taskId)")

    Task {
      do {
        try await designGenerationService.updateTaskStatus(taskId: task.taskId, status: .deleted)
        Logger.info("ProfilePageViewModel: Image task \(task.taskId) marked as deleted")
      } catch {
        await MainActor.run {
          self.errorMessage = "Failed to delete task: \(error.localizedDescription)"
        }
        Logger.error("ProfilePageViewModel: Failed to delete image task \(task.taskId) - \(error)")
      }
    }
  }

  /// Delete a failed video generation task
  private func deleteVideoTask(_ task: VideoGenerationTask) {
    Logger.info("ProfilePageViewModel: Deleting video task: \(task.taskId)")

    Task {
      do {
        try await videoGenerationService.updateTaskStatus(taskId: task.taskId, status: .deleted)
        Logger.info("ProfilePageViewModel: Video task \(task.taskId) marked as deleted")
      } catch {
        await MainActor.run {
          self.errorMessage = "Failed to delete task: \(error.localizedDescription)"
        }
        Logger.error("ProfilePageViewModel: Failed to delete video task \(task.taskId) - \(error)")
      }
    }
  }

  /// Delete an asset
  private func deleteAsset(_ asset: UserAsset) {
    Logger.info("ProfilePageViewModel: Deleting asset: \(asset.id)")

    Task {
      do {
        let response = try await assetService.deleteAsset(id: asset.id)
        Logger.info("ProfilePageViewModel: Asset \(asset.id) deleted - \(response.message)")
      } catch {
        await MainActor.run {
          self.errorMessage = "Failed to delete asset: \(error.localizedDescription)"
        }
        Logger.error("ProfilePageViewModel: Failed to delete asset \(asset.id) - \(error)")
      }
    }
  }

  // MARK: - Data Observation Methods

  /// Observe user assets
  private func observeUserAssets() {
    guard let userId = userService.currentUser?.id else { return }

    let query = AssetListQuery(page: 1, limit: 50)

    assetSubscription = assetService.observeUserAssets(query: query)
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          if case .failure(let error) = completion {
            self?.errorMessage = "Failed to load assets: \(error.localizedDescription)"
            Logger.error("ProfilePageViewModel: Failed to observe user assets - \(error)")
          }
        },
        receiveValue: { [weak self] response in
          self?.userAssets = response.assets
          self?.mergeAndSortCreations()
          Logger.debug("ProfilePageViewModel: User assets updated - \(response.assets.count) items")
        }
      )
  }

  /// Observe image generation tasks
  private func observeImageTasks() {
    guard let userId = userService.currentUser?.id else { return }

    imageTaskSubscription = designGenerationService.observeTaskList(userId: userId, limit: 50)
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          if case .failure(let error) = completion {
            self?.errorMessage = "Failed to load image tasks: \(error.localizedDescription)"
            Logger.error("ProfilePageViewModel: Failed to observe image tasks - \(error)")
          }
        },
        receiveValue: { [weak self] tasks in
          self?.imageTasks = tasks
          self?.mergeAndSortCreations()
          Logger.debug("ProfilePageViewModel: Image tasks updated - \(tasks.count) items")
        }
      )
  }

  /// Observe video generation tasks
  private func observeVideoTasks() {
    guard let userId = userService.currentUser?.id else { return }

    videoTaskSubscription = videoGenerationService.observeTaskList(userId: userId, limit: 50)
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          if case .failure(let error) = completion {
            self?.errorMessage = "Failed to load video tasks: \(error.localizedDescription)"
            Logger.error("ProfilePageViewModel: Failed to observe video tasks - \(error)")
          }
        },
        receiveValue: { [weak self] tasks in
          self?.videoTasks = tasks
          self?.mergeAndSortCreations()
          Logger.debug("ProfilePageViewModel: Video tasks updated - \(tasks.count) items")
        }
      )
  }

  /// Merge and sort all creation items
  private func mergeAndSortCreations() {
    var items: [CreationItem] = []

    // Add completed AI-generated assets
    items.append(contentsOf: userAssets.filter { $0.isAIGenerated }.map { .asset($0) })

    // Add only failed and in-progress image tasks (exclude succeeded and deleted tasks)
    let succeededImageTaskIds = Set(userAssets.compactMap { $0.sourceTaskId })
    let activeImageTasks = imageTasks.filter { task in
      // Only include tasks that are not succeeded, or succeeded tasks that don't have corresponding assets yet
      task.status != .succeeded || !succeededImageTaskIds.contains(task.taskId)
    }.filter { task in
      // Further filter to only include failed and in-progress tasks (exclude deleted)
      task.status == .failed || task.status == .processing || task.status == .pending
        || task.status == .no_result
    }.filter { task in
      // Exclude deleted tasks
      task.status != .deleted
    }
    items.append(contentsOf: activeImageTasks.map { .imageTask($0) })

    // Add only failed and in-progress video tasks (exclude succeeded and deleted tasks)
    let succeededVideoTaskIds = Set(userAssets.compactMap { $0.sourceTaskId })
    let activeVideoTasks = videoTasks.filter { task in
      // Only include tasks that are not succeeded, or succeeded tasks that don't have corresponding assets yet
      task.status != .succeeded || !succeededVideoTaskIds.contains(task.taskId)
    }.filter { task in
      // Further filter to only include failed and in-progress tasks (exclude deleted)
      task.status == .failed || task.status == .processing || task.status == .pending
        || task.status == .no_result
    }.filter { task in
      // Exclude deleted tasks
      task.status != .deleted
    }
    items.append(contentsOf: activeVideoTasks.map { .videoTask($0) })

    // Sort by creation time, with in-progress tasks prioritized
    creationItems = items.sorted { item1, item2 in
      let date1 = item1.createdAt ?? Date.distantPast
      let date2 = item2.createdAt ?? Date.distantPast

      // In-progress tasks have priority
      if !item1.isCompleted && item2.isCompleted { return true }
      if item1.isCompleted && !item2.isCompleted { return false }

      return date1 > date2
    }

    Logger.debug(
      "ProfilePageViewModel: Merged \(items.count) creation items - \(userAssets.filter { $0.isAIGenerated }.count) assets, \(activeImageTasks.count) image tasks, \(activeVideoTasks.count) video tasks"
    )
  }

  // MARK: - Private Methods

  /// Setup reactive observers
  private func setupObservers() {
    // Observe user changes
    userService.currentUserPublisher
      .receive(on: DispatchQueue.main)
      .sink { [weak self] user in
        self?.user = user
        // When user changes, start observing their creations
        if user != nil {
          self?.loadUserCreations()
        }
      }
      .store(in: &cancellables)

    // Observe subscription changes
    subscriptionStateManager.$currentSubscription
      .receive(on: DispatchQueue.main)
      .sink { [weak self] _ in
        // Trigger UI update when subscription changes
        self?.objectWillChange.send()
      }
      .store(in: &cancellables)
  }
}
