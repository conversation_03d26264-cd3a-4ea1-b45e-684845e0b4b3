//
//  CreateDetailPageViewModel.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import Foundation
import AVKit

@MainActor
final class CreateDetailPageViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var isDownloading = false
    @Published var showingDownloadAlert = false
    @Published var downloadMessage = ""
    @Published var isPlaying = false
    @Published var showRecreateView = false
    @Published var likeCount: Int = 0
    @Published var isLiked = false
    
    // MARK: - Private Properties
    
    private let asset: UserAsset
    private let mediaService: MediaService
    private var player: AVPlayer?
    
    // MARK: - Initialization
    
    init(
        asset: UserAsset,
        mediaService: MediaService = AppDependencyContainer.shared.mediaModule.mediaService
    ) {
        self.asset = asset
        self.mediaService = mediaService
        self.likeCount = asset.likeCount
    }
    
    // MARK: - Public Methods
    
    func handleDownload() {
        Task {
            isDownloading = true
            
            guard let url = URL(string: asset.url) else {
                downloadMessage = "Invalid URL"
                showingDownloadAlert = true
                isDownloading = false
                return
            }
            
            let mediaType: MediaType = asset.isVideo ? .video : .image
            let result = await mediaService.saveToPhotos(from: url, mediaType: mediaType)
            
            isDownloading = false
            
            switch result {
            case .success:
                downloadMessage = "Successfully saved to Photos"
            case .failure(let error):
                downloadMessage = error.localizedDescription
            }
            
            showingDownloadAlert = true
        }
    }
    
    func handleLike() {
        // Toggle like state
        isLiked.toggle()
        likeCount += isLiked ? 1 : -1
        
        // TODO: Call like API through UseCase
        Logger.info("Like tapped for asset: \(asset.id), new count: \(likeCount)")
    }
    
    func handleShare() {
        // TODO: Implement share functionality through UseCase
        Logger.info("Share tapped for asset: \(asset.id)")
    }
    
    func handleRecreate() {
        Logger.info("Recreate tapped for asset: \(asset.id)")
        showRecreateView = true
    }
    
    func handleEnhance() {
        // TODO: Implement AI enhancement functionality
        Logger.info("Enhance tapped for asset: \(asset.id)")
    }
    
    func handleMore() {
        // TODO: Implement more actions (share, delete, etc.)
        Logger.info("More tapped for asset: \(asset.id)")
    }
    
    // MARK: - Media Player Methods
    
    func setupMediaPlayer() {
        if asset.isVideo, let url = URL(string: asset.url) {
            player = AVPlayer(url: url)
            
            // 设置自动循环播放
            setupLooping()
            
            // 自动开始播放
            player?.play()
            isPlaying = true
        }
    }
    
    func cleanupMediaPlayer() {
        player?.pause()
        
        // 移除通知观察者
        NotificationCenter.default.removeObserver(
            self,
            name: .AVPlayerItemDidPlayToEndTime,
            object: player?.currentItem
        )
        
        player = nil
    }
    
    func togglePlayback() {
        guard let player = player else { return }
        
        if isPlaying {
            player.pause()
        } else {
            player.play()
        }
        isPlaying.toggle()
    }
    
    // MARK: - Computed Properties
    
    var isVideo: Bool {
        return asset.isVideo
    }
    
    var isImage: Bool {
        return asset.isImage
    }
    
    var displayName: String {
        return getUserDisplayName()
    }
    
    var videoPlayer: AVPlayer? {
        return player
    }
    
    // MARK: - Private Methods
    
    private func setupLooping() {
        guard let player = player else { return }
        
        // 监听播放结束通知
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: player.currentItem,
            queue: .main
        ) { _ in
            // 重新播放
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                player.seek(to: .zero)
                player.play()
            }
        }
    }
    
    private func getUserDisplayName() -> String {
        // TODO: Fetch actual username from userId
        // For now, return a placeholder based on userId
        return "User\(asset.userId.prefix(6))"
    }
}