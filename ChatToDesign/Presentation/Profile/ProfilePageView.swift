//
//  ProfilePageView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/3.
//

import Kingfisher
import SwiftUI

/// Profile page view matching Figma design
struct ProfilePageView: View {
  @StateObject private var viewModel = ProfilePageViewModel()
  @State private var showSettings = false
  @State private var showPaywall = false
  @State private var selectedFailedItem: CreationItem?

  var body: some View {
    NavigationView {
      ZStack {
        // Background
        Color(red: 0.035, green: 0.035, blue: 0.043)  // #09090b
          .ignoresSafeArea()

        ScrollView {
          VStack(spacing: 0) {
            // Navigation Header
            navigationHeader

            // Profile Content
            VStack(spacing: 24) {
              // User Profile Card
              userProfileCard

              // My Creations Section
              myCreationsSection
            }
            .padding(.top, 24)

            // 底部间距，为底部导航栏留出空间
            Spacer()
              .frame(height: 120)
          }
        }

        // Task Failure Alert (全屏显示)
        if let failedItem = selectedFailedItem {
          TaskFailureAlert(
            item: failedItem,
            onRetry: {
              viewModel.retryCreationTask(item: failedItem)
            },
            onDelete: {
              viewModel.deleteCreationTask(item: failedItem)
            },
            onDismiss: {
              selectedFailedItem = nil
            }
          )
        }
      }
      .navigationBarHidden(true)
      .sheet(isPresented: $showSettings) {
        ProfileSettingsView()
      }
      .fullScreenCover(isPresented: $showPaywall) {
        PaywallView(
          options: .fullScreen(offeringId: nil),
          onDismiss: {
            showPaywall = false
          },
          onPurchaseCompleted: { subscription in
            Logger.info("ProfilePageView: 购买完成 - \(subscription.tier.displayName)")
            showPaywall = false
          },
          onRestoreCompleted: { subscription in
            if let subscription = subscription {
              Logger.info("ProfilePageView: 恢复购买完成 - \(subscription.tier.displayName)")
            } else {
              Logger.info("ProfilePageView: 恢复购买完成 - 无订阅")
            }
            showPaywall = false
          }
        )
      }
      .onAppear {
        viewModel.loadUserProfile()
        viewModel.loadUserCreations()
      }
    }
  }

  // MARK: - Navigation Header

  private var navigationHeader: some View {
    VStack(spacing: 0) {

      // Navigation Bar
      HStack {
        // Left side - Title
        HStack(spacing: 4) {
          Text("Profile")
            .font(.custom("Inter", size: 20).weight(.semibold))
            .foregroundColor(.white)
        }

        Spacer()

        // Right side - Badge and Settings
        HStack(spacing: 12) {
          // Credits Badge
          HStack(spacing: 2) {
            Image(systemName: "sparkles")
              .font(.system(size: 16))
              .foregroundColor(.black)

            Text("200")
              .font(.custom("Geist", size: 12).weight(.semibold))
              .foregroundColor(.black)
          }
          .padding(.horizontal, 10)
          .padding(.vertical, 2)
          .background(Color.white)
          .clipShape(Capsule())

          // Settings Button
          Button(action: {
            showSettings = true
          }) {
            Image(systemName: "gearshape")
              .font(.system(size: 20))
              .foregroundColor(.white)
          }
          .frame(width: 40, height: 40)
        }
      }
      .padding(.horizontal, 24)
      .padding(.vertical, 8)
      .background(Color(red: 0.094, green: 0.094, blue: 0.106))  // #18181b
    }
  }

  // MARK: - User Profile Card

  private var userProfileCard: some View {
    VStack(spacing: 10) {
      // User Profile Section
      HStack(spacing: 16) {
        if let photoURL = viewModel.user?.photoURL {
          KFImage(photoURL)
            .placeholder {
              Circle()
                .fill(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
                .overlay(
                  Image(systemName: "person.fill")
                    .font(.system(size: 32))
                    .foregroundColor(.white)
                )
            }
            .retry(maxCount: 3)
            .fade(duration: 0.25)
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(width: 64, height: 64)
            .clipShape(Circle())
        } else {
          Circle()
            .fill(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
            .frame(width: 64, height: 64)  // 统一头像大小为64x64
            .overlay(
              Image(systemName: "person.fill")
                .font(.system(size: 32))
                .foregroundColor(.white)
            )
        }

        // User Details
        VStack(alignment: .leading, spacing: 4) {
          Text(viewModel.user?.displayName ?? "userName")
            .font(.custom("Inter", size: 16).weight(.semibold))  // text-base/semibold
            .foregroundColor(.white)

          Text("ID \(viewModel.user?.id.suffix(7) ?? "1234566")")
            .font(.custom("Inter", size: 14))  // text-sm/normal
            .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // Muted Foreground: #a1a1aa
        }

        Spacer()
      }
      .padding(.horizontal, 24)  // 给用户信息部分添加水平padding

      subscriptionCard.padding(.vertical, 16)
    }
    .padding(.horizontal, 24)
    .padding(.vertical, 24)
    .frame(maxWidth: .infinity, alignment: .leading)
    .background(Color(red: 0.153, green: 0.153, blue: 0.165))  // Muted: #27272a
    .clipShape(RoundedRectangle(cornerRadius: 16))  // radius/2xl: 16px
  }

  // MARK: - Subscription Card

  private var subscriptionCard: some View {
    HStack(spacing: 12) {  // spacing/3: 12px
      VStack(alignment: .leading, spacing: 4) {  // spacing/1: 4px
        Text("Get Pro")
          .font(.custom("Inter", size: 16).weight(.semibold))  // text-base/semibold
          .foregroundColor(.white)

        Text("Unlock all AI capabilities!")
          .font(.custom("Inter", size: 14))  // text-sm/normal
          .foregroundColor(Color.white.opacity(0.8))
      }

      Spacer()

      // if !viewModel.isPremiumUser {
        Button(action: {
          showPaywall = true
        }) {
          HStack(spacing: 4) {  // spacing/1: 4px
            Text("Upgrade")
              .font(.custom("Inter", size: 14).weight(.semibold))  // text-sm/semibold
              .foregroundColor(.white)

            Image(systemName: "bolt.fill")
              .font(.system(size: 14))
              .foregroundColor(.white)
          }
          .padding(.horizontal, 16)  // spacing/4: 16px
          .padding(.vertical, 8)
          .background(
            LinearGradient(
              colors: [
                Color(red: 1.0, green: 0.2, blue: 0.2),  // 红色
                Color(red: 1.0, green: 0.6, blue: 0.0),  // 橙色
              ],
              startPoint: .leading,
              endPoint: .trailing
            )
          )
          .clipShape(Capsule())
        }
        .buttonStyle(PlainButtonStyle())
      // }
    }
    .padding(24)  // spacing/6: 24px padding
    .background(
      LinearGradient(
        colors: [
          Color(red: 0.2, green: 0.3, blue: 0.8),  // 深蓝色
          Color(red: 0.3, green: 0.5, blue: 0.9),  // 浅蓝色
        ],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
      )
    )
    .clipShape(RoundedRectangle(cornerRadius: 16))  // radius/2xl: 16px
  }

  // MARK: - My Creations Section

  private var myCreationsSection: some View {
    VStack(alignment: .leading, spacing: 16) {
      // Section Title
      HStack {
        Text("My Creations")
          .font(.custom("Inter", size: 16).weight(.medium))
          .foregroundColor(.white)

        Spacer()
      }
      .padding(.horizontal, 24)

      // Creations Grid
      creationsGrid
    }
  }

  // MARK: - Creations Grid

  private var creationsGrid: some View {
    let screenWidth = UIScreen.main.bounds.width
    let padding: CGFloat = 24
    let spacing: CGFloat = 8
    let itemWidth = (screenWidth - padding * 2 - spacing) / 2

    return LazyVStack(spacing: 8) {
      ForEach(Array(stride(from: 0, to: viewModel.creationItems.count, by: 2)), id: \.self) {
        index in
        HStack(spacing: spacing) {
          CreationItemView(
            item: viewModel.creationItems[index],
            width: itemWidth,
            onRetry: { item in
              viewModel.retryCreationTask(item: item)
            },
            onFailedTaskTap: { item in
              selectedFailedItem = item
            }
          )

          if index + 1 < viewModel.creationItems.count {
            CreationItemView(
              item: viewModel.creationItems[index + 1],
              width: itemWidth,
              onRetry: { item in
                viewModel.retryCreationTask(item: item)
              },
              onFailedTaskTap: { item in
                selectedFailedItem = item
              }
            )
          } else {
            Spacer()
              .frame(width: itemWidth)
          }
        }
      }
    }
    .padding(.horizontal, 24)
  }
}

#Preview {
  ProfilePageView()
}
