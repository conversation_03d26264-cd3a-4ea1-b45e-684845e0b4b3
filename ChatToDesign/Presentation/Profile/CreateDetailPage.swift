//
//  CreateDetailPage.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/3.
//

import AVKit
import Kingfisher
import SwiftUI

/// Detail page for viewing user creations (images/videos)
struct CreateDetailPage: View {
  let asset: UserAsset
  @Environment(\.dismiss) private var dismiss
  @StateObject private var viewModel: CreateDetailPageViewModel
  
  init(asset: UserAsset) {
    self.asset = asset
    self._viewModel = StateObject(
      wrappedValue: CreateDetailPageViewModel(asset: asset)
    )
  }

  var body: some View {
    GeometryReader { geometry in
      VStack(spacing: 0) {
        customNavigationBar

        // 主内容区域
        ZStack {
          // 背景色/渐变
          Color.black

          // 图片/视频内容（正确尺寸）
          mainContentView

          // 右侧悬浮按钮
          VStack {
            Spacer()
            HStack {
              Spacer()
              LikeShareActionsView(
                likeCount: viewModel.likeCount,
                onLike: viewModel.handleLike,
                onShare: viewModel.handleShare
              )
              .padding(.trailing, 16)
            }
          }
        }

        bottomActionArea
      }
    }
    .background(blurredBackground)
    .navigationBarHidden(true)
    .onAppear {
      viewModel.setupMediaPlayer()
    }
    .onDisappear {
      viewModel.cleanupMediaPlayer()
    }
    .alert("Download", isPresented: $viewModel.showingDownloadAlert) {
      Button("OK") {}
    } message: {
      Text(viewModel.downloadMessage)
    }
    .fullScreenCover(isPresented: $viewModel.showRecreateView) {
      let params = AssetToTemplateAdapter.convert(from: asset)
      
      CreateVideoFromTemplatePage(
        templateId: params.templateId,
        demoUrl: params.demoUrl,
        inputImageUrls: params.inputImageUrls,
        inputImageLimits: params.inputImageUrls.count,
        inputGuide: params.inputGuide,
        demoPrompt: params.demoPrompt
      )
    }
  }

  // MARK: - Layout Components

  private var blurredBackground: some View {
    KFImage(URL(string: asset.thumbnailUrl ?? asset.url))
      .resizable()
      .aspectRatio(contentMode: .fill)
      .scaleEffect(1.2)
      .blur(radius: 20)
      .opacity(0.3)
      .ignoresSafeArea()
  }

  private var customNavigationBar: some View {
    HStack {
      Button(action: { dismiss() }) {
        Image(systemName: "chevron.left")
          .font(.system(size: 22, weight: .semibold))
          .foregroundColor(.white)
          .shadow(color: .black.opacity(0.8), radius: 2, x: 0, y: 1)
          .background(
            Circle()
              .fill(Color.black.opacity(0.3))
              .frame(width: 40, height: 40)
          )
      }
      Spacer()
    }
    .padding(.horizontal, 24)
    .padding(.vertical, 12)
  }

  private var mainContentView: some View {
    VStack {
      // Spacer()

      if viewModel.isVideo {
        videoPlayerView
          .aspectRatio(contentMode: .fill)
          .frame(maxWidth: UIScreen.main.bounds.width, maxHeight: UIScreen.main.bounds.height)
          .clipped()
          .clipShape(RoundedRectangle(cornerRadius: 16))
      } else {
        imageView
          .aspectRatio(contentMode: .fill)
          .frame(maxWidth: UIScreen.main.bounds.width, maxHeight: UIScreen.main.bounds.height)
          .clipped()
          .clipShape(RoundedRectangle(cornerRadius: 16))
      }

      // Spacer()
    }
  }

  private var bottomActionArea: some View {
    VStack(spacing: 0) {
      UserInfoView(
        username: viewModel.displayName,
        prompt: asset.generationPrompt
      )

      ConnectedActionButtonsView(
        isDownloading: viewModel.isDownloading,
        onRecreate: viewModel.handleRecreate,
        onEnhance: viewModel.handleEnhance,
        onDownload: viewModel.handleDownload,
        onMore: viewModel.handleMore
      )
    }
    .background(Color.black.opacity(0.4))
  }

  // MARK: - Image View

  private var imageView: some View {
    KFImage(URL(string: asset.url))
      .placeholder {
        Rectangle()
          .fill(Color.gray.opacity(0.3))
          .overlay(
            VStack(spacing: 12) {
              ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)

              Text("Loading image...")
                .font(.custom("Inter", size: 14))
                .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
            }
          )
      }
      .retry(maxCount: 3)
      .fade(duration: 0.25)
      .resizable()
      .aspectRatio(contentMode: .fit)
  }

  // MARK: - Video Player View

  private var videoPlayerView: some View {
    Group {
      if let player = viewModel.videoPlayer {
        VideoPlayer(player: player)
          .onTapGesture {
            viewModel.togglePlayback()
          }
      } else {
        Rectangle()
          .fill(Color.gray.opacity(0.3))
          .overlay(
            VStack(spacing: 12) {
              ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)

              Text("Loading video...")
                .font(.custom("Inter", size: 14))
                .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
            }
          )
      }
    }
  }

}

// MARK: - Action Button Component

struct CreateDetailActionButton: View {
  let icon: String?
  let iconName: String?
  let title: String
  var isLoading: Bool = false
  let action: () -> Void

  // Convenience initializers
  init(icon: String, title: String, isLoading: Bool = false, action: @escaping () -> Void) {
    self.icon = icon
    self.iconName = nil
    self.title = title
    self.isLoading = isLoading
    self.action = action
  }

  init(iconName: String, title: String, isLoading: Bool = false, action: @escaping () -> Void) {
    self.icon = nil
    self.iconName = iconName
    self.title = title
    self.isLoading = isLoading
    self.action = action
  }

  var body: some View {
    Button(action: action) {
      VStack(spacing: 8) {
        // Icon
        Group {
          if isLoading {
            ProgressView()
              .progressViewStyle(
                CircularProgressViewStyle(tint: Color(red: 0.631, green: 0.631, blue: 0.667))
              )
              .scaleEffect(0.8)
          } else if let iconName = iconName {
            // Custom image asset
            Image(iconName)
              .resizable()
              .renderingMode(.template)
              .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
              .frame(width: 24, height: 24)
          } else if let icon = icon {
            // System icon
            Image(systemName: icon)
              .font(.system(size: 24))
              .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.667))  // #a1a1aa
          }
        }
        .frame(width: 24, height: 24)

        // Title
        Text(title)
          .font(.custom("Inter", size: 14).weight(.semibold))
          .foregroundColor(.white)
      }
      .frame(maxWidth: .infinity)
      .frame(height: 80)
      .background(Color(red: 0.157, green: 0.157, blue: 0.188))  // #27272a
      .clipShape(RoundedRectangle(cornerRadius: 16))
    }
    .disabled(isLoading)
  }
}
