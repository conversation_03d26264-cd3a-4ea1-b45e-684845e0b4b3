//
//  UserProfileDetailView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//

import Kingfisher
import SwiftUI

struct UserProfileDetailView: View {
  let user: User?
  @Environment(\.dismiss) private var dismiss
  @Environment(\.colorScheme) private var colorScheme
  @State private var showingLogoutAlert = false
  @State private var showPaywall = false

  // Dependencies
  private let subscriptionStateManager: SubscriptionStateManager

  // MARK: - Initialization

  init(
    user: User?,
    subscriptionStateManager: SubscriptionStateManager = AppDependencyContainer.shared
      .subscriptionModule.subscriptionStateManager
  ) {
    self.user = user
    self.subscriptionStateManager = subscriptionStateManager
  }

  // Mock data for demonstration
  private var userPoints: Int {
    2440  // Based on the screenshot
  }

  private var subscriptionStatus: String {
    return subscriptionStateManager.currentTier.displayName
  }

  private var currentLanguage: String {
    "English"
  }

  var body: some View {
    VStack(spacing: 0) {
      // Points display
      pointsHeaderView

      // User info section
      userInfoSection

      // View Profile button
      // viewProfileButton

      // Settings list
      settingsList

      Spacer()

      // Footer
      footerView
    }
    .background(Color(.systemGroupedBackground))
    .navigationTitle("Profile")
    .navigationBarTitleDisplayMode(.inline)
    .alert("Log Out", isPresented: $showingLogoutAlert) {
      Button("Cancel", role: .cancel) {}
      Button("Log Out", role: .destructive) {
        handleLogout()
      }
    } message: {
      Text("Are you sure you want to log out?")
    }
    .fullScreenCover(isPresented: $showPaywall) {
      PaywallView(
        options: .fullScreen(offeringId: nil),
        onDismiss: {
          showPaywall = false
        },
        onPurchaseCompleted: { subscription in
          Logger.info("UserProfileDetailView: 购买完成 - \(subscription.tier.displayName)")
          showPaywall = false
        },
        onRestoreCompleted: { subscription in
          if let subscription = subscription {
            Logger.info("UserProfileDetailView: 恢复购买完成 - \(subscription.tier.displayName)")
          } else {
            Logger.info("UserProfileDetailView: 恢复购买完成 - 无订阅")
          }
          showPaywall = false
        }
      )
    }
  }

  // MARK: - Points Header View

  private var pointsHeaderView: some View {
    HStack {
      Spacer()

      // Points display
      HStack(spacing: 4) {
        Text("\(userPoints)")
          .font(.system(size: 16, weight: .semibold))
          .foregroundColor(.white)
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 8)
      .background(
        LinearGradient(
          gradient: Gradient(colors: [
            Color(red: 1.0, green: 0.4, blue: 0.4),
            Color(red: 0.8, green: 0.3, blue: 0.8),
          ]),
          startPoint: .leading,
          endPoint: .trailing
        )
      )
      .cornerRadius(16)
    }
    .padding(.horizontal, 16)
    .padding(.top, 16)
  }

  // MARK: - User Info Section

  private var userInfoSection: some View {
    HStack(spacing: 16) {
      // User avatar
      if let photoURL = user?.photoURL {
        KFImage(photoURL)
          .placeholder {
            Circle()
              .fill(Color(.systemGray4))
              .overlay(
                Image(systemName: "person.fill")
                  .font(.system(size: 24))
                  .foregroundColor(.white)
              )
          }
          .retry(maxCount: 3)
          .fade(duration: 0.25)
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: 60, height: 60)
          .clipShape(Circle())
      } else {
        Circle()
          .fill(Color(.systemGray4))
          .frame(width: 60, height: 60)
          .overlay(
            Image(systemName: "person.fill")
              .font(.system(size: 24))
              .foregroundColor(.white)
          )
      }

      // User name and handle
      VStack(alignment: .leading, spacing: 4) {
        Text(user?.displayName ?? "Guest User")
          .font(.system(size: 20, weight: .semibold))
          .foregroundColor(.primary)

        Text("@\(generateUsername())")
          .font(.system(size: 16, weight: .regular))
          .foregroundColor(.secondary)
      }

      Spacer()
    }
    .padding(.horizontal, 16)
    .padding(.top, 24)
  }

  // MARK: - View Profile Button

  private var viewProfileButton: some View {
    Button(action: {
      // Handle view profile action
    }) {
      Text("View Profile")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.primary)
        .frame(maxWidth: .infinity)
        .frame(height: 44)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
    .padding(.horizontal, 16)
    .padding(.top, 16)
  }

  // MARK: - Settings List

  private var settingsList: some View {
    VStack(spacing: 0) {
      // My Subscription
      SettingsRowView(
        icon: "star.circle",
        title: "My Subscription",
        trailing: {
          HStack(spacing: 8) {
            Text(subscriptionStatus)
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.secondary)

            Image(systemName: "chevron.right")
              .font(.system(size: 12, weight: .medium))
              .foregroundColor(.secondary)
          }
        },
        action: {
          showPaywall = true
        }
      )

      Divider()
        .padding(.leading, 56)

      // Language
      SettingsRowView(
        icon: "globe",
        title: "Language",
        trailing: {
          Text(currentLanguage)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.secondary)
        },
        action: {}
      )

      Divider()
        .padding(.leading, 56)

      // AI Video Effects
      NavigationLink(destination: AIVideoEffectsView()) {
        HStack(spacing: 16) {
          // Icon
          Image(systemName: "play.rectangle")
            .font(.system(size: 18, weight: .medium))
            .foregroundColor(.primary)
            .frame(width: 24, height: 24)

          // Title
          Text("AI Video Effects")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.primary)

          Spacer()

          // Trailing chevron
          Image(systemName: "chevron.right")
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(.secondary)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .contentShape(Rectangle())
      }
      .buttonStyle(PlainButtonStyle())

      Divider()
        .padding(.leading, 56)

      // Support
      SettingsRowView(
        icon: "phone",
        title: "Support",
        trailing: {
          Image(systemName: "chevron.right")
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(.secondary)
        },
        action: {}
      )

      Divider()
        .padding(.leading, 56)

      // Log out
      SettingsRowView(
        icon: "arrow.right.square",
        title: "Log out",
        titleColor: .red,
        action: {
          showingLogoutAlert = true
        }
      )
    }
    .background(Color(.systemBackground))
    .cornerRadius(12)
    .padding(.horizontal, 16)
    .padding(.top, 24)
  }

  // MARK: - Footer View

  private var footerView: some View {
    VStack(spacing: 8) {
      Text("Picadabra • 2025 • v1.0.0")
        .font(.system(size: 12, weight: .medium))
        .foregroundColor(.secondary)
    }
    .padding(.bottom, 4)
  }

  // MARK: - Helper Methods

  private func generateUsername() -> String {
    guard let displayName = user?.displayName, !displayName.isEmpty else {
      return "guestuser"
    }

    // Generate a simple username from display name
    let cleanName = displayName.lowercased()
      .replacingOccurrences(of: " ", with: "")
      .replacingOccurrences(of: "[^a-z0-9]", with: "", options: .regularExpression)

    return cleanName.isEmpty ? "user" : cleanName
  }

  private func handleLogout() {
    Task {
      do {
        // Use the auth service to sign out
        try await AppDependencyContainer.shared.authModule.authService.signOut()

        // Dismiss the view on main thread
        await MainActor.run {
          dismiss()
        }
      } catch {
        // Handle logout error
        Logger.error("Failed to logout: \(error.localizedDescription)")

        // Even if logout fails, clear local state and dismiss
        await MainActor.run {
          UserStateManager.shared.clearCurrentUser()
          dismiss()
        }
      }
    }
  }
}

// MARK: - Settings Row View

struct SettingsRowView<Trailing: View>: View {
  let icon: String
  let title: String
  let titleColor: Color
  let trailing: () -> Trailing
  let action: () -> Void

  init(
    icon: String,
    title: String,
    titleColor: Color = .primary,
    @ViewBuilder trailing: @escaping () -> Trailing = { EmptyView() },
    action: @escaping () -> Void
  ) {
    self.icon = icon
    self.title = title
    self.titleColor = titleColor
    self.trailing = trailing
    self.action = action
  }

  var body: some View {
    Button(action: action) {
      HStack(spacing: 16) {
        // Icon
        Image(systemName: icon)
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(.primary)
          .frame(width: 24, height: 24)

        // Title
        Text(title)
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(titleColor)

        Spacer()

        // Trailing content
        trailing()
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
      .contentShape(Rectangle())
    }
    .buttonStyle(PlainButtonStyle())
  }
}

#Preview {
  UserProfileDetailView(
    user: User.create(
      id: "1",
      email: "<EMAIL>",
      displayName: "Sam lee",
      photoURL: URL(string: "https://example.com/avatar.jpg")
    )
  )
}
