//
//  UserProfileView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/1.
//

import Kingfisher
import SwiftUI

/// 用户资料视图
struct UserProfileView: View {
  let user: User?
  let showName: Bool
  let size: CGFloat

  init(user: User?, showName: Bool = false, size: CGFloat = 32) {
    self.user = user
    self.showName = showName
    self.size = size
  }

  var body: some View {
    HStack(spacing: 10) {
      // 用户头像
      if let photoURL = user?.photoURL {
        KFImage(photoURL)
          .placeholder {
            Circle()
              .fill(Color(.systemGray4))
              .overlay(
                Image(systemName: "person.fill")
                  .font(.system(size: size * 0.5))
                  .foregroundColor(.white)
              )
          }
          .retry(maxCount: 3)
          .fade(duration: 0.25)
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: size, height: size)
          .clipShape(Circle())
      } else {
        Circle()
          .fill(Color(.systemGray4))
          .frame(width: size, height: size)
          .overlay(
            Image(systemName: "person.fill")
              .font(.system(size: size * 0.5))
              .foregroundColor(.white)
          )
      }

      // 用户名 (可选显示)
      if showName {
        if let displayName = user?.displayName, !displayName.isEmpty {
          Text(displayName)
            .font(.headline)
            .lineLimit(1)
        } else {
          Text("未登录")
            .font(.headline)
            .foregroundColor(.gray)
        }
      }
    }
  }
}
