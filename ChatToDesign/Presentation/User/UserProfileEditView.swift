// UserProfileEditView.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/14.
//

import PhotosUI
import SwiftUI

/// 用户资料编辑视图
/// 提供用户编辑个人资料的界面
struct UserProfileEditView: View {
  @StateObject private var viewModel = UserProfileEditViewModel()
  @Environment(\.presentationMode) var presentationMode
  @State private var photoPickerItem: PhotosPickerItem?

  // MARK: - Subviews
//  private var profileImageSection: some View {
//    VStack {
//        if let image = $viewModel.selectedImage {
//        Image(uiImage: image)
//          .resizable()
//          .scaledToFill()
//          .frame(width: 120, height: 120)
//          .clipShape(Circle())
//          .overlay(Circle().stroke(Color.primary, lineWidth: 1))
//      } else {
//        Image(systemName: "person.circle.fill")
//          .resizable()
//          .foregroundColor(.gray)
//          .frame(width: 120, height: 120)
//          .clipShape(Circle())
//          .overlay(Circle().stroke(Color.primary, lineWidth: 1))
//      }
//
//      PhotosPicker(
//        selection: $photoPickerItem,
//        matching: .images
//      ) {
//        Text("更换头像")
//          .foregroundColor(.blue)
//      }
//      .onChange(of: photoPickerItem) { newValue in
//        Task {
//          if let data = try? await newValue?.loadTransferable(type: Data.self),
//            let image = UIImage(data: data)
//          {
//            viewModel.selectedImage = image
//          }
//        }
//      }
//    }
//  }

  private var formSection: some View {
    Form {
      // 基本信息
      Section(header: Text("基本信息")) {
        TextField("显示名称", text: $viewModel.displayName)
        TextField("个人简介", text: $viewModel.bio)
          .lineLimit(3)
        TextField("所在地", text: $viewModel.location)
      }
      
      // 偏好设置
      Section(header: Text("偏好设置")) {
        Picker("主题", selection: $viewModel.theme) {
          Text("系统").tag("system")
          Text("浅色").tag("light")
          Text("深色").tag("dark")
        }
        
        Picker("语言", selection: $viewModel.language) {
          Text("简体中文").tag("zh-CN")
          Text("English").tag("en-US")
        }
        
        Toggle("通知", isOn: $viewModel.notificationsEnabled)
      }
      
      // 成功消息
      if let successMessage = viewModel.successMessage {
        Section {
          Text(successMessage)
            .foregroundColor(.green)
        }
      }
      
      // 错误消息
      if let errorMessage = viewModel.errorMessage {
        Section {
          Text(errorMessage)
            .foregroundColor(.red)
        }
      }
      
      // 保存按钮
      Section {
        Button("保存更改") {
          viewModel.saveProfile()
        }
        .frame(maxWidth: .infinity, alignment: .center)
        .disabled(viewModel.isLoading)
      }
    }
  }

  // MARK: - Body
  var body: some View {
    NavigationView {
      VStack(spacing: 30) {
//        profileImageSection
//          .padding(.top, 20)

        formSection
      }
      .navigationTitle("编辑资料")
      .navigationBarItems(
        leading: Button("关闭") {
          presentationMode.wrappedValue.dismiss()
        },
        trailing: viewModel.isLoading ? AnyView(ProgressView()) : AnyView(EmptyView())
      )
    }
  }
}
