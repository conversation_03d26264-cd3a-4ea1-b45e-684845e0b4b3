// UserProfileEditViewModel.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/3/14.
//

import Combine
import Foundation
import SwiftUI

/// 用户资料编辑视图模型
/// 处理用户资料编辑的状态和逻辑
final class UserProfileEditViewModel: ObservableObject {
    // MARK: - 状态
    
    /// 当前用户
    @Published var user: User?
    
    /// 显示名称
    @Published var displayName: String = ""
    
    /// 个人简介
    @Published var bio: String = ""
    
    /// 位置
    @Published var location: String = ""
    
    /// 主题偏好
    @Published var theme: String = "system"
    
    /// 语言偏好
    @Published var language: String = "zh-CN"
    
    /// 是否开启通知
    @Published var notificationsEnabled: Bool = true
    
    /// 是否正在加载
    @Published var isLoading = false
    
    /// 成功消息
    @Published var successMessage: String?
    
    /// 错误消息
    @Published var errorMessage: String?

    /// 头像
    @Published var photoURL: URL?
    
    // MARK: - 用户资料字段
        
    // MARK: - 数据源
    
    /// 用户资料服务
    private let userService: UserService
    
    /// 取消令牌集合
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    
    /// 初始化用户资料编辑视图模型
    /// - Parameter userService: 用户服务
    init(userService: UserService = AppDependencyContainer.shared.userModule.userService) {
        self.userService = userService
        
        // 监听用户信息变化
        setupObservers()
        
        // 加载当前用户资料
        loadUserData()
    }
    
    // MARK: - 生命周期
    
    /// 设置观察者
    private func setupObservers() {
        // 监听用户变化
        userService.currentUserPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] user in
                self?.updateUserFields(user)
            }
            .store(in: &cancellables)
    }
    
    /// 加载当前用户数据
    func loadUserData() {
        self.user = userService.currentUser
        updateUserFields(userService.currentUser)
    }
    
    /// 更新用户字段
    /// - Parameter user: 用户对象
    private func updateUserFields(_ user: User?) {
        guard let user = user else { return }
        
        self.displayName = user.displayName
        self.bio = user.bio ?? ""
        self.location = user.location ?? ""
        self.theme = user.preferences.theme
        self.language = user.preferences.language
        self.notificationsEnabled = user.preferences.notificationsEnabled
        self.photoURL = user.photoURL  ?? nil
    }
    
    // MARK: - 保存资料
    
    /// 保存用户资料
    func saveProfile() {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                
                let preferences = UserPreferences(
                    theme: theme,
                    language: language,
                    notificationsEnabled: notificationsEnabled
                )
                
                try await userService.updatePreferences(preferences: preferences)
                
            } catch {
                errorMessage = "保存失败: \(error.localizedDescription)"
            }
        }
    }
}
