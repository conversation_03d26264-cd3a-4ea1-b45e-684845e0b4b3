//
//  CreateVideoPageView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import PhotosUI
import SwiftUI

/// Create Video 页面主视图
/// 展示视频创建的详细界面
public struct CreateVideoPageView: View {

  // MARK: - Properties

  @StateObject private var viewModel = CreateVideoPageViewModel()
  @Environment(\.dismiss) private var dismiss
  @Environment(\.colorScheme) private var colorScheme

  // MARK: - Body

  public var body: some View {
    ZStack {
      // 背景
      Color.black
        .ignoresSafeArea()

      VStack(spacing: 0) {
        // 导航栏
        CreateNavigationBar(
          title: "Create Video",
          credits: 200,
          showCredits: true,
          onClose: {
            dismiss()
          }
        )

        // 内容区域
        contentSection

        Spacer()

        // 创建按钮
        createButtonSection
      }

      // Loading overlay
      if viewModel.isLoading || viewModel.loadingState == .completed {
        LoadingOverlayView(
          loadingState: viewModel.loadingState,
          previewImageUrl: previewImage,
          generatedVideoUrl: viewModel.generatedVideoUrl,
          onCancel: {
            viewModel.cancelVideoCreation()
          },
          onViewProfile: {
            // 重置 ViewModel 状态
            viewModel.viewInProfile()

            // 先关闭当前页面
            dismiss()

            // 延迟发送导航通知，确保页面关闭动画完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
              NotificationCenter.default.post(name: .navigateToProfile, object: nil)
            }
          },
          onClose: {
            dismiss()
          }
        )
        .transition(.opacity)
        .animation(.easeInOut(duration: 0.3), value: viewModel.isLoading)
      }
    }
    .preferredColorScheme(.dark)
    .navigationBarHidden(true)
    .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
      Button("OK") {
        viewModel.errorMessage = nil
      }
    } message: {
      if let errorMessage = viewModel.errorMessage {
        Text(errorMessage)
      }
    }
    .sheet(isPresented: $viewModel.isUserAssetSelectionPresented) {
      UserAssetSelectionView(
        onAssetsSelected: { assets in
          viewModel.handleSelectedAssets(assets)
        },
        onDismiss: {
          viewModel.isUserAssetSelectionPresented = false
        },
        maxSelectionCount: 9 - viewModel.selectedImages.count
      )
      .presentationDetents([.medium, .large])
      .presentationDragIndicator(.visible)
    }
  }

  // MARK: - Content Section

  private var contentSection: some View {
    ScrollView {
      VStack(spacing: 24) {
        // Tab Section
        tabSection

        // Content based on selected tab
        if viewModel.selectedTab == "Text to Video" {
          // Text to Video Content
          textToVideoContent
        } else {
          // Image to Video Content
          imageToVideoContent
        }

        Spacer(minLength: 100)
      }
      .padding(.horizontal, 24)
      .padding(.top, 24)
    }
  }

  // MARK: - Text to Video Content

  private var textToVideoContent: some View {
    VStack(spacing: 24) {
      // Prompt Section
      PromptSectionView(
        promptText: $viewModel.promptText,
        showSampleSuggestions: viewModel.showSampleSuggestions,
        sampleSuggestions: viewModel.sampleSuggestions,
        onSampleSelected: { suggestion in
          viewModel.selectSampleSuggestion(suggestion)
        },
        onShuffleSamples: {
          viewModel.shuffleSampleSuggestions()
        }
      )

      // Aspect Ratio Section
      AspectRatioSectionView(selectedAspectRatio: $viewModel.selectedAspectRatio)
    }
  }

  // MARK: - Image to Video Content

  private var imageToVideoContent: some View {
    VStack(spacing: 24) {
      // Image Upload Section
      ImageUploadSectionView(
        selectedImages: viewModel.selectedImages,
        onAddImage: {
          viewModel.isUserAssetSelectionPresented = true
        },
        onRemoveImage: { index in
          viewModel.removeImage(at: index)
        }
      )

      // Try Sample Section
      TrySampleImagesView { imageUrl in
        viewModel.addOrReplaceImage(imageUrl)
      }

      // Prompt Section (for Image to Video)
      ImageToVideoPromptSectionView(promptText: $viewModel.promptText)

      // Aspect Ratio Section
      AspectRatioSectionView(selectedAspectRatio: $viewModel.selectedAspectRatio)
    }
  }

  // MARK: - Tab Section

  private var tabSection: some View {
    TabSectionView(selectedTab: $viewModel.selectedTab)
  }

  // MARK: - Create Button Section

  private var createButtonSection: some View {
    CreateButtonView(
      credits: 35,
      isLoading: viewModel.isLoading,
      action: {
        viewModel.startVideoCreation()
      }
    )
  }

  // MARK: - Computed Properties

  private var previewImage: String? {
    // Return the first selected image as preview, or nil
    guard let firstImageUrl = viewModel.selectedImages.first else { return nil }
    return firstImageUrl
  }

}

// MARK: - ViewModel

// MARK: - Video Creation Loading State

public enum VideoCreationLoadingState: Equatable {
  case idle
  case creatingTask
  case queued
  case generating
  case completed
  case failed(String)
}

// MARK: - Preview

#Preview {
  CreateVideoPageView()
}
