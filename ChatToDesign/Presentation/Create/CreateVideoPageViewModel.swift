//
//  CreateVideoPageViewModel.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/3.
//

import PhotosUI
import SwiftUI

/// Create Video 页面的 ViewModel
@MainActor
public class CreateVideoPageViewModel: ObservableObject {

  // MARK: - Published Properties

  @Published public var isLoading: Bool = false
  @Published public var loadingState: VideoCreationLoadingState = .idle
  @Published public var errorMessage: String?
  @Published public var credits: Int = 200

  // Content Properties
  @Published public var promptText: String = ""
  @Published public var selectedAspectRatio: String = "16:9"
  @Published public var selectedTab: String = "Text to Video"
  @Published public var showSampleSuggestions: Bool = true

  // Image to Video Properties
  @Published public var selectedImages: [String] = []
  @Published public var isImagePickerPresented: Bool = false
  @Published public var isUserAssetSelectionPresented: Bool = false
  @Published public var isImageLoading: Bool = false
  @Published public var generatedVideoUrl: String? = nil

  // Sample suggestions
  @Published public var sampleSuggestions: [String] = [
    "A futuristic skyline with flying cars.",
    "A city street at sunset with pedestrians.",
    "A serene mountain landscape with flowing rivers.",
    "An underwater scene with colorful coral reefs.",
    "A bustling marketplace in an ancient city.",
    "A space station orbiting around Earth.",
  ]

  // MARK: - Initialization

  public init() {
    // 初始化逻辑
  }

  // MARK: - Public Methods

  /// 开始创建视频
  public func startVideoCreation() {
    guard !promptText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
      errorMessage = "Please enter a prompt to create your video."
      return
    }

    isLoading = true
    loadingState = .creatingTask
    errorMessage = nil

    // TODO: 实现视频创建逻辑
    print("Starting video creation with prompt: \(promptText)")
    print("Aspect ratio: \(selectedAspectRatio)")

    // Simulate API call with different states
    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
      self.loadingState = .queued
    }

    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
      self.loadingState = .generating
    }

    DispatchQueue.main.asyncAfter(deadline: .now() + 8) {
      // 模拟生成的视频 URL
      self.generatedVideoUrl = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
      self.isLoading = false
      self.loadingState = .completed
      // Handle success or error
    }
  }

  /// 更新积分
  public func updateCredits(_ newCredits: Int) {
    credits = newCredits
  }

  /// 随机打乱示例建议
  public func shuffleSampleSuggestions() {
    sampleSuggestions.shuffle()
  }

  /// 选择示例建议
  public func selectSampleSuggestion(_ suggestion: String) {
    promptText = suggestion
  }

  /// 添加图片
  public func addImage(_ imageUrl: String) {
    selectedImages.append(imageUrl)
  }

  /// Add or replace image - if images exist, replace the first one; otherwise add new image
  public func addOrReplaceImage(_ imageUrl: String) {
    if selectedImages.isEmpty {
      // No images exist, add new image
      selectedImages.append(imageUrl)
    } else {
      // Images exist, replace the first one
      selectedImages[0] = imageUrl
    }
  }

  /// 移除图片
  public func removeImage(at index: Int) {
    guard index < selectedImages.count else { return }
    selectedImages.remove(at: index)
  }

  /// 处理从 UserAssetSelectionView 选择的资源
  public func handleSelectedAssets(_ assets: [AssetResponse]) {
    Task {
      for asset in assets {
        await MainActor.run {
          addImage(asset.url)
        }
      }
      await MainActor.run {
        isUserAssetSelectionPresented = false
      }
    }
  }

  /// 取消视频创建
  public func cancelVideoCreation() {
    isLoading = false
    loadingState = .idle
    errorMessage = nil
  }

  /// 查看个人资料
  public func viewInProfile() {
    // 重置状态
    // isLoading = false
    // loadingState = .idle
    // generatedVideoUrl = nil
  }
}
