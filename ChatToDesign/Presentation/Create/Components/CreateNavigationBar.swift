//
//  CreateNavigationBar.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/3.
//

import SwiftUI

/// Reusable navigation bar for Create pages
struct CreateNavigationBar: View {
  let title: String
  let credits: Int?
  let showCredits: Bool
  let onClose: () -> Void
  
  // MARK: - Initialization
  
  init(
    title: String,
    credits: Int? = nil,
    showCredits: Bool = true,
    onClose: @escaping () -> Void
  ) {
    self.title = title
    self.credits = credits
    self.showCredits = showCredits
    self.onClose = onClose
  }
  
  // MARK: - Body
  
  var body: some View {
    VStack(spacing: Spacing.space8) {
      // 导航栏内容
      HStack {
        // 左侧标题
        HStack(spacing: Spacing.space4) {
          Text(title)
            .font(Typography.headline3)
            .foregroundColor(FigmaTokens.foreground)
          
          Spacer()
        }
        
        // 右侧控件
        HStack(spacing: Spacing.space12) {
          // 积分徽章 (可选)
          if showCredits {
            creditsBadge
          }
          
          // 关闭按钮
          closeButton
        }
      }
      .padding(.horizontal, Spacing.space24)
      .frame(height: NavigationTokens.navigationHeight)
    }
  }
  
  // MARK: - Credits Badge
  
  private var creditsBadge: some View {
    HStack(spacing: Spacing.space2) {
      // 星星图标
      Image(systemName: "sparkles")
        .font(Typography.labelSmall.weight(.semibold))
        .foregroundColor(NavigationTokens.creditsBadgeText)
      
      // 积分数量
      Text("\(credits ?? 0)")
        .font(Typography.labelSmall.weight(.semibold))
        .foregroundColor(NavigationTokens.creditsBadgeText)
    }
    .padding(.horizontal, Spacing.space8)
    .padding(.vertical, Spacing.space2)
    .background(NavigationTokens.creditsBadgeBackground)
    .cornerRadius(.infinity)
  }
  
  // MARK: - Close Button
  
  private var closeButton: some View {
    Button(action: onClose) {
      Image(systemName: "xmark")
        .font(Typography.labelLarge.weight(.medium))
        .foregroundColor(FigmaTokens.foreground)
        .frame(width: NavigationTokens.closeButtonSize, height: NavigationTokens.closeButtonSize)
        .background(NavigationTokens.closeButtonBackground)
        .cornerRadius(Spacing.space8)
    }
    .buttonStyle(PlainButtonStyle())
  }
}

// MARK: - Preview

#Preview {
  VStack {
    CreateNavigationBar(
      title: "Create Video",
      credits: 200,
      showCredits: true,
      onClose: {}
    )
    .background(Color.black)
    
    Spacer()
    
    CreateNavigationBar(
      title: "Create",
      showCredits: false,
      onClose: {}
    )
    .background(Color.black)
  }
}

// MARK: - Design Tokens
extension CreateNavigationBar {
    private struct NavigationTokens {
        // Layout
        static let navigationHeight: CGFloat = Spacing.space40
        static let closeButtonSize: CGFloat = Spacing.space40
        
        // Colors
        static let creditsBadgeBackground = ColorPalette.gray700
        static let creditsBadgeText = ColorTokens.textInverse
        static let closeButtonIcon = ColorTokens.textPrimary
        static let closeButtonBackground = Color.clear
    }
}
