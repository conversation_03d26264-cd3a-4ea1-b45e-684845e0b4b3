//
//  ImageToVideoPromptSectionView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import SwiftUI

/// Prompt section specifically for Image to Video
struct ImageToVideoPromptSectionView: View {
  @Binding var promptText: String

  var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      // Prompt Label
      Text("Prompt")
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.white)

      // Prompt Input
      VStack(spacing: 0) {
        TextEditor(text: $promptText)
          .font(.system(size: 14, weight: .regular))
          .foregroundColor(.white)
          .scrollContentBackground(.hidden)
          .background(Color.clear)
          .frame(minHeight: 80)
          .padding(16)
          .overlay(
            // Placeholder text
            Group {
              if promptText.isEmpty {
                HStack {
                  VStack {
                    Text("Describe the motion or transformation you want...")
                      .font(.system(size: 14, weight: .regular))
                      .foregroundColor(Color(.systemGray))
                      .padding(.leading, 20)
                      .padding(.top, 24)
                    Spacer()
                  }
                  Spacer()
                }
              }
            }
          )
      }
      .background(Color(red: 0.15, green: 0.15, blue: 0.17))  // #27272a equivalent
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .stroke(Color(red: 0.2, green: 0.2, blue: 0.22), lineWidth: 1)
      )
    }
  }
}
