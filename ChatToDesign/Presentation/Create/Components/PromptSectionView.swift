//
//  PromptSectionView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import SwiftUI

/// Prompt input section for Text to Video
struct PromptSectionView: View {
  @Binding var promptText: String
  let showSampleSuggestions: Bool
  let sampleSuggestions: [String]
  let onSampleSelected: (String) -> Void
  let onShuffleSamples: () -> Void

  var body: some View {
    VStack(alignment: .leading, spacing: PromptTokens.labelSpacing) {
      // Prompt Label
      Text("Prompt")
        .font(PromptTokens.labelFont)
        .foregroundColor(PromptTokens.labelColor)

      // Prompt Input
      VStack(spacing: Spacing.space0) {
        TextEditor(text: $promptText)
          .font(PromptTokens.inputFont)
          .foregroundColor(PromptTokens.inputTextColor)
          .scrollContentBackground(.hidden)
          .background(Color.clear)
          .frame(minHeight: PromptTokens.inputMinHeight)
          .padding(PromptTokens.inputPadding)
          .overlay(
            // Placeholder text
            Group {
              if promptText.isEmpty {
                HStack {
                  VStack {
                    Text("What do you want to create with this Video?")
                      .font(PromptTokens.inputFont)
                      .foregroundColor(PromptTokens.placeholderText)
                      .padding(.leading, PromptTokens.placeholderOffsetX)
                      .padding(.top, PromptTokens.placeholderOffsetY)
                    Spacer()
                  }
                  Spacer()
                }
              }
            }
          )
      }
      .background(PromptTokens.inputBackground)
      .cornerRadius(PromptTokens.inputCornerRadius)
      .overlay(
        RoundedRectangle(cornerRadius: PromptTokens.inputCornerRadius)
          .stroke(PromptTokens.inputBorder, lineWidth: PromptTokens.inputBorderWidth)
      )

      // Sample Suggestions
      if showSampleSuggestions {
        SampleSuggestionsView(
          suggestions: Array(sampleSuggestions.prefix(2)),
          onSampleSelected: onSampleSelected,
          onShuffleSamples: onShuffleSamples
        )
      }
    }
  }
}

// MARK: - Design Tokens
extension PromptSectionView {
    private struct PromptTokens {
        // Use shared form patterns from CreateTokens
        static let labelFont = CreateTokens.FormLabel.font
        static let labelColor = CreateTokens.FormLabel.color
        static let labelSpacing = CreateTokens.FormLabel.spacing
        
        static let inputFont = CreateTokens.FormInput.font
        static let inputTextColor = CreateTokens.FormInput.textColor
        static let inputBackground = CreateTokens.FormInput.backgroundColor
        static let inputBorder = CreateTokens.FormInput.borderColor
        static let inputBorderWidth = CreateTokens.FormInput.borderWidth
        static let inputCornerRadius = CreateTokens.FormInput.cornerRadius
        static let inputPadding = CreateTokens.FormInput.padding
        static let placeholderText = CreateTokens.FormInput.placeholderColor
        
        // Component-specific tokens
        static let inputMinHeight: CGFloat = 126
        static let placeholderOffsetX: CGFloat = Spacing.space20
        static let placeholderOffsetY: CGFloat = Spacing.space24
    }
}
