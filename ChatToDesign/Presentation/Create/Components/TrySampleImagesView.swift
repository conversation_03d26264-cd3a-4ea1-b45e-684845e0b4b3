//
//  TrySampleImagesView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/3.
//

import Kingfisher
import SwiftUI

/// Try sample images component
struct TrySampleImagesView: View {
  let onImageSelected: (String) -> Void
  
  // Mock sample image URLs
  private let sampleImages = [
    "https://assets.picadabra.ai/sample_woman_1.jpg",
    "https://assets.picadabra.ai/sample_woman_2.jpg", 
    "https://assets.picadabra.ai/sample_man_1.jpg",
    "https://assets.picadabra.ai/sample_man_2.jpg"
  ]
  
  var body: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack {
        Text("Try sample")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.white)
        
        Spacer()
      }
      
      HStack(spacing: 12) {
        ForEach(sampleImages, id: \.self) { imageUrl in
          Button(action: { onImageSelected(imageUrl) }) {
            KFImage(URL(string: imageUrl))
              .placeholder {
                RoundedRectangle(cornerRadius: 8)
                  .fill(Color.gray.opacity(0.3))
                  .frame(width: 60, height: 60)
                  .overlay(
                    ProgressView()
                      .progressViewStyle(CircularProgressViewStyle(tint: .white))
                      .scaleEffect(0.6)
                  )
              }
              .retry(maxCount: 3)
              .fade(duration: 0.25)
              .resizable()
              .aspectRatio(contentMode: .fill)
              .frame(width: 60, height: 60)
              .clipShape(RoundedRectangle(cornerRadius: 8))
          }
          .buttonStyle(PlainButtonStyle())
        }
        
        Spacer()
      }
    }
  }
}

// MARK: - Preview

#Preview {
  TrySampleImagesView { imageUrl in
    print("Selected sample image: \(imageUrl)")
  }
  .padding()
  .background(Color.black)
}
