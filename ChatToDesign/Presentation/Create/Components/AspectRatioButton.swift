//
//  AspectRatioButton.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import SwiftUI

/// Button for aspect ratio selection
struct AspectRatioButton: View {
  let ratio: String
  let isSelected: Bool
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      VStack(spacing: 8) {
        // Visual representation of aspect ratio
        Rectangle()
          .fill(Color.clear)
          .frame(
            width: aspectRatioSize.width,
            height: aspectRatioSize.height
          )
          .overlay(
            RoundedRectangle(cornerRadius: 4)
              .stroke(isSelected ? AspectRatioTokens.selectedBorder : AspectRatioTokens.unselectedBorder, lineWidth: isSelected ? 2 : 1)
          )

        // Ratio text
        Text(ratio)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(isSelected ? AspectRatioTokens.selectedText : AspectRatioTokens.unselectedText)
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
      .background(
        RoundedRectangle(cornerRadius: 8)
          .fill(isSelected ? AspectRatioTokens.selectedBackground : Color.clear)
      )
    }
    .buttonStyle(PlainButtonStyle())
  }

  private var aspectRatioSize: CGSize {
    switch ratio {
    case "16:9":
      return CGSize(width: 48, height: 27)
    case "9:16":
      return CGSize(width: 27, height: 48)
    case "1:1":
      return CGSize(width: 36, height: 36)
    default:
      return CGSize(width: 36, height: 36)
    }
  }
}

// MARK: - Design Tokens
extension AspectRatioButton {
    private struct AspectRatioTokens {
        static let selectedBorder = ColorTokens.borderPrimary
        static let unselectedBorder = ColorPalette.gray750
        static let selectedText = ColorTokens.textInverse
        static let unselectedText = ColorTokens.textSecondary
        static let selectedBackground = ColorPalette.gray950
    }
}
