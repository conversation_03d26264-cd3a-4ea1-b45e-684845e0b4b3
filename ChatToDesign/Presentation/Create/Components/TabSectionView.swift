//
//  TabSectionView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import SwiftUI

/// Tab Section for switching between Text to Video and Image to Video
struct TabSectionView: View {
  @Binding var selectedTab: String

  var body: some View {
    HStack(spacing: 0) {
      // Text to Video Tab
      TabButton(
        title: "Text to Video",
        isSelected: selectedTab == "Text to Video",
        action: {
          selectedTab = "Text to Video"
        }
      )

      // Image to Video Tab
      TabButton(
        title: "Image to Video",
        isSelected: selectedTab == "Image to Video",
        action: {
          selectedTab = "Image to Video"
        }
      )

      Spacer()
    }
  }
}
