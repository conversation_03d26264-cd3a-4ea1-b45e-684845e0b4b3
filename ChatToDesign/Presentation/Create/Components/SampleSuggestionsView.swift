//
//  SampleSuggestionsView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import SwiftUI

/// Sample suggestions with shuffle button
struct SampleSuggestionsView: View {
  let suggestions: [String]
  let onSampleSelected: (String) -> Void
  let onShuffleSamples: () -> Void

  var body: some View {
    HStack(spacing: 10) {
      VStack(spacing: 8) {
        ForEach(Array(suggestions.enumerated()), id: \.offset) { index, suggestion in
          SuggestionButton(
            text: suggestion,
            action: {
              onSampleSelected(suggestion)
            }
          )
          .frame(maxWidth: .infinity, alignment: .leading)
        }
      }

      // Shuffle <PERSON><PERSON>(action: onShuffleSamples) {
        Image(systemName: "shuffle")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(Color(.systemGray))
          .frame(width: 32, height: 32)
          .background(Color.clear)
          .cornerRadius(6)
      }
      .buttonStyle(PlainButtonStyle())
    }
    .padding(.top, 4)
  }
}
