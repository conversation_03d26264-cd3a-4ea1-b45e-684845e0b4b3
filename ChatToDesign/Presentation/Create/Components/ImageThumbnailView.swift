//
//  ImageThumbnailView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import Kingfisher
import SwiftUI

/// Thumbnail view for uploaded images with delete functionality
struct ImageThumbnailView: View {
  let imageUrl: String
  let isLoading: Bool
  let onDelete: () -> Void
  let height: CGFloat?

  var body: some View {
    ZStack {
      if isLoading {
        // Loading state
        RoundedRectangle(cornerRadius: ThumbnailTokens.cornerRadius)
          .fill(ThumbnailTokens.loadingBackground)
          .frame(height: height ?? ThumbnailTokens.defaultHeight)
          .overlay(
            ProgressView()
              .progressViewStyle(CircularProgressViewStyle(tint: ThumbnailTokens.loadingIndicator))
          )
      } else {
        // Image display using KFImage
        KFImage(URL(string: imageUrl))
          .placeholder {
            RoundedRectangle(cornerRadius: ThumbnailTokens.cornerRadius)
              .fill(ThumbnailTokens.placeholderBackground)
              .frame(height: height ?? ThumbnailTokens.defaultHeight)
              .overlay(
                Image(systemName: "photo")
                  .font(ThumbnailTokens.placeholderIconFont)
                  .foregroundColor(ThumbnailTokens.placeholderIcon)
              )
          }
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(height: height ?? ThumbnailTokens.defaultHeight)
          .clipped()
          .cornerRadius(ThumbnailTokens.cornerRadius)
          .overlay(
            // Delete button overlay
            ZStack {
              ThumbnailTokens.overlayBackground

              Button(action: onDelete) {
                Image(systemName: "trash")
                  .font(ThumbnailTokens.deleteButtonFont)
                  .foregroundColor(ThumbnailTokens.deleteButtonIcon)
                  .frame(width: ThumbnailTokens.deleteButtonSize, height: ThumbnailTokens.deleteButtonSize)
                  .background(ThumbnailTokens.deleteButtonBackground)
                  .cornerRadius(ThumbnailTokens.deleteButtonCornerRadius)
              }
              .buttonStyle(PlainButtonStyle())
            }
            .cornerRadius(ThumbnailTokens.cornerRadius)
          )
      }
    }
    // .overlay(
    //   RoundedRectangle(cornerRadius: 16)
    //     .stroke(Color(red: 0.2, green: 0.2, blue: 0.22), lineWidth: 1)
    // )
  }
}

// MARK: - Design Tokens
extension ImageThumbnailView {
    private struct ThumbnailTokens {
        // Typography
        static let placeholderIconFont = Typography.headline3
        static let deleteButtonFont = Typography.labelLarge.weight(.medium)
        
        // Layout
        static let defaultHeight: CGFloat = CreateTokens.GridLayout.defaultImageHeight
        static let cornerRadius: CGFloat = Spacing.space16
        static let deleteButtonSize: CGFloat = Spacing.space24
        static let deleteButtonCornerRadius: CGFloat = Spacing.space12
        
        // Colors - Use shared Create tokens where possible
        static let loadingBackground = CreateTokens.loadingBackground
        static let loadingIndicator = ColorTokens.textInverse
        static let placeholderBackground = CreateTokens.loadingBackground
        static let placeholderIcon = ColorTokens.textTertiary
        static let overlayBackground = CreateTokens.overlayBackground
        static let deleteButtonIcon = ColorTokens.textInverse
        static let deleteButtonBackground = CreateTokens.deleteButtonOverlay
    }
}
