//
//  CreateButtonView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import SwiftUI

/// Main create button with credits display
struct CreateButtonView: View {
  let credits: Int
  let isLoading: Bool
  let action: () -> Void

  var body: some View {
    VStack(spacing: Spacing.space0) {
      // Shadow/blur effect
      Rectangle()
        .fill(CreateButtonTokens.shadowColor)
        .frame(height: CreateButtonTokens.shadowHeight)
        .blur(radius: CreateButtonTokens.shadowBlurRadius)
        .offset(y: CreateButtonTokens.shadowOffset)

      // Button container with home indicator
      VStack(spacing: CreateButtonTokens.containerSpacing) {
        // Create button
        Button(action: action) {
          HStack(spacing: CreateButtonTokens.contentSpacing) {
            if isLoading {
              // Loading state
              ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: CreateButtonTokens.loadingIndicator))
                .scaleEffect(CreateButtonTokens.loadingScale)

              Text("Creating...")
                .font(CreateButtonTokens.buttonFont)
                .foregroundColor(CreateButtonTokens.textColor)
            } else {
              // Normal state
              // Spark icon
              Image(systemName: "sparkles")
                .font(CreateButtonTokens.iconFont)
                .foregroundColor(CreateButtonTokens.textColor)

              // Credits count
              Text("\(credits)")
                .font(CreateButtonTokens.buttonFont)
                .foregroundColor(CreateButtonTokens.textColor)

              // Create text
              Text("Create")
                .font(CreateButtonTokens.buttonFont)
                .foregroundColor(CreateButtonTokens.textColor)
            }
          }
          .frame(maxWidth: .infinity)
          .frame(height: CreateButtonTokens.buttonHeight)
          .background(CreateButtonTokens.buttonBackground)
          .cornerRadius(.infinity)
          .shadow(
            color: CreateButtonTokens.shadowPrimary,
            radius: CreateButtonTokens.shadowPrimaryRadius,
            x: 0,
            y: CreateButtonTokens.shadowPrimaryY
          )
          .shadow(
            color: CreateButtonTokens.shadowSecondary,
            radius: CreateButtonTokens.shadowSecondaryRadius,
            x: 0,
            y: CreateButtonTokens.shadowSecondaryY
          )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isLoading)
        .opacity(isLoading ? CreateButtonTokens.loadingOpacity : 1.0)
        .padding(.horizontal, CreateButtonTokens.horizontalPadding)
      }
      .background(CreateButtonTokens.containerBackground)
    }
  }
}

// MARK: - Design Tokens
extension CreateButtonView {
    private struct CreateButtonTokens {
        // Typography
        static let buttonFont = Typography.labelLarge.weight(.semibold)
        static let iconFont = Typography.labelLarge.weight(.semibold)
        
        // Layout
        static let buttonHeight: CGFloat = Spacing.space40
        static let containerSpacing: CGFloat = 10 // Keep original spacing for visual balance
        static let contentSpacing: CGFloat = Spacing.space2
        static let horizontalPadding: CGFloat = Spacing.space24
        
        // Shadow Layout
        static let shadowHeight: CGFloat = 35
        static let shadowBlurRadius: CGFloat = 35
        static let shadowOffset: CGFloat = -31
        static let shadowPrimaryRadius: CGFloat = 6
        static let shadowPrimaryY: CGFloat = 4
        static let shadowSecondaryRadius: CGFloat = 4
        static let shadowSecondaryY: CGFloat = 2
        
        // States
        static let loadingScale: CGFloat = 0.8
        static let loadingOpacity: Double = 0.6
        
        // Colors
        static let shadowColor = Color.black.opacity(0.3)
        static let loadingIndicator = ColorTokens.textInverse
        static let textColor = ColorTokens.textInverse
        static let buttonBackground = ColorTokens.interactive
        static let shadowPrimary = Color.black.opacity(0.1)
        static let shadowSecondary = Color.black.opacity(0.06)
        static let containerBackground = Color.black
    }
}
