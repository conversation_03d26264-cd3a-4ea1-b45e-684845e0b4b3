//
//  ImageUploadSectionView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import SwiftUI

/// Image upload section for Image to Video
struct ImageUploadSectionView: View {
  let selectedImages: [String]
  let onAddImage: () -> Void
  let onRemoveImage: (Int) -> Void
  let title: String
  let subtitle: String?
  let maxImages: Int
  let columns: Int
  let height: CGFloat?

  // MARK: - Initialization

  init(
    selectedImages: [String],
    onAddImage: @escaping () -> Void,
    onRemoveImage: @escaping (Int) -> Void,
    title: String = "Upload Images",
    subtitle: String? = nil,
    maxImages: Int = 9,
    columns: Int = 3,
    height: CGFloat? = nil
  ) {
    self.selectedImages = selectedImages
    self.onAddImage = onAddImage
    self.onRemoveImage = onRemoveImage
    self.title = title
    self.subtitle = subtitle
    self.maxImages = maxImages
    self.columns = columns
    self.height = height
  }

  var body: some View {
    VStack(alignment: .leading, spacing: UploadTokens.sectionSpacing) {
      // Section title and subtitle
      VStack(alignment: .leading, spacing: UploadTokens.headerSpacing) {
        Text(title)
          .font(UploadTokens.titleFont)
          .foregroundColor(UploadTokens.titleColor)

        if let subtitle = subtitle {
          Text(subtitle)
            .font(UploadTokens.subtitleFont)
            .foregroundColor(UploadTokens.subtitleColor)
        }
      }

      // Image Grid
      LazyVGrid(
        columns: Array(repeating: GridItem(.flexible(), spacing: UploadTokens.gridSpacing), count: columns),
        spacing: UploadTokens.gridSpacing
      ) {
        // Add Image Button (show if less than maxImages)
        if selectedImages.count < maxImages {
          AddImageButton(
            title: "Add Image",
            height: height ?? UploadTokens.defaultImageHeight,
            action: onAddImage
          )
        }
        // Uploaded Images
        ForEach(Array(selectedImages.enumerated()), id: \.offset) { index, imageUrl in
          ImageThumbnailView(
            imageUrl: imageUrl,
            isLoading: false,
            onDelete: {
              onRemoveImage(index)
            },
            height: height
          )
        }
      }
    }
  }
}

// MARK: - Preview

#Preview {
  VStack {
    ImageUploadSectionView(
      selectedImages: [
        "https://picsum.photos/300/400?random=1",
        "https://picsum.photos/300/400?random=2",
      ],
      onAddImage: {},
      onRemoveImage: { _ in },
      title: "Upload Images",
      subtitle: "Upload 2-4 images to create your personalized video",
      maxImages: 4,
      columns: 4
    )
    .padding()
    .background(Color.black)

    Spacer()

    ImageUploadSectionView(
      selectedImages: [],
      onAddImage: {},
      onRemoveImage: { _ in }
    )
    .padding()
    .background(Color.black)
  }
}

// MARK: - Design Tokens
extension ImageUploadSectionView {
    private struct UploadTokens {
        // Use shared form patterns from CreateTokens
        static let titleFont = CreateTokens.FormLabel.font
        static let titleColor = CreateTokens.FormLabel.color
        static let subtitleFont = CreateTokens.SubtitleText.font
        static let subtitleColor = CreateTokens.SubtitleText.color
        
        static let sectionSpacing = CreateTokens.sectionSpacing
        static let headerSpacing = CreateTokens.headerSpacing
        static let gridSpacing = CreateTokens.GridLayout.spacing
        static let defaultImageHeight = CreateTokens.GridLayout.defaultImageHeight
    }
}
