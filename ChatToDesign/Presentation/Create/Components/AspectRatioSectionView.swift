//
//  AspectRatioSectionView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import SwiftUI

/// Aspect ratio selection section
struct AspectRatioSectionView: View {
  @Binding var selectedAspectRatio: String

  var body: some View {
    VStack(alignment: .leading, spacing: SectionTokens.labelSpacing) {
      // Aspect Ratio Label
      Text("Aspect Ratio")
        .font(SectionTokens.labelFont)
        .foregroundColor(SectionTokens.labelColor)

      // Aspect Ratio Options Container
      HStack(spacing: Spacing.space0) {
        AspectRatioButton(
          ratio: "16:9",
          isSelected: selectedAspectRatio == "16:9",
          action: {
            selectedAspectRatio = "16:9"
          }
        )

        AspectRatioButton(
          ratio: "9:16",
          isSelected: selectedAspectRatio == "9:16",
          action: {
            selectedAspectRatio = "9:16"
          }
        )

        AspectRatioButton(
          ratio: "1:1",
          isSelected: selectedAspectRatio == "1:1",
          action: {
            selectedAspectRatio = "1:1"
          }
        )

        Spacer()
      }
      .padding(SectionTokens.containerPadding)
      .background(SectionTokens.containerBackground)
      .cornerRadius(SectionTokens.containerCornerRadius)
    }
  }
}

// MARK: - Design Tokens
extension AspectRatioSectionView {
    private struct SectionTokens {
        // Use shared form patterns from CreateTokens
        static let labelFont = CreateTokens.FormLabel.font
        static let labelColor = CreateTokens.FormLabel.color
        static let labelSpacing = CreateTokens.FormLabel.spacing
        
        static let containerBackground = CreateTokens.FormContainer.backgroundColor
        static let containerCornerRadius = CreateTokens.FormContainer.cornerRadius
        static let containerPadding = CreateTokens.FormContainer.padding
    }
}
