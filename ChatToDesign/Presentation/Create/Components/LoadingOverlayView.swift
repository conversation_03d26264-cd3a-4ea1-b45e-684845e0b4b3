//
//  LoadingOverlayView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import AVKit
import Kingfisher
import SwiftUI

/// Loading overlay view for video creation process
struct LoadingOverlayView: View {
  let loadingState: VideoCreationLoadingState
  let previewImageUrl: String?
  let generatedVideoUrl: String?  // 生成的视频URL
  let onCancel: () -> Void
  let onViewProfile: () -> Void  // 查看个人资料回调
  let onClose: () -> Void

  @State private var rotationAngle: Double = 0

  var body: some View {
    ZStack {
      // Background
      LoadingTokens.backgroundColor
        .ignoresSafeArea()

      VStack(spacing: 0) {

        CreateNavigationBar(
          title: "Create Video",
          showCredits: false,
          onClose: onClose
        )


        // Main content
        VStack(spacing: LoadingTokens.contentSpacing) {
          // Preview image with loading indicator
          previewImageView

          // Status text
          statusTextView

          // Action button
          actionButton
        }
        .padding(.horizontal, LoadingTokens.horizontalPadding)
        .padding(.top, LoadingTokens.topPadding)

        Spacer()
      }
    }
  }

  // MARK: - Preview Image View

  private var previewImageView: some View {
    ZStack {
      // Background content
      if loadingState == .completed, let videoUrl = generatedVideoUrl {
        // 完成状态：显示生成的视频（自动循环播放）
        VideoPlayerView(url: videoUrl)
          .frame(width: LoadingTokens.videoWidth, height: LoadingTokens.videoHeight)
          .cornerRadius(LoadingTokens.mediaCornerRadius)
      } else if let previewImageUrl = previewImageUrl {
        // 加载状态：显示预览图片
        KFImage(URL(string: previewImageUrl))
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: LoadingTokens.imageWidth, height: LoadingTokens.imageHeight)
          .clipped()
          .cornerRadius(LoadingTokens.mediaCornerRadius)
      } else {
        // 占位符
        RoundedRectangle(cornerRadius: LoadingTokens.mediaCornerRadius)
          .fill(LoadingTokens.placeholderBackground)
          .frame(width: LoadingTokens.imageWidth, height: LoadingTokens.imageHeight)
      }

      // 只在非完成状态显示暗色覆盖层和加载动画
      if loadingState != .completed {
        // Dark overlay
        RoundedRectangle(cornerRadius: LoadingTokens.mediaCornerRadius)
          .fill(LoadingTokens.overlayBackground)
          .frame(width: LoadingTokens.imageWidth, height: LoadingTokens.imageHeight)

        // Loading spinner
        loadingSpinner
      }
    }
  }

  // MARK: - Loading Spinner

  private var loadingSpinner: some View {
    ZStack {
      // Background circle (full circle with opacity)
      Circle()
        .stroke(LoadingTokens.spinnerTrack, lineWidth: LoadingTokens.spinnerLineWidth)
        .frame(width: LoadingTokens.spinnerSize, height: LoadingTokens.spinnerSize)

      // Animated arc (partial circle)
      Circle()
        .trim(from: 0, to: LoadingTokens.spinnerProgress)
        .stroke(LoadingTokens.spinnerActiveColor, style: StrokeStyle(lineWidth: LoadingTokens.spinnerLineWidth, lineCap: .round))
        .frame(width: LoadingTokens.spinnerSize, height: LoadingTokens.spinnerSize)
        .rotationEffect(.degrees(rotationAngle))
        .animation(
          Animation.linear(duration: LoadingTokens.spinnerAnimationDuration).repeatForever(autoreverses: false),
          value: rotationAngle
        )
    }
    .frame(width: LoadingTokens.spinnerSize, height: LoadingTokens.spinnerSize)
    .onAppear {
      rotationAngle = 360
    }
  }

  // MARK: - Status Text View

  private var statusTextView: some View {
    VStack(spacing: Spacing.space0) {
      Text(statusMessage)
        .font(LoadingTokens.statusFont)
        .foregroundColor(LoadingTokens.statusTextColor)
        .multilineTextAlignment(.center)
        .lineLimit(nil)
    }
    .frame(maxWidth: .infinity)
  }

  // MARK: - Action Button

  private var actionButton: some View {
    Button(action: buttonAction) {
      Text(buttonText)
        .font(LoadingTokens.actionButtonFont)
        .foregroundColor(LoadingTokens.actionButtonTextColor)
        .frame(maxWidth: .infinity)
        .frame(height: LoadingTokens.actionButtonHeight)
        .background(LoadingTokens.actionButtonBackground)
        .cornerRadius(.infinity)
    }
  }

  // MARK: - Computed Properties

  private var statusMessage: String {
    switch loadingState {
    case .idle:
      return ""
    case .creatingTask:
      return "Creating your video task..."
    case .queued:
      return "Your request has been queued due to\nincreased demand right now"
    case .generating:
      return "Generating your video...\nThis may take a few moments"
    case .completed:
      return "Creation Completed"
    case .failed(let error):
      return "Failed to generate video:\n\(error)"
    }
  }

  private var buttonText: String {
    switch loadingState {
    case .idle:
      return "Continue"
    case .completed:
      return "View in Profile"
    case .creatingTask, .queued, .generating:
      return "View in Profile"
    case .failed:
      return "Try Again"
    }
  }

  private var buttonAction: () -> Void {
    switch loadingState {
    case .completed:
      return onViewProfile
    default:
      return onViewProfile
    }
  }
}

// MARK: - Design Tokens
extension LoadingOverlayView {
    private struct LoadingTokens {
        // Typography
        static let statusFont = Typography.bodyMedium
        static let actionButtonFont = Typography.labelLarge.weight(.medium)
        
        // Layout
        static let contentSpacing: CGFloat = Spacing.space24
        static let horizontalPadding: CGFloat = 54 // (393 - 285) / 2 = 54 for centered content
        static let topPadding: CGFloat = 60
        
        // Media dimensions
        static let imageWidth: CGFloat = 177
        static let imageHeight: CGFloat = 242
        static let videoWidth: CGFloat = 256
        static let videoHeight: CGFloat = 342
        static let mediaCornerRadius: CGFloat = Spacing.space16
        
        // Spinner
        static let spinnerSize: CGFloat = Spacing.space24
        static let spinnerLineWidth: CGFloat = 3.33
        static let spinnerProgress: CGFloat = 0.5
        static let spinnerAnimationDuration: Double = 1.0
        
        // Action Button
        static let actionButtonHeight: CGFloat = Spacing.space40
        
        // Colors
        static let backgroundColor = ColorTokens.backgroundInverse
        static let placeholderBackground = CreateTokens.placeholderBackground
        static let overlayBackground = CreateTokens.overlayBackground
        static let spinnerTrack = CreateTokens.spinnerTrack
        static let spinnerActiveColor = ColorTokens.textInverse
        static let statusTextColor = ColorTokens.textInverse
        static let actionButtonTextColor = ColorTokens.textPrimary
        static let actionButtonBackground = ColorTokens.surfacePrimary
    }
}

// MARK: - Video Player View

struct VideoPlayerView: View {
  let url: String
  @State private var player: AVPlayer?

  var body: some View {
    VideoPlayer(player: player)
      .onAppear {
        setupPlayer()
      }
      .onDisappear {
        player?.pause()
      }
  }

  private func setupPlayer() {
    guard let videoURL = URL(string: url) else { return }
    player = AVPlayer(url: videoURL)

    // 设置自动循环播放
    NotificationCenter.default.addObserver(
      forName: .AVPlayerItemDidPlayToEndTime,
      object: player?.currentItem,
      queue: .main
    ) { _ in
      player?.seek(to: .zero)
      player?.play()
    }

    // 自动开始播放
    player?.play()
  }
}

// MARK: - Preview

#Preview {
  LoadingOverlayView(
    loadingState: .queued,
    previewImageUrl: nil,
    generatedVideoUrl: nil,
    onCancel: {},
    onViewProfile: {},
    onClose: {}
  )
}
