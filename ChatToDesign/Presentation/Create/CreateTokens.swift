//
//  CreateTokens.swift
//  ChatToDesign
//
//  Created by DesignSystem Integration
//

import SwiftUI

/// Shared design tokens for Create module components
/// These tokens provide consistency across all Create module forms and sections
struct CreateTokens {

  // MARK: - Form Section Patterns

  /// Standard label styling for form sections
  struct FormLabel {
    static let font = Typography.labelLarge
    static let color = ColorTokens.textInverse
    static let spacing: CGFloat = Spacing.space8
  }

  /// Standard input field styling for forms
  struct FormInput {
    static let font = Typography.bodyMedium
    static let textColor = ColorTokens.textInverse
    static let backgroundColor = ColorPalette.gray800
    static let borderColor = ColorPalette.gray750
    static let borderWidth: CGFloat = 1
    static let cornerRadius: CGFloat = Spacing.space16
    static let padding: CGFloat = Spacing.space16
    static let placeholderColor = ColorTokens.textTertiary
  }

  /// Standard container styling for form sections
  struct FormContainer {
    static let backgroundColor = ColorPalette.gray800
    static let cornerRadius: CGFloat = Spacing.space16
    static let padding: CGFloat = Spacing.space16
    static let sectionSpacing: CGFloat = Spacing.space16
  }

  /// Standard grid layout for image/content grids
  struct GridLayout {
    static let spacing: CGFloat = Spacing.space8
    static let defaultImageHeight: CGFloat = 100
  }

  // MARK: - Typography Hierarchy

  /// Section titles (e.g., "Prompt", "Aspect Ratio", "Upload Images")
  static let sectionTitle = FormLabel.self

  /// Input text and content
  static let inputText = FormInput.self

  /// Subtitle and helper text
  struct SubtitleText {
    static let font = Typography.bodySmall
    static let color = ColorTokens.textTertiary
  }

  // MARK: - Layout Spacing

  /// Spacing between form sections
  static let sectionSpacing: CGFloat = Spacing.space16

  /// Spacing between label and input
  static let labelInputSpacing: CGFloat = Spacing.space8

  /// Spacing within header (title + subtitle)
  static let headerSpacing: CGFloat = Spacing.space8

  // MARK: - Backgrounds
  /// Standard input field background color
  public static let inputBackground = ColorPalette.gray800

  /// Semi-transparent overlay for modal/overlay effects
  public static let overlayBackground = Color.black.opacity(0.2)

  /// Loading state background for placeholders
  public static let loadingBackground = ColorPalette.gray800

  /// Light placeholder background for empty states
  public static let placeholderBackground = ColorPalette.gray150

  /// Primary background for main containers
  public static let primaryBackground = ColorPalette.gray950

  // MARK: - Borders
  /// Standard input field border
  public static let inputBorder = ColorPalette.gray750

  /// Border for selected interactive elements
  public static let selectedBorder = ColorTokens.borderPrimary

  /// Border for unselected interactive elements
  public static let unselectedBorder = ColorPalette.gray750

  // MARK: - Shadows & Effects
  /// Light shadow for subtle elevation
  public static let shadowLight = Color.black.opacity(0.06)

  /// Medium shadow for moderate elevation
  public static let shadowMedium = Color.black.opacity(0.1)

  /// Strong shadow for prominent elements
  public static let shadowStrong = Color.black.opacity(0.3)

  /// Dark overlay for delete/destructive actions
  public static let deleteButtonOverlay = Color.black.opacity(0.6)

  // MARK: - Loading & Interactive
  /// Background track for loading spinners
  public static let spinnerTrack = Color.white.opacity(0.5)

  /// Credits badge background with appropriate opacity
  public static let creditsBadgeBackground = ColorPalette.gray700.opacity(0.8)

}
