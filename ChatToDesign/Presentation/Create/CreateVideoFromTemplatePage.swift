//
//  CreateVideoFromTemplatePage.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/3.
//

import AVKit
import Kingfisher
import SwiftUI

/// Create Video From Template Page
/// Allows users to create videos using a specific template with custom input images
public struct CreateVideoFromTemplatePage: View {

  // MARK: - Properties

  @StateObject private var viewModel: CreateVideoFromTemplateViewModel
  @Environment(\.dismiss) private var dismiss

  // MARK: - Initialization

  public init(
    templateId: String,
    demoUrl: String,
    inputImageUrls: [String],
    inputImageLimits: Int,
    inputGuide: String,
    demoPrompt: String
  ) {
    self._viewModel = StateObject(
      wrappedValue: CreateVideoFromTemplateViewModel(
        templateId: templateId,
        demoUrl: demoUrl,
        inputImageUrls: inputImageUrls,
        inputImageLimits: inputImageLimits,
        inputGuide: inputGuide,
        demoPrompt: demoPrompt
      ))
  }

  // MARK: - Body

  public var body: some View {
    ZStack {
      // Background
      Color(red: 0.035, green: 0.035, blue: 0.043)  // #09090b
        .ignoresSafeArea()

      VStack(spacing: 0) {
        CreateNavigationBar(
          title: "Create Video",
          credits: 200,
          showCredits: true,
          onClose: {
            dismiss()
          }
        )

        // Main content
        ScrollView {
          VStack(spacing: 24) {
            // Demo section
            demoSection

            // Image upload section
            imageUploadSection

            // Bottom spacing for create button
            Spacer()
              .frame(height: 120)
          }
          .padding(.horizontal, 24)
        }

        Spacer()

        // Create button
        createButtonSection
      }

      // Loading overlay
      if viewModel.loadingState != .idle && viewModel.loadingState != .creatingTask {
        LoadingOverlayView(
          loadingState: viewModel.loadingState,
          previewImageUrl: viewModel.selectedImages.first,
          generatedVideoUrl: viewModel.generatedVideoUrl,
          onCancel: {
            viewModel.cancelVideoCreation()
          },
          onViewProfile: {
            // 重置 ViewModel 状态
            viewModel.viewInProfile()

            // 先关闭当前页面
            dismiss()

            // 延迟发送导航通知，确保页面关闭动画完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
              NotificationCenter.default.post(name: .navigateToProfile, object: nil)
            }
          },
          onClose: {
            dismiss()
          }
        )
        .transition(.opacity)
        .animation(.easeInOut(duration: 0.3), value: viewModel.isLoading)
      }
    }
    .preferredColorScheme(.dark)
    .navigationBarHidden(true)
    .onAppear {
      // ViewModel is already initialized with template data
      // Setup demo video if needed
      if !viewModel.templateDemoUrl.isEmpty {
        viewModel.setupDemoVideo(url: viewModel.templateDemoUrl)
      }
    }
    .sheet(isPresented: $viewModel.isUserAssetSelectionPresented) {
      UserAssetSelectionView(
        onAssetsSelected: { assets in
          viewModel.handleSelectedAssets(assets)
        },
        onDismiss: {
          viewModel.isUserAssetSelectionPresented = false
        },
        preselectedAssets: [],
        maxSelectionCount: viewModel.templateInputImageLimits
      )
      .presentationDetents([.medium, .large])
      .presentationDragIndicator(.visible)
    }
  }

  // MARK: - Demo Section

  private var demoSection: some View {
    VStack(alignment: .leading, spacing: 16) {
      // Demo video/image
      ZStack {
        if !viewModel.templateDemoUrl.isEmpty
          && (viewModel.templateDemoUrl.contains(".mp4")
            || viewModel.templateDemoUrl.contains("video"))
        {
          // Video player
          VideoPlayer(player: viewModel.demoPlayer)
            .frame(height: 200)
            .cornerRadius(16)
        } else if !viewModel.templateDemoUrl.isEmpty {
          // Image
          KFImage(URL(string: viewModel.templateDemoUrl))
            .placeholder {
              RoundedRectangle(cornerRadius: 16)
                .fill(Color(red: 0.933, green: 0.933, blue: 0.933))  // #EEE
            }
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(height: 200)
            .clipped()
            .cornerRadius(16)
        } else {
          // Default placeholder when no demo URL
          RoundedRectangle(cornerRadius: 16)
            .fill(Color(red: 0.933, green: 0.933, blue: 0.933))  // #EEE
            .frame(height: 200)
            .overlay(
              VStack {
                Image(systemName: "play.rectangle")
                  .font(.system(size: 32))
                  .foregroundColor(.gray)
                Text("Template Preview")
                  .font(.system(size: 14, weight: .medium))
                  .foregroundColor(.gray)
              }
            )
        }
      }

      // Demo prompt
      // if !viewModel.templateDemoPrompt.isEmpty {
      //   Text(viewModel.templateDemoPrompt)
      //     .font(.system(size: 14, weight: .regular))
      //     .foregroundColor(Color(red: 0.631, green: 0.631, blue: 0.631))  // #a1a1aa
      //     .multilineTextAlignment(.leading)
      // }
    }
  }

  // MARK: - Image Upload Section

  private var imageUploadSection: some View {
    VStack(spacing: 16) {

      // Image Upload Section
      ImageUploadSectionView(
        selectedImages: viewModel.selectedImages,
        onAddImage: {
          viewModel.isUserAssetSelectionPresented = true
        },
        onRemoveImage: { index in
          viewModel.removeImage(at: index)
        },
        title: "Images",
        maxImages: viewModel.templateInputImageLimits,
        columns: 1,
        height: 192
      )

      // Try Sample Section
      TrySampleImagesView { imageUrl in
        viewModel.addOrReplaceImage(imageUrl)
      }
    }
  }

  // MARK: - Create Button Section

  private var createButtonSection: some View {
    VStack(spacing: 16) {
      DSButton(
        title: "Create Video (35 credits)",
        style: .primary,
        size: .large,
        width: .full,
        isLoading: viewModel.isLoading,
        isDisabled: viewModel.selectedImages.isEmpty,
        action: {
          viewModel.startVideoCreation()
        }
      )
    }
    .padding(.horizontal, 24)
    .padding(.bottom, 24)
  }
}

// MARK: - ViewModel

// MARK: - Preview

#Preview {
  CreateVideoFromTemplatePage(
    templateId: "template_001",
    demoUrl: "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
    inputImageUrls: [
      "https://picsum.photos/300/400?random=1",
      "https://picsum.photos/300/400?random=2",
    ],
    inputImageLimits: 4,
    inputGuide:
      "Upload 2-4 images to create your personalized video. Make sure images are clear and well-lit.",
    demoPrompt:
      "Create an engaging video showcasing your products with smooth transitions and professional effects."
  )
}
