//
//  VirtualizedVideoGrid.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import SwiftUI
import UIKit

/// 虚拟化视频网格
struct VirtualizedVideoGrid: UIViewRepresentable {
    let videoEffects: [VideoEffect]
    let onEffectTap: (VideoEffect) -> Void
    
    func makeUIView(context: Context) -> UICollectionView {
        let layout = createCompositionalLayout()
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        
        // 配置集合视图
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.showsVerticalScrollIndicator = false
        
        // 配置虚拟化
        collectionView.isPrefetchingEnabled = true
        collectionView.prefetchDataSource = context.coordinator
        
        // 注册 Cell
        collectionView.register(
            VideoEffectCell.self,
            forCellWithReuseIdentifier: VideoEffectCell.identifier
        )
        
        // 设置数据源和代理
        collectionView.dataSource = context.coordinator
        collectionView.delegate = context.coordinator
        
        return collectionView
    }
    
    func updateUIView(_ uiView: UICollectionView, context: Context) {
        // 更新数据
        context.coordinator.update(with: videoEffects, onEffectTap: onEffectTap)
        
        // 重新加载数据以确保UI同步
        uiView.reloadData()
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator()
    }
    
    // MARK: - Layout
    
    private func createCompositionalLayout() -> UICollectionViewCompositionalLayout {
        let layout = UICollectionViewCompositionalLayout { (sectionIndex, environment) in
            
            // 项目大小
            let itemSize = NSCollectionLayoutSize(
                widthDimension: .fractionalWidth(1.0),
                heightDimension: .absolute(150)
            )
            let item = NSCollectionLayoutItem(layoutSize: itemSize)
            
            // 组大小 - 水平滚动，两行
            let groupSize = NSCollectionLayoutSize(
                widthDimension: .absolute(130),
                heightDimension: .absolute(316) // 2行 x 150 + 间距
            )
            let group = NSCollectionLayoutGroup.vertical(
                layoutSize: groupSize,
                subitem: item,
                count: 2
            )
            group.interItemSpacing = .fixed(16)
            
            // 段落配置
            let section = NSCollectionLayoutSection(group: group)
            section.orthogonalScrollingBehavior = .continuous
            section.interGroupSpacing = 16
            section.contentInsets = NSDirectionalEdgeInsets(
                top: 0, leading: 24, bottom: 0, trailing: 24
            )
            
            return section
        }
        
        return layout
    }
}

// MARK: - Coordinator

extension VirtualizedVideoGrid {
    
    class Coordinator: NSObject, UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDataSourcePrefetching {
        
        private var videoEffects: [VideoEffect] = []
        private var onEffectTap: ((VideoEffect) -> Void)?
        private var visibleCells: Set<IndexPath> = []
        private var pendingVisibilityChanges: [IndexPath: DispatchWorkItem] = [:] // 防抖机制
        
        // MARK: - Update
        
        func update(with videoEffects: [VideoEffect], onEffectTap: @escaping (VideoEffect) -> Void) {
            self.videoEffects = videoEffects
            self.onEffectTap = onEffectTap
            // 重置可见性状态，因为数据已更新
            self.visibleCells.removeAll()
            
            // 取消所有挂起的可见性变更
            for (_, workItem) in pendingVisibilityChanges {
                workItem.cancel()
            }
            pendingVisibilityChanges.removeAll()
        }
        
        // MARK: - UICollectionViewDataSource
        
        func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
            return videoEffects.count
        }
        
        func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
            let cell = collectionView.dequeueReusableCell(
                withReuseIdentifier: VideoEffectCell.identifier,
                for: indexPath
            ) as! VideoEffectCell
            
            let videoEffect = videoEffects[indexPath.item]
            cell.configure(with: videoEffect) { [weak self] in
                self?.onEffectTap?(videoEffect)
            }
            
            return cell
        }
        
        // MARK: - UICollectionViewDelegate
        
        func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
            guard let videoCell = cell as? VideoEffectCell else { return }
            
            // 取消之前的隐藏操作（如果有的话）
            if let pendingWork = pendingVisibilityChanges[indexPath] {
                pendingWork.cancel()
                pendingVisibilityChanges.removeValue(forKey: indexPath)
            }
            
            // 防抖：延迟一小段时间再真正显示，避免快速滚动时的抖动
            let workItem = DispatchWorkItem { [weak self] in
                guard let self = self else { return }
                
                // 再次检查cell是否仍然可见
                if collectionView.visibleCells.contains(cell) {
                    self.visibleCells.insert(indexPath)
                    videoCell.didBecomeVisible()
                    
                    Logger.debug("👀 VirtualizedVideoGrid: Cell became visible at index \(indexPath.item), visible cells count: \(self.visibleCells.count)")
                }
                
                self.pendingVisibilityChanges.removeValue(forKey: indexPath)
            }
            
            pendingVisibilityChanges[indexPath] = workItem
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1, execute: workItem)
        }
        
        func collectionView(_ collectionView: UICollectionView, didEndDisplaying cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
            guard let videoCell = cell as? VideoEffectCell else { return }
            
            // 取消之前的显示操作（如果有的话）
            if let pendingWork = pendingVisibilityChanges[indexPath] {
                pendingWork.cancel()
                pendingVisibilityChanges.removeValue(forKey: indexPath)
            }
            
            // 防抖：延迟一小段时间再真正隐藏，避免快速滚动时的抖动
            let workItem = DispatchWorkItem { [weak self] in
                guard let self = self else { return }
                
                // 再次检查cell是否真的不可见
                if !collectionView.visibleCells.contains(cell) {
                    self.visibleCells.remove(indexPath)
                    videoCell.didBecomeInvisible()
                    
                    Logger.debug("👻 VirtualizedVideoGrid: Cell became invisible at index \(indexPath.item), visible cells count: \(self.visibleCells.count)")
                }
                
                self.pendingVisibilityChanges.removeValue(forKey: indexPath)
            }
            
            pendingVisibilityChanges[indexPath] = workItem
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: workItem) // 隐藏延迟稍长，避免误关闭
        }
        
        func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
            let videoEffect = videoEffects[indexPath.item]
            onEffectTap?(videoEffect)
        }
        
        // MARK: - UICollectionViewDataSourcePrefetching
        
        func collectionView(_ collectionView: UICollectionView, prefetchItemsAt indexPaths: [IndexPath]) {
            // 预加载缩略图 - 使用posterUrl作为缩略图
            let thumbnailUrls: [String] = indexPaths.compactMap { indexPath in
                guard indexPath.item < videoEffects.count else { return nil }
                return videoEffects[indexPath.item].posterUrl
            }
            
            // 这里可以添加图片预加载逻辑，如使用Kingfisher等
            Logger.debug("VirtualizedVideoGrid: Prefetching thumbnails for \(thumbnailUrls.count) items")
        }
        
        func collectionView(_ collectionView: UICollectionView, cancelPrefetchingForItemsAt indexPaths: [IndexPath]) {
            // 取消预加载（如果需要的话）
            let urls: [URL] = indexPaths.compactMap { indexPath in
                guard indexPath.item < videoEffects.count else { return nil }
                return URL(string: videoEffects[indexPath.item].videoUrl)
            }
            
            // 取消加载任务
            for url in urls {
                Task {
                    await VideoLoadingPool.shared.cancelLoad(url: url)
                }
            }
            
            Logger.debug("VirtualizedVideoGrid: Cancelled prefetching for \(urls.count) items")
        }
        
        // MARK: - Scroll Optimization
        
        func scrollViewDidScroll(_ scrollView: UIScrollView) {
            // 可以在这里添加滚动优化逻辑
            // 例如：根据滚动速度调整预加载策略
        }
        
        func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
            // 滚动结束后，可以触发更积极的预加载
            if let collectionView = scrollView as? UICollectionView {
                preloadNearbyItems(collectionView: collectionView)
            }
        }
        
        // MARK: - Private Methods
        
        private func preloadNearbyItems(collectionView: UICollectionView) {
            let visibleIndexPaths = collectionView.indexPathsForVisibleItems.sorted(by: { $0.item < $1.item })
            
            var preloadIndexPaths: [IndexPath] = []
            
            // 预加载可见项目前后的项目
            for indexPath in visibleIndexPaths {
                // 前面的项目
                if indexPath.item > 0 {
                    preloadIndexPaths.append(IndexPath(item: indexPath.item - 1, section: indexPath.section))
                }
                // 后面的项目
                if indexPath.item < videoEffects.count - 1 {
                    preloadIndexPaths.append(IndexPath(item: indexPath.item + 1, section: indexPath.section))
                }
            }
            
            // 去重
            let uniqueIndexPaths = Array(Set(preloadIndexPaths))
            
            // 预加载视频
            for indexPath in uniqueIndexPaths {
                guard indexPath.item < videoEffects.count else { continue }
                
                let videoEffect = videoEffects[indexPath.item]
                guard let url = URL(string: videoEffect.videoUrl) else { continue }
                
                Task {
                    await VideoLoadingPool.shared.requestLoad(
                        url: url,
                        priority: .low
                    ) { _ in
                        // 预加载完成，无需处理结果
                    }
                }
            }
            
            Logger.debug("VirtualizedVideoGrid: Preloading \(uniqueIndexPaths.count) nearby items")
        }
    }
}

