//
//  VideoEffectCell.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/14.
//

import UIKit

/// 高性能视频效果 Cell
class VideoEffectCell: UICollectionViewCell {
    
    static let identifier = "VideoEffectCell"
    
    // MARK: - UI Components
    
    private var thumbnailImageView: UIImageView!
    private var playerView: PlayerView?
    private var loadingIndicator: UIActivityIndicatorView!
    private var playButton: UIButton!
    private var titleLabel: UILabel!
    private var gradientLayer: CAGradientLayer!
    
    // MARK: - Properties
    
    private var videoEffect: VideoEffect?
    private var isVisible = false
    private var loadingState: LoadingState = .placeholder
    private var onTap: (() -> Void)?
    
    // MARK: - Types
    
    private enum LoadingState {
        case placeholder
        case loadingThumbnail
        case thumbnailLoaded
        case loadingVideo
        case videoLoaded
        case error
    }
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupGestures()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupGestures()
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        // 更新布局
        thumbnailImageView.frame = contentView.bounds
        playerView?.frame = contentView.bounds
        loadingIndicator.center = contentView.center
        
        // 更新渐变层
        gradientLayer.frame = contentView.bounds
        
        // 播放按钮居中
        playButton.center = CGPoint(
            x: contentView.bounds.midX,
            y: contentView.bounds.midY
        )
        
        // 标题标签在底部
        let titleHeight: CGFloat = 40
        titleLabel.frame = CGRect(
            x: 8,
            y: contentView.bounds.height - titleHeight,
            width: contentView.bounds.width - 16,
            height: titleHeight
        )
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        
        Logger.debug("🔄 VideoEffectCell: prepareForReuse called for \(videoEffect?.name ?? "unknown")")
        
        // 重要：清理资源
        stopVideoPlayback()
        thumbnailImageView.image = nil
        titleLabel.text = ""
        videoEffect = nil
        isVisible = false
        loadingState = .placeholder
        onTap = nil
        
        // 重置 UI 状态
        loadingIndicator.stopAnimating()
        playButton.isHidden = true
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        contentView.backgroundColor = UIColor.systemGray6
        contentView.layer.cornerRadius = 16
        contentView.clipsToBounds = true
        
        // 缩略图
        thumbnailImageView = UIImageView()
        thumbnailImageView.contentMode = .scaleAspectFill
        thumbnailImageView.clipsToBounds = true
        contentView.addSubview(thumbnailImageView)
        
        // 渐变遮罩
        gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.clear.cgColor,
            UIColor.black.withAlphaComponent(0.6).cgColor
        ]
        gradientLayer.locations = [0.0, 1.0]
        contentView.layer.addSublayer(gradientLayer)
        
        // 加载指示器
        loadingIndicator = UIActivityIndicatorView(style: .medium)
        loadingIndicator.color = .white
        loadingIndicator.hidesWhenStopped = true
        contentView.addSubview(loadingIndicator)
        
        // 播放按钮
        playButton = UIButton(type: .system)
        playButton.setImage(UIImage(systemName: "play.circle.fill"), for: .normal)
        playButton.tintColor = .white
        playButton.titleLabel?.font = UIFont.systemFont(ofSize: 32)
        playButton.isHidden = true
        contentView.addSubview(playButton)
        
        // 标题标签
        titleLabel = UILabel()
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
        titleLabel.textColor = .white
        titleLabel.numberOfLines = 2
        titleLabel.textAlignment = .left
        contentView.addSubview(titleLabel)
        
        // 边框
        contentView.layer.borderWidth = 1
        contentView.layer.borderColor = UIColor.systemGray4.cgColor
    }
    
    private func setupGestures() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap))
        contentView.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Configuration
    
    func configure(with videoEffect: VideoEffect, onTap: @escaping () -> Void) {
        self.videoEffect = videoEffect
        self.onTap = onTap
        
        // 设置标题
        titleLabel.text = videoEffect.name
        
        Logger.debug("🔧 VideoEffectCell: Configuring cell for \(videoEffect.name), isVisible: \(isVisible)")
        
        // 开始加载缩略图
        loadThumbnail()
        
        // 如果当前可见，开始加载视频
        if isVisible {
            startVideoLoading()
        }
    }
    
    // MARK: - Visibility Management
    
    func didBecomeVisible() {
        isVisible = true
        Logger.debug("👀 VideoEffectCell: Cell became visible for \(videoEffect?.name ?? "unknown"), loadingState: \(loadingState)")
        
        guard let videoEffect = videoEffect else {
            Logger.error("❌ VideoEffectCell: No videoEffect when becoming visible")
            return
        }
        
        switch loadingState {
        case .thumbnailLoaded:
            // 理想情况：缩略图已加载，开始视频加载
            startVideoLoading()
        case .placeholder, .loadingThumbnail:
            // 缩略图还在加载中，等待完成后会自动触发视频加载
            Logger.debug("⏳ VideoEffectCell: Waiting for thumbnail to load, current state: \(loadingState)")
        case .loadingVideo:
            // 视频已在加载中，无需重复操作
            Logger.debug("🔄 VideoEffectCell: Video already loading")
        case .videoLoaded:
            // 视频已加载，确保正在播放
            if let playerView = playerView {
                playerView.play()
                Logger.debug("▶️ VideoEffectCell: Resumed video playback")
            }
        case .error:
            // 错误状态，尝试重新加载缩略图
            Logger.info("🔄 VideoEffectCell: Retrying from error state")
            loadThumbnail()
        }
    }
    
    func didBecomeInvisible() {
        isVisible = false
        Logger.debug("👻 VideoEffectCell: Cell became invisible for \(videoEffect?.name ?? "unknown")")
        stopVideoPlayback()
    }
    
    // MARK: - Loading Management
    
    private func loadThumbnail() {
        guard let videoEffect = videoEffect else { 
            Logger.error("❌ VideoEffectCell: No videoEffect in loadThumbnail")
            return 
        }
        
        guard let url = URL(string: videoEffect.posterUrl) else { 
            Logger.error("❌ VideoEffectCell: Invalid poster URL: \(videoEffect.posterUrl)")
            return 
        }
        
        loadingState = .loadingThumbnail
        loadingIndicator.startAnimating()
        
        Task {
            do {
                let (data, _) = try await URLSession.shared.data(from: url)
                let thumbnail = UIImage(data: data)
                
                await MainActor.run {
                    self.loadingIndicator.stopAnimating()
                    
                    if let thumbnail = thumbnail {
                        self.thumbnailImageView.image = thumbnail
                        self.loadingState = .thumbnailLoaded
                        self.playButton.isHidden = false
                        
                        Logger.debug("✅ VideoEffectCell: Thumbnail loaded for \(videoEffect.name), isVisible: \(self.isVisible)")
                        
                        // 如果可见，继续加载视频
                        if self.isVisible {
                            self.startVideoLoading()
                        }
                    } else {
                        Logger.error("❌ VideoEffectCell: Failed to create thumbnail image from data")
                        self.loadingState = .error
                        self.showErrorState()
                    }
                }
            } catch {
                Logger.error("❌ VideoEffectCell: Thumbnail loading failed: \(error)")
                await MainActor.run {
                    self.loadingIndicator.stopAnimating()
                    self.loadingState = .error
                    self.showErrorState()
                }
            }
        }
    }
    
    private func startVideoLoading() {
        guard let videoEffect = videoEffect else { 
            Logger.error("❌ VideoEffectCell: No videoEffect in startVideoLoading")
            return 
        }
        
        guard let url = URL(string: videoEffect.videoUrl) else {
            Logger.error("❌ VideoEffectCell: Invalid video URL: \(videoEffect.videoUrl)")
            return
        }
        
        guard loadingState == .thumbnailLoaded else {
            Logger.debug("⏳ VideoEffectCell: Thumbnail not loaded yet, current state: \(loadingState)")
            return
        }
        
        loadingState = .loadingVideo
        
        Task {
            await VideoLoadingPool.shared.requestLoad(
                url: url,
                priority: .high
            ) { [weak self] result in
                DispatchQueue.main.async {
                    guard let self = self else { return }
                    
                    switch result {
                    case .success(let localURL):
                        Logger.debug("✅ VideoEffectCell: Video loaded successfully for \(videoEffect.name)")
                        self.setupVideoPlayer(with: localURL)
                    case .failure(let error):
                        Logger.error("❌ VideoEffectCell: Video loading failed for \(videoEffect.name): \(error)")
                        self.loadingState = .error
                        self.showErrorState()
                    }
                }
            }
        }
    }
    
    private func setupVideoPlayer(with url: URL) {
        // 从复用池获取播放器
        playerView = PlayerPool.shared.getPlayer(priority: .normal)
        guard let playerView = playerView else { 
            Logger.error("❌ VideoEffectCell: Failed to get player from pool - pool may be exhausted")
            // 降级处理：回到缩略图状态
            loadingState = .thumbnailLoaded
            playButton.isHidden = false
            return 
        }
        
        // 配置播放器
        playerView.configure(with: url)
        playerView.frame = contentView.bounds
        
        // 插入到缩略图上方
        contentView.insertSubview(playerView, aboveSubview: thumbnailImageView)
        
        // 隐藏播放按钮，显示视频
        playButton.isHidden = true
        loadingState = .videoLoaded
        
        // 开始播放
        playerView.play()
        
        Logger.debug("▶️ VideoEffectCell: Started video playback for \(videoEffect?.name ?? "unknown")")
    }
    
    private func stopVideoPlayback() {
        if let playerView = playerView {
            Logger.debug("⏹️ VideoEffectCell: Stopping video playback for \(videoEffect?.name ?? "unknown")")
            PlayerPool.shared.returnPlayer(playerView)
            playerView.removeFromSuperview()
            self.playerView = nil
        }
        
        // 显示播放按钮
        if loadingState == .videoLoaded {
            playButton.isHidden = false
            loadingState = .thumbnailLoaded
        }
    }
    
    private func showErrorState() {
        playButton.isHidden = true
        
        // 显示错误图标
        let errorImageView = UIImageView(image: UIImage(systemName: "exclamationmark.triangle"))
        errorImageView.tintColor = .systemRed
        errorImageView.contentMode = .scaleAspectFit
        errorImageView.frame = CGRect(x: 0, y: 0, width: 32, height: 32)
        errorImageView.center = contentView.center
        contentView.addSubview(errorImageView)
    }
    
    // MARK: - Actions
    
    @objc private func handleTap() {
        onTap?()
    }
}