//
//  CreateVideoFromTemplateViewModel.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/3.
//

import AVKit
import Combine
import Foundation
import SwiftUI

/// Create Video From Template ViewModel
@MainActor
public class CreateVideoFromTemplateViewModel: ObservableObject {

  // MARK: - Published Properties

  @Published public var isLoading: Bool = false
  @Published public var loadingState: VideoCreationLoadingState = .idle
  @Published public var errorMessage: String?
  @Published public var selectedImages: [String] = []
  @Published public var isUserAssetSelectionPresented: Bool = false
  @Published public var generatedVideoUrl: String?
  @Published public var demoPlayer: AVPlayer?

  // MARK: - Private Properties

  private let templateId: String
  private let demoUrl: String
  private let inputImageUrls: [String]
  private let inputImageLimits: Int
  private let inputGuide: String
  private let demoPrompt: String

  // VideoGenerationHandler 集成
  private let videoGenerationHandler: VideoGenerationHandler
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Initialization

  public init(
    templateId: String,
    demoUrl: String,
    inputImageUrls: [String],
    inputImageLimits: Int,
    inputGuide: String,
    demoPrompt: String
  ) {
    self.templateId = templateId
    self.demoUrl = demoUrl
    self.inputImageUrls = inputImageUrls
    self.inputImageLimits = inputImageLimits
    self.inputGuide = inputGuide
    self.demoPrompt = demoPrompt

    // 初始化 VideoGenerationHandler
    let container = AppDependencyContainer.shared
    self.videoGenerationHandler = VideoGenerationHandler(
      videoGenerationService: container.videoModule.videoGenerationService,
      assetService: container.assetModule.assetService
    )

    // Pre-load input images if provided
    loadInputImages()

    // 设置状态观察
    setupVideoGenerationObservers()
  }

  // MARK: - Computed Properties

  public var previewImage: UIImage? {
    guard let firstImageUrl = selectedImages.first,
      let url = URL(string: firstImageUrl),
      let data = try? Data(contentsOf: url)
    else { return nil }
    return UIImage(data: data)
  }

  // MARK: - Public Properties (computed from private properties)

  public var templateInputGuide: String {
    return inputGuide
  }

  public var templateInputImageLimits: Int {
    return inputImageLimits
  }

  public var templateDemoUrl: String {
    return demoUrl
  }

  public var templateDemoPrompt: String {
    return demoPrompt
  }

  // MARK: - Public Methods

  /// Setup demo video player
  public func setupDemoVideo(url: String) {
    guard let videoURL = URL(string: url) else { return }
    demoPlayer = AVPlayer(url: videoURL)

    // Setup auto-loop
    NotificationCenter.default.addObserver(
      forName: .AVPlayerItemDidPlayToEndTime,
      object: demoPlayer?.currentItem,
      queue: .main
    ) { [weak self] _ in
      Task { @MainActor in
        self?.demoPlayer?.seek(to: .zero)
        self?.demoPlayer?.play()
      }
    }

    // Auto-play
    demoPlayer?.play()
  }

  /// Start video creation process - 使用 VideoGenerationHandler
  public func startVideoCreation() {
    guard !selectedImages.isEmpty else {
      errorMessage = "Please select at least one image to create your video."
      return
    }

    // 使用 VideoGenerationHandler 开始生成
    videoGenerationHandler.startGenerationWithImageUrls(
      prompt: demoPrompt,
      imageUrls: selectedImages,
      template: templateId,
      chatId: nil
    )
  }

  /// Add image to selection
  public func addImage(_ imageUrl: String) {
    guard selectedImages.count < inputImageLimits else { return }
    selectedImages.append(imageUrl)
  }

  /// Add or replace image - if images exist, replace the first one; otherwise add new image
  public func addOrReplaceImage(_ imageUrl: String) {
    if selectedImages.isEmpty {
      // No images exist, add new image
      guard selectedImages.count < inputImageLimits else { return }
      selectedImages.append(imageUrl)
    } else {
      // Images exist, replace the first one
      selectedImages[0] = imageUrl
    }
  }

  /// Remove image at index
  public func removeImage(at index: Int) {
    guard index < selectedImages.count else { return }
    selectedImages.remove(at: index)
  }

  /// Handle selected assets from UserAssetSelectionView
  public func handleSelectedAssets(_ assets: [UserAsset]) {
    Task {
      for asset in assets {
        guard selectedImages.count < inputImageLimits else { break }

        await MainActor.run {
          addImage(asset.url)
        }
      }
      await MainActor.run {
        isUserAssetSelectionPresented = false
      }
    }
  }

  /// Cancel video creation
  public func cancelVideoCreation() {
    videoGenerationHandler.cancelGeneration()
  }

  /// Retry video creation
  public func retryVideoCreation() {
    // 清除错误状态
    errorMessage = nil

    // 重新开始生成
    startVideoCreation()
  }

  /// View in profile
  public func viewInProfile() {
    // Reset state
    // isLoading = false
    // loadingState = .idle
    // generatedVideoUrl = nil
  }

  // MARK: - Private Methods

  /// 设置视频生成状态观察
  private func setupVideoGenerationObservers() {
    // 观察 VideoGenerationHandler 的状态变化
    videoGenerationHandler.$isLoading
      .combineLatest(
        videoGenerationHandler.$taskCreatedSuccessfully,
        videoGenerationHandler.$generatedVideoUrls,
        videoGenerationHandler.$errorMessage
      )
      .receive(on: DispatchQueue.main)
      .sink { [weak self] (isLoading, taskCreated, videoUrls, errorMessage) in
        self?.updateLoadingState(
          isLoading: isLoading,
          taskCreated: taskCreated,
          videoUrls: videoUrls,
          errorMessage: errorMessage
        )
      }
      .store(in: &cancellables)
  }

  /// 更新加载状态
  private func updateLoadingState(
    isLoading: Bool,
    taskCreated: Bool,
    videoUrls: [String]?,
    errorMessage: String?
  ) {
    // 更新基础加载状态
    self.isLoading = isLoading
    self.errorMessage = errorMessage

    // 映射到 VideoCreationLoadingState
    if let errorMessage = errorMessage {
      self.loadingState = .failed(errorMessage)
    } else if let videoUrls = videoUrls, !videoUrls.isEmpty {
      self.generatedVideoUrl = videoUrls.first
      self.loadingState = .completed
    } else if isLoading && taskCreated {
      self.loadingState = .generating
    } else if isLoading && !taskCreated {
      self.loadingState = .creatingTask
    } else {
      self.loadingState = .idle
    }
  }

  /// Load input images from URLs
  private func loadInputImages() {
    guard !inputImageUrls.isEmpty else { return }

    Task {
      for imageUrl in inputImageUrls {
        guard selectedImages.count < inputImageLimits else { break }

        await MainActor.run {
          selectedImages.append(imageUrl)
        }
      }
    }
  }
}
