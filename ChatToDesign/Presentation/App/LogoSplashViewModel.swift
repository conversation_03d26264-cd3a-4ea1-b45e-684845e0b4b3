//
//  LogoSplashViewModel.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/14.
//

import Combine
import Foundation
import SwiftUI

/// Logo启动页面的ViewModel
/// 负责监听CMS数据加载状态并控制页面跳转
@MainActor
class LogoSplashViewModel: ObservableObject {
  // MARK: - Published Properties
  
  /// 是否显示加载动画
  @Published var isLoading: Bool = true
  
  /// 是否应该显示主页面
  @Published var shouldShowHomePage: Bool = false
  
  // MARK: - Private Properties
  
  /// HomePageViewModel实例，用于监听CMS加载状态
  private let homePageViewModel: HomePageViewModel
  
  /// 取消令牌集合
  private var cancellables = Set<AnyCancellable>()
  
  /// 最小显示时间（秒）- 确保Logo至少显示一定时间
  private let minimumDisplayTime: TimeInterval = 2.0
  
  /// 启动时间
  private let startTime: Date
  
  // MARK: - Initialization
  
  init(homePageViewModel: HomePageViewModel) {
    self.homePageViewModel = homePageViewModel
    self.startTime = Date()
    
    setupCMSLoadingObserver()
  }
  
  // MARK: - Private Methods
  
  /// 设置CMS数据加载状态观察者
  private func setupCMSLoadingObserver() {
    // 监听CMS数据加载完成状态
    homePageViewModel.$isCMSDataLoaded
      .combineLatest(homePageViewModel.$isLoadingCMS)
      .receive(on: DispatchQueue.main)
      .sink { [weak self] (isLoaded, isLoading) in
        guard let self = self else { return }
        
        // 当CMS数据加载完成且不再加载中时
        if isLoaded && !isLoading {
          self.handleCMSLoadingCompleted()
        }
      }
      .store(in: &cancellables)
  }
  
  /// 处理CMS数据加载完成
  private func handleCMSLoadingCompleted() {
    let elapsedTime = Date().timeIntervalSince(startTime)
    let remainingTime = max(0, minimumDisplayTime - elapsedTime)
    
    // 确保Logo至少显示最小时间
    DispatchQueue.main.asyncAfter(deadline: .now() + remainingTime) { [weak self] in
      guard let self = self else { return }
      
      withAnimation(.easeInOut(duration: 0.5)) {
        self.isLoading = false
        self.shouldShowHomePage = true
      }
      
      Logger.info("Logo splash completed, showing home page")
    }
  }
}
