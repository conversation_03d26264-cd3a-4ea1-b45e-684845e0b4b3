import AVKit
import Kingfisher
import Swift<PERSON>

struct VideoTaskGridItemView: View {
  let task: VideoGenerationTask
  @State private var showVideo = false
  @State private var player: AVPlayer?
  @State private var isPlayerReady = false
  @State private var cachedVideoURL: URL?

  var body: some View {
    ZStack {
      // Main Content
      if showVideo && task.status == .succeeded && isPlayerReady {
        // Auto-playing video
        VideoPlayer(player: player)
          .aspectRatio(1, contentMode: .fill)
          .clipped()
          .onDisappear {
            // 只暂停，不销毁 player
            player?.pause()
          }
      } else {
        // Cover Image or Placeholder
        coverImageView
          .aspectRatio(1, contentMode: .fill)
          .clipped()
      }

      // Video Play Icon Overlay (only show when not playing video)
      if !showVideo && task.status == .succeeded && task.videoUrl != nil {
        videoPlayOverlay
      }

      // Status Overlay
      if task.status != .succeeded {
        statusOverlay
      }
    }
    .background(Color.gray.opacity(0.05))
    .cornerRadius(16)
    .clipped()
    .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    .onAppear {
      // 预加载和设置视频
      Task {
        await setupVideoIfNeeded()
      }
    }
    .onDisappear {
      // 只暂停播放，保持 player 实例以便快速恢复
      player?.pause()
      showVideo = false
    }
  }

  /// 设置视频播放器（如果需要）
  private func setupVideoIfNeeded() async {
    guard task.status == .succeeded,
      let videoUrlString = task.videoUrl,
      let originalURL = URL(string: videoUrlString)
    else {
      return
    }

    // 如果 player 已存在且 URL 相同，直接显示
    if let existingPlayer = player,
      let currentItem = existingPlayer.currentItem,
      let currentURL = (currentItem.asset as? AVURLAsset)?.url,
      currentURL == originalURL || currentURL == cachedVideoURL
    {
      isPlayerReady = true
      showVideo = true
      existingPlayer.play()
      return
    }

    // 尝试获取缓存的视频
    let videoURL = await getCachedVideoURL(originalURL: originalURL)

    // 创建新的 player
    await MainActor.run {
      setupPlayer(url: videoURL)
    }
  }

  /// 获取缓存的视频 URL，如果没有缓存则使用原始 URL
  private func getCachedVideoURL(originalURL: URL) async -> URL {
    // 检查是否已有缓存
    if let cached = await VideoCacheManager.shared.getCachedVideoURL(for: originalURL) {
      cachedVideoURL = cached
      return cached
    }

    // 启动后台预加载（不阻塞当前播放）
    Task.detached {
      await VideoCacheManager.shared.preloadVideo(url: originalURL)
    }

    return originalURL
  }

  /// 设置播放器
  private func setupPlayer(url: URL) {
    player = AVPlayer(url: url)
    player?.isMuted = true  // Auto-play silently

    // Loop the video
    NotificationCenter.default.addObserver(
      forName: .AVPlayerItemDidPlayToEndTime,
      object: player?.currentItem,
      queue: .main
    ) { _ in
      self.player?.seek(to: .zero)
      self.player?.play()
    }

    // 开始播放
    player?.play()

    // 设置准备就绪状态（减少延迟）
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
      self.isPlayerReady = true
      self.showVideo = true
    }
  }

  @ViewBuilder
  private var coverImageView: some View {
    // 根据任务状态选择显示的图片
    let imageUrlString: String? = {
      if task.status == .succeeded {
        // 成功状态：优先显示 coverImageUrl，如果没有则显示 inputImageUrl
        return task.coverImageUrl ?? task.inputImageUrl
      } else {
        // 非成功状态：优先显示 inputImageUrl，如果没有则显示 coverImageUrl
        return task.inputImageUrl ?? task.coverImageUrl
      }
    }()

    if let imageUrlString = imageUrlString,
      let imageUrl = URL(string: imageUrlString)
    {
      KFImage(imageUrl)
        .placeholder {
          ProgressView()
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.gray.opacity(0.1))
        }
        .onFailure { _ in
          // Kingfisher handles failure internally, but we can add custom logic here if needed
        }
        .retry(maxCount: 3)
        .fade(duration: 0.25)
        .resizable()
        .aspectRatio(contentMode: .fill)
    } else {
      // Placeholder if no image URL is available
      VStack {
        Image(systemName: "video.fill")
          .font(.title)
          .foregroundColor(.gray.opacity(0.5))
        Text("Video")
          .font(.caption2)
          .foregroundColor(.gray.opacity(0.7))
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(Color.gray.opacity(0.1))
    }
  }

  @ViewBuilder
  private var videoPlayOverlay: some View {
    VStack {
      Spacer()
      HStack {
        Spacer()

        // Play button in bottom-right corner
        Image(systemName: "play.circle.fill")
          .font(.title2)
          .foregroundColor(.white)
          .background(
            Circle()
              .fill(Color.black.opacity(0.6))
              .frame(width: 32, height: 32)
          )
          .padding(8)
      }
    }
  }

  @ViewBuilder
  private var statusOverlay: some View {
    VStack {
      HStack {
        Spacer()
        if task.status == .processing || task.status == .pending {
          progressIndicator
            .padding(8)
        } else {
          statusIndicator
            .padding(8)
        }
      }
      Spacer()
    }
  }

  @ViewBuilder
  private var progressIndicator: some View {
    VStack(spacing: 4) {
      if let progress = task.progress {
        CircularProgressView(progress: Double(progress) / 100.0)
          .frame(width: 24, height: 24)

        Text("\(progress)%")
          .font(.caption2)
          .fontWeight(.semibold)
          .foregroundColor(.white)
      } else {
        ProgressView()
          .progressViewStyle(CircularProgressViewStyle(tint: .white))
          .scaleEffect(0.8)

        Text("Processing")
          .font(.caption2)
          .fontWeight(.semibold)
          .foregroundColor(.white)
      }
    }
    .padding(.horizontal, 8)
    .padding(.vertical, 6)
    .background(
      RoundedRectangle(cornerRadius: 12)
        .fill(Color.black.opacity(0.75))
        .background(
          RoundedRectangle(cornerRadius: 12)
            .fill(.ultraThinMaterial)
        )
    )
  }

  @ViewBuilder
  private var statusIndicator: some View {
    HStack(spacing: 4) {
      Circle()
        .fill(statusColor)
        .frame(width: 8, height: 8)

      Text(statusText)
        .font(.caption2)
        .fontWeight(.semibold)
        .foregroundColor(.white)
    }
    .padding(.horizontal, 10)
    .padding(.vertical, 6)
    .background(
      RoundedRectangle(cornerRadius: 12)
        .fill(Color.black.opacity(0.75))
        .background(
          RoundedRectangle(cornerRadius: 12)
            .fill(.ultraThinMaterial)
        )
    )
  }

  private var statusText: String {
    switch task.status {
    case .pending:
      return "Pending"
    case .processing:
      return "Processing"
    case .failed, .no_result:
      return "Failed"
    case .succeeded:
      return "Done"
    case .deleted:
      return "Deleted"
    case .unknown:
      return "Unknown"
    }
  }

  private var statusColor: Color {
    switch task.status {
    case .pending, .processing:
      return .orange
    case .succeeded:
      return .green
    case .failed, .no_result:
      return .red
    case .deleted:
      return .gray
    case .unknown:
      return .gray
    }
  }
}

#Preview {
  VStack(spacing: 20) {
    // Processing video task with progress (shows input image)
    VideoTaskGridItemView(
      task: VideoGenerationTask(
        taskId: "processing-video-task",
        userId: "sample-user",
        status: .processing,
        progress: 65,
        inputImageUrl: "https://example.com/input.jpg",
        coverImageUrl: "https://example.com/cover.jpg"
      )
    )
    .frame(width: 150, height: 150)

    // Succeeded video task (shows cover image)
    VideoTaskGridItemView(
      task: VideoGenerationTask(
        taskId: "succeeded-video-task",
        userId: "sample-user",
        status: .succeeded,
        inputImageUrl: "https://example.com/input.jpg",
        coverImageUrl: "https://example.com/cover.jpg",
        videoUrl: "https://example.com/video.mp4"
      )
    )
    .frame(width: 150, height: 150)

    // Failed video task (shows input image)
    VideoTaskGridItemView(
      task: VideoGenerationTask(
        taskId: "failed-video-task",
        userId: "sample-user",
        status: .failed,
        inputImageUrl: "https://example.com/input.jpg",
        coverImageUrl: nil,
        videoUrl: nil
      )
    )
    .frame(width: 150, height: 150)

    // Pending video task with only input image
    VideoTaskGridItemView(
      task: VideoGenerationTask(
        taskId: "pending-video-task",
        userId: "sample-user",
        status: .pending,
        inputImageUrl: "https://example.com/input.jpg"
      )
    )
    .frame(width: 150, height: 150)
  }
  .padding()
  .background(Color.gray.opacity(0.1))
}
