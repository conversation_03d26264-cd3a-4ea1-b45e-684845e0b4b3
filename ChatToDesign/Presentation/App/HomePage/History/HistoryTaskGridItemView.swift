import Kingfisher
import Swift<PERSON>

struct HistoryTaskGridItemView: View {
  let task: ImageGenerationTask

  var body: some View {
    ZStack {
      // Main Image
      resultImageView
        .aspectRatio(1, contentMode: .fill)
        .clipped()

      // Status Overlay
      if task.status != .succeeded {
        statusOverlay
      }
    }
    .background(Color.gray.opacity(0.05))
    .cornerRadius(16)
    .clipped()
    .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
  }

  @ViewBuilder
  private var resultImageView: some View {
    if let firstImageUrlString = task.resultImageUrls?.first,
      let imageUrl = URL(string: firstImageUrlString)
    {
      KFImage(imageUrl)
        .placeholder {
          ProgressView()
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.gray.opacity(0.1))
        }
        .onFailure { _ in
          // Kingfisher handles failure internally, but we can add custom logic here if needed
        }
        .retry(maxCount: 3)
        .fade(duration: 0.25)
        .resizable()
        .aspectRatio(contentMode: .fill)
    } else if let firstInputImageUrlString = task.inputImageUrls?.first,
      let imageUrl = URL(string: firstInputImageUrlString)
    {
      // Fallback to input image if no result image
      KFImage(imageUrl)
        .placeholder {
          ProgressView()
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.gray.opacity(0.1))
        }
        .onFailure { _ in
          // Kingfisher handles failure internally, but we can add custom logic here if needed
        }
        .retry(maxCount: 3)
        .fade(duration: 0.25)
        .resizable()
        .aspectRatio(contentMode: .fill)
    } else {
      // Placeholder if no image URL is available
      Image(systemName: "photo.artframe")
        .font(.title)
        .foregroundColor(.gray.opacity(0.5))
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.gray.opacity(0.1))
    }
  }

  @ViewBuilder
  private var statusOverlay: some View {
    VStack {
      HStack {
        Spacer()
        if task.status == .processing || task.status == .pending {
          progressIndicator
            .padding(8)
        } else {
          statusIndicator
            .padding(8)
        }
      }
      Spacer()
    }
  }

  @ViewBuilder
  private var progressIndicator: some View {
    ZStack {
      // Background circle
      Circle()
        .stroke(Color.white.opacity(0.3), lineWidth: 3)
        .frame(width: 40, height: 40)

      // Progress circle
      Circle()
        .trim(from: 0, to: CGFloat(task.progress ?? 0) / 100.0)
        .stroke(Color.white, lineWidth: 3)
        .rotationEffect(.degrees(-90))
        .frame(width: 40, height: 40)

      // Progress text
      Text("\(task.progress ?? 0)")
        .font(.system(size: 14, weight: .bold))
        .foregroundColor(.white)
    }
    .background(
      Circle()
        .fill(Color.black.opacity(0.6))
        .frame(width: 50, height: 50)
    )
  }

  @ViewBuilder
  private var statusIndicator: some View {
    HStack(spacing: 4) {
      Circle()
        .fill(statusColor)
        .frame(width: 8, height: 8)

      Text(statusText)
        .font(.caption2)
        .fontWeight(.semibold)
        .foregroundColor(.white)
    }
    .padding(.horizontal, 10)
    .padding(.vertical, 6)
    .background(
      RoundedRectangle(cornerRadius: 12)
        .fill(Color.black.opacity(0.75))
        .background(
          RoundedRectangle(cornerRadius: 12)
            .fill(.ultraThinMaterial)
        )
    )
  }

  private var statusText: String {
    switch task.status {
    case .pending:
      return "Pending"
    case .processing:
      return "Processing"
    case .failed, .no_result:
      return "Failed"
    case .succeeded:
      return "Done"
    case .deleted:
      return "Deleted"
    case .unknown:
      return "Unknown"
    }
  }

  private var statusColor: Color {
    switch task.status {
    case .pending, .processing:
      return .orange
    case .succeeded:
      return .green
    case .failed, .no_result:
      return .red
    case .deleted:
      return .gray
    case .unknown:
      return .gray
    }
  }
}

#Preview {
  VStack(spacing: 20) {
    // Processing task with progress
    HistoryTaskGridItemView(
      task: ImageGenerationTask(
        taskId: "processing-task",
        userId: "sample-user",
        status: .processing,
        prompt: "A beautiful landscape",
        resultImageUrls: ["https://example.com/image.jpg"],
        progress: 75
      )
    )
    .frame(width: 150, height: 150)

    // Failed task
    HistoryTaskGridItemView(
      task: ImageGenerationTask(
        taskId: "failed-task",
        userId: "sample-user",
        status: .failed,
        prompt: "A failed generation",
        resultImageUrls: nil,
        progress: nil
      )
    )
    .frame(width: 150, height: 150)

    // Succeeded task (no overlay)
    HistoryTaskGridItemView(
      task: ImageGenerationTask(
        taskId: "success-task",
        userId: "sample-user",
        status: .succeeded,
        prompt: "A successful generation",
        resultImageUrls: ["https://example.com/image.jpg"],
        progress: 100
      )
    )
    .frame(width: 150, height: 150)
  }
  .padding()
}
