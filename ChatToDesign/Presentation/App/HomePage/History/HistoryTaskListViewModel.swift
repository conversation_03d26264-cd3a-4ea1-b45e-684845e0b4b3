import Combine
import FirebaseFirestore
import Foundation
import os

// MARK: - Protocol for unified task handling
protocol HistoryTask: Identifiable {
  var id: String? { get }
  var taskId: String { get }
  var userId: String { get }
  var status: TaskStatus { get }
  var createdAt: Timestamp? { get }
  var progress: Int? { get }
}

// MARK: - Extensions to make existing tasks conform to protocol
extension ImageGenerationTask: HistoryTask {}
extension VideoGenerationTask: HistoryTask {}

@MainActor
class HistoryTaskListViewModel: ObservableObject {
  // MARK: - Published Properties
  @Published var selectedTaskType: TaskType = .image
  @Published var imageTasks: [ImageGenerationTask] = []
  @Published var videoTasks: [VideoGenerationTask] = []
  @Published var isLoading: Bool = false
  @Published var error: Error? = nil
  @Published var searchText: String = ""

  // MARK: - Private Properties
  private var cancellables = Set<AnyCancellable>()
  private var imageTaskSubscription: AnyCancellable?
  private var videoTaskSubscription: AnyCancellable?

  private let designGenerationService: DesignGenerationService
  private let videoGenerationService: VideoGenerationService
  private let userService: UserService

  // MARK: - Initialization
  init(
    designGenerationService: DesignGenerationService = AppDependencyContainer.shared.designModule
      .designGenerationService,
    videoGenerationService: VideoGenerationService = AppDependencyContainer.shared.videoModule
      .videoGenerationService,
    userService: UserService = AppDependencyContainer.shared.userModule.userService
  ) {
    self.designGenerationService = designGenerationService
    self.videoGenerationService = videoGenerationService
    self.userService = userService

    setupUserObserver()
    setupSearchObserver()
  }

  // MARK: - Computed Properties
  var filteredTasks: [any HistoryTask] {
    let tasks: [any HistoryTask] = selectedTaskType == .image ? imageTasks : videoTasks

    if searchText.isEmpty {
      return tasks.sorted { task1, task2 in
        let date1 = task1.createdAt?.dateValue() ?? Date.distantPast
        let date2 = task2.createdAt?.dateValue() ?? Date.distantPast
        return date1 > date2
      }
    } else {
      return tasks.filter { task in
        // For image tasks, search in prompt
        if let imageTask = task as? ImageGenerationTask {
          return (imageTask.prompt ?? "").localizedCaseInsensitiveContains(searchText)
        }
        // For video tasks, search in input data or other relevant fields
        if let videoTask = task as? VideoGenerationTask {
          // You might want to add searchable fields for video tasks
          return videoTask.taskId.localizedCaseInsensitiveContains(searchText)
        }
        return false
      }.sorted { task1, task2 in
        let date1 = task1.createdAt?.dateValue() ?? Date.distantPast
        let date2 = task2.createdAt?.dateValue() ?? Date.distantPast
        return date1 > date2
      }
    }
  }

  var groupedTasks: [(String, [any HistoryTask])] {
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = "d MMM yyyy"
    dateFormatter.locale = Locale(identifier: "en_US")

    let grouped = Dictionary(grouping: filteredTasks) { task in
      guard let createdAt = task.createdAt?.dateValue() else {
        return "Unknown Date"
      }
      return dateFormatter.string(from: createdAt)
    }

    // Sort groups by date (most recent first)
    return grouped.sorted { (group1, group2) in
      let date1 = dateFormatter.date(from: group1.key) ?? Date.distantPast
      let date2 = dateFormatter.date(from: group2.key) ?? Date.distantPast
      return date1 > date2
    }
  }

  // MARK: - Public Methods
  func fetchTasks() async {
    guard let userId = userService.currentUser?.id, !userId.isEmpty else {
      Logger.warning("User ID is empty, cannot fetch history tasks.")
      error = HistoryTaskError.userNotFound
      return
    }

    // 检查是否已有缓存数据，避免不必要的loading状态
    let shouldShowLoading: Bool
    switch selectedTaskType {
    case .image:
      shouldShowLoading = imageTasks.isEmpty
    case .video:
      shouldShowLoading = videoTasks.isEmpty
    }

    if shouldShowLoading {
      isLoading = true
    }
    error = nil

    do {
      switch selectedTaskType {
      case .image:
        // 如果已有数据，跳过重复获取
        if !imageTasks.isEmpty {
          Logger.info("Image tasks already cached (\(imageTasks.count) items), skipping fetch.")
          return
        }
        let tasks = try await designGenerationService.getTaskList(
          userId: userId, limit: 50, startAfterTaskId: nil)
        self.imageTasks = tasks
        Logger.info("Successfully fetched \(tasks.count) image tasks for user \(userId).")

      case .video:
        // 如果已有数据，跳过重复获取
        if !videoTasks.isEmpty {
          Logger.info("Video tasks already cached (\(videoTasks.count) items), skipping fetch.")
          return
        }
        let tasks = try await videoGenerationService.getTaskList(
          userId: userId, limit: 50, startAfterTaskId: nil)
        self.videoTasks = tasks
        Logger.info("Successfully fetched \(tasks.count) video tasks for user \(userId).")
      }
    } catch {
      Logger.error(
        "Error fetching \(selectedTaskType.rawValue.lowercased()) tasks: \(error.localizedDescription)"
      )
      self.error = error
    }

    if shouldShowLoading {
      isLoading = false
    }
  }

  func refreshTasks() async {
    // 强制刷新：清空缓存并重新获取
    switch selectedTaskType {
    case .image:
      imageTasks = []
    case .video:
      videoTasks = []
    }
    await fetchTasks()
  }

  func fetchTasksIfNeeded() async {
    // 只在没有数据时获取，用于tab切换
    await fetchTasks()
  }

  // MARK: - Private Methods
  private func setupUserObserver() {
    userService.currentUserPublisher
      .receive(on: DispatchQueue.main)
      .sink { [weak self] user in
        guard let self = self else { return }
        if user != nil {
          Task {
            await self.fetchTasks()
          }
          self.observeTasks()
        } else {
          Logger.info("User is nil. Clearing history tasks and stopping observers.")
          self.stopObserving()
          self.imageTasks = []
          self.videoTasks = []
          self.error = nil
        }
      }
      .store(in: &cancellables)
  }

  private func setupSearchObserver() {
    $searchText
      .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
      .sink { [weak self] _ in
        // Search filtering is handled in computed property
        // This is just for potential future search-related side effects
      }
      .store(in: &cancellables)
  }

  private func observeTasks() {
    guard let userId = userService.currentUser?.id, !userId.isEmpty else {
      Logger.warning("User ID is empty, cannot observe history tasks.")
      error = HistoryTaskError.userNotFound
      return
    }

    stopObserving()

    // Observe image tasks
    imageTaskSubscription = designGenerationService.observeTaskList(userId: userId, limit: 50)
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          if case .failure(let error) = completion {
            Logger.error("Error observing image tasks: \(error.localizedDescription)")
            self?.error = error
          }
        },
        receiveValue: { [weak self] tasks in
          self?.imageTasks = tasks
          Logger.info("Updated image tasks: \(tasks.count)")
        }
      )

    // Observe video tasks
    videoTaskSubscription = videoGenerationService.observeTaskList(userId: userId, limit: 50)
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          if case .failure(let error) = completion {
            Logger.error("Error observing video tasks: \(error.localizedDescription)")
            self?.error = error
          }
        },
        receiveValue: { [weak self] tasks in
          self?.videoTasks = tasks
          Logger.info("Updated video tasks: \(tasks.count)")
        }
      )
  }

  private func stopObserving() {
    imageTaskSubscription?.cancel()
    videoTaskSubscription?.cancel()
    imageTaskSubscription = nil
    videoTaskSubscription = nil
  }
}

// MARK: - Error Types
enum HistoryTaskError: Error, LocalizedError {
  case userNotFound
  case fetchFailed(Error)

  var errorDescription: String? {
    switch self {
    case .userNotFound:
      return "User not found. Please log in again."
    case .fetchFailed(let error):
      return "Failed to fetch tasks: \(error.localizedDescription)"
    }
  }
}
