import SwiftUI
import os

// MARK: - Task Type Selection
enum TaskType: String, CaseIterable, Identifiable {
  case image = "Image"
  case video = "Video"

  var id: String { self.rawValue }
}

struct HistoryTaskListView: View {
  @ObservedObject var viewModel: HistoryTaskListViewModel

  var body: some View {
    VStack(alignment: .leading, spacing: 0) {
      // Task Type Selector
      taskTypeSelector
        .padding(.horizontal, 16)
        .padding(.bottom, 16)

      // Content Area
      if viewModel.isLoading {
        ProgressView("Loading tasks...")
          .frame(maxWidth: .infinity, maxHeight: .infinity)
      } else if let error = viewModel.error {
        VStack {
          Text("Error loading tasks")
            .font(.headline)
          Text(error.localizedDescription)
            .font(.caption)
            .foregroundColor(.gray)
          But<PERSON>("Retry") {
            Task {
              await viewModel.fetchTasks()
            }
          }
          .padding(.top)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
      } else if viewModel.filteredTasks.isEmpty {
        Text("No tasks found.")
          .font(.subheadline)
          .foregroundColor(.gray)
          .frame(maxWidth: .infinity, maxHeight: .infinity)
      } else {
        ScrollView {
          LazyVStack(alignment: .leading, spacing: 32) {
            ForEach(viewModel.groupedTasks, id: \.0) { dateGroup in
              VStack(alignment: .leading, spacing: 16) {
                // Date Header
                HStack {
                  Text(dateGroup.0)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                  Spacer()
                }
                .padding(.horizontal, 16)

                // Tasks Grid - Dynamic columns based on task type
                LazyVGrid(
                  columns: viewModel.selectedTaskType == .video
                    ? Array(repeating: GridItem(.flexible(), spacing: 8), count: 2)
                    : Array(repeating: GridItem(.flexible(), spacing: 8), count: 4),
                  spacing: 8
                ) {
                  ForEach(dateGroup.1, id: \.taskId) { task in
                    if viewModel.selectedTaskType == .image {
                      if let imageTask = task as? ImageGenerationTask {
                        HistoryTaskGridItemView(task: imageTask)
                          .aspectRatio(1, contentMode: .fit)
                      }
                    } else {
                      if let videoTask = task as? VideoGenerationTask {
                        VideoTaskGridItemView(task: videoTask)
                          .aspectRatio(1, contentMode: .fit)
                      }
                    }
                  }
                }
                .padding(.horizontal, 16)
              }
            }
          }
          .padding(.top, 20)
          .padding(.bottom, 40)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
      }
    }

    .onAppear {
      Task {
        await viewModel.fetchTasksIfNeeded()
      }
      Logger.info(
        "HistoryTaskListView appeared. Filtered tasks count: \(viewModel.filteredTasks.count)"
      )
    }
  }

  // MARK: - Task Type Selector
  @ViewBuilder
  private var taskTypeSelector: some View {
    Picker("Task Type", selection: $viewModel.selectedTaskType) {
      ForEach(TaskType.allCases) { taskType in
        Text(taskType.rawValue).tag(taskType)
      }
    }
    .pickerStyle(SegmentedPickerStyle())
    .onChange(of: viewModel.selectedTaskType) { newTaskType in
      Task {
        await viewModel.fetchTasksIfNeeded()

        // 如果切换到视频 tab，预加载前几个视频
        if newTaskType == .video {
          await preloadVisibleVideos()
        }
      }
    }
  }

  /// 预加载可见的视频（前6个）
  private func preloadVisibleVideos() async {
    let videoTasks = viewModel.filteredTasks.compactMap { $0 as? VideoGenerationTask }
    let visibleTasks = Array(videoTasks.prefix(6))  // 预加载前6个视频

    for task in visibleTasks {
      guard task.status == .succeeded,
        let videoUrlString = task.videoUrl,
        let videoURL = URL(string: videoUrlString)
      else {
        continue
      }

      // 异步预加载，不阻塞 UI
      Task.detached {
        await VideoCacheManager.shared.preloadVideo(url: videoURL)
      }
    }
  }
}

// Preview might need adjustment to pass a mock ViewModel
// struct HistoryTaskListView_Previews: PreviewProvider {
//   static var previews: some View {
//     // Create a mock HomePageViewModel instance for preview
//     let mockViewModel = HomePageViewModel(
//       designGenerationService: MockDesignGenerationService(),  // You'll need a mock service
//       userService: MockUserService()  // You'll need a mock user service
//     )
//     // Optionally, populate with some mock tasks for different filter states
//     // mockViewModel.historyTasks = ImageGenerationTask.mockTasksForPreview()
//     // mockViewModel.selectedTaskFilter = .inProgress

//     return HistoryTaskListView(viewModel: mockViewModel)
//   }
// }
