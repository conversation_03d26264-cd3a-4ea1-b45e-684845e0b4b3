import Combine
import Kingfisher
import SwiftUI

struct ImageEditView: View {
  let originalTask: ImageGenerationTask
  let selectedImageIndex: Int
  @Environment(\.dismiss) private var dismiss

  // Edit functionality
  @StateObject private var editHandler: DesignGenerationHandler
  @State private var editPrompt: String = ""
  @State private var selectedPreset: EditPreset? = nil
  @State private var isEditing = false
  @State private var showingErrorAlert = false
  @State private var errorMessage = ""
  @State private var cancellables = Set<AnyCancellable>()

  // MARK: - Edit Presets

  enum EditPreset: String, CaseIterable, Identifiable {
    case removeBackground = "Remove Background"
    case enhanceImage = "Enhance Image"
    case changeStyle = "Change Style"
    case addObjects = "Add Objects"
    case changeColors = "Change Colors"
    case improveQuality = "Improve Quality"

    var id: String { rawValue }

    var icon: String {
      switch self {
      case .removeBackground: return "scissors"
      case .enhanceImage: return "wand.and.stars"
      case .changeStyle: return "paintbrush"
      case .addObjects: return "plus.circle"
      case .changeColors: return "eyedropper"
      case .improveQuality: return "sparkles"
      }
    }

    var description: String {
      switch self {
      case .removeBackground: return "Remove or replace the background"
      case .enhanceImage: return "Improve image quality and details"
      case .changeStyle: return "Apply different artistic styles"
      case .addObjects: return "Add new elements to the image"
      case .changeColors: return "Modify colors and tones"
      case .improveQuality: return "Upscale and enhance resolution"
      }
    }

    var promptTemplate: String {
      switch self {
      case .removeBackground: return "Remove the background from this image"
      case .enhanceImage: return "Enhance and improve the quality of this image"
      case .changeStyle: return "Change the style of this image to "
      case .addObjects: return "Add to this image: "
      case .changeColors: return "Change the colors in this image to "
      case .improveQuality: return "Improve the quality and resolution of this image"
      }
    }
  }

  // MARK: - Initialization

  init(originalTask: ImageGenerationTask, selectedImageIndex: Int) {
    self.originalTask = originalTask
    self.selectedImageIndex = selectedImageIndex

    // Initialize DesignGenerationHandler with dependencies
    self._editHandler = StateObject(
      wrappedValue: DesignGenerationHandler()
    )
  }

  var body: some View {
    NavigationView {
      VStack(spacing: 0) {
        // Original image preview
        originalImageSection

        // Edit options
        ScrollView {
          VStack(spacing: 20) {
            // Preset options
            presetsSection

            // Custom edit input
            customEditSection

            // Action button
            actionButtonSection
          }
          .padding(.horizontal, 16)
          .padding(.vertical, 20)
        }
        .background(Color(.systemGroupedBackground))
        .onTapGesture {
          // Dismiss keyboard when tapping outside
          hideKeyboard()
        }
      }
      .navigationTitle("Edit Image")
      .navigationBarTitleDisplayMode(.inline)
      .navigationBarBackButtonHidden(true)
      .toolbar {
        ToolbarItem(placement: .navigationBarLeading) {
          Button("Cancel") {
            dismiss()
          }
          .foregroundColor(.primary)
        }
      }
    }
    .alert("Error", isPresented: $showingErrorAlert) {
      Button("OK") {}
    } message: {
      Text(errorMessage)
    }
    .onAppear {
      setupEditHandler()
    }
  }

  // MARK: - Original Image Section

  private var originalImageSection: some View {
    VStack(spacing: 12) {
      Text("Original Image")
        .font(.headline)
        .foregroundColor(.secondary)

      if let resultUrls = originalTask.resultImageUrls,
        selectedImageIndex < resultUrls.count
      {
        KFImage(URL(string: resultUrls[selectedImageIndex]))
          .placeholder {
            RoundedRectangle(cornerRadius: 12)
              .fill(Color(.systemGray5))
              .frame(height: 200)
              .overlay(
                ProgressView()
                  .progressViewStyle(CircularProgressViewStyle())
              )
          }
          .retry(maxCount: 3)
          .fade(duration: 0.25)
          .resizable()
          .aspectRatio(contentMode: .fit)
          .frame(maxHeight: 200)
          .cornerRadius(12)
      }
    }
    .padding(.horizontal, 20)
    .padding(.vertical, 16)
    .background(Color(.systemBackground))
  }

  // MARK: - Presets Section

  private var presetsSection: some View {
    VStack(alignment: .leading, spacing: 12) {
      Text("Quick Edits")
        .font(.system(size: 18, weight: .semibold))
        .foregroundColor(.primary)

      LazyVGrid(
        columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 3), spacing: 8
      ) {
        ForEach(EditPreset.allCases) { preset in
          PresetCard(
            preset: preset,
            isSelected: selectedPreset == preset,
            onTap: {
              selectPreset(preset)
            }
          )
        }
      }
    }
  }

  // MARK: - Custom Edit Section

  private var customEditSection: some View {
    VStack(alignment: .leading, spacing: 12) {
      Text("Custom Edit")
        .font(.system(size: 18, weight: .semibold))
        .foregroundColor(.primary)

      VStack(alignment: .leading, spacing: 12) {
        Text("Describe what you want to change:")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.secondary)

        ZStack(alignment: .topLeading) {
          RoundedRectangle(cornerRadius: 12)
            .foregroundColor(Color(.systemBackground))
            .overlay(
              RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
            )
            .frame(minHeight: 80)

          TextField(
            "e.g., Make the sky more dramatic, add flowers...",
            text: $editPrompt,
            axis: .vertical
          )
          .font(.system(size: 15))
          .padding(12)
          .lineLimit(3...6)
          .textFieldStyle(PlainTextFieldStyle())
        }
      }
      .padding(16)
      .background(Color(.systemGray6).opacity(0.5))
      .cornerRadius(16)
    }
  }

  // MARK: - Action Button Section

  private var actionButtonSection: some View {
    Button(action: {
      startEditing()
    }) {
      HStack(spacing: 8) {
        if isEditing {
          ProgressView()
            .progressViewStyle(CircularProgressViewStyle(tint: .white))
            .scaleEffect(0.7)
        } else {
          Image(systemName: "wand.and.stars")
            .font(.system(size: 14, weight: .medium))
        }

        Text(isEditing ? "Creating Edit..." : "Apply Edit")
          .font(.system(size: 15, weight: .semibold))
      }
      .foregroundColor(.white)
      .frame(maxWidth: .infinity)
      .padding(.vertical, 14)
      .background(canStartEdit ? Color.accentColor : Color(.systemGray3))
      .cornerRadius(12)
    }
    .disabled(!canStartEdit || isEditing)
    .padding(.top, 16)
  }

  // MARK: - Computed Properties

  private var canStartEdit: Bool {
    selectedPreset != nil || !editPrompt.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
  }

  private var finalEditPrompt: String {
    if let preset = selectedPreset {
      if preset.promptTemplate.hasSuffix(" ") {
        return preset.promptTemplate + editPrompt.trimmingCharacters(in: .whitespacesAndNewlines)
      } else {
        return preset.promptTemplate
      }
    } else {
      return editPrompt.trimmingCharacters(in: .whitespacesAndNewlines)
    }
  }

  // MARK: - Methods

  private func hideKeyboard() {
    UIApplication.shared.sendAction(
      #selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
  }

  private func selectPreset(_ preset: EditPreset) {
    selectedPreset = selectedPreset == preset ? nil : preset

    // Auto-fill prompt for presets that need additional input
    if selectedPreset == preset
      && (preset == .changeStyle || preset == .addObjects || preset == .changeColors)
    {
      editPrompt = ""
    }
  }

  private func setupEditHandler() {
    editHandler.$isLoading
      .receive(on: DispatchQueue.main)
      .sink { loading in
        isEditing = loading
      }
      .store(in: &cancellables)

    editHandler.$errorMessage
      .receive(on: DispatchQueue.main)
      .sink { error in
        if let error = error {
          errorMessage = error
          showingErrorAlert = true
        }
      }
      .store(in: &cancellables)

  }

  private func startEditing() {
    guard let resultUrls = originalTask.resultImageUrls,
      selectedImageIndex < resultUrls.count
    else {
      errorMessage = "Original image not available"
      showingErrorAlert = true
      return
    }

    let imageUrl = resultUrls[selectedImageIndex]
    let prompt = finalEditPrompt

    Task {
      do {
        try await editHandler.startGenerationWithImageUrls(
          prompt: prompt,
          imageUrls: [imageUrl],
          designId: originalTask.designId,
          chatId: nil
        )

        // Dismiss immediately after task submission
        await MainActor.run {
          dismiss()
        }

      } catch {
        await MainActor.run {
          errorMessage = "Failed to start editing: \(error.localizedDescription)"
          showingErrorAlert = true
        }
      }
    }
  }
}

// MARK: - Preset Card

struct PresetCard: View {
  let preset: ImageEditView.EditPreset
  let isSelected: Bool
  let onTap: () -> Void

  var body: some View {
    Button(action: onTap) {
      VStack(spacing: 6) {
        Image(systemName: preset.icon)
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(isSelected ? .white : Color.accentColor)

        VStack(spacing: 2) {
          Text(preset.rawValue)
            .font(.system(size: 11, weight: .semibold))
            .foregroundColor(isSelected ? .white : .primary)
            .lineLimit(1)
            .minimumScaleFactor(0.8)

          Text(preset.description)
            .font(.system(size: 9))
            .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)
            .multilineTextAlignment(.center)
            .lineLimit(2)
            .minimumScaleFactor(0.8)
        }
      }
      .frame(maxWidth: .infinity)
      .padding(.vertical, 10)
      .padding(.horizontal, 6)
      .background(isSelected ? Color.accentColor : Color(.systemBackground))
      .cornerRadius(10)
      .overlay(
        RoundedRectangle(cornerRadius: 10)
          .stroke(isSelected ? Color.clear : Color(.systemGray5), lineWidth: 1)
      )
    }
    .buttonStyle(PlainButtonStyle())
  }
}

#Preview {
  ImageEditView(
    originalTask: ImageGenerationTask(
      taskId: "test",
      userId: "user1",
      status: .succeeded,
      prompt: "Disney style cartoon character",
      resultImageUrls: ["https://example.com/image1.jpg"]
    ),
    selectedImageIndex: 0
  )
}
