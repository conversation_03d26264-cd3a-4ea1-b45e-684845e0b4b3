//
//  ImageTemplateItemDetailView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//

import Kingfisher
import PhotosUI
import SwiftUI

// MARK: - Upload State
enum ImageSelectionState {
  case idle
  case selectedUpload(UIImage)
  case selectedAsset(UserAsset)
}

struct ImageTemplateItemDetailView: View {
  let ImageTemplateItem: ImageTemplateItem
  @Environment(\.dismiss) var dismiss
  @Environment(\.colorScheme) private var colorScheme

  // MARK: - Image Selection State
  @State private var imageSelectionState: ImageSelectionState = .idle
  @State private var showingPhotoPicker = false
  @State private var photoPickerItem: PhotosPickerItem?
  @State private var showingAssetSelection = false

  // Mock data for description and placeholder images
  private var mockDescription: String {
    switch ImageTemplateItem.name.lowercased() {
    case let name where name.contains("disney"):
      return "Transfer a figure into Disney style"
    case let name where name.contains("anime"):
      return "Works best with figures (human, pets, anything cute)"
    default:
      return
        "Transform your images with this amazing style. Perfect for creating stunning visuals with unique artistic flair."
    }
  }

  private var mockPlaceholderImages: [String] {
    [
      "https://picsum.photos/200/200?random=1",
      "https://picsum.photos/200/200?random=2",
      "https://picsum.photos/200/200?random=3",
      "https://picsum.photos/200/200?random=4",
      "https://picsum.photos/200/200?random=5",
    ]
  }

  var body: some View {
    ZStack {
      // Background with blur effect
      Rectangle()
        .fill(Color.black.opacity(0.4))
        .ignoresSafeArea()
        .background(.ultraThinMaterial)

      VStack(spacing: 0) {
        // Main image section
        ZStack {
          // Main image
          KFImage(URL(string: ImageTemplateItem.outputUrl))
            .placeholder {
              Rectangle()
                .fill(
                  LinearGradient(
                    gradient: Gradient(colors: [
                      Color(red: 0.3, green: 0.5, blue: 0.9),
                      Color(red: 0.5, green: 0.3, blue: 0.8),
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                  )
                )
                .frame(maxWidth: .infinity)
                .frame(height: UIScreen.main.bounds.height * 0.46)
                .overlay(
                  ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                )
            }
            .retry(maxCount: 3)
            .fade(duration: 0.25)
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(maxWidth: .infinity)
            .frame(height: UIScreen.main.bounds.height * 0.46)
            .clipped()

          // Right side controls overlay
          VStack {
            HStack {
              Spacer()
              VStack(spacing: 16) {
                // Close button
                Button(action: {
                  dismiss()
                }) {
                  Circle()
                    .fill(Color.black.opacity(0.4))
                    .frame(width: 48, height: 48)
                    .overlay(
                      Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    )
                }

                // Additional control buttons (like in Figma)
                // Button(action: {
                //   // Add action for this button
                // }) {
                //   Circle()
                //     .fill(Color.white)
                //     .frame(width: 32, height: 32)
                //     .overlay(
                //       Image(systemName: "plus")
                //         .font(.system(size: 14, weight: .medium))
                //         .foregroundColor(.black)
                //     )
                // }

                // Button(action: {
                //   // Add action for this button
                // }) {
                //   Circle()
                //     .fill(Color.white)
                //     .frame(width: 32, height: 32)
                //     .overlay(
                //       Image(systemName: "heart")
                //         .font(.system(size: 14, weight: .medium))
                //         .foregroundColor(.black)
                //     )
                // }
              }
              .padding(.trailing, 16)
            }
            .padding(.top, 16)
            Spacer()
          }
        }

        // Bottom content area with gradient overlay
        ZStack(alignment: .bottom) {
          // Gradient overlay from transparent to dark
          LinearGradient(
            gradient: Gradient(stops: [
              .init(color: Color.black.opacity(0), location: 0),
              .init(color: Color.black.opacity(1), location: 1),
            ]),
            startPoint: .top,
            endPoint: .bottom
          )
          .frame(height: 300)

          // Content area
          VStack(alignment: .leading, spacing: 20) {
            // Title and description section
            VStack(alignment: .center, spacing: 8) {
              Text(ImageTemplateItem.name)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)

              Text(mockDescription)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .lineLimit(2)
            }
            .padding(.horizontal, 16)

            // Gallery section
            ScrollView(.horizontal, showsIndicators: false) {
              HStack(spacing: 8) {
                ForEach(mockPlaceholderImages, id: \.self) { imageUrl in
                  KFImage(URL(string: imageUrl))
                    .placeholder {
                      RoundedRectangle(cornerRadius: 16)
                        .fill(Color.gray.opacity(0.5))
                        .frame(width: 120, height: 120)
                        .overlay(
                          ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        )
                    }
                    .retry(maxCount: 3)
                    .fade(duration: 0.25)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 120, height: 120)
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                }
              }
              .padding(.horizontal, 24)
            }

            // Upload photo button
            Button(action: {
              showingPhotoPicker = true
            }) {
              HStack {
                Text("Upload photo")
                  .font(.system(size: 14, weight: .semibold))
                  .foregroundColor(.black)
              }
              .frame(maxWidth: .infinity)
              .frame(height: 48)
              .background(Color.white)
              .cornerRadius(40)
            }
            .padding(.horizontal, 24)

            // Select From Assets button
            Button(action: {
              showingAssetSelection = true
            }) {
              Text("Select From Assets")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white)
                .underline()
            }
            .frame(maxWidth: .infinity)
            .padding(.top, 8)

            // Bottom safe area padding
            // Spacer(minLength: 40)
          }
          .padding(.bottom, 20)
        }
      }
    }
    .photosPicker(isPresented: $showingPhotoPicker, selection: $photoPickerItem, matching: .images)
    .onChange(of: photoPickerItem) { newItem in
      if let newItem = newItem {
        loadSelectedImage(from: newItem)
      }
    }
    .sheet(isPresented: $showingAssetSelection) {
      UserAssetSelectionView(
        onAssetsSelected: { assets in
          if let firstAsset = assets.first {
            imageSelectionState = .selectedAsset(firstAsset)
          }
        },
        onDismiss: {
          showingAssetSelection = false
        },
        maxSelectionCount: 1
      )
    }
    .sheet(
      isPresented: .constant(
        {
          switch imageSelectionState {
          case .selectedUpload, .selectedAsset:
            return true
          case .idle:
            return false
          }
        }())
    ) {
      switch imageSelectionState {
      case .selectedUpload(let selectedImage):
        UploadResultView(
          imageSource: .uploaded(selectedImage),
          ImageTemplateItem: ImageTemplateItem,
          onDismiss: {
            imageSelectionState = .idle
            dismiss()
          }
        )
      case .selectedAsset(let asset):
        UploadResultView(
          imageSource: .asset(asset),
          ImageTemplateItem: ImageTemplateItem,
          onDismiss: {
            imageSelectionState = .idle
            dismiss()
          }
        )
      case .idle:
        EmptyView()
      }
    }
  }

  // MARK: - Private Methods

  private func loadSelectedImage(from item: PhotosPickerItem) {
    Task {
      do {
        if let data = try await item.loadTransferable(type: Data.self),
          let image = UIImage(data: data)
        {
          await MainActor.run {
            imageSelectionState = .selectedUpload(image)
          }
        }
      } catch {
        // Handle error if needed
        Logger.error("Failed to load selected image: \(error)")
      }
    }
  }
}

#Preview {
  ImageTemplateItemDetailView(
    ImageTemplateItem: ImageTemplateItem(
      id: "1",
      name: "Enchanted Anime",
      outputUrl:
        "https://a1d.ai/cdn-cgi/image/width=256,height=256,fit=scale-down,anim=0,f=auto/https://twitter-r2.a1d.ai/app-style/Anime-Artist/Anime-Artist_Enchanted-Anime.png",
      groupRank: 1,
      inGroupRank: 1,
      category: "Anime Artist",
      isDisabled: false,
      prompt: "Transform the image into a Studio Ghibli style",
      usecaseType: .promptOnly
    )
  )
}
