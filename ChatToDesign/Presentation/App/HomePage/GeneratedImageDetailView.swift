import Combine
import Kingfisher
import SwiftUI

struct GeneratedImageDetailView: View {
  let task: ImageGenerationTask
  @Environment(\.dismiss) private var dismiss
  @State private var selectedImageIndex: Int = 0

  // Download functionality
  @StateObject private var imageSaver = ImageSaver()
  @State private var isDownloading = false
  @State private var showingSuccessAlert = false
  @State private var showingErrorAlert = false
  @State private var errorMessage = ""

  // Regenerate functionality
  @StateObject private var regenerationHandler: DesignGenerationHandler
  @State private var isRegenerating = false
  @State private var cancellables = Set<AnyCancellable>()

  // Edit functionality
  @State private var showingEditView = false

  // MARK: - Initialization

  init(task: ImageGenerationTask) {
    self.task = task

    // Initialize DesignGenerationHandler with dependencies

    self._regenerationHandler = StateObject(
      wrappedValue: DesignGenerationHandler()
    )
  }

  var body: some View {
    ZStack {
      // Background
      Color.black
        .ignoresSafeArea()

      VStack(spacing: 0) {
        // Top handle
        topHandle

        // Header section
        headerSection
          .padding(.top, 16)

        // Main image
        mainImageSection
          .padding(.horizontal, 63)
          .padding(.top, 40)

        Spacer()

        // Bottom actions
        bottomActionsSection
          .padding(.horizontal, 24)
          .padding(.bottom, 44)
      }
    }
    .presentationDetents([.medium, .large])
    .presentationDragIndicator(.visible)
    .alert("Success", isPresented: $showingSuccessAlert) {
      Button("OK") {}
    } message: {
      Text("Image saved to Photos successfully!")
    }
    .alert("Error", isPresented: $showingErrorAlert) {
      Button("OK") {}
    } message: {
      Text(errorMessage)
    }

    .sheet(isPresented: $showingEditView) {
      ImageEditView(originalTask: task, selectedImageIndex: selectedImageIndex)
    }
    .onAppear {
      setupImageSaver()
      setupRegenerationHandler()
    }
  }

  // MARK: - Top Handle

  private var topHandle: some View {
    VStack(spacing: 0) {
      Rectangle()
        .fill(Color(hex: "#C7C7C7"))
        .frame(width: 48, height: 4)
        .cornerRadius(2)
        .padding(.top, 7)

      HStack {
        Spacer()

        Button(action: {
          dismiss()
        }) {
          ZStack {
            Circle()
              .fill(Color(hex: "#272727"))
              .frame(width: 32, height: 32)

            Image(systemName: "xmark")
              .font(.system(size: 13, weight: .medium))
              .foregroundColor(Color(hex: "#D9D9D9"))
          }
        }
        .padding(.trailing, 16)
        .padding(.top, 9)
      }
    }
  }

  // MARK: - Header Section

  private var headerSection: some View {
    VStack(spacing: 4) {
      Text("Image generated")
        .font(.system(size: 20, weight: .semibold))
        .foregroundColor(Color(hex: "#FAFAFA"))
        .multilineTextAlignment(.center)

      Text(task.prompt ?? "Untitled")
        .font(.system(size: 14, weight: .semibold))
        .foregroundColor(Color(hex: "#666666"))
        .multilineTextAlignment(.center)
        .lineLimit(2)
    }
    .padding(.horizontal, 16)
  }

  // MARK: - Main Image Section

  private var mainImageSection: some View {
    ZStack {
      // Background
      Color(hex: "#131313")
        .cornerRadius(8)

      if let resultUrls = task.resultImageUrls, !resultUrls.isEmpty {
        // Generated image
        KFImage(URL(string: resultUrls[selectedImageIndex]))
          .placeholder {
            // Loading placeholder
            VStack(spacing: 12) {
              ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)

              Text("Loading image...")
                .font(.system(size: 14))
                .foregroundColor(.gray)
            }
            .frame(width: 250, height: 380)
          }
          .retry(maxCount: 3)
          .fade(duration: 0.25)
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: 250, height: 380)
          .clipped()
          .cornerRadius(8)

        // Image navigation dots (if multiple images)
        if resultUrls.count > 1 {
          VStack {
            Spacer()

            HStack(spacing: 8) {
              ForEach(0..<resultUrls.count, id: \.self) { index in
                Circle()
                  .fill(index == selectedImageIndex ? Color.white : Color.gray.opacity(0.5))
                  .frame(width: 6, height: 6)
                  .onTapGesture {
                    selectedImageIndex = index
                  }
              }
            }
            .padding(.bottom, 16)
          }
        }

      } else {
        // No image placeholder
        VStack(spacing: 12) {
          Image(systemName: "photo")
            .font(.system(size: 40))
            .foregroundColor(.gray)

          Text("No image available")
            .font(.system(size: 16))
            .foregroundColor(.gray)
        }
        .frame(width: 250, height: 380)
      }
    }
    .frame(width: 250, height: 380)
  }

  // MARK: - Bottom Actions Section

  private var bottomActionsSection: some View {
    VStack(spacing: 16) {
      // Primary action - Download button
      Button(action: {
        downloadCurrentImage()
      }) {
        HStack(spacing: 12) {
          if isDownloading {
            ProgressView()
              .progressViewStyle(CircularProgressViewStyle(tint: Color(hex: "#FAFAFA")))
              .scaleEffect(0.8)
          } else {
            Image(systemName: "arrow.down")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(Color(hex: "#FAFAFA"))
          }

          Text(isDownloading ? "Downloading..." : "Download")
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(Color(hex: "#FAFAFA"))
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(Color(hex: "#007AFF"))  // iOS blue for primary action
        .cornerRadius(12)
      }
      .disabled(isDownloading)

      // Secondary actions
      HStack(spacing: 12) {
        // Edit button
        Button(action: {
          editImage()
        }) {
          HStack(spacing: 8) {
            Image(systemName: "pencil")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(Color(hex: "#FAFAFA"))

            Text("Edit")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(Color(hex: "#FAFAFA"))
          }
          .frame(maxWidth: .infinity)
          .padding(.vertical, 12)
          .background(Color(hex: "#272727"))
          .cornerRadius(8)
        }

        // Regenerate button
        Button(action: {
          regenerateImage()
        }) {
          HStack(spacing: 8) {
            if isRegenerating {
              ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: Color(hex: "#FAFAFA")))
                .scaleEffect(0.7)
            } else {
              Image(systemName: "arrow.clockwise")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color(hex: "#FAFAFA"))
            }

            Text(isRegenerating ? "Generating..." : "Regenerate")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(Color(hex: "#FAFAFA"))
          }
          .frame(maxWidth: .infinity)
          .padding(.vertical, 12)
          .background(Color(hex: "#272727"))
          .cornerRadius(8)
        }
        .disabled(isRegenerating)
      }
    }
  }

  // MARK: - Download Functionality

  private func setupImageSaver() {
    imageSaver.onSuccess = {
      showingSuccessAlert = true
    }

    imageSaver.onError = { error in
      errorMessage = error.localizedDescription
      showingErrorAlert = true
    }
  }

  private func downloadCurrentImage() {
    guard let resultUrls = task.resultImageUrls,
      !resultUrls.isEmpty,
      selectedImageIndex < resultUrls.count
    else {
      errorMessage = "No image available to download"
      showingErrorAlert = true
      return
    }

    let imageUrl = resultUrls[selectedImageIndex]
    isDownloading = true

    Task {
      do {
        // Download image data using DesignModule from AppDependencyContainer
        let designModule = AppDependencyContainer.shared.designModule
        let imageData = try await designModule.designGenerationService.downloadDesignImage(
          from: imageUrl)

        // Convert data to UIImage
        guard let uiImage = UIImage(data: imageData) else {
          await MainActor.run {
            isDownloading = false
            errorMessage = "Failed to process downloaded image"
            showingErrorAlert = true
          }
          return
        }

        // Save to photo library
        await MainActor.run {
          isDownloading = false
          imageSaver.writeToPhotoAlbum(image: uiImage)
        }

      } catch {
        await MainActor.run {
          isDownloading = false
          errorMessage = "Failed to download image: \(error.localizedDescription)"
          showingErrorAlert = true
        }
      }
    }
  }

  // MARK: - Regenerate Functionality

  private func setupRegenerationHandler() {
    // Monitor regeneration handler state
    regenerationHandler.$isLoading
      .receive(on: DispatchQueue.main)
      .sink { loading in
        isRegenerating = loading
      }
      .store(in: &cancellables)

    regenerationHandler.$errorMessage
      .receive(on: DispatchQueue.main)
      .sink { error in
        if let error = error {
          errorMessage = error
          showingErrorAlert = true
        }
      }
      .store(in: &cancellables)

    regenerationHandler.$taskCreatedSuccessfully
      .receive(on: DispatchQueue.main)
      .sink { success in
        if success {
          // Dismiss the detail view when regeneration task is created successfully
          dismiss()
        }
      }
      .store(in: &cancellables)
  }

  private func regenerateImage() {
    guard let prompt = task.prompt, !prompt.isEmpty else {
      errorMessage = "No prompt available for regeneration"
      showingErrorAlert = true
      return
    }

    // Reset state and start regeneration
    isRegenerating = true

    Task {
      do {
        // Use the existing image URLs directly instead of downloading and re-uploading
        let imageUrls = task.inputImageUrls ?? []

        // Start regeneration using the new URL-based method
        try await regenerationHandler.startGenerationWithImageUrls(
          prompt: prompt,
          imageUrls: imageUrls,
          designId: task.designId,
          chatId: nil  // We don't have chatId in the task, so pass nil
        )

      } catch {
        await MainActor.run {
          isRegenerating = false
          errorMessage = "Failed to start regeneration: \(error.localizedDescription)"
          showingErrorAlert = true
        }
      }
    }
  }

  private func editImage() {
    showingEditView = true
  }

}

#Preview {
  GeneratedImageDetailView(
    task: ImageGenerationTask(
      taskId: "test",
      userId: "user1",
      status: .succeeded,
      prompt: "Disney style cartoon character",
      resultImageUrls: ["https://example.com/image1.jpg"]
    )
  )
}
