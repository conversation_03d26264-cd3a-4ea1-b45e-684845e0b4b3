//
//  SelectedAssetsInputView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//

import Kingfisher
import SwiftUI

struct SelectedAssetsInputView: View {
  @ObservedObject var viewModel: PromptFooterViewModel
  @FocusState private var inputFocused: Bool

  var body: some View {
    VStack(spacing: 0) {
      // Selected assets row
      if !viewModel.selectedAssets.isEmpty {
        selectedAssetsRow
      }

      // Input area
      inputArea
    }
    .background(Color(.systemBackground))
  }

  // MARK: - Selected Assets Row

  private var selectedAssetsRow: some View {
    ScrollView(.horizontal, showsIndicators: false) {
      HStack(spacing: 8) {
        ForEach(viewModel.selectedAssets, id: \.id) { asset in
          SelectedAssetItemView(asset: asset) {
            viewModel.removeSelectedAsset(asset)
          }
        }
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
    }
  }

  // MARK: - Input Area

  private var inputArea: some View {
    HStack(spacing: 12) {
      // Add more photos button
      Button(action: {
        viewModel.showAssetSelection()
      }) {
        Image(systemName: "photo")
          .font(.system(size: 20, weight: .medium))
          .foregroundColor(.secondary)
      }

      // Input field
      TextField("Imagine...", text: $viewModel.promptText)
        .font(.system(size: 16, weight: .regular))
        .foregroundColor(.primary)
        .focused($inputFocused)
        .submitLabel(.send)
        .onSubmit {
          if viewModel.isSubmitEnabled {
            viewModel.submitPrompt()
          }
        }

      Spacer()

      // Settings button
      Button(action: {
        viewModel.requestSettings()
      }) {
        Image(systemName: "slider.horizontal.3")
          .font(.system(size: 20, weight: .medium))
          .foregroundColor(.secondary)
      }

      // Send button
      Button(action: {
        viewModel.submitPrompt()
      }) {
        if viewModel.isGenerating {
          ProgressView()
            .scaleEffect(0.8)
            .frame(width: 24, height: 24)
        } else {
          Image(systemName: "arrow.up.circle.fill")
            .font(.system(size: 24, weight: .medium))
            .foregroundColor(viewModel.isSubmitEnabled ? .accentColor : .secondary)
        }
      }
      .disabled(!viewModel.isSubmitEnabled)
    }
    .padding(.horizontal, 16)
    .padding(.vertical, 12)
    .background(
      Rectangle()
        .fill(Color(.systemGray6))
        .frame(height: 1),
      alignment: .top
    )
  }
}

// MARK: - Selected Asset Item View

struct SelectedAssetItemView: View {
  let asset: AssetResponse
  let onRemove: () -> Void

  var body: some View {
    ZStack(alignment: .topTrailing) {
      // Asset image
      KFImage(URL(string: asset.thumbnailUrl ?? asset.url))
        .placeholder {
          RoundedRectangle(cornerRadius: 8)
            .fill(Color(.systemGray5))
            .frame(width: 60, height: 60)
            .overlay(
              ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .secondary))
                .scaleEffect(0.6)
            )
        }
        .retry(maxCount: 3)
        .fade(duration: 0.25)
        .resizable()
        .aspectRatio(contentMode: .fill)
        .frame(width: 60, height: 60)
        .clipped()
        .cornerRadius(8)

      // Remove button
      Button(action: onRemove) {
        Image(systemName: "xmark")
          .font(.system(size: 10, weight: .bold))
          .foregroundColor(.white)
          .frame(width: 16, height: 16)
          .background(
            Circle()
              .fill(Color.black.opacity(0.6))
          )
      }
      .offset(x: 4, y: -4)
    }
  }
}

// MARK: - Preview

struct SelectedAssetsInputView_Previews: PreviewProvider {
  static var previews: some View {
    SelectedAssetsInputView(viewModel: PromptFooterViewModel.create())
  }
}
