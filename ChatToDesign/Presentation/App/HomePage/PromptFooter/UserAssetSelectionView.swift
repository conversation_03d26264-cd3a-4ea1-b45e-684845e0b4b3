//
//  UserAssetSelectionView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//

import Kingfisher
import PhotosUI
import SwiftUI

struct UserAssetSelectionView: View {
  @StateObject private var viewModel: UserAssetSelectionViewModel
  @Environment(\.dismiss) private var dismiss
  @Environment(\.colorScheme) private var colorScheme
  @State private var selectedItems: [PhotosPickerItem] = []

  // Grid layout configuration - Changed from 5 to 4 columns for larger items
  private let columns = Array(repeating: GridItem(.flexible(), spacing: 8), count: 4)

  // MARK: - Initialization

  init(
    onAssetsSelected: @escaping ([UserAsset]) -> Void,
    onDismiss: @escaping () -> Void,
    preselectedAssets: [UserAsset] = [],
    maxSelectionCount: Int = 3
  ) {
    self._viewModel = StateObject(
      wrappedValue: UserAssetSelectionViewModel.create(
        preselectedAssets: preselectedAssets,
        onAssetsSelected: onAssetsSelected,
        onDismiss: onDismiss,
        maxSelectionCount: maxSelectionCount
      ))
  }

  var body: some View {
    VStack(spacing: 0) {
      Spacer()
      // Content area
      VStack(spacing: 24) {
        // Tab section
        tabSection

        // Assets grid
        assetsGridView
      }
      .padding(.horizontal, 24)
      .padding(.bottom, 24)

    }
    .background(Color(red: 0.2, green: 0.2, blue: 0.2))  // #333333
  }

  // MARK: - Tab Section

  private var tabSection: some View {
    HStack(spacing: 0) {
      Spacer()

      // Assets tab
      Button(action: {
        viewModel.switchTab(to: "Assets")
      }) {
        Text("Assets")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(
            viewModel.selectedTab == "Assets"
              ? Color(red: 0.098, green: 0.098, blue: 0.106)
              : Color(red: 0.445, green: 0.445, blue: 0.482)
          )  // #18181B : #71717a
          .frame(width: 128, height: 32)
          .background(
            Group {
              if viewModel.selectedTab == "Assets" {
                Color(red: 0.96, green: 0.96, blue: 0.97)  // #f4f4f5
                  .overlay(
                    RoundedRectangle(cornerRadius: 9999)
                      .stroke(Color.white, lineWidth: 1)
                  )
              } else {
                Color.clear
              }
            }
          )
          .clipShape(RoundedRectangle(cornerRadius: 9999))
      }

      // Creations tab
      Button(action: {
        viewModel.switchTab(to: "Creations")
      }) {
        Text("Creations")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(
            viewModel.selectedTab == "Creations"
              ? Color(red: 0.098, green: 0.098, blue: 0.106)
              : Color(red: 0.445, green: 0.445, blue: 0.482)
          )  // #18181B : #71717a
          .frame(width: 128, height: 32)
          .background(
            Group {
              if viewModel.selectedTab == "Creations" {
                Color(red: 0.96, green: 0.96, blue: 0.97)  // #f4f4f5
                  .overlay(
                    RoundedRectangle(cornerRadius: 9999)
                      .stroke(Color.white, lineWidth: 1)
                  )
              } else {
                Color.clear
              }
            }
          )
          .clipShape(RoundedRectangle(cornerRadius: 9999))
      }

      Spacer()
    }
    .padding(.top, 16)
  }

  // MARK: - Upload Button

  private var uploadButton: some View {
    PhotosPicker(
      selection: $selectedItems,
      maxSelectionCount: 4,
      matching: .images
    ) {
      VStack(spacing: 12) {
        Image(systemName: "plus")
          .font(.system(size: 24, weight: .medium))
          .foregroundColor(Color(red: 0.098, green: 0.098, blue: 0.106))  // #18181B
      }
      .frame(width: 80, height: 80)
      .background(Color(red: 0.96, green: 0.96, blue: 0.97))  // #f4f4f5
      .clipShape(RoundedRectangle(cornerRadius: 16))
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .stroke(Color(red: 0.898, green: 0.898, blue: 0.918), lineWidth: 1)  // #e4e4e7
      )
    }
    .onChange(of: selectedItems) { newItems in
      viewModel.uploadImages(newItems)
      selectedItems = []  // Clear selection after processing
    }
  }

  // private var loadingButton: some View {
  //   VStack(spacing: 12) {
  //     ProgressView()
  //       .progressViewStyle(
  //         CircularProgressViewStyle(tint: Color(red: 0.098, green: 0.098, blue: 0.106))
  //       )  // #18181
  //       .scaleEffect(0.6)
  //   }
  //   .frame(width: 80, height: 80)
  //   .background(Color(red: 0.96, green: 0.96, blue: 0.97))  // #f4f4f5
  //   .clipShape(RoundedRectangle(cornerRadius: 16))
  //   .overlay(
  //     RoundedRectangle(cornerRadius: 16)
  //       .stroke(Color(red: 0.898, green: 0.898, blue: 0.918), lineWidth: 1)  // #e4e4e7
  //   )
  // }

  // MARK: - Assets Grid View

  private var assetsGridView: some View {
    VStack(spacing: 0) {
      // Selection info header
      if !viewModel.selectedAssets.isEmpty {
        HStack {
          Spacer()

          Text("\(viewModel.selectedAssets.count)/\(viewModel.maxSelectionCount) selected")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.white)

          Spacer()

          Button("Done") {
            viewModel.confirmSelection()
          }
          .font(.system(size: 16, weight: .semibold))
          .foregroundColor(.white)
          .padding(.bottom, 8)
          .disabled(viewModel.selectedAssets.isEmpty)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(red: 0.145, green: 0.145, blue: 0.145))  // #252525
      }

      ScrollView {
        LazyVGrid(columns: columns, spacing: 8) {
          // Upload button - only show in Assets tab
          if viewModel.shouldShowUploadButton {
            uploadButton
          }

          // Assets based on selected tab
          ForEach(viewModel.currentAssets, id: \.id) { asset in
            AssetGridItemView(
              asset: asset,
              isSelected: viewModel.isAssetSelected(asset),
              onTap: {
                viewModel.toggleAssetSelection(asset)
              },
              onDelete: {
                viewModel.deleteAsset(asset)
              }
            )
          }
        }
        .padding(.horizontal, 0)
        .padding(.top, 0)
        .padding(.bottom, 32)
      }
    }
    .overlay(
      Group {
        if viewModel.isLoadingAssets {
          ProgressView("Loading assets...")
            .foregroundColor(.white)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color(red: 0.2, green: 0.2, blue: 0.2).opacity(0.8))  // #333333
        } else if viewModel.isUploading {
          ProgressView("Uploading images...")
            .foregroundColor(.white)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color(red: 0.2, green: 0.2, blue: 0.2).opacity(0.8))  // #333333
        } else if viewModel.isDeleting {
          ProgressView("Deleting asset...")
            .foregroundColor(.white)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color(red: 0.2, green: 0.2, blue: 0.2).opacity(0.8))  // #333333
        } else if viewModel.currentAssets.isEmpty && !viewModel.isLoadingAssets
          && viewModel.selectedTab == "Creations"
        {
          VStack(spacing: 16) {
            Image(systemName: "sparkles")
              .font(.system(size: 48, weight: .light))
              .foregroundColor(Color(red: 0.445, green: 0.445, blue: 0.482))  // #71717a

            Text("No creations found")
              .font(.system(size: 18, weight: .medium))
              .foregroundColor(.white)
          }
          .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
      }
    )
  }

  // MARK: - Private Methods

}

// MARK: - Asset Grid Item View

struct AssetGridItemView: View {
  let asset: UserAsset
  let isSelected: Bool
  let onTap: () -> Void
  let onDelete: (() -> Void)?
  @State private var showDeleteConfirmation = false

  init(
    asset: UserAsset, isSelected: Bool = false, onTap: @escaping () -> Void,
    onDelete: (() -> Void)? = nil
  ) {
    self.asset = asset
    self.isSelected = isSelected
    self.onTap = onTap
    self.onDelete = onDelete
  }

  var body: some View {
    GeometryReader { geometry in
      KFImage(URL(string: asset.thumbnailUrl ?? asset.url))
        .placeholder {
          RoundedRectangle(cornerRadius: 16)
            .fill(Color(red: 0.96, green: 0.96, blue: 0.97))  // #f4f4f5
            .frame(width: geometry.size.width, height: geometry.size.width)
            .overlay(
              ProgressView()
                .progressViewStyle(
                  CircularProgressViewStyle(tint: Color(red: 0.098, green: 0.098, blue: 0.106))
                )  // #18181
                .scaleEffect(0.6)
            )
        }
        .retry(maxCount: 3)
        .fade(duration: 0.25)
        .resizable()
        .aspectRatio(contentMode: .fill)
        .frame(width: geometry.size.width, height: geometry.size.width)
        .clipped()
        .cornerRadius(16)
    }
    .aspectRatio(1, contentMode: .fit)
    .overlay(
      // Selection border overlay
      Group {
        if isSelected {
          RoundedRectangle(cornerRadius: 16)
            .stroke(Color.white, lineWidth: 3)
        }
      }
    )
    .overlay(
      // Delete button overlay
      Group {
        if onDelete != nil {
          VStack {
            HStack {
              Spacer()
              Button(action: {
                showDeleteConfirmation = true
              }) {
                Image(systemName: "trash")
                  .font(.system(size: 12, weight: .medium))
                  .foregroundColor(Color(red: 0.098, green: 0.098, blue: 0.106))  // #18181B
                  .frame(width: 24, height: 24)
                  .background(
                    Circle()
                      .fill(Color.white)
                      .shadow(color: Color.black.opacity(0.1), radius: 1.5, x: 0, y: 1)
                      .shadow(color: Color.black.opacity(0.06), radius: 1, x: 0, y: 1)
                  )
              }
              .padding(4)
            }
            Spacer()
          }
        }
      }
    )
    .onTapGesture {
      onTap()
    }
    .confirmationDialog(
      "Delete Asset",
      isPresented: $showDeleteConfirmation,
      titleVisibility: .visible
    ) {
      Button("Delete", role: .destructive) {
        onDelete?()
      }
      Button("Cancel", role: .cancel) {}
    } message: {
      Text("Are you sure you want to delete this asset? This action cannot be undone.")
    }
  }
}

// MARK: - Preview

struct UserAssetSelectionView_Previews: PreviewProvider {
  static var previews: some View {
    UserAssetSelectionView(
      onAssetsSelected: { _ in },
      onDismiss: {},
      preselectedAssets: []
    )
  }
}
