//
//  PromptFooterViewModel.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//

import Foundation
import PhotosUI
import SwiftUI

@MainActor
class PromptFooterViewModel: ObservableObject {
  // MARK: - Published Properties

  @Published var promptText: String = ""
  @Published var isInputActive: Bool = false
  @Published var selectedAssets: [AssetResponse] = []
  @Published var isUploading: Bool = false
  @Published var isAssetSelectionPresented: Bool = false
  @Published var isGenerating: Bool = false
  @Published var showPaywall: Bool = false

  // MARK: - Dependencies

  private let assetService: AssetApplicationService
  private let designGenerationHandler: DesignGenerationHandler
  private let subscriptionStateManager: SubscriptionStateManager

  // MARK: - Callbacks

  var onPromptSubmit: ((String) -> Void)?
  var onAssetSelected: (([AssetResponse]) -> Void)?
  var onSettingsRequested: (() -> Void)?

  // MARK: - Initialization

  init(
    assetService: AssetApplicationService,
    designGenerationHandler: DesignGenerationHandler,
    subscriptionStateManager: SubscriptionStateManager
  ) {
    self.assetService = assetService
    self.designGenerationHandler = designGenerationHandler
    self.subscriptionStateManager = subscriptionStateManager
  }

  // MARK: - Public Methods

  /// Submit the current prompt
  func submitPrompt() {
    guard !promptText.isEmpty || !selectedAssets.isEmpty else { return }
    guard !isGenerating else { return }

    // 检查是否需要高级权限
    if !subscriptionStateManager.isProUser {
      Logger.info("PromptFooterViewModel: 需要专业版权限，显示 Paywall")
      showPaywall = true
      return
    }

    Logger.info("Submitting prompt: \(promptText)")

    // Extract image URLs from selected assets
    let imageUrls = selectedAssets.map { $0.url }
    let prompt = promptText.trimmingCharacters(in: .whitespacesAndNewlines)

    // Set loading state
    isGenerating = true

    Task {
      do {
        try await designGenerationHandler.startGenerationWithImageUrls(
          prompt: prompt,
          imageUrls: imageUrls,
          designId: nil,
          chatId: nil
        )

        await MainActor.run {
          // Clear prompt and assets after successful submission
          self.promptText = ""
          self.selectedAssets = []
          self.isInputActive = false
          self.isGenerating = false
          Logger.info("Design generation started successfully")
        }
      } catch {
        await MainActor.run {
          self.isGenerating = false
          Logger.error("Failed to start design generation: \(error.localizedDescription)")
          // TODO: Show error to user
        }
      }
    }
  }

  /// Show asset selection sheet
  func showAssetSelection() {
    isAssetSelectionPresented = true
  }

  /// Hide asset selection sheet
  func hideAssetSelection() {
    isAssetSelectionPresented = false
  }

  /// Request settings
  func requestSettings() {
    Logger.info("Settings requested")
    onSettingsRequested?()
  }

  /// Set selected assets (replace all)
  func setSelectedAssets(_ assets: [AssetResponse]) {
    selectedAssets = assets
    Logger.info("Set selected assets: \(assets.count) assets")
  }

  /// Remove selected asset
  func removeSelectedAsset(_ asset: AssetResponse) {
    selectedAssets.removeAll { $0.id == asset.id }
    Logger.info("Removed selected asset: \(asset.name)")
  }

  /// Clear all selected assets
  func clearSelectedAssets() {
    selectedAssets.removeAll()
    Logger.info("Cleared all selected assets")
  }

  /// Update input active state
  func setInputActive(_ active: Bool) {
    isInputActive = active
  }

  /// Hide paywall
  func hidePaywall() {
    showPaywall = false
    Logger.info("PromptFooterViewModel: 隐藏 Paywall")
  }

  // MARK: - Computed Properties

  /// Whether the submit button should be enabled
  var isSubmitEnabled: Bool {
    return (!promptText.isEmpty || !selectedAssets.isEmpty) && !isGenerating
  }

  // MARK: - Private Methods

}

// MARK: - Factory Methods

extension PromptFooterViewModel {
  /// Create a view model with callbacks connected to HomePageViewModel
  static func create() -> PromptFooterViewModel {
    // Get dependencies from dependency container
    let container = AppDependencyContainer.shared
    let designGenerationHandler = DesignGenerationHandler()

    let viewModel = PromptFooterViewModel(
      assetService: container.assetModule.assetService,
      designGenerationHandler: designGenerationHandler,
      subscriptionStateManager: container.subscriptionModule.subscriptionStateManager
    )

    return viewModel
  }
}
