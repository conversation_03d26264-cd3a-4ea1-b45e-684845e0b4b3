//
//  AssetSelectionViewModel.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//

import Combine
import Foundation
import PhotosUI
import SwiftUI

@MainActor
class UserAssetSelectionViewModel: ObservableObject {
  // MARK: - Published Properties

  @Published var selectedAssets: [UserAsset] = []

  // Maximum selection limit - now configurable
  @Published var maxSelectionCount: Int = 3

  // MARK: - Tab Management

  @Published var selectedTab: String = "Assets"

  // MARK: - Published Properties for UI

  @Published private(set) var allAssets: [UserAsset] = []
  @Published private(set) var isLoadingAssets: Bool = false
  @Published private(set) var assetsError: Error? = nil
  @Published private(set) var isUploading: Bool = false
  @Published private(set) var isDeleting: Bool = false

  // MARK: - Asset Observation

  /// 资产观察取消令牌
  private var assetsObservationCancellable: AnyCancellable?

  // MARK: - Dependencies

  private let assetService: AssetApplicationService
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Callbacks

  var onAssetsSelected: (([UserAsset]) -> Void)?
  var onDismiss: (() -> Void)?

  // MARK: - Initialization

  init(
    assetService: AssetApplicationService,
    preselectedAssets: [UserAsset] = [],
    onAssetsSelected: (([UserAsset]) -> Void)? = nil,
    onDismiss: (() -> Void)? = nil,
    maxSelectionCount: Int = 3
  ) {
    self.assetService = assetService
    self.selectedAssets = preselectedAssets
    self.onAssetsSelected = onAssetsSelected
    self.onDismiss = onDismiss
    self.maxSelectionCount = maxSelectionCount

    // 开始观察用户资产
    startObservingAssets()
  }

  deinit {
    // 清理资产观察
    assetsObservationCancellable?.cancel()
  }

  // MARK: - Private Methods

  /// 开始观察用户资产
  private func startObservingAssets() {
    let query = AssetListQuery(
      page: 1,
      limit: 100,  // 增加限制以获取更多资产
      type: "image/*",  // 只获取图片类型
      sourceType: nil,  // 不按sourceType过滤，获取所有类型
      status: .active,
      tags: nil,
      search: nil
    )

    isLoadingAssets = true

    assetsObservationCancellable = assetService.observeUserAssets(query: query)
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          self?.isLoadingAssets = false
          if case .failure(let error) = completion {
            self?.assetsError = error
            Logger.error("Assets observation failed: \(error.localizedDescription)")
          }
        },
        receiveValue: { [weak self] response in
          self?.allAssets = response.assets
          self?.isLoadingAssets = false
          self?.assetsError = nil
          Logger.debug("Received \(response.assets.count) total assets")
        }
      )
  }

  // MARK: - Computed Properties

  /// 根据当前选中的tab返回相应的资产列表
  var currentAssets: [UserAsset] {
    switch selectedTab {
    case "Assets":
      return allAssets.filter { $0.sourceType == .userUpload }
    case "Creations":
      return allAssets.filter { $0.sourceType == .aiGenerated }
    default:
      return allAssets.filter { $0.sourceType == .userUpload }
    }
  }

  /// 是否显示上传按钮（只在Assets tab显示）
  var shouldShowUploadButton: Bool {
    return selectedTab == "Assets"
  }

  // MARK: - Public Methods

  /// 刷新用户资产数据
  func refreshAssets() {
    startObservingAssets()
  }

  /// 手动重新获取资产数据
  func loadUserAssets() {
    startObservingAssets()
  }

  /// 切换tab
  func switchTab(to tab: String) {
    selectedTab = tab
    Logger.info("Switched to tab: \(tab)")
  }

  // MARK: - Asset Selection Management

  /// Toggle asset selection
  func toggleAssetSelection(_ asset: UserAsset) {
    if let index = selectedAssets.firstIndex(where: { $0.id == asset.id }) {
      // Remove if already selected
      selectedAssets.remove(at: index)
      Logger.info("Removed asset from selection: \(asset.name)")
    } else if selectedAssets.count < maxSelectionCount {
      // Add if under limit
      selectedAssets.append(asset)
      Logger.info("Added asset to selection: \(asset.name)")
    } else {
      Logger.info("Selection limit reached (\(maxSelectionCount))")
    }
  }

  /// Check if asset is selected
  func isAssetSelected(_ asset: UserAsset) -> Bool {
    return selectedAssets.contains(where: { $0.id == asset.id })
  }

  /// Confirm selection and call callback
  func confirmSelection() {
    Logger.info("Confirming selection of \(selectedAssets.count) assets")
    onAssetsSelected?(selectedAssets)
    onDismiss?()
  }

  /// Handle multiple asset selection confirmation
  func confirmSelection(_ assets: [UserAsset]) {
    Logger.info("Confirming selection of \(assets.count) assets")

    // Notify parent via callback
    onAssetsSelected?(assets)

    // Dismiss the view
    dismiss()
  }

  /// Handle file upload action
  func handleFileUpload() {
    Logger.info("File upload tapped")
    // This will be handled by the view with PhotosPicker
  }

  /// Upload selected images directly using AssetApplicationService
  func uploadImages(_ items: [PhotosPickerItem]) {
    guard !items.isEmpty else { return }

    isUploading = true

    Task {
      var uploadedAssets: [UserAsset] = []

      do {
        // 为每个图片创建上传任务
        for item in items {
          guard let data = try await item.loadTransferable(type: Data.self),
            let uiImage = UIImage(data: data)
          else {
            Logger.error("Failed to load image data from PhotosPickerItem")
            continue
          }

          // Generate a unique filename
          let fileName = "uploaded_image_\(UUID().uuidString)"

          // Upload image using upload-with-asset API
          let asset = try await assetService.uploadImageWithAsset(
            image: uiImage,
            fileName: fileName,
            quality: 0.8,
            prefix: "uploads/user_assets",
            tags: ["user_upload", "asset_selection"],
            description: "User uploaded image from asset selection",
            metadata: nil,
            isPublic: false
          )

          uploadedAssets.append(asset)
          Logger.info("Successfully uploaded asset: \(asset.name)")
        }

        await MainActor.run {
          self.isUploading = false
          // 触发回调，传递所有上传的资产
          // if !uploadedAssets.isEmpty {
          //   self.onAssetsSelected?(uploadedAssets)
          // }
        }

      } catch {
        await MainActor.run {
          self.isUploading = false
          self.assetsError = error
          Logger.error("Upload failed: \(error.localizedDescription)")
        }
      }
    }
  }

  /// Dismiss the view
  func dismiss() {
    onDismiss?()
  }

  /// Delete asset directly using AssetApplicationService
  func deleteAsset(_ asset: UserAsset) {
    isDeleting = true

    Task {
      do {
        let deleteResponse = try await assetService.deleteAsset(id: asset.id)
        Logger.info("Asset deleted successfully: \(deleteResponse.message)")

        await MainActor.run {
          self.isDeleting = false
          // 从本地列表中移除已删除的资产
          self.allAssets.removeAll { $0.id == asset.id }
          // 如果已选择的资产中包含被删除的资产，也要移除
          self.selectedAssets.removeAll { $0.id == asset.id }
        }

      } catch {
        await MainActor.run {
          self.isDeleting = false
          self.assetsError = error
          Logger.error("Delete failed: \(error.localizedDescription)")
        }
      }
    }
  }
}

// MARK: - Factory Methods

extension UserAssetSelectionViewModel {
  /// Create a view model with default dependencies
  static func create(
    preselectedAssets: [UserAsset] = [],
    onAssetsSelected: @escaping ([UserAsset]) -> Void,
    onDismiss: @escaping () -> Void,
    maxSelectionCount: Int = 3
  ) -> UserAssetSelectionViewModel {
    let container = AppDependencyContainer.shared

    return UserAssetSelectionViewModel(
      assetService: container.assetModule.assetService,
      preselectedAssets: preselectedAssets,
      onAssetsSelected: onAssetsSelected,
      onDismiss: onDismiss,
      maxSelectionCount: maxSelectionCount
    )
  }
}
