//
//  PromptFooterView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/4/24.
//

import SwiftUI

struct PromptFooterView: View {
  @ObservedObject var viewModel: PromptFooterViewModel
  @FocusState private var inputFocused: <PERSON><PERSON>
  @Binding var isInputActive: Bool

  var body: some View {
    VStack {
      Spacer()

      // Show SelectedAssetsInputView if there are selected assets, otherwise show original input
      if !viewModel.selectedAssets.isEmpty {
        SelectedAssetsInputView(viewModel: viewModel)
      } else {
        // Original input design (no selected assets display)
        HStack(spacing: 12) {
          // Left asset button

          // Input field
          HStack {
            Spacer()

            Button(action: {
              viewModel.showAssetSelection()
            }) {
              Image(systemName: "photo.on.rectangle")
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(.secondary)
                .frame(width: 24, height: 24)
                .padding(.leading, 12)
            }

            TextField("Imagine...", text: $viewModel.promptText)
              .font(.system(size: 16, weight: .regular))
              .foregroundColor(.primary)
              .padding(.leading, isInputActive ? 12 : 8)
              .padding(.trailing, 12)
              .focused($inputFocused)
              .onChange(of: inputFocused) { newValue in
                isInputActive = newValue
                viewModel.setInputActive(newValue)
              }
              .onChange(of: isInputActive) { newValue in
                if !newValue {
                  inputFocused = false
                }
              }

            // Right icon buttons
            if isInputActive {
              // Confirm button
              Button(action: {
                viewModel.submitPrompt()
                if !viewModel.isGenerating {
                  isInputActive = false
                }
              }) {
                if viewModel.isGenerating {
                  ProgressView()
                    .scaleEffect(0.8)
                    .frame(width: 24, height: 24)
                    .padding(.trailing, 12)
                } else {
                  Image(systemName: "arrow.up.circle.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(viewModel.isSubmitEnabled ? .accentColor : .secondary)
                    .frame(width: 24, height: 24)
                    .padding(.trailing, 12)
                }
              }
              .disabled(!viewModel.isSubmitEnabled)
            } else {

              // Settings/Filter button
              Button(action: {
                viewModel.requestSettings()
              }) {
                Image(systemName: "slider.horizontal.3")
                  .font(.system(size: 20, weight: .medium))
                  .foregroundColor(.secondary)
                  .frame(width: 24, height: 24)
                  .padding(.trailing, 12)
              }
            }
          }
          .frame(height: 44)
          .background(
            RoundedRectangle(cornerRadius: 22)
              .fill(Color(.systemGray6))
          )
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
      }
    }
    .sheet(isPresented: $viewModel.isAssetSelectionPresented) {
      UserAssetSelectionView(
        onAssetsSelected: { assets in
          // Update PromptFooterViewModel with selected assets
          viewModel.setSelectedAssets(assets)
          Logger.info("Assets selected in PromptFooterView: \(assets.count) assets")
        },
        onDismiss: {
          viewModel.hideAssetSelection()
        },
        preselectedAssets: viewModel.selectedAssets
      )
      .presentationDragIndicator(.visible)
    }
    .fullScreenCover(isPresented: $viewModel.showPaywall) {
      PaywallView(
        options: .fullScreen(offeringId: nil),
        onDismiss: {
          viewModel.hidePaywall()
        },
        onPurchaseCompleted: { subscription in
          Logger.info("PromptFooterView: 购买完成 - \(subscription.tier.displayName)")
          viewModel.hidePaywall()
        },
        onRestoreCompleted: { subscription in
          if let subscription = subscription {
            Logger.info("PromptFooterView: 恢复购买完成 - \(subscription.tier.displayName)")
          } else {
            Logger.info("PromptFooterView: 恢复购买完成 - 无订阅")
          }
          viewModel.hidePaywall()
        }
      )
    }
  }
}
