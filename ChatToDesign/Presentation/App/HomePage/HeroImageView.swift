//
//  HeroImageView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//

import SwiftUI

struct HeroImageView: View {
  var body: some View {
    ZStack {
      // Background image
      RoundedRectangle(cornerRadius: 8)
        .fill(
          LinearGradient(
            gradient: Gradient(colors: [
              Color(red: 0.2, green: 0.4, blue: 0.8),
              Color(red: 0.4, green: 0.6, blue: 0.9)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
          )
        )
        .frame(height: 220)
        .overlay(
          // Placeholder for actual image
          RoundedRectangle(cornerRadius: 8)
            .fill(Color.black.opacity(0.3))
        )
    }
    .padding(.horizontal, 16)
  }
}

#Preview {
  HeroImageView()
}
