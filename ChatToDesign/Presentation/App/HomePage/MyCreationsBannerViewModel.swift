import Combine
import Foundation
import os

@MainActor
class MyCreationsBannerViewModel: ObservableObject {
  // MARK: - Published Properties
  @Published var imageTasks: [ImageGenerationTask] = []
  @Published var isLoading: Bool = false
  @Published var error: Error? = nil
  
  // MARK: - Private Properties
  private var cancellables = Set<AnyCancellable>()
  private var taskSubscription: AnyCancellable?
  
  private let designGenerationService: DesignGenerationService
  private let userService: UserService
  
  // MARK: - Initialization
  init(
    designGenerationService: DesignGenerationService = AppDependencyContainer.shared.designModule.designGenerationService,
    userService: UserService = AppDependencyContainer.shared.userModule.userService
  ) {
    self.designGenerationService = designGenerationService
    self.userService = userService
    
    setupUserObserver()
  }
  
  // MARK: - Computed Properties
  
  // Today's tasks
  var todayTasks: [ImageGenerationTask] {
    let calendar = Calendar.current
    let today = calendar.startOfDay(for: Date())
    
    return imageTasks.filter { task in
      guard let createdAt = task.createdAt?.dateValue() else { return false }
      return calendar.isDate(createdAt, inSameDayAs: today)
    }
  }
  
  // Active tasks (pending or processing)
  var activeTasks: [ImageGenerationTask] {
    return todayTasks.filter { task in
      task.status == .pending || task.status == .processing
    }
  }
  
  // Completed tasks (succeeded)
  var completedTasks: [ImageGenerationTask] {
    return todayTasks.filter { task in
      task.status == .succeeded
    }.sorted { task1, task2 in
      let date1 = task1.createdAt?.dateValue() ?? Date.distantPast
      let date2 = task2.createdAt?.dateValue() ?? Date.distantPast
      return date1 > date2
    }
  }
  
  // MARK: - Public Methods
  func fetchTasks() async {
    guard let userId = userService.currentUser?.id, !userId.isEmpty else {
      Logger.warning("User ID is empty, cannot fetch tasks for MyCreationsBanner.")
      error = MyCreationsBannerError.userNotFound
      return
    }
    
    isLoading = true
    error = nil
    
    do {
      let tasks = try await designGenerationService.getTaskList(
        userId: userId, limit: 50, startAfterTaskId: nil)
      self.imageTasks = tasks
      Logger.info("Successfully fetched \(tasks.count) tasks for MyCreationsBanner.")
    } catch {
      Logger.error("Error fetching tasks for MyCreationsBanner: \(error.localizedDescription)")
      self.error = error
    }
    
    isLoading = false
  }
  
  // MARK: - Private Methods
  private func setupUserObserver() {
    userService.currentUserPublisher
      .receive(on: DispatchQueue.main)
      .sink { [weak self] user in
        guard let self = self else { return }
        if user != nil {
          Task {
            await self.fetchTasks()
          }
          self.observeTasks()
        } else {
          Logger.info("User is nil. Clearing MyCreationsBanner tasks and stopping observer.")
          self.stopObserving()
          self.imageTasks = []
          self.error = nil
        }
      }
      .store(in: &cancellables)
  }
  
  private func observeTasks() {
    guard let userId = userService.currentUser?.id, !userId.isEmpty else {
      Logger.warning("User ID is empty, cannot observe tasks for MyCreationsBanner.")
      error = MyCreationsBannerError.userNotFound
      return
    }
    
    stopObserving()
    
    taskSubscription = designGenerationService.observeTaskList(userId: userId, limit: 50)
      .receive(on: DispatchQueue.main)
      .sink(
        receiveCompletion: { [weak self] completion in
          if case .failure(let error) = completion {
            Logger.error("Error observing tasks for MyCreationsBanner: \(error.localizedDescription)")
            self?.error = error
          }
        },
        receiveValue: { [weak self] tasks in
          self?.imageTasks = tasks
          Logger.info("Updated MyCreationsBanner tasks: \(tasks.count)")
        }
      )
  }
  
  private func stopObserving() {
    taskSubscription?.cancel()
    taskSubscription = nil
  }
}

// MARK: - Error Types
enum MyCreationsBannerError: Error, LocalizedError {
  case userNotFound
  case fetchFailed(Error)
  
  var errorDescription: String? {
    switch self {
    case .userNotFound:
      return "User not found. Please log in again."
    case .fetchFailed(let error):
      return "Failed to fetch tasks: \(error.localizedDescription)"
    }
  }
}
