//
//  SearchView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/4/24.
//

import SwiftUI

struct SearchView: View {
  @Binding var searchText: String
  let onClose: () -> Void

  var body: some View {
    VStack {
      // Search bar
      HStack {
        Image(systemName: "magnifyingglass")
          .foregroundColor(.secondary)

        TextField("Search designs", text: $searchText)
          .autocapitalization(.none)
          .disableAutocorrection(true)

        if !searchText.isEmpty {
          Button(action: { searchText = "" }) {
            Image(systemName: "xmark.circle.fill")
              .foregroundColor(.secondary)
          }
        }

        But<PERSON>("Cancel", action: onClose)
          .foregroundColor(.primary)
      }
      .padding()
      .background(Color(.secondarySystemBackground))
      .cornerRadius(10)
      .padding()

      // Search results will be displayed here
      Text("Search results will be displayed here")
        .foregroundColor(.secondary)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .opacity(searchText.isEmpty ? 0 : 1)

      Spacer()
    }
    .background(Color(.systemBackground))
  }
}
