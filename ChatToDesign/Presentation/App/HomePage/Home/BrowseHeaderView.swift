//
//  BrowseHeaderView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/4/24.
//

import SwiftUI

// Enum to represent the selected tab
enum TabSelection {
  case home
  case projects
}

struct BrowseHeaderView: View {
  // State to keep track of the selected tab
  @State private var selectedTab: TabSelection = .home
  let onSearchTap: () -> Void
  // Callback to notify the parent view about tab selection
  let onTabSelected: (TabSelection) -> Void

  // Current user
  private var currentUser: User? {
    UserStateManager.shared.currentUser
  }

  var body: some View {
    HStack {
      HStack(spacing: 20) {  // Added spacing for better visual separation
        // Home Tab
        VStack(alignment: .leading, spacing: 4) {
          Text("Home")
            .font(.system(size: 24, weight: .bold))
            .foregroundColor(selectedTab == .home ? Color.primary : Color.primary.opacity(0.4))
            .onTapGesture {
              selectedTab = .home
              onTabSelected(.home)
              Logger.info("Home tab selected")
            }

          // Underline indicator for Home tab
          if selectedTab == .home {
            Rectangle()
              .fill(Color.primary)
              .frame(width: 64, height: 3)
              .offset(y: -2)
          }
        }

        // Projects Tab
        VStack(alignment: .leading, spacing: 4) {
          Text("Projects")
            .font(.system(size: 24, weight: .bold))
            .foregroundColor(selectedTab == .projects ? Color.primary : Color.primary.opacity(0.4))
            .onTapGesture {
              selectedTab = .projects
              onTabSelected(.projects)
              Logger.info("Projects tab selected")
            }
        }
      }

      Spacer()

      // User profile button
      NavigationLink(destination: UserProfileDetailView(user: currentUser)) {
        UserProfileView(user: currentUser)
      }
    }
  }
}

#Preview {
  BrowseHeaderView(
    onSearchTap: {
      Logger.debug("Search tapped in preview")
    },
    onTabSelected: { tab in
      Logger.debug("Tab selected in preview: \(tab)")
    }
  )
  .padding()
  .background(Color.gray.opacity(0.2))  // For better visibility in preview
}
