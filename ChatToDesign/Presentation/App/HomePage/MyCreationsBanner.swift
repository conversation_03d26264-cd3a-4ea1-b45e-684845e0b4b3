import Kingfisher
import Swift<PERSON>

struct MyCreationsBanner: View {
  @ObservedObject var viewModel: MyCreationsBannerViewModel
  @State private var isExpanded: Bool = false
  @State private var selectedCompletedTask: ImageGenerationTask? = nil
  @State private var showAllCompletedTasks: Bool = false
  @State private var previousTaskIds: Set<String> = []
  @State private var hasInitialized: Bool = false

  var body: some View {
    VStack(spacing: 0) {
      // Header
      headerView

      // Expanded content
      if isExpanded {
        expandedContentView
          .transition(.opacity.combined(with: .move(edge: .top)))
      }
    }
    .background(
      RoundedRectangle(cornerRadius: 12)
        .fill(.ultraThinMaterial)
        .overlay(
          RoundedRectangle(cornerRadius: 12)
            .stroke(Color.primary.opacity(0.1), lineWidth: 0.5)
        )
    )
    .padding(.horizontal, 16)
    .onAppear {
      // Initialize with current task IDs
      let currentTaskIds = Set(viewModel.imageTasks.map { $0.taskId })
      previousTaskIds = currentTaskIds
      hasInitialized = true
    }
    .onChange(of: viewModel.imageTasks.count) { _ in
      // Only process changes after initial load
      guard hasInitialized else { return }

      let currentTaskIds = Set(viewModel.imageTasks.map { $0.taskId })
      let newTaskIds = currentTaskIds.subtracting(previousTaskIds)

      // Auto-expand when new tasks are added or when there are active tasks
      let hasNewTasks = !newTaskIds.isEmpty
      let hasActiveTasks = !viewModel.activeTasks.isEmpty

      if hasNewTasks || (hasActiveTasks && !isExpanded) {
        // Add a small delay to ensure the UI is ready for the animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
          withAnimation(.easeInOut(duration: 0.4)) {
            isExpanded = true
          }
        }
      }

      // Update previous task IDs
      previousTaskIds = currentTaskIds
    }
    .onChange(of: viewModel.activeTasks.count) { _ in
      // Also monitor active tasks changes to auto-expand when tasks become active
      guard hasInitialized else { return }

      if !viewModel.activeTasks.isEmpty && !isExpanded {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
          withAnimation(.easeInOut(duration: 0.4)) {
            isExpanded = true
          }
        }
      }
    }
    .sheet(item: $selectedCompletedTask) { task in
      GeneratedImageDetailView(task: task)
    }
  }

  // MARK: - Header View

  private var headerView: some View {
    HStack {
      Text("My Creations")
        .font(.system(size: 20, weight: .semibold))
        .foregroundColor(.primary)

      Spacer()

      // Expand/Collapse button
      Button(action: {
        withAnimation(.easeInOut(duration: 0.3)) {
          isExpanded.toggle()
          // Reset show all completed tasks when collapsing
          if !isExpanded {
            showAllCompletedTasks = false
          }
        }
      }) {
        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.primary)
          .rotationEffect(.degrees(isExpanded ? 0 : 0))
      }
    }
    .padding(.horizontal, 20)
    .padding(.vertical, 16)
  }

  // MARK: - Expanded Content View

  private var expandedContentView: some View {
    VStack(spacing: 12) {
      // Active tasks section
      if !viewModel.activeTasks.isEmpty {
        activeTasksSection
      }

      // Completed tasks section
      if !viewModel.completedTasks.isEmpty {
        completedTasksSection
      }

      // Empty state
      if viewModel.todayTasks.isEmpty && !viewModel.isLoading {
        emptyStateView
      }

      // Loading state
      if viewModel.isLoading {
        loadingView
      }
    }
    .padding(.horizontal, 20)
    .padding(.bottom, 20)
  }

  // MARK: - Active Tasks Section

  private var activeTasksSection: some View {
    VStack(alignment: .leading, spacing: 8) {
      Text("In Progress")
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.secondary)

      ForEach(viewModel.activeTasks, id: \.taskId) { task in
        TaskRowView(task: task, isActive: true)
      }
    }
  }

  // MARK: - Completed Tasks Section

  private var completedTasksSection: some View {
    VStack(alignment: .leading, spacing: 8) {
      if !viewModel.activeTasks.isEmpty {
        Divider()
          .background(Color.primary.opacity(0.2))
          .padding(.vertical, 8)
      }

      Text("Completed")
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.secondary)

      let tasksToShow =
        showAllCompletedTasks ? viewModel.completedTasks : Array(viewModel.completedTasks.prefix(3))
      ForEach(tasksToShow, id: \.taskId) { task in
        TaskRowView(task: task, isActive: false)
          .onTapGesture {
            selectedCompletedTask = task
          }
      }

      if viewModel.completedTasks.count > 3 {
        Button(action: {
          withAnimation(.easeInOut(duration: 0.3)) {
            showAllCompletedTasks.toggle()
          }
        }) {
          Text(
            showAllCompletedTasks
              ? "Show less" : "View all \(viewModel.completedTasks.count) creations"
          )
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(Color(red: 0.42, green: 0.65, blue: 0.81))
        }
        .padding(.top, 4)
      }
    }
  }

  // MARK: - Empty State View

  private var emptyStateView: some View {
    VStack(spacing: 8) {
      Image(systemName: "photo.artframe")
        .font(.system(size: 24))
        .foregroundColor(.secondary)

      Text("No creations today")
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.secondary)

      Text("Start creating to see your progress here")
        .font(.system(size: 12))
        .foregroundColor(.secondary.opacity(0.8))
        .multilineTextAlignment(.center)
    }
    .padding(.vertical, 20)
  }

  // MARK: - Loading View

  private var loadingView: some View {
    HStack {
      ProgressView()
        .progressViewStyle(CircularProgressViewStyle(tint: .primary))
        .scaleEffect(0.8)

      Text("Loading your creations...")
        .font(.system(size: 14))
        .foregroundColor(.secondary)
    }
    .padding(.vertical, 20)
  }
}

// MARK: - Task Row View

struct TaskRowView: View {
  let task: ImageGenerationTask
  let isActive: Bool

  var body: some View {
    HStack(spacing: 12) {
      // Task icon/thumbnail
      taskIconView

      // Task info
      VStack(alignment: .leading, spacing: 4) {
        Text(task.prompt ?? "Untitled Creation")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.primary)
          .lineLimit(1)

        if isActive {
          progressView
        } else {
          statusView
        }
      }

      Spacer()

      // Status indicator
      statusIndicator
    }
    .padding(.vertical, 8)
    .padding(.horizontal, 12)
    .background(
      RoundedRectangle(cornerRadius: 8)
        .fill(Color.primary.opacity(0.05))
        .overlay(
          RoundedRectangle(cornerRadius: 8)
            .stroke(Color.primary.opacity(0.1), lineWidth: 0.5)
        )
    )
  }

  private var taskIconView: some View {
    ZStack {
      // Try to display task image, fallback to grid pattern
      if let imageUrl = getTaskImageUrl() {
        KFImage(URL(string: imageUrl))
          .placeholder {
            // Loading placeholder
            GridPatternView()
              .frame(width: 40, height: 40)
              .cornerRadius(6)
              .overlay(
                ProgressView()
                  .progressViewStyle(CircularProgressViewStyle(tint: .white))
                  .scaleEffect(0.4)
              )
          }
          .retry(maxCount: 3)
          .fade(duration: 0.25)
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: 40, height: 40)
          .clipped()
          .cornerRadius(6)
      } else {
        // Fallback to grid pattern when no image available
        GridPatternView()
          .frame(width: 40, height: 40)
          .cornerRadius(6)
      }

      // Status overlay for active tasks
      if isActive {
        Color.black.opacity(0.3)
          .frame(width: 40, height: 40)
          .cornerRadius(6)

        ProgressView()
          .progressViewStyle(CircularProgressViewStyle(tint: .white))
          .scaleEffect(0.6)
      }
    }
  }

  // Helper function to get the appropriate image URL
  private func getTaskImageUrl() -> String? {
    // Priority: resultImageUrls first, then inputImageUrls
    if let resultUrls = task.resultImageUrls, !resultUrls.isEmpty {
      return resultUrls.first
    } else if let inputUrls = task.inputImageUrls, !inputUrls.isEmpty {
      return inputUrls.first
    }
    return nil
  }

  private var progressView: some View {
    VStack(alignment: .leading, spacing: 2) {
      HStack {
        Text(task.status.rawValue.capitalized)
          .font(.system(size: 12))
          .foregroundColor(.secondary)

        Spacer()

        if let progress = task.progress {
          Text("\(progress)%")
            .font(.system(size: 12))
            .foregroundColor(.secondary)
        }
      }

      ProgressView(value: Double(task.progress ?? 0), total: 100.0)
        .progressViewStyle(LinearProgressViewStyle(tint: Color(red: 0.42, green: 0.65, blue: 0.81)))
        .frame(height: 2)
    }
  }

  private var statusView: some View {
    Text("Completed")
      .font(.system(size: 12))
      .foregroundColor(.green)
  }

  private var statusIndicator: some View {
    Circle()
      .fill(isActive ? Color(red: 0.42, green: 0.65, blue: 0.81) : .green)
      .frame(width: 8, height: 8)
  }
}

// MARK: - Grid Pattern View

struct GridPatternView: View {
  var body: some View {
    Canvas { context, size in
      let dotSize: CGFloat = 2
      let spacing: CGFloat = 4
      let rows = Int(size.height / spacing)
      let cols = Int(size.width / spacing)

      for row in 0..<rows {
        for col in 0..<cols {
          let x = CGFloat(col) * spacing + dotSize / 2
          let y = CGFloat(row) * spacing + dotSize / 2

          // Create gradient colors
          let colors = [Color.blue, Color.purple, Color.pink, Color.green]
          let colorIndex = (row + col) % colors.count

          context.fill(
            Path(ellipseIn: CGRect(x: x, y: y, width: dotSize, height: dotSize)),
            with: .color(colors[colorIndex].opacity(0.6))
          )
        }
      }
    }
    .background(Color(red: 0.23, green: 0.23, blue: 0.36))
  }
}

#Preview {
  MyCreationsBanner(viewModel: MyCreationsBannerViewModel())
    .background(Color(.systemBackground))
    .padding()
}
