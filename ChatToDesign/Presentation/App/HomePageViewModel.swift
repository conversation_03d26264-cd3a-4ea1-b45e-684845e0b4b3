import Combine
import Foundation
import PhotosUI
import SwiftUI
import os  // Import os for Logger

enum CategoryTab: String, CaseIterable, Identifiable {
  case trending = "Trending"
  case recent = "Recent"
  case quickTricks = "Quick tricks"
  case animation = "Animation"
  case sketch = "Sketch"

  var id: String { self.rawValue }
}

@MainActor
class HomePageViewModel: ObservableObject {
  // Page state
  @Published var selectedTab: CategoryTab = .trending
  @Published var currentBrowseMode: TabSelection = .home
  @Published var searchText: String = ""
  @Published var isSearching: Bool = false
  @Published var selectedImageTemplateItem: ImageTemplateItem? = nil

  // Paywall state
  @Published var showPaywall: Bool = false

  // CMS data - 使用 SWR Hook
  @Published var cmsCategories: [CMSCategory] = []
  @Published var isLoadingCMS: Bool = false
  @Published var cmsError: Error? = nil

  // CMS loading completion publisher for external observers
  @Published var isCMSDataLoaded: Bool = false

  // SWR Hook for CMS data
  private var cmsHook: SWRHook<[ImageTemplateItem]>?

  // Child ViewModels
  @Published var historyTaskListViewModel: HistoryTaskListViewModel
  @Published var myCreationsBannerViewModel: MyCreationsBannerViewModel

  // Input state
  @Published var isKeyboardVisible: Bool = false

  private var cancellables = Set<AnyCancellable>()
  private let userService: UserService
  private let apiService: APIService
  private let subscriptionStateManager: SubscriptionStateManager

  init(
    userService: UserService = AppDependencyContainer.shared.userModule.userService,
    apiService: APIService = AppDependencyContainer.shared.apiService,
    subscriptionStateManager: SubscriptionStateManager = AppDependencyContainer.shared
      .subscriptionModule.subscriptionStateManager
  ) {
    self.userService = userService
    self.apiService = apiService
    self.subscriptionStateManager = subscriptionStateManager

    // Initialize child ViewModels
    self.historyTaskListViewModel = HistoryTaskListViewModel(
      designGenerationService: AppDependencyContainer.shared.designModule.designGenerationService,
      videoGenerationService: AppDependencyContainer.shared.videoModule.videoGenerationService,
      userService: userService
    )
    self.myCreationsBannerViewModel = MyCreationsBannerViewModel(
      designGenerationService: AppDependencyContainer.shared.designModule.designGenerationService,
      userService: userService
    )

    setupCMSHook()

    // Monitor search text changes
    $searchText
      .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
      .sink { [weak self] _ in
        self?.performSearch()
      }
      .store(in: &cancellables)
  }

  /// 设置 CMS SWR 查询
  private func setupCMSHook() {
    // 创建 SWR Hook 实例
    cmsHook = SWRHook.create(
      key: "cms_data",
      maxAge: 30 * 60,  // 5分钟后触发后台刷新
      staleTime: 60 * 60,  // 1小时后数据完全过期
      networkCall: { [weak self] in
        guard let self = self else {
          throw APIServiceError.unknown(NSError(domain: "ViewModel", code: -1))
        }
        return try await self.apiService.fetchImageUseCaseCMS()
      },
      autoFetch: true
    )

    // 监听 SWR Hook 的状态变化
    observeCMSHookChanges()
  }

  /// 监听 CMS Hook 状态变化
  private func observeCMSHookChanges() {
    guard let cmsHook = cmsHook else { return }

    // 监听数据变化
    cmsHook.$data
      .receive(on: DispatchQueue.main)
      .sink { [weak self] ImageTemplateItems in
        if let items = ImageTemplateItems {
          self?.processCMSData(items)
          self?.isCMSDataLoaded = true
        }
      }
      .store(in: &cancellables)

    // 监听加载状态
    cmsHook.$isLoading
      .receive(on: DispatchQueue.main)
      .sink { [weak self] isLoading in
        self?.isLoadingCMS = isLoading
      }
      .store(in: &cancellables)

    // 监听错误状态
    cmsHook.$error
      .receive(on: DispatchQueue.main)
      .sink { [weak self] error in
        self?.cmsError = error
      }
      .store(in: &cancellables)
  }

  /// 处理 CMS 数据，转换为分类格式
  private func processCMSData(_ ImageTemplateItems: [ImageTemplateItem]) {
    // Group items by category
    let groupedItems = Dictionary(grouping: ImageTemplateItems) { $0.category }

    // Convert to CMSCategory objects and sort by group rank
    let categories = groupedItems.map { (categoryName, items) in
      CMSCategory(name: categoryName, items: items)
    }.sorted { category1, category2 in
      // Sort categories by the minimum group_rank of their items
      let minRank1 = category1.items.map(\.groupRank).min() ?? Int.max
      let minRank2 = category2.items.map(\.groupRank).min() ?? Int.max
      return minRank1 < minRank2
    }

    self.cmsCategories = categories
    Logger.info("Successfully processed \(categories.count) CMS categories")
  }

  /// 手动刷新 CMS 数据
  func fetchImageUseCaseCMS() async {
    cmsHook?.refresh()
  }

  /// Handle CMS item tap - shows the item detail sheet
  func handleImageTemplateItemTap(_ item: ImageTemplateItem) {
    Logger.info("CMS item tapped: \(item.name) - \(item.category)")
    selectedImageTemplateItem = item
  }

  func selectTab(_ tab: CategoryTab) {
    selectedTab = tab
    // In a real application, this should load different designs based on the selected tab
  }

  func toggleFavorite(for design: DesignCard) {
    // In a real application, this should update the favorite status in the database
    // if let index = trendingDesigns.firstIndex(where: { $0.id == design.id }) {
    //   var updatedDesign = design
    //   updatedDesign.isFavorite.toggle()
    //   trendingDesigns[index] = updatedDesign
    // }
  }

  private func performSearch() {
    // In a real application, this should filter designs based on search text
    isSearching = !searchText.isEmpty
  }

  // New features

  func toggleSearch() {
    isSearching.toggle()
    if !isSearching {
      searchText = ""
    }
  }

  // Hide keyboard
  func hideKeyboard() {
    UIApplication.shared.sendAction(
      #selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    isKeyboardVisible = false  // isKeyboardVisible is not declared, comment out or remove
  }

  // Method to update the browse mode
  func setBrowseMode(_ mode: TabSelection) {
    currentBrowseMode = mode
    // You might want to add logging or other logic here when the mode changes
    Logger.info("Browse mode switched to: \(mode)")
  }

  // MARK: - Paywall Methods

  /// 检查是否需要显示 Paywall（基于权限类型）
  /// - Parameter entitlement: 需要的权限类型
  /// - Returns: 是否需要显示 Paywall
  func shouldShowPaywall(for entitlement: EntitlementType) -> Bool {
    return !subscriptionStateManager.hasEntitlement(entitlement)
  }

  /// 检查是否需要显示 Paywall（基于功能）
  /// - Parameter feature: 需要的功能
  /// - Returns: 是否需要显示 Paywall
  func shouldShowPaywall(for feature: AppFeature) -> Bool {
    return subscriptionStateManager.shouldShowPaywall(for: feature)
  }

  /// 显示 Paywall（基于权限类型）
  /// - Parameter entitlement: 需要的权限类型（可选）
  func showPaywallIfNeeded(for entitlement: EntitlementType? = nil) {
    if let entitlement = entitlement {
      if shouldShowPaywall(for: entitlement) {
        showPaywall = true
        Logger.info("HomePageViewModel: 显示 Paywall，需要权限: \(entitlement)")
      }
    } else {
      // 无特定权限要求，直接显示 Paywall
      showPaywall = true
      Logger.info("HomePageViewModel: 显示 Paywall")
    }
  }

  /// 显示 Paywall（基于功能）
  /// - Parameter feature: 需要的功能
  func showPaywallIfNeeded(for feature: AppFeature) {
    if shouldShowPaywall(for: feature) {
      showPaywall = true
      Logger.info("HomePageViewModel: 显示 Paywall，需要功能: \(feature.displayName)")
    }
  }

  /// 隐藏 Paywall
  func hidePaywall() {
    showPaywall = false
    Logger.info("HomePageViewModel: 隐藏 Paywall")
  }

  /// 检查高级功能访问权限
  func checkPremiumAccess() -> Bool {
    return subscriptionStateManager.isProUser
  }

  /// 检查是否可以使用特定功能
  /// - Parameter feature: 功能类型
  /// - Returns: 是否可以使用
  func canUseFeature(_ feature: AppFeature) -> Bool {
    return subscriptionStateManager.canUseFeature(feature)
  }

  /// 获取当前用户层级
  var currentUserTier: UserTier {
    return subscriptionStateManager.userTier
  }

}
