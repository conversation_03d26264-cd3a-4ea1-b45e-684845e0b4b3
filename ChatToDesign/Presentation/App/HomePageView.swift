import SwiftUI

struct HomePageView: View {
  @ObservedObject private var viewModel: HomePageViewModel
  // @WARNING:  这是因为需要持久化这个 viewModel， 不然的话 拖动屏幕的时候会触发重新渲染 这个viewModel 会被重新创建
  @StateObject private var promptFooterViewModel = PromptFooterViewModel.create()
  @Environment(\.colorScheme) private var colorScheme
  @State private var isPromptFocused: Bool = false

  // MARK: - Initialization

  init(viewModel: HomePageViewModel? = nil) {
    self.viewModel = viewModel ?? HomePageViewModel()
  }

  var body: some View {
    NavigationView {
      ZStack {
        // Background clickable area (for keyboard dismissal)
        Color.clear
          .contentShape(Rectangle())
          .onTapGesture {
            isPromptFocused = false
            viewModel.hideKeyboard()
          }

        ScrollView {
          VStack(alignment: .leading, spacing: 24) {
            // Top navigation bar
            BrowseHeaderView(
              onSearchTap: { viewModel.toggleSearch() },
              onTabSelected: { selectedMode in
                viewModel.setBrowseMode(selectedMode)
              }
            )
            .padding(.horizontal)
            .padding(.top, 8)
            .onTapGesture {
              isPromptFocused = false
              viewModel.hideKeyboard()
            }

            // Conditional content based on BrowseHeaderView selection
            if viewModel.currentBrowseMode == .home {
              // Hero image section
              HeroImageView()
                .onTapGesture {
                  isPromptFocused = false
                  viewModel.hideKeyboard()
                }

              // My Creations Banner
              MyCreationsBanner(viewModel: viewModel.myCreationsBannerViewModel)
                .onTapGesture {
                  isPromptFocused = false
                  viewModel.hideKeyboard()
                }

              // Trending Styles section
              TrendingSectionView(
                cmsCategories: viewModel.cmsCategories,
                isLoading: viewModel.isLoadingCMS,
                error: viewModel.cmsError,
                onRetry: {
                  Task {
                    await viewModel.fetchImageUseCaseCMS()
                  }
                },
                onItemTap: { ImageTemplateItem in
                  viewModel.handleImageTemplateItemTap(ImageTemplateItem)
                }
              )
              .onTapGesture {
                isPromptFocused = false
                viewModel.hideKeyboard()
              }

              // Other CMS Categories sections
              CMSCategoriesView(
                cmsCategories: viewModel.cmsCategories,
                isLoading: viewModel.isLoadingCMS,
                error: viewModel.cmsError,
                onRetry: {
                  Task {
                    await viewModel.fetchImageUseCaseCMS()
                  }
                },
                onItemTap: { ImageTemplateItem in
                  viewModel.handleImageTemplateItemTap(ImageTemplateItem)
                }
              )
              .onTapGesture {
                isPromptFocused = false
                viewModel.hideKeyboard()
              }
            } else if viewModel.currentBrowseMode == .projects {
              HistoryTaskListView(viewModel: viewModel.historyTaskListViewModel)
                .padding(.horizontal)
            }

            Spacer(minLength: 80)  // Space for the bottom prompt
          }
        }
        .refreshable {
          await viewModel.fetchImageUseCaseCMS()
        }
        .simultaneousGesture(
          DragGesture().onChanged { _ in
            isPromptFocused = false
            viewModel.hideKeyboard()
          }
        )
      }
      .background(Color(.systemBackground))
      .trackScreenView(ScreenNames.home, screenClass: ScreenClasses.main)
      .overlay(
        PromptFooterView(
          viewModel: promptFooterViewModel,
          isInputActive: $isPromptFocused
        )
      )
      .sheet(isPresented: $viewModel.isSearching) {
        SearchView(searchText: $viewModel.searchText, onClose: { viewModel.toggleSearch() })
      }
      .sheet(item: $viewModel.selectedImageTemplateItem) { ImageTemplateItem in
        ImageTemplateItemDetailView(ImageTemplateItem: ImageTemplateItem)
          .presentationDragIndicator(.visible)
      }

      .navigationBarHidden(true)
    }
  }
}

struct HomePageView_Previews: PreviewProvider {
  static var previews: some View {
    HomePageView(viewModel: HomePageViewModel())
  }
}
