//
//  LogoSplashView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/14.
//

import SwiftUI

/// Logo启动页面视图
/// 显示应用Logo和加载动画，等待CMS数据加载完成
struct LogoSplashView: View {
  // MARK: - Properties

  /// ViewModel
  @StateObject private var viewModel: LogoSplashViewModel

  /// 传入的HomePageViewModel
  private let homePageViewModel: HomePageViewModel

  /// 动画状态
  @State private var logoScale: CGFloat = 0.8
  @State private var logoOpacity: Double = 0.0
  @State private var loadingRotation: Double = 0.0

  // MARK: - Initialization

  init(homePageViewModel: HomePageViewModel) {
    self.homePageViewModel = homePageViewModel
    self._viewModel = StateObject(
      wrappedValue: LogoSplashViewModel(homePageViewModel: homePageViewModel))
  }

  // MARK: - Body

  var body: some View {
    ZStack {
      // 背景
      Color(.systemBackground)
        .ignoresSafeArea()

      VStack(spacing: 40) {
        Spacer()

        // Logo区域
        VStack(spacing: 20) {
          // Logo图片
          Image("logo")
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(width: 120, height: 120)
            .scaleEffect(logoScale)
            .opacity(logoOpacity)
            .animation(.easeInOut(duration: 1.0), value: logoScale)
            .animation(.easeInOut(duration: 1.0), value: logoOpacity)

          // 应用名称
          Text("Picadabra")
            .font(.system(size: 28, weight: .bold, design: .rounded))
            .foregroundColor(.primary)
            .opacity(logoOpacity)
            .animation(.easeInOut(duration: 1.0).delay(0.3), value: logoOpacity)
        }

        Spacer()

        // 加载动画区域
        if viewModel.isLoading {
          VStack(spacing: 16) {
            // 旋转加载指示器
            Circle()
              .trim(from: 0, to: 0.7)
              .stroke(
                LinearGradient(
                  colors: [.blue, .purple],
                  startPoint: .topLeading,
                  endPoint: .bottomTrailing
                ),
                style: StrokeStyle(lineWidth: 3, lineCap: .round)
              )
              .frame(width: 32, height: 32)
              .rotationEffect(.degrees(loadingRotation))
              .animation(
                .linear(duration: 1.0).repeatForever(autoreverses: false), value: loadingRotation)

            // 加载文本
            Text("Loading...")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.secondary)
              .opacity(0.8)
          }
          .transition(.opacity)
        }

        Spacer()
      }
      .padding(.horizontal, 40)
    }
    .onAppear {
      startAnimations()
    }
    .fullScreenCover(isPresented: $viewModel.shouldShowHomePage) {
      HomePageView(viewModel: homePageViewModel)
    }
  }

  // MARK: - Private Methods

  /// 启动动画
  private func startAnimations() {
    // Logo出现动画
    withAnimation(.easeInOut(duration: 1.0)) {
      logoScale = 1.0
      logoOpacity = 1.0
    }

    // 开始加载指示器旋转
    withAnimation(.linear(duration: 1.0).repeatForever(autoreverses: false)) {
      loadingRotation = 360.0
    }
  }
}

// MARK: - Preview

struct LogoSplashView_Previews: PreviewProvider {
  static var previews: some View {
    LogoSplashView(homePageViewModel: HomePageViewModel())
  }
}
