//
//  MainTabView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/3.
//

import SwiftUI

/// Main tab view container managing the three main pages
struct MainTabView: View {
  @State private var selectedTab: RootTabType = .profile

  var body: some View {
    ZStack(alignment: .bottom) {
      // Main content area
      contentView
        .frame(maxWidth: .infinity, maxHeight: .infinity)

      // Custom bottom navigation bar
      BottomNavigationBar(selectedTab: $selectedTab)
    }
    .background(Color.black)  // Ensure consistent background
    .ignoresSafeArea(.keyboard, edges: .bottom)  // Handle keyboard properly
    .onReceive(NotificationCenter.default.publisher(for: .navigateToProfile)) { _ in
      selectedTab = .profile
    }
  }

  // MARK: - Content View

  @ViewBuilder
  private var contentView: some View {
    switch selectedTab {
    case .explore:
      ExplorePageView(viewModel: ExplorePageViewModel())
        .transition(.opacity)

    case .create:
      CreatePageView()
        .transition(.opacity)

    case .profile:
      ProfilePageView()
        .transition(.opacity)
    }
  }
}

#Preview {
  MainTabView()
}
