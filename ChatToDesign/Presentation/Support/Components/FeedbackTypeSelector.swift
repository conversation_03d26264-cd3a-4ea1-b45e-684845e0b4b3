//
//  FeedbackTypeSelector.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/20.
//

import SwiftUI

/// Component for selecting feedback type
struct FeedbackTypeSelector: View {
    
    /// Selected feedback type
    @Binding var selectedType: FeedbackType
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("What can we help you with?")
                .font(.custom("Inter", size: 18).weight(.semibold))
                .foregroundColor(.primary)
            
            VStack(spacing: 12) {
                ForEach(FeedbackType.allCases, id: \.self) { type in
                    FeedbackTypeRow(
                        type: type,
                        isSelected: selectedType == type,
                        onTap: {
                            selectedType = type
                        }
                    )
                }
            }
        }
    }
}

/// Individual row for feedback type selection
private struct FeedbackTypeRow: View {
    
    let type: FeedbackType
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Selection indicator
                Image(systemName: isSelected ? "largecircle.fill.circle" : "circle")
                    .foregroundColor(isSelected ? .blue : .secondary)
                    .font(.system(size: 20))
                
                // Icon
                Image(systemName: type.iconName)
                    .foregroundColor(isSelected ? .blue : .secondary)
                    .font(.system(size: 16))
                    .frame(width: 20)
                
                // Title
                Text(type.displayName)
                    .font(.custom("Inter", size: 16).weight(.medium))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemGray6))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview

#Preview {
    VStack {
        FeedbackTypeSelector(selectedType: .constant(.bug))
            .padding()
        
        Spacer()
    }
    .background(Color(.systemBackground))
}
