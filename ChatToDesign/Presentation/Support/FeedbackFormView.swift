//
//  FeedbackFormView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/20.
//

import SwiftUI

/// Main feedback form view
struct FeedbackFormView: View {
    
    /// View model for feedback form
    @StateObject private var viewModel: FeedbackFormViewModel
    
    /// Dismiss environment
    @Environment(\.dismiss) private var dismiss
    
    /// Initialize with services
    /// - Parameters:
    ///   - feedbackService: Service for submitting feedback
    ///   - uploadService: Service for uploading attachments
    init(feedbackService: FeedbackService, uploadService: FileUploadService) {
        self._viewModel = StateObject(
            wrappedValue: FeedbackFormViewModel(
                feedbackService: feedbackService,
                uploadService: uploadService
            )
        )
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    feedbackTypeSection
                    subjectSection
                    contentSection
                    attachmentSection
                    submitSection
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 16)
            }
            .navigationTitle("Contact Support")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.primary)
                }
            }
        }
        .alert("Feedback Submitted", isPresented: $viewModel.showSuccessAlert) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Thank you for your feedback! We'll get back to you soon.")
        }
        .alert("Error", isPresented: $viewModel.showErrorAlert) {
            Button("Retry") {
                Task {
                    await viewModel.submitFeedback()
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text(viewModel.errorMessage)
        }
    }
    
    // MARK: - Sections
    
    private var feedbackTypeSection: some View {
        FeedbackTypeSelector(selectedType: $viewModel.selectedType)
    }
    
    private var subjectSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Subject")
                .font(.custom("Inter", size: 16).weight(.medium))
                .foregroundColor(.primary)
            
            TextField("Brief description of your issue", text: $viewModel.subject)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .font(.custom("Inter", size: 16))
        }
    }
    
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Details")
                    .font(.custom("Inter", size: 16).weight(.medium))
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text("\(viewModel.contentCharacterCount)/\(viewModel.maxContentLength)")
                    .font(.custom("Inter", size: 12))
                    .foregroundColor(.secondary)
            }
            
            TextEditor(text: $viewModel.content)
                .frame(minHeight: 120)
                .padding(8)
                .background(Color(.systemGray6))
                .cornerRadius(8)
                .font(.custom("Inter", size: 16))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
                .overlay(
                    // Placeholder text
                    Group {
                        if viewModel.content.isEmpty {
                            VStack {
                                HStack {
                                    Text("Please provide more details about your issue...")
                                        .font(.custom("Inter", size: 16))
                                        .foregroundColor(.secondary)
                                        .padding(.leading, 12)
                                        .padding(.top, 16)
                                    Spacer()
                                }
                                Spacer()
                            }
                        }
                    }
                )
        }
    }
    
    private var attachmentSection: some View {
        AttachmentUploader(viewModel: viewModel)
    }
    
    private var submitSection: some View {
        VStack(spacing: 16) {
            Button(action: {
                Task {
                    await viewModel.submitFeedback()
                }
            }) {
                HStack {
                    if viewModel.isSubmitting {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                        
                        Text("Submitting...")
                            .font(.custom("Inter", size: 16).weight(.semibold))
                            .foregroundColor(.white)
                    } else {
                        Text("Submit Feedback")
                            .font(.custom("Inter", size: 16).weight(.semibold))
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(viewModel.isFormValid && !viewModel.isSubmitting ? Color.blue : Color.gray)
                )
            }
            .disabled(!viewModel.isFormValid || viewModel.isSubmitting)
            .buttonStyle(PlainButtonStyle())
            
            // Form validation hint
            if !viewModel.isFormValid {
                Text("Please fill in all required fields (minimum 10 characters for details)")
                    .font(.custom("Inter", size: 12))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
}

// MARK: - Preview

#Preview {
    FeedbackFormView(
        feedbackService: MockFeedbackService(),
        uploadService: MockFileUploadService()
    )
}

// MARK: - Mock Services for Preview

private class MockFeedbackService: FeedbackService {
    func submitFeedback(_ feedback: Feedback) async throws {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000)
    }
}

private class MockFileUploadService: FileUploadService {
    func uploadFile(data: Data, mimeType: String, fileName: String, prefix: String?) async throws -> URL {
        return URL(string: "https://example.com/mock.jpg")!
    }
    
    func uploadImage(image: UIImage, fileName: String, quality: CGFloat, prefix: String?) async throws -> URL {
        return URL(string: "https://example.com/mock.jpg")!
    }
    
    func validateFile(data: Data, mimeType: String) throws {
        // Mock implementation
    }
}
