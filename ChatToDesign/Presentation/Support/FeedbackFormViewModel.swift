//
//  FeedbackFormViewModel.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/20.
//

import Foundation
import SwiftUI

/// ViewModel for feedback form
@MainActor
final class FeedbackFormViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// Selected feedback type
    @Published var selectedType: FeedbackType = .question
    
    /// Subject/title of the feedback
    @Published var subject: String = ""
    
    /// Detailed content of the feedback
    @Published var content: String = ""
    
    /// Attached files
    @Published var attachments: [FeedbackAttachment] = []
    
    /// Whether form is being submitted
    @Published var isSubmitting: Bool = false
    
    /// Whether attachment is being uploaded
    @Published var isUploadingAttachment: Bool = false
    
    /// Whether to show success alert
    @Published var showSuccessAlert: Bool = false
    
    /// Whether to show error alert
    @Published var showErrorAlert: Bool = false
    
    /// Error message to display
    @Published var errorMessage: String = ""
    
    // MARK: - Dependencies
    
    private let feedbackService: FeedbackService
    private let uploadService: FileUploadService
    
    // MARK: - Computed Properties
    
    /// Whether the form is valid for submission
    var isFormValid: Bool {
        !subject.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        content.count >= 10
    }
    
    /// Character count for content
    var contentCharacterCount: Int {
        content.count
    }
    
    /// Maximum content length
    let maxContentLength = 5000
    
    /// Maximum number of attachments
    let maxAttachments = 3
    
    /// Whether more attachments can be added
    var canAddMoreAttachments: Bool {
        attachments.count < maxAttachments
    }
    
    // MARK: - Initialization
    
    /// Initialize with dependencies
    /// - Parameters:
    ///   - feedbackService: Service for submitting feedback
    ///   - uploadService: Service for uploading attachments
    init(feedbackService: FeedbackService, uploadService: FileUploadService) {
        self.feedbackService = feedbackService
        self.uploadService = uploadService
    }
    
    // MARK: - Actions
    
    /// Submit the feedback form
    func submitFeedback() async {
        guard isFormValid else { return }
        
        isSubmitting = true
        
        do {
            let feedback = Feedback.create(
                userId: UserStateManager.shared.currentUser?.id,
                type: selectedType,
                subject: subject.trimmingCharacters(in: .whitespacesAndNewlines),
                content: content.trimmingCharacters(in: .whitespacesAndNewlines),
                attachments: attachments
            )
            
            try await feedbackService.submitFeedback(feedback)
            
            showSuccessAlert = true
            Logger.info("Feedback submitted successfully")
            
        } catch {
            errorMessage = error.localizedDescription
            showErrorAlert = true
            Logger.error("Failed to submit feedback: \(error)")
        }
        
        isSubmitting = false
    }
    
    /// Add an image attachment
    /// - Parameter image: UIImage to upload
    func addImageAttachment(_ image: UIImage) async {
        guard canAddMoreAttachments else {
            errorMessage = "Maximum \(maxAttachments) attachments allowed"
            showErrorAlert = true
            return
        }
        
        isUploadingAttachment = true
        
        do {
            let filename = "feedback_\(UUID().uuidString).jpg"
            
            let url = try await uploadService.uploadImage(
                image: image,
                fileName: filename,
                quality: 0.8,
                prefix: "feedback"
            )
            
            let attachment = FeedbackAttachment(
                id: UUID().uuidString,
                url: url.absoluteString,
                filename: filename
            )
            
            attachments.append(attachment)
            Logger.info("Feedback attachment uploaded: \(filename)")
            
        } catch {
            Logger.error("Failed to upload feedback attachment: \(error)")
            errorMessage = "Failed to upload image. Please try again."
            showErrorAlert = true
        }
        
        isUploadingAttachment = false
    }
    
    /// Remove an attachment
    /// - Parameter attachmentId: ID of attachment to remove
    func removeAttachment(_ attachmentId: String) {
        attachments.removeAll { $0.id == attachmentId }
    }
    
    /// Reset form to initial state
    func resetForm() {
        selectedType = .question
        subject = ""
        content = ""
        attachments = []
        isSubmitting = false
        isUploadingAttachment = false
        showSuccessAlert = false
        showErrorAlert = false
        errorMessage = ""
    }
}
