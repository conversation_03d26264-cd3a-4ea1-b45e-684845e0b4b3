<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>ChatToDesign.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>0</integer>
		</dict>
		<key>Promises (Playground).xcscheme</key>
		<dict>
			<key>orderHint</key>
			<integer>7</integer>
		</dict>
		<key>ReactiveSwift (Playground).xcscheme</key>
		<dict>
			<key>orderHint</key>
			<integer>4</integer>
		</dict>
		<key>ReactiveSwift-UIExamples (Playground).xcscheme</key>
		<dict>
			<key>orderHint</key>
			<integer>5</integer>
		</dict>
		<key>Rx (Playground).xcscheme</key>
		<dict>
			<key>orderHint</key>
			<integer>6</integer>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict>
		<key>60B1E2662D844E11006E33C4</key>
		<dict>
			<key>primary</key>
			<true/>
		</dict>
	</dict>
</dict>
</plist>
