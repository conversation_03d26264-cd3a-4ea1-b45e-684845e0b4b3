{"originHash": "ef78aa03badb7189d9f5fa268c7328134f535a53f2f9ca36d819bb0f52ddfa3b", "pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "bbe8b69694d7873315fd3a4ad41efe043e1c07c5", "version": "1.2024072200.0"}}, {"identity": "activityindicatorview", "kind": "remoteSourceControl", "location": "https://github.com/exyte/ActivityIndicatorView", "state": {"revision": "9970fd0bb7a05dad0b6566ae1f56937716686b24", "version": "1.1.1"}}, {"identity": "alamofire", "kind": "remoteSourceControl", "location": "https://github.com/Alamofire/Alamofire.git", "state": {"revision": "513364f870f6bfc468f9d2ff0a95caccc10044c5", "version": "5.10.2"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}}, {"identity": "appauth-ios", "kind": "remoteSourceControl", "location": "https://github.com/openid/AppAuth-iOS.git", "state": {"revision": "2781038865a80e2c425a1da12cc1327bcd56501f", "version": "1.7.6"}}, {"identity": "chat", "kind": "remoteSourceControl", "location": "https://github.com/exyte/Chat.git", "state": {"revision": "a72f04a79b8a4043139fcafda09836a16a150bd8", "version": "2.4.1"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk", "state": {"revision": "eb523407e4293568ed55590728205c359cbecc5b", "version": "11.9.0"}}, {"identity": "giphy-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/Giphy/giphy-ios-sdk", "state": {"revision": "9a3970c258ef5cc005f9359ba4c6a344fe062abe", "version": "2.2.15"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "d80e25104abe76d69a134d4ec18f011edd8df06c", "version": "11.9.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "617af071af9aa1d6a091d59a202910ac482128f9", "version": "10.1.0"}}, {"identity": "googlesignin-ios", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleSignIn-iOS", "state": {"revision": "65fb3f1aa6ffbfdc79c4e22178a55cd91561f5e9", "version": "8.0.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "53156c7ec267db846e6b64c9f4c4e31ba4cf75eb", "version": "8.0.2"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "cc0001a0cf963aa40501d9c2b181e7fc9fd8ec71", "version": "1.69.0"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "a2ab612cb980066ee56d90d60d8462992c07f24b", "version": "3.5.0"}}, {"identity": "gtmappauth", "kind": "remoteSourceControl", "location": "https://github.com/google/GTMAppAuth.git", "state": {"revision": "5d7d66f647400952b1758b230e019b07c0b4b22a", "version": "4.1.1"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "040d087ac2267d2ddd4cca36c757d1c6a05fdbfe", "version": "101.0.0"}}, {"identity": "kingfisher", "kind": "remoteSourceControl", "location": "https://github.com/onevcat/Kingfisher.git", "state": {"revision": "b450a3b30e0767277a6eed7e02dbdebd7d173da0", "version": "8.3.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "libwebp-xcode", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/libwebp-Xcode", "state": {"revision": "0d60654eeefd5d7d2bef3835804892c40225e8b2", "version": "1.5.0"}}, {"identity": "lookinserver", "kind": "remoteSourceControl", "location": "https://github.com/QMUI/LookinServer/", "state": {"revision": "e553d1b689d147817dc54ad5c28fcff71e860101", "version": "1.2.8"}}, {"identity": "lottie-spm", "kind": "remoteSourceControl", "location": "https://github.com/airbnb/lottie-spm.git", "state": {"revision": "8c6edf4f0fa84fe9c058600a4295eb0c01661c69", "version": "4.5.1"}}, {"identity": "mediapicker", "kind": "remoteSourceControl", "location": "https://github.com/exyte/MediaPicker.git", "state": {"revision": "6f8745755530209462b60445ec4ba332c3058474", "version": "2.2.4"}}, {"identity": "moya", "kind": "remoteSourceControl", "location": "https://github.com/Moya/Moya.git", "state": {"revision": "c263811c1f3dbf002be9bd83107f7cdc38992b26", "version": "15.0.3"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "purchases-ios-spm", "kind": "remoteSourceControl", "location": "https://github.com/RevenueCat/purchases-ios-spm.git", "state": {"revision": "cdfe7100fc8cca4a655a9c0f1d34e1fcbc40edd0", "version": "5.28.1"}}, {"identity": "reactiveswift", "kind": "remoteSourceControl", "location": "https://github.com/ReactiveCocoa/ReactiveSwift.git", "state": {"revision": "c43bae3dac73fdd3cb906bd5a1914686ca71ed3c", "version": "6.7.0"}}, {"identity": "rxswift", "kind": "remoteSourceControl", "location": "https://github.com/ReactiveX/RxSwift.git", "state": {"revision": "5dd1907d64f0d36f158f61a466bab75067224893", "version": "6.9.0"}}, {"identity": "sentry-cocoa", "kind": "remoteSourceControl", "location": "https://github.com/getsentry/sentry-cocoa/", "state": {"revision": "21223d1c864db0561d91f48d80f269a363a1625d", "version": "8.47.0"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "d72aed98f8253ec1aa9ea1141e28150f408cf17f", "version": "1.29.0"}}], "version": 3}